# TypeScript Patterns for e4s Project

## Type Definitions
- Define interfaces for all data models in `*-models.ts` files
- Use descriptive interface names with domain context (e.g., `AthleteModel`, `CompetitionEvent`)
- Export interfaces from model files for reuse across components

## Service Layer Types
- Define service method return types explicitly
- Use generic types for common patterns (e.g., `ApiResponse<T>`)
- Create union types for status enums and constants

## Component Types
- Use `Vue.extend()` or `defineComponent()` for component definitions
- Define prop types using PropType from Vue
- Create computed property return types explicitly

## Store Types
- Define state interfaces for Vuex modules
- Type action payloads and mutation parameters
- Use typed getters with proper return types

## Common Type Patterns
```typescript
// Model interfaces
interface AthleteModel {
  id: number;
  firstName: string;
  surName: string;
  gender: string;
  club: string;
  // ... other properties
}

// Service response types
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// Component prop types
interface ComponentProps {
  athlete: AthleteModel;
  showDeleteButton?: boolean;
  readonly?: boolean;
}
```

## Error Handling Types
- Define custom error interfaces
- Use discriminated unions for different error types
- Type catch blocks appropriately

## Utility Types
- Use `Partial<T>` for optional updates
- Use `Pick<T, K>` for selecting specific properties
- Use `Omit<T, K>` for excluding properties

## Import/Export Patterns
- Use named exports for interfaces and types
- Group related types in barrel exports (index.ts)
- Import types with `import type` when possible

## Vue-Specific TypeScript
- Use `PropType<T>` for complex prop types
- Define component data return types
- Type event handlers properly
- Use `$refs` with proper typing

## Async/Promise Types
- Type Promise return values explicitly
- Use async/await with proper error handling
- Define callback function types

## Array and Object Types
- Use array type syntax `T[]` or `Array<T>`
- Define object index signatures when needed
- Use readonly modifiers for immutable data

## Type Guards
- Create type guard functions for runtime type checking
- Use `is` keyword for type predicates
- Implement proper type narrowing

## Configuration Types
- Define configuration interfaces
- Use const assertions for literal types
- Type environment variables and config objects
