# Testing Patterns for e4s Project

## Testing Framework Setup
- Jest as the primary testing framework
- Vue Test Utils for component testing
- TypeScript support with ts-jest
- JSDOM for DOM simulation

## Test File Organization
- Co-locate test files with source files using `.spec.ts` suffix
- Integration tests in separate `jest.config.integration.js`
- Mock data in dedicated mock files
- Shared test utilities in `src/test/` directory

## Unit Testing Patterns

### Service Testing
```typescript
describe('AthleteService', () => {
  let service: AthleteService;
  
  beforeEach(() => {
    service = new AthleteService();
  });
  
  it('should fetch athlete by id', async () => {
    // Arrange
    const athleteId = 1;
    const expectedAthlete = mockAthlete();
    
    // Act
    const result = await service.getAthlete(athleteId);
    
    // Assert
    expect(result).toEqual(expectedAthlete);
  });
});
```

### Component Testing
```typescript
describe('AthleteCard.vue', () => {
  let wrapper: Wrapper<Vue>;
  
  beforeEach(() => {
    wrapper = shallowMount(AthleteCard, {
      propsData: {
        athlete: mockAthlete()
      }
    });
  });
  
  it('should display athlete name', () => {
    expect(wrapper.find('.athlete-name').text()).toContain('John Doe');
  });
});
```

## Mock Patterns
- Create mock data factories for consistent test data
- Mock external dependencies and API calls
- Use Jest mocks for service dependencies
- Mock Vuex store for component tests

```typescript
// Mock data factory
export const mockAthlete = (overrides?: Partial<AthleteModel>): AthleteModel => ({
  id: 1,
  firstName: 'John',
  surName: 'Doe',
  gender: 'M',
  club: 'Test Club',
  ...overrides
});
```

## Component Testing Best Practices
- Test component behavior, not implementation details
- Use shallow mounting for unit tests
- Use full mounting for integration tests
- Test user interactions and events
- Verify prop handling and event emission

## Store Testing
- Test mutations in isolation
- Test actions with mocked dependencies
- Test getters with various state scenarios
- Use mock store for component tests

```typescript
describe('athlete store', () => {
  it('should add athlete to state', () => {
    const state = { athletes: [] };
    const athlete = mockAthlete();
    
    mutations.ADD_ATHLETE(state, athlete);
    
    expect(state.athletes).toContain(athlete);
  });
});
```

## API Testing
- Mock HTTP requests using Jest mocks
- Test error handling scenarios
- Verify request parameters and headers
- Test response data transformation

## Integration Testing
- Test complete user workflows
- Use real store and router instances
- Test component interactions
- Verify data flow between components

## Test Data Management
- Use factories for creating test data
- Maintain test data consistency
- Clean up test data between tests
- Use realistic but anonymized data

## Async Testing
- Use async/await for asynchronous tests
- Test loading states and error states
- Mock timers for time-dependent code
- Test promise resolution and rejection

## Coverage Goals
- Aim for high coverage on business logic
- Focus on critical user paths
- Test error scenarios and edge cases
- Monitor coverage reports regularly

## Test Utilities
- Create helper functions for common test operations
- Provide mock implementations for external services
- Implement test data builders
- Create custom matchers for domain-specific assertions

## Performance Testing
- Test component rendering performance
- Verify memory leak prevention
- Test large dataset handling
- Monitor test execution time

## Debugging Tests
- Use descriptive test names and descriptions
- Implement proper error messages
- Use console logging sparingly
- Leverage Jest debugging features
