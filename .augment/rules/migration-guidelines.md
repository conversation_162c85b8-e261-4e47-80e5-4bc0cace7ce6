# Vue 2 to Vue 3 Migration Guidelines for e4s Project

## Migration Strategy Overview
Based on the established 4-phase migration plan:
1. Phase 1: Update to Vue 2.7, upgrade Webpack 4→5
2. Phase 2: Migrate components (leaf→composition→options→class-based)
3. Phase 3: Vue 3 core migration using @vue/compat
4. Phase 4: Remove compatibility mode and optimize

## Current State (Vue 2.7)
- Leverage Vue 2.7 Composition API features
- Maintain backward compatibility with Options API
- Use `<script setup>` syntax where beneficial
- Prepare components for easier Vue 3 migration

## Component Migration Priorities

### Leaf Components (Phase 2a)
- Start with components that have no child components
- Simple presentational components
- Utility components and UI elements
- Example: buttons, cards, form inputs

### Composition API Components (Phase 2b)
- Components already using Composition API
- Complex state management components
- Components with heavy lifecycle usage

### Options API Components (Phase 2c)
- Standard Vue 2 components
- Components with simple state and methods
- Most existing components in the codebase

### Class-based Components (Phase 2d)
- Components using vue-class-component
- Components with vue-property-decorator
- Most complex migration requiring complete rewrite

## Breaking Changes to Address

### Global API Changes
- `Vue.createApp()` instead of `new Vue()`
- Global properties registration changes
- Plugin installation changes

### Component API Changes
- `$children` removal - use refs or provide/inject
- `$listeners` merged into `$attrs`
- Functional component syntax changes
- Custom directive API changes

### Reactivity System Changes
- New reactivity system with Proxy
- `ref()` and `reactive()` for reactive data
- `computed()` and `watch()` API changes
- Effect cleanup handling

### Router Changes (Vue Router 4)
- New router creation API
- Route component changes
- Navigation guard changes
- History mode configuration

### Store Changes (Vuex 4 or Pinia)
- New store creation API
- TypeScript improvements
- Composition API integration

## Migration Patterns

### Options API to Composition API
```typescript
// Before (Options API)
export default {
  data() {
    return {
      athletes: []
    };
  },
  methods: {
    fetchAthletes() {
      // implementation
    }
  }
};

// After (Composition API)
export default defineComponent({
  setup() {
    const athletes = ref([]);
    
    const fetchAthletes = () => {
      // implementation
    };
    
    return {
      athletes,
      fetchAthletes
    };
  }
});
```

### Class Component Migration
```typescript
// Before (Class Component)
@Component
export default class AthleteCard extends Vue {
  @Prop() athlete!: AthleteModel;
  
  get athleteName() {
    return `${this.athlete.firstName} ${this.athlete.surName}`;
  }
}

// After (Composition API)
export default defineComponent({
  props: {
    athlete: {
      type: Object as PropType<AthleteModel>,
      required: true
    }
  },
  setup(props) {
    const athleteName = computed(() => 
      `${props.athlete.firstName} ${props.athlete.surName}`
    );
    
    return {
      athleteName
    };
  }
});
```

## Compatibility Mode Strategy
- Use @vue/compat for gradual migration
- Enable compatibility warnings
- Migrate components incrementally
- Test thoroughly at each step

## Testing During Migration
- Maintain existing test coverage
- Update tests for new APIs
- Test compatibility mode behavior
- Verify no regressions in functionality

## Performance Considerations
- Monitor bundle size changes
- Test rendering performance
- Optimize for new reactivity system
- Leverage new Vue 3 features for performance

## Common Pitfalls to Avoid
- Don't migrate everything at once
- Maintain backward compatibility during transition
- Test thoroughly in compatibility mode
- Document breaking changes for team

## Tools and Resources
- Vue 3 Migration Build (@vue/compat)
- ESLint rules for Vue 3
- TypeScript support improvements
- Vue DevTools for debugging

## Timeline Considerations
- Allow adequate time for each phase
- Plan for testing and bug fixes
- Consider team training needs
- Coordinate with other project dependencies
