# Augment Rules for e4s Sports Management System

This directory contains comprehensive coding guidelines and patterns for the e4s project, a Vue 2.7 TypeScript application for sports management.

## Rule Files Overview

### [vue-patterns.md](./vue-patterns.md)
Vue.js specific patterns and best practices including:
- Component structure and organization
- Template patterns and directives
- Props, events, and component communication
- Lifecycle hooks and computed properties
- Vue 2.7 Composition API usage

### [typescript-patterns.md](./typescript-patterns.md)
TypeScript usage patterns and conventions:
- Type definitions and interfaces
- Service layer typing
- Component and store types
- Error handling and utility types
- Import/export patterns

### [domain-models.md](./domain-models.md)
Sports management domain knowledge and terminology:
- Core entities (Athlete, Competition, Event, Club)
- Business logic patterns
- Data relationships and operations
- Status enums and integration points

### [architecture.md](./architecture.md)
Application architecture and design patterns:
- Project structure and organization
- Service layer and data access patterns
- Store architecture with Vuex
- Component architecture principles
- API integration and error handling

### [testing.md](./testing.md)
Testing strategies and patterns:
- Jest and Vue Test Utils setup
- Unit and integration testing patterns
- Mock patterns and test data management
- Component and service testing
- Coverage goals and debugging

### [migration-guidelines.md](./migration-guidelines.md)
Vue 2 to Vue 3 migration strategy:
- 4-phase migration plan
- Component migration priorities
- Breaking changes to address
- Compatibility mode strategy
- Common pitfalls and timeline considerations

### [naming-conventions.md](./naming-conventions.md)
Consistent naming patterns across the codebase:
- File and directory naming
- Component and variable naming
- CSS class conventions
- API and event naming
- Type and interface naming

### [code-quality.md](./code-quality.md)
Code quality standards and best practices:
- TypeScript best practices
- Vue component quality guidelines
- Performance and security practices
- Documentation standards
- Code review guidelines

## Project Context

### Technology Stack
- **Frontend**: Vue 2.7 with TypeScript
- **Build Tool**: Vite (with Webpack fallback)
- **State Management**: Vuex
- **Routing**: Vue Router
- **Testing**: Jest with Vue Test Utils
- **Styling**: CSS with Materialize framework

### Domain Focus
The e4s system manages athletic competitions and events, handling:
- Athlete registration and management
- Competition setup and administration
- Event scheduling and results
- Club and team management
- Payment processing and entry management

### Migration Status
Currently in Vue 2.7 with planned migration to Vue 3 following a structured 4-phase approach. The codebase leverages Vue 2.7's Composition API features while maintaining backward compatibility.

## Usage Guidelines

### For Developers
1. Review relevant rule files before starting new features
2. Follow established patterns for consistency
3. Use the migration guidelines when updating components
4. Refer to testing patterns for comprehensive test coverage

### For Code Reviews
1. Check adherence to naming conventions
2. Verify architectural patterns are followed
3. Ensure proper TypeScript usage
4. Validate Vue component best practices

### For New Team Members
1. Start with domain-models.md to understand the business context
2. Review architecture.md for overall system design
3. Study vue-patterns.md and typescript-patterns.md for coding standards
4. Use testing.md for writing effective tests

## Continuous Improvement

These rules should evolve with the project. When patterns emerge or change:
1. Update relevant rule files
2. Communicate changes to the team
3. Update existing code gradually
4. Maintain consistency across the codebase

## Related Documentation

- [Vue 2.7 Documentation](https://v2.vuejs.org/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Vue Test Utils](https://vue-test-utils.vuejs.org/)
- [Jest Documentation](https://jestjs.io/docs/getting-started)
