# Vue Component Patterns for e4s Project

## Component Structure
- Use Vue 2.7 with Composition API where beneficial
- Maintain Options API for existing components unless migrating
- Follow single-file component (.vue) structure with `<template>`, `<script>`, `<style>`

## Component Naming
- Use PascalCase for component names in imports
- Use kebab-case in templates
- Prefix custom components with domain context (e.g., `athlete-`, `competition-`, `event-`)

## Props and Events
- Define props with TypeScript interfaces
- Use descriptive prop names that indicate data type and purpose
- Emit events with descriptive names using kebab-case
- Always validate props with proper types

## Template Patterns
- Use `v-text` for simple text binding instead of mustache syntax when possible
- Use `v-html` sparingly and only for trusted content
- Prefer `v-if`/`v-else` over `v-show` for conditional rendering
- Use `v-for` with `:key` for list rendering

## Component Communication
- Use props down, events up pattern
- Leverage Vuex store for shared state
- Use provide/inject for deep component trees
- Emit custom events for parent communication

## Lifecycle Hooks
- Use `created()` for data initialization
- Use `mounted()` for DOM manipulation
- Use `beforeDestroy()` for cleanup
- Avoid heavy operations in `updated()`

## Computed Properties
- Use computed properties for derived data
- Keep computed properties pure (no side effects)
- Use getters for complex data transformations

## Methods
- Keep methods focused and single-purpose
- Use async/await for asynchronous operations
- Handle errors appropriately with try/catch

## Styling
- Use scoped styles with `<style scoped>`
- Follow existing CSS class naming conventions (e4s-*)
- Use CSS custom properties for theming
- Avoid inline styles except for dynamic values

## Vue 2.7 Specific
- Leverage Composition API for new complex components
- Use `defineComponent` for better TypeScript support
- Utilize `<script setup>` syntax where appropriate
- Maintain backward compatibility with Options API

## Common Patterns in e4s
- Card-based layouts for data display
- Grid components for tabular data
- Form components with validation
- Modal dialogs for user interactions
- Type-ahead components for search functionality
