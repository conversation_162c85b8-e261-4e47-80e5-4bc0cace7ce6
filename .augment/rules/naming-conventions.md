# Naming Conventions for e4s Project

## File Naming

### Vue Components
- Use kebab-case for component files: `athlete-card.vue`
- Include domain prefix: `athlete-maint-card.vue`
- Use descriptive names that indicate purpose
- Avoid abbreviations unless commonly understood

### TypeScript Files
- Use kebab-case with descriptive suffixes:
  - `athlete-models.ts` - Type definitions
  - `athlete-service.ts` - Business logic
  - `athlete-data.ts` - Data access
  - `athlete-store.ts` - Vuex store
  - `athlete.spec.ts` - Unit tests

### Directories
- Use lowercase with hyphens for multi-word names
- Group by domain/feature: `athlete/`, `competition/`, `event/`
- Use descriptive subdirectories: `admin/`, `reports/`, `ui/`

## Component Naming

### Component Classes/Exports
- Use PascalCase: `AthleteCard`, `CompetitionForm`
- Include domain context in name
- Be descriptive about component purpose

### Component Usage in Templates
- Use kebab-case: `<athlete-card>`, `<competition-form>`
- Match the file name convention
- Use self-closing tags when no content

## Variable and Method Naming

### Variables
- Use camelCase: `athleteData`, `competitionList`
- Use descriptive names that indicate data type
- Avoid single-letter variables except for loops
- Use boolean prefixes: `isLoading`, `hasError`, `canEdit`

### Methods
- Use camelCase with verb prefixes: `getAthlete()`, `saveCompetition()`
- Use descriptive action words: `fetch`, `create`, `update`, `delete`
- Event handlers: `onAthleteClick()`, `handleFormSubmit()`
- Computed properties: `athleteName`, `competitionStatus`

### Constants
- Use SCREAMING_SNAKE_CASE: `API_BASE_URL`, `MAX_ATHLETES_PER_TEAM`
- Group related constants in objects or enums
- Use descriptive names that indicate purpose

## CSS Class Naming

### Component-Specific Classes
- Use kebab-case with component prefix: `.athlete-card-name`
- Follow BEM methodology where appropriate: `.athlete-card__header--active`
- Use semantic names over presentational: `.athlete-status` not `.red-text`

### Utility Classes
- Follow existing e4s conventions: `.e4s-flex-row`, `.e4s-gap--standard`
- Use consistent spacing and sizing scales
- Prefix with project identifier: `e4s-`

## API and Data Naming

### API Endpoints
- Use RESTful conventions: `/api/athletes`, `/api/competitions/{id}`
- Use plural nouns for collections
- Use clear resource names that match domain models

### Database/Model Properties
- Use camelCase in TypeScript: `firstName`, `surName`
- Match API response structure
- Use consistent naming across related models

### Store/State Properties
- Use camelCase: `selectedAthlete`, `competitionList`
- Use descriptive names that indicate state purpose
- Group related state properties logically

## Event Naming

### Custom Events
- Use kebab-case: `athlete-selected`, `competition-updated`
- Use descriptive action names
- Include context when needed: `athlete-card-clicked`

### Event Handlers
- Prefix with `on` or `handle`: `onAthleteSelected`, `handleFormSubmit`
- Use descriptive names that indicate the action
- Match the event name pattern

## Type and Interface Naming

### Interfaces
- Use PascalCase with descriptive names: `AthleteModel`, `CompetitionEvent`
- Suffix with purpose: `Model`, `Config`, `Options`, `Response`
- Avoid generic names like `Data` or `Info`

### Enums
- Use PascalCase: `CompetitionStatus`, `AthleteGender`
- Use descriptive enum values: `DRAFT`, `OPEN`, `CLOSED`

### Type Aliases
- Use PascalCase: `AthleteId`, `CompetitionCallback`
- Be specific about the type purpose

## Route Naming

### Route Paths
- Use kebab-case: `/athlete-management`, `/competition-results`
- Use hierarchical structure: `/admin/athletes`, `/public/competitions`
- Use descriptive path segments

### Route Names
- Use camelCase: `athleteManagement`, `competitionResults`
- Include context for nested routes: `adminAthletes`, `publicCompetitions`

## Test Naming

### Test Files
- Match source file with `.spec.ts` suffix: `athlete-service.spec.ts`
- Use same naming convention as source files

### Test Descriptions
- Use descriptive sentences: `'should fetch athlete by id'`
- Include context and expected behavior
- Use consistent tense and structure

## Documentation Naming

### README Files
- Use UPPERCASE: `README.md`
- Include descriptive filenames for specific docs: `MIGRATION_GUIDE.md`

### Comment Conventions
- Use clear, descriptive comments
- Explain why, not what
- Use JSDoc format for functions and classes
