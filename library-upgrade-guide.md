# Library Upgrade Guide

## Overview
This guide provides step-by-step instructions for upgrading the independent libraries identified in the analysis. Follow these instructions carefully, testing after each upgrade.

## Prerequisites

### 1. Node.js Version Update
Before starting, ensure you're using Node.js 18.19.0:

```bash
# Switch to Node.js 18.19.0
nvm use 18.19.0

# Verify the version
node --version
# Expected output: v18.19.0
```

### 2. Create Backup
Follow the instructions in `package-backup-instructions.md` to create a backup of your current package.json and package-lock.json.

## Library Upgrade Instructions

### Priority 1: Core Utilities

#### 1. axios Upgrade

**Current Version**: 0.19.2
**Target Version**: 1.x (latest)

##### Step 1: Update axios
```bash
# Update axios to the latest 1.x version
npm install axios@^1.0.0

# Or update to a specific version
npm install axios@1.6.2
```

##### Step 2: Verify Installation
```bash
# Check the installed version
npm list axios
# Expected: axios@1.6.2 (or latest 1.x)
```

##### Step 3: Test Development Server
```bash
# Start development server
npm run dev
```

**Expected Results**:
- Server starts without errors
- No runtime errors in browser console
- Build time should be reasonable (< 60 seconds)

**Potential Issues**:
- TypeScript errors related to axios API changes
- Breaking changes in interceptors or request/response handling

##### Step 4: Test Production Build
```bash
# Create production build
npm run build
```

**Expected Results**:
- Build completes without errors
- No significant warnings
- Output files generated correctly

##### Step 5: Regression Testing
Test the following features that likely use axios:
- API calls to backend services
- Form submissions
- Data fetching operations
- Error handling for network requests

#### 2. core-js Upgrade

**Current Version**: 3.6.5
**Target Version**: Latest 3.x

##### Step 1: Update core-js
```bash
# Update core-js to the latest version
npm install core-js@^3.0.0

# Or update to a specific version
npm install core-js@3.34.0
```

##### Step 2: Verify Installation
```bash
# Check the installed version
npm list core-js
# Expected: core-js@3.34.0 (or latest 3.x)
```

##### Step 3: Test Development Server
```bash
# Start development server
npm run dev
```

**Expected Results**:
- Server starts without errors
- No runtime errors related to polyfills
- All JavaScript features work correctly

**Potential Issues**:
- Changes in polyfill behavior
- Missing polyfills for specific browsers
- Bundle size changes

##### Step 4: Test Production Build
```bash
# Create production build
npm run build
```

**Expected Results**:
- Build completes without errors
- Bundle size may change (monitor for significant increases)

##### Step 5: Regression Testing
Test the following:
- ES6+ features in the application
- Browser compatibility (test in target browsers)
- Performance of JavaScript operations

### Priority 2: Utility Libraries

#### 3. date-fns Upgrade

**Current Version**: 1.30.1
**Target Version**: 2.x or 3.x

**Important Note**: This is a major version upgrade with significant API changes.

##### Step 1: Update date-fns
```bash
# Update date-fns to version 2.x
npm install date-fns@^2.0.0

# Or update to a specific version
npm install date-fns@2.30.0
```

##### Step 2: Verify Installation
```bash
# Check the installed version
npm list date-fns
# Expected: date-fns@2.30.0 (or latest 2.x)
```

##### Step 3: Test Development Server
```bash
# Start development server
npm run dev
```

**Expected Results**:
- Server starts, but likely with import errors
- TypeScript errors related to date-fns API changes

**This is expected** - we need to update the imports in the next step.

##### Step 4: Update date-fns Imports

The major API changes between v1 and v2 include:

**Before (v1)**:
```typescript
import { format, addDays } from 'date-fns';
format(new Date(), 'MM/DD/YYYY');
addDays(new Date(), 1);
```

**After (v2)**:
```typescript
import { format, addDays } from 'date-fns';
format(new Date(), 'MM/dd/yyyy'); // Note: case change in format
addDays(new Date(), 1);
```

**Common Changes Needed**:
1. Format strings are now case-sensitive (use lowercase)
2. Some function signatures have changed
3. Locale handling has changed

##### Step 5: Find and Update date-fns Usage

Search for date-fns imports in your codebase:
```bash
# Find all files using date-fns
grep -r "date-fns" src/
```

Update each file according to the v2 API changes.

##### Step 6: Test After Updates
```bash
# Test development server
npm run dev

# Test production build
npm run build
```

##### Step 7: Regression Testing
Test all date/time functionality:
- Date display formatting
- Date calculations
- Time zone handling
- Calendar/date picker components

#### 4. ramda Upgrade

**Current Version**: 0.27.0
**Target Version**: Latest

##### Step 1: Update ramda
```bash
# Update ramda to the latest version
npm install ramda@latest

# Or update to a specific version
npm install ramda@0.29.1
```

##### Step 2: Verify Installation
```bash
# Check the installed version
npm list ramda
# Expected: ramda@0.29.1 (or latest)
```

##### Step 3: Test Development Server
```bash
# Start development server
npm run dev
```

**Expected Results**:
- Server starts without errors
- No breaking changes expected (ramda has stable API)

##### Step 4: Test Production Build
```bash
# Create production build
npm run build
```

**Expected Results**:
- Build completes without errors

##### Step 5: Regression Testing
Test functional programming features:
- Data transformation operations
- Function composition
- Immutable operations
- Utility functions

#### 5. socket.io-client Upgrade

**Current Version**: 4.0.1
**Target Version**: Latest

##### Step 1: Update socket.io-client
```bash
# Update socket.io-client to the latest version
npm install socket.io-client@latest

# Or update to a specific version
npm install socket.io-client@4.7.4
```

##### Step 2: Verify Installation
```bash
# Check the installed version
npm list socket.io-client
# Expected: socket.io-client@4.7.4 (or latest)
```

##### Step 3: Test Development Server
```bash
# Start development server
npm run dev
```

**Expected Results**:
- Server starts without errors
- No breaking changes in socket connection

**Potential Issues**:
- Changes in connection options
- API changes in event handling
- TypeScript definition changes

##### Step 4: Test Production Build
```bash
# Create production build
npm run build
```

**Expected Results**:
- Build completes without errors

##### Step 5: Regression Testing
Test real-time features:
- WebSocket connections
- Real-time data updates
- Event handling
- Connection management

## Testing Protocol

### After Each Upgrade

#### 1. Development Server Test
```bash
npm run dev
```

**Checklist**:
- [ ] Server starts without errors
- [ ] No TypeScript compilation errors
- [ ] No runtime errors in browser console
- [ ] Build time is reasonable (< 60 seconds)
- [ ] Hot reload works correctly

#### 2. Production Build Test
```bash
npm run build
```

**Checklist**:
- [ ] Build completes without errors
- [ ] No significant warnings
- [ ] Output files generated correctly
- [ ] Bundle size is reasonable
- [ ] Source maps generated correctly

#### 3. Regression Testing
**Checklist**:
- [ ] All major application features work
- [ ] No performance degradation
- [ ] No breaking changes in functionality
- [ ] Browser compatibility maintained
- [ ] Mobile responsiveness intact

## Troubleshooting

### Common Issues and Solutions

#### 1. TypeScript Errors After Upgrade
**Issue**: TypeScript compilation errors after library upgrade
**Solution**:
```bash
# Clear TypeScript cache
rm -rf .tsbuildinfo

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Update TypeScript if needed
npm install typescript@latest
```

#### 2. Build Time Increase
**Issue**: Build time significantly increases after upgrade
**Solution**:
```bash
# Check bundle analyzer
npm run build -- --profile
# Then check the report
```

#### 3. Runtime Errors
**Issue**: Application works in dev but fails in production
**Solution**:
```bash
# Check for development-only dependencies
npm list --depth=0

# Check for missing polyfills
# Review core-js configuration
```

#### 4. Dependency Conflicts
**Issue**: Conflicts between updated library and other dependencies
**Solution**:
```bash
# Check for dependency conflicts
npm ls axios
# Look for multiple versions

# Use npm resolutions to force specific versions
# Add to package.json:
"resolutions": {
  "axios": "1.6.2"
}
```

## Rollback Procedure

If any upgrade causes issues:

1. **Stop the upgrade process**
2. **Document the issue** in `library-upgrade-log.md`
3. **Restore from backup**:
   ```bash
   git checkout HEAD -- package.json package-lock.json
   npm install
   ```
4. **Verify functionality**:
   ```bash
   npm run dev
   npm run build
   ```
5. **Proceed with next library** or investigate the issue further

## Documentation Updates

After each successful upgrade:

1. **Update** `library-upgrade-log.md` with:
   - Version changes
   - Build times
   - Issues encountered
   - Solutions applied

2. **Update** any internal documentation that references the upgraded libraries

3. **Communicate** changes to the team if applicable

## Next Steps

After completing all library upgrades:

1. **Review** all changes in `library-upgrade-log.md`
2. **Test** the entire application thoroughly
3. **Commit** the changes:
   ```bash
   git add package.json package-lock.json
   git commit -m "Upgrade independent libraries"
   ```
4. **Proceed** to the next phase of the Vue 3 migration