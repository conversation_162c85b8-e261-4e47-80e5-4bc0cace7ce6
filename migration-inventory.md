# Vue Migration Inventory

## Overview
This document provides a comprehensive inventory of the Vue.js application components, dependencies, and custom implementations that need to be considered for the Vue 2 to Vue 3 migration.

## Current Dependencies Analysis

### Core Vue Dependencies
- Vue: 2.6.14
- @vue/composition-api: 1.3.3 (already installed, good for migration)
- vue-router: 3.1.6
- vuex: 3.1.1
- vue-class-component: 7.2.3
- vue-property-decorator: 8.4.2
- vue-loader: 15.9.2
- vue-template-compiler: 2.6.14

### Third-Party UI Libraries
- vee-validate: 2.2.2 (validation library)
- vue-ctk-date-time-picker: 2.5.0 (datetime picker)
- vue-i18n: 8.17.5 (internationalization)
- vue-mq: 1.0.1 (media queries)
- vue2-editor: 2.10.2 (rich text editor)

### Other Key Dependencies
- axios: 0.19.2 (HTTP client)
- ramda: 0.27.0 (utility library)
- date-fns: 1.30.1 (date utilities)
- socket.io-client: 4.0.1 (WebSockets)
- typescript: 3.9.2
- webpack: 4.43.0

## Component Inventory

### athleteCompSched Directory Components
- ageinfo.vue
- athlete-type-ahead.vue
- athletecompsched.vue
- AthleteRegistrationAlert.vue
- comp-rules.vue
- event-card.vue
- event-switch-modal.vue
- events-card-grid.vue
- order-summary.vue
- showcompsched.vue

### Common UI Components
- buttons-cancel-save.vue
- close-back.vue
- close-icon.vue
- common-crud-form.vue
- delete-icon.vue
- e4s-header.vue
- e4s-layout.vue
- e4s-modal.vue
- edit-icon.vue
- file-upload.vue
- gender-select.vue
- loading-spinner-row.vue
- loading-spinner-v2.vue
- loading-spinner.vue
- power-of-ten-link.vue
- section-divider-line.vue
- simple-object-drop-down.vue

### Athlete Directory Components
- athlete-card-grid.vue
- athlete-card.vue
- athlete-delete.vue
- athlete-filter.vue
- athlete-grid.vue
- athlete-po10-link.vue

### Additional Component Directories
- admin/ (multiple subdirectories with components)
- auth/ (authentication components)
- builder/ (form builder components)
- cart/ (shopping cart components)
- club/ (club management components)
- competition/ (competition management components)
- launch/ (launch page components)

## Custom Directives

### click-outside.ts
- **Purpose**: Handles clicks outside an element
- **Current Implementation**: Uses Vue 2 directive API with bind/unbind hooks
- **Migration Considerations**: Will need to be updated to Vue 3 directive API
- **Usage**: Used in index.ts for global registration

### nickPurple (Directive Function)
- **Purpose**: Simple directive to change text color to purple
- **Current Implementation**: Simple function that modifies element style
- **Migration Considerations**: Will need to be updated to Vue 3 directive API

## Custom Validators

### validator-extend.ts
- **Purpose**: Extends vee-validate with custom validators
- **Current Implementation**: Uses Validator.extend() method
- **Custom Validators**:
  - dateValidator: Validates date format (DD/MM/YYYY)
  - timeValidator: Validates time format (HH:mm)
- **Migration Considerations**: 
  - vee-validate has significant changes between v2 and v3
  - Will need to update validation syntax and possibly migration approach

## Global Components and Mixins

### common-vue.ts
- **Purpose**: Registers vue-class-component hooks
- **Current Implementation**: Uses Component.registerHooks() for vue-router navigation guards
- **Registered Hooks**:
  - beforeRouteEnter
  - beforeRouteLeave
  - beforeRouteUpdate
- **Migration Considerations**: 
  - vue-class-component behavior changes in Vue 3
  - May need to update to use Composition API with vue-router

## File Casing Issues

### Identified Issues
- Directory named "athleteCompSched" (camelCase)
- Potential references with incorrect casing "athletecompsched" (all lowercase)
- This needs to be systematically addressed before migration

### Impact
- Import statements may break on case-sensitive systems
- Build processes may fail
- Development environment inconsistencies

## Key Migration Challenges

### 1. Third-Party Library Compatibility
- vee-validate v2 to v3/v4 migration required
- vue-ctk-date-time-picker may need replacement
- vue-i18n v8 to v9 migration required
- vue2-editor needs replacement for Vue 3 compatibility

### 2. TypeScript Configuration
- Current TypeScript version (3.9.2) needs updating
- Vue 3 has different type definitions
- Component typing will need updates

### 3. Build System
- Migration from Webpack 4 to Vite recommended
- Build configuration will need complete rewrite
- Plugin compatibility needs verification

### 4. State Management
- Vuex 3 to 4 or Pinia migration required
- Store patterns may need refactoring for Composition API

### 5. Component Architecture
- Mix of Options API and some Composition API (via @vue/composition-api)
- Need to standardize on Composition API
- Class components (vue-class-component) need refactoring

## Recommended Migration Order

1. **Address file casing issues** - Critical first step
2. **Update TypeScript and build tools** - Foundation for other changes
3. **Third-party library updates** - Handle breaking changes
4. **Custom directives and validators** - Update to Vue 3 APIs
5. **Component migration** - Options API to Composition API
6. **State management migration** - Vuex to Pinia recommended
7. **Testing and validation** - Ensure all functionality works

## Next Steps

1. Create a systematic approach to fix file casing issues
2. Set up automated testing to catch regressions during migration
3. Create proof-of-concept for migrating complex components
4. Establish migration patterns for reusable code