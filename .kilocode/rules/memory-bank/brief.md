# Project Brief — Entry4Sports (E4S)

- Purpose: A web application for athletics competition entry, management, payments, and public results viewing. Organisers configure competitions, athletes enter events, and officials manage results and schedules.
- Audience:
  - Competition organisers and officials
  - Club/team managers and athletes
  - Public viewers (results, schedules)
- High-level goals:
  - Reliable entry and payment workflows for single and multi-event competitions
  - Tools for check-in, bib allocation, schedule/heat management, and live scoreboards
  - Public-facing results and ticketing flows
- Current state:
  - SPA built with Vue 2.7 + TypeScript, bundled by Vite 6
  - Routing with vue-router 3; state management with Vuex 3
  - Legacy class-style components still in use (vue-class-component, vue-property-decorator)
- Success criteria:
  - Stable dev and production builds via Vite
  - Low-friction entry flows and accurate fee calculations
  - Scalable rendering for large competitions and teams
  - Clear public results and scoreboard UX
- Key constraints and considerations:
  - Vue 2.7 runtime with an eventual path to Vue 3 (compat/migration tracked separately)
  - Conservative dependency upgrades due to wide feature surface and legacy modules
  - Backwards compatibility for organiser tools and URLs where possible
- Repository landmarks:
  - App bootstrap: [new Vue()](e4s/src/index.ts:417), router creation at [router](e4s/src/index.ts:265)
  - Build config: [e4s/vite.config.ts](e4s/vite.config.ts) (entry uses [defineConfig()](e4s/vite.config.ts:1))
  - HTML entry: [e4s/index.html](e4s/index.html)
  - Global store: [e4s/src/app.store.ts](e4s/src/app.store.ts)
  - Vite upgrade documentation: [e4s/docs/vite-upgrade-steps.md](e4s/docs/vite-upgrade-steps.md)
- Out of scope (for now):
  - Backend service definitions and deployment infra details
  - Full Vue 3 migration plan (tracked in dedicated docs)