# Context — Entry4Sports (E4S)

Current objective
- ✅ **RESOLVED**: Critical circular dependency issue that was preventing Vite builds
- ✅ **RESOLVED**: Secondary circular dependencies in store modules (IRootState imports)
- Monitor production stability and continue Vue 2.7 hardening while preparing for eventual Vue 3 migration

Recent changes
- **CRITICAL FIX (2025-09-23)**: Resolved build-breaking circular dependency
  - Created runtime context utility: [runtime-context.ts](e4s/src/common/runtime-context.ts:1)
  - Refactored HTTP client to use lazy access: [https.ts](e4s/src/common/https.ts:7)
  - Initialize context after store/router creation: [index.ts](e4s/src/index.ts:262)
  - Documentation: [circular-dependency-fix.md](e4s/docs/circular-dependency-fix.md:1)
- **RUNTIME FIX (2025-09-24)**: Resolved secondary circular dependencies in store modules
  - Fixed all IRootState imports in store modules to import from [root-state.ts](e4s/src/store/root-state.ts) instead of [app.store.ts](e4s/src/app.store.ts)
  - Fixed store state initialization to avoid premature service container access
  - Affected 17 store modules across the application
  - Eliminated "Cannot access 'Zr' before initialization" and "Service not found: configService" runtime errors
- **Build Status**: ✅ Production builds working, ✅ Dev server working
- Added Memory Bank scaffold:
  - Instructions at [.kilocode/rules/memory-bank-instructions.md](.kilocode/rules/memory-bank-instructions.md)
  - Brief at [.kilocode/rules/memory-bank/brief.md](.kilocode/rules/memory-bank/brief.md)
  - Product overview at [.kilocode/rules/memory-bank/product.md](.kilocode/rules/memory-bank/product.md)
- Verified project entry/runtime and build:
  - Bootstrap at [new Vue()](e4s/src/index.ts:417) with router created at [router](e4s/src/index.ts:265)
  - Runtime context initialized at [setRuntimeContext()](e4s/src/index.ts:262)
  - Build config via [defineConfig()](e4s/vite.config.ts:1); dev server and proxy defined in [vite.config.ts](e4s/vite.config.ts:43)
  - HTML entry at [e4s/index.html](e4s/index.html:27)
- Dev ergonomics:
  - Performance flag uses Vite's env: [Vue.config.performance](e4s/src/index.ts:127)
  - ESLint wired during dev using vite-plugin-checker (TypeScript overlay disabled): [checker()](e4s/vite.config.ts:27)
  - Visualizer output at dist/vite-stats.html: [visualizer()](e4s/vite.config.ts:21)

Decisions (current)
- Stay on Vue 2.7 runtime while completing Vite hardening; Vue 3 migration tracked separately in [docs/vue3-class-component-migration.md](e4s/docs/vue3-class-component-migration.md:238).
- Keep class-style component support where needed (vue-class-component, vue-property-decorator).
- Use Vite proxy for API resources to configurable dev host: [server.proxy](e4s/vite.config.ts:47).
- Prefer explicit render function at root rather than in-DOM template compilation: [render(h)](e4s/src/index.ts:447).
- Keep router meta-based titles resolved in guards; default title fallback to “Entry4Sports”: [isLoggedInGuardGeneric()](e4s/src/index.ts:321).

Operational notes
- Global error handling funnels to admin messaging: [Vue.config.errorHandler](e4s/src/index.ts:99).
- App config hydration on startup:
  - Vuex dispatch in [beforeCreate()](e4s/src/index.ts:423) loads app config and sets theme from [window.E4S_GLOBAL_THEME_IDENTIFIER](e4s/index.html:29).
  - Additional config fetch path exists in login guards for anonymous users: [ConfigData().getAppConfig()](e4s/src/index.ts:353).
- Router lifecycle logging present for navigation tracing: [router.afterEach](e4s/src/index.ts:267).
- Responsive breakpoints centralized via Vue MQ: [Vue.use(VueMq,…)](e4s/src/index.ts:85).

Environment and configuration
- Vite env usage: `import.meta.env.PROD` consumed at [performance flag](e4s/src/index.ts:127).
- Dev host variable: `VITE_E4S_HOST` with default fallback in [vite config](e4s/vite.config.ts:12).
- Dev lint toggle: `VITE_DISABLE_DEV_LINT` observed at [vite config](e4s/vite.config.ts:11).
- Dev port: 5173: [server.port](e4s/vite.config.ts:44).
- Build output directory: `dist/vite`: [build.outDir](e4s/vite.config.ts:59).

Risks and watchouts
- Legacy class components may mask reactivity edge cases; maintain conservative upgrades.
- Vite/TypeScript checker overlay disabled for now to avoid version drift; rely on separate TS checks in CI if needed.
- CDN-loaded globals (jQuery, Materialize) from [index.html](e4s/index.html:16) require network availability during runtime in dev; consider vendoring or public/ assets in future.

Next steps (immediate - next 1-2 weeks)
- **Monitor production**: Ensure no runtime issues with the circular dependency fixes
- Validate Memory Bank activation on task start and refine summaries.

Next steps (medium term - next 2-4 weeks)
- **Service Locator Pattern**: Design and implement centralized service container
- **Refactor Data Services**: Update all 35+ data service classes to use service locator
- **Lazy Service Loading**: Implement performance improvements through lazy instantiation

Later steps (tracked)
- **Full Dependency Injection**: HTTP service abstraction and service registration system (4-6 weeks)
- Begin Vue 3 compat migration per [migration doc](e4s/docs/vue3-class-component-migration.md:245).
- Tighten lint/type-check flows (optionally re-enable TS overlay or add separate `type-check` script).
- Consider moving third-party CDNs to local/public assets aligned with Vite conventions.

Maintenance guidance
- Update this file whenever significant architectural or process changes occur.
- Keep "Decisions (current)" accurate; add "Decided: …" entries with date when applicable.
- Record blockers with minimal reproduction and link to the lines where they occur.
- **NEW**: When adding services, use runtime context pattern to avoid circular dependencies
- **NEW**: Monitor build performance and circular dependency warnings during development

Recent decisions
- **Decided (2025-09-23)**: Use runtime context pattern for breaking circular dependencies rather than full dependency injection rewrite
- **Decided (2025-09-23)**: Maintain backward compatibility while implementing incremental architectural improvements
- **Decided (2025-09-23)**: Document comprehensive refactoring strategy for future implementation