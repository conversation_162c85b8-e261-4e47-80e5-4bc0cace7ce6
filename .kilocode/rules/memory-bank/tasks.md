# Tasks — Entry4Sports (E4S)

Repeatable procedures and runbooks for common workflows. Keep commands concise and deterministic.

Prerequisites
- Node.js 18+ and npm
- Working directory for commands: e4s/

Install
- Fresh install:
  - cd e4s && npm install

Development server
- Start Vite dev server (http://localhost:5173):
  - npm run vite:dev
- Notes:
  - API proxy target resolved from Vite env var VITE_E4S_HOST in [defineConfig()](e4s/vite.config.ts:1)
  - ESLint overlay via vite-plugin-checker can be disabled with VITE_DISABLE_DEV_LINT in [defineConfig()](e4s/vite.config.ts:1)
  - Router created at [router](e4s/src/index.ts:265); afterEach logs at [router.afterEach()](e4s/src/index.ts:267)

Preview built app
- Serve the production build locally:
  - npm run vite:preview

Production build
- Build artifacts to dist/vite:
  - npm run vite:build
- Bundle visualization:
  - Open dist/vite-stats.html (generated by [visualizer()](e4s/vite.config.ts:21))

Lint and format
- Lint all .ts/.js/.vue:
  - npm run lint
- Auto-fix (where safe):
  - npm run lint:fix

Unit tests
- Run Jest in watch-all:
  - npm test

Troubleshooting
- Titles not updating:
  - Check [isLoggedInGuardGeneric()](e4s/src/index.ts:321) where document.title is set (default at [document.title assignment](e4s/src/index.ts:343))
- Perf flag unexpected:
  - Confirm `import.meta.env.PROD` usage at [Vue.config.performance](e4s/src/index.ts:127)
- Anonymous bootstrap/config missing:
  - Flow starts at [ConfigData().getAppConfig()](e4s/src/index.ts:353) and commits to store in guards
- Proxy/API calls failing in dev:
  - Verify proxy config at [server.proxy](e4s/vite.config.ts:47) and ensure VITE_E4S_HOST is set

Memory Bank — maintenance workflow
- Files live under .kilocode/rules/memory-bank/
  - Project brief: .kilocode/rules/memory-bank/brief.md
  - Product: .kilocode/rules/memory-bank/product.md
  - Context (update frequently): .kilocode/rules/memory-bank/context.md
  - Architecture: .kilocode/rules/memory-bank/architecture.md
  - Tech: .kilocode/rules/memory-bank/tech.md
  - This procedures file: .kilocode/rules/memory-bank/tasks.md
  - Authoring guide: [.kilocode/rules/memory-bank-instructions.md](.kilocode/rules/memory-bank-instructions.md)
- Update cadence:
  - Append significant changes to context.md after each meaningful task
  - Record technical decisions and module boundaries in architecture.md with links to source (e.g., [new Vue()](e4s/src/index.ts:417), [render(h)](e4s/src/index.ts:447))
  - Reflect dependency/tooling changes in tech.md (e.g., [plugins](e4s/vite.config.ts:15))
- Validation:
  - Start a new task; expect Kilo Code to begin with “Memory Bank: Active” and a short summary
  - If not active, verify file paths and that content resides in .kilocode/rules/memory-bank/

Release checklist (lightweight)
- Build:
  - npm run vite:build
- Smoke preview:
  - npm run vite:preview
- Inspect bundles:
  - Open dist/vite-stats.html (created by [visualizer()](e4s/vite.config.ts:21))
- Spot-check critical routes:
  - Launch/home (children from [launch routes](e4s/src/index.ts:174))
  - Login/register ([isLoggedInGuard()](e4s/src/index.ts:315), [isLoggedInGuardV2()](e4s/src/index.ts:309))
  - Scoreboard ([scoreboard route](e4s/src/index.ts:219), [scoreboard-routes](e4s/src/index.ts:222))

Notes
- Root bootstrap is classically styled Vue 2.7: [new Vue()](e4s/src/index.ts:417) with explicit [render(h)](e4s/src/index.ts:447)
- Store entry point at [appStore](e4s/src/app.store.ts:150); use [useStore()](e4s/src/app.store.ts:159) in composition contexts
- Keep CDN availability in mind for dev (see [index.html](e4s/index.html:16)); consider vendoring when feasible