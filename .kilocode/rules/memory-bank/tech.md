# Tech — Entry4Sports (E4S)

Stack and runtimes
- Language: TypeScript 5.x (dev dep at 5.4.5 from [e4s/package.json](e4s/package.json))
- Framework: Vue 2.7.x (runtime at 2.7.16 from [e4s/package.json](e4s/package.json))
- Router: vue-router 3.x (3.6.5)
- State: Vuex 3.x (3.6.2) with typed root store at [appStore](e4s/src/app.store.ts:150)
- Build tool: Vite 6.x with Vue 2 plugin and legacy plugin via [defineConfig()](e4s/vite.config.ts:1)
- Linting: ESLint 8 + @typescript-eslint + eslint-plugin-vue; wired in dev via [checker()](e4s/vite.config.ts:27)
- Internationalization: vue-i18n 8.x configured in [i18n](e4s/src/config/il8n.ts:23)
- Validation: VeeValidate 2.x registered at [Vue.use(VeeValidate)](e4s/src/index.ts:42)
- Responsive breakpoints: vue-mq registered at [Vue.use(VueMq,…)](e4s/src/index.ts:85)

Node and tooling requirements
- Node.js: 18+ recommended for Vite 6 compatibility
- Package manager: npm (package-lock not committed here; use npm to match scripts in [e4s/package.json](e4s/package.json))

Project entry points
- HTML entry and mount point: [e4s/index.html](e4s/index.html) with <div id="app"> and module script to [/src/index.ts](e4s/index.html:27)
- Application bootstrap: [new Vue()](e4s/src/index.ts:417) with explicit [render(h)](e4s/src/index.ts:447)
- Router instance and afterEach logging: [router](e4s/src/index.ts:265) and [router.afterEach()](e4s/src/index.ts:267)

Vite configuration
- Plugins: [@vitejs/plugin-vue2](e4s/vite.config.ts:2), [@vitejs/plugin-legacy](e4s/vite.config.ts:3), [rollup-plugin-visualizer](e4s/vite.config.ts:4), [vite-plugin-checker](e4s/vite.config.ts:5)
- Aliases: @ → src at [resolve.alias](e4s/vite.config.ts:39)
- Dev server: [server.port = 5173](e4s/vite.config.ts:44), [server.proxy."/resources"] (targets `VITE_E4S_HOST`) at [server.proxy](e4s/vite.config.ts:47)
- Build output: [build.outDir = "dist/vite"](e4s/vite.config.ts:59), [build.sourcemap = true](e4s/vite.config.ts:60)
- CSS dev sourcemaps disabled: [css.devSourcemap = false](e4s/vite.config.ts:55)

Environment variables (Vite semantics)
- `VITE_E4S_HOST`: Dev API host (default https://dev.entry4sports.com) set in [vite config](e4s/vite.config.ts:12)
- `VITE_DISABLE_DEV_LINT`: Disable ESLint overlay/checker in dev when `'true'` at [flag](e4s/vite.config.ts:11)
- Production checks use `import.meta.env.PROD` at [Vue.config.performance](e4s/src/index.ts:127)

Key runtime utilities and decisions
- Global error handler: [Vue.config.errorHandler](e4s/src/index.ts:99) surfaces admin messages
- Title handling in guards: [isLoggedInGuardGeneric()](e4s/src/index.ts:321) defaults to “Entry4Sports” at [document.title assignment](e4s/src/index.ts:343)
- Anonymous user bootstrap path: [ConfigData().getAppConfig()](e4s/src/index.ts:353) with message dispatching wrappers

Install and run
- Install deps:
  - `npm install` in `e4s/`
- Development server:
  - `npm run vite:dev` (launches on http://localhost:5173 per [server.port](e4s/vite.config.ts:44))
- Preview built app:
  - `npm run vite:preview` (serves `dist/vite`)
- Production build:
  - `npm run vite:build` (outputs to [dist/vite](e4s/vite.config.ts:59))
- Lint:
  - `npm run lint` (ESLint over .ts/.js/.vue; see [scripts](e4s/package.json:5))
- Unit tests:
  - `npm test` (Jest 26-based setup in [e4s/package.json](e4s/package.json))

External/CDN considerations
- jQuery, jQuery UI loaded via CDN in [index.html](e4s/index.html:16). Ensure dev network access; consider vendoring/public assets later.

Notes for future migration
- Vue 3 migration path tracked in [docs/vue3-class-component-migration.md](e4s/docs/vue3-class-component-migration.md)
- Router and store upgrades will accompany bootstrap switch from [new Vue()](e4s/src/index.ts:417) to createApp