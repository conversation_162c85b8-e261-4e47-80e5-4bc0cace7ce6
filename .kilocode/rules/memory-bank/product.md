# Product — Entry4Sports (E4S)

Why this product exists
- Enable organisers to configure athletics competitions and accept entries online
- Provide athletes/teams with a reliable entry and payment experience
- Give officials tools for check-in, bib allocation, heats/schedule, and scoreboards
- Publish public-facing results and schedules for spectators

Problems being solved
- Fragmented entry workflows across spreadsheets/forms
- Manual payment reconciliation and entry validation
- On-the-day operational friction for check-in and bib assignment
- Inconsistent publication of schedules and results

Primary user groups
- Organisers and officials (competition setup, operations, results)
- Club/team managers (bulk entries, payments, athlete management)
- Individual athletes (self entry, payments, schedule view)
- Public viewers (results and schedules)

Key user journeys
- Public landing ➜ entries and results
  - App bootstrap at [new Vue()](e4s/src/index.ts:417) with router created at [router](e4s/src/index.ts:265)
  - Routes defined in [e4s/src/index.ts](e4s/src/index.ts) including scoreboard at [scoreboard route](e4s/src/index.ts:219)
- Competition entry
  - Launch screens and routes: [launch routes](e4s/src/index.ts:171) and [v2 launch routes](e4s/src/index.ts:182)
  - Validation via VeeValidate and custom validators loaded in [e4s/src/index.ts](e4s/src/index.ts)
- Authentication / access control
  - Guarded routes use [isLoggedInGuard()](e4s/src/index.ts:315) and [isLoggedInGuardV2()](e4s/src/index.ts:309)
  - Config-driven user state hydrated on startup in [beforeCreate()](e4s/src/index.ts:423)
- Operations (check-in, bibs, schedules)
  - Vuex modules provide state slices; store constructed at [appStore](e4s/src/app.store.ts:150)
  - Public schedules/results features exist under competition routes and scoreboard modules
- Public results and scoreboards
  - Scoreboard section entry at [scoreboard route](e4s/src/index.ts:219) with children from [scoreboard-routes](e4s/src/index.ts:222)

User experience principles
- Reliability first: conservative dependency upgrades; maintain legacy class-style components where needed
- Clarity over flash: predictable navigation and route titles set in guards
- Performance-aware: Vite-based build with bundle visualization; code-split routes for heavy areas
- Accessibility: legible components, responsive breakpoints configured via Vue MQ in [Vue.use(VueMq,…)](e4s/src/index.ts:85)

Core requirements and goals
- Accurate fee calculations and resilient cart/checkout flows
- Scalable rendering for large competitions (thousands of entries)
- Live operations support (check-in, bib assignment, heat/schedule management)
- Public results with near-real-time updates and printable views

Non-goals (current scope)
- Backend service definitions and infra deployment specifics
- Full Vue 3 migration (tracked separately; compat strategy documented)

Project entry points and notable files
- Build configuration uses [defineConfig()](e4s/vite.config.ts:1)
- HTML entry uses [e4s/index.html](e4s/index.html) with module script to [e4s/src/index.ts](e4s/src/index.ts)
- Router creation at [router](e4s/src/index.ts:265); global afterEach at [router.afterEach()](e4s/src/index.ts:267)
- Global store created at [appStore](e4s/src/app.store.ts:150)

Success metrics
- Dev/preview/build stability via Vite; acceptable bundle size (tracked with visualizer)
- Low-friction entry flows and successful payment completion rates
- Operational efficiency metrics (check-in time per athlete, bib assignment throughput)
- Public engagement: page load performance and results availability

Notes
- Dev API requests proxied via Vite server to VITE_E4S_HOST configured in [e4s/vite.config.ts](e4s/vite.config.ts)
- Migration path to Vue 3 documented in [e4s/docs/vue3-class-component-migration.md](e4s/docs/vue3-class-component-migration.md)