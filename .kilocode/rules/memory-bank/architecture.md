# Architecture — Entry4Sports (E4S)

System overview
- Client-side SPA built with Vue 2.7 + TypeScript, bundled by Vite 6
- Router-driven navigation with vue-router 3; state managed via Vuex 3 modules
- Root app bootstrapped via [new Vue()](e4s/src/index.ts:417), rendering a single [router-view](e4s/index.html:24)
- Build and dev server configured via [defineConfig()](e4s/vite.config.ts:1)

Key runtime seams
- App bootstrap and lifecycle
  - Vue instance created at [new Vue()](e4s/src/index.ts:417)
  - Explicit render function used at [render(h)](e4s/src/index.ts:447) to avoid in-DOM template compilation
  - Global error handler funnels severe issues to admin messaging: [Vue.config.errorHandler](e4s/src/index.ts:99)
  - Performance instrumentation toggled via Vite env: [Vue.config.performance](e4s/src/index.ts:127)
- Routing
  - Router created at [router](e4s/src/index.ts:265), with afterEach logging at [router.afterEach()](e4s/src/index.ts:267)
  - Title handling and anonymous bootstrap done in guards: [isLoggedInGuardGeneric()](e4s/src/index.ts:321)
  - Scoreboard route entry at [scoreboard](e4s/src/index.ts:219) with children from [scoreboard-routes](e4s/src/index.ts:222)
- State (Vuex)
  - Root store constructed at [appStore](e4s/src/app.store.ts:150)
  - Typing for root state and composition helper exposed at [useStore()](e4s/src/app.store.ts:159)
- Internationalization
  - i18n instance created in [i18n](e4s/src/config/il8n.ts:23) and injected into root at [new Vue()](e4s/src/index.ts:420)
- Responsive/UX utilities
  - Breakpoints centralized via Vue MQ: [Vue.use(VueMq,…)](e4s/src/index.ts:85)

Configuration and environment
- Vite 6 config
  - Plugin setup for Vue 2 and Legacy builds: [plugins](e4s/vite.config.ts:15)
  - Visualizer for bundle insight: [visualizer()](e4s/vite.config.ts:21)
  - Dev checker with ESLint only (TS overlay disabled): [checker()](e4s/vite.config.ts:27)
  - Dev server and proxy: [server](e4s/vite.config.ts:43) and [server.proxy](e4s/vite.config.ts:47)
  - Output directory and sourcemaps: [build](e4s/vite.config.ts:59)
- Env variables (Vite semantics)
  - `VITE_E4S_HOST` default dev host: [loadEnv usage](e4s/vite.config.ts:12)
  - `VITE_DISABLE_DEV_LINT` toggle for dev lint overlay: [flag](e4s/vite.config.ts:11)
  - Client-side prod check uses `import.meta.env.PROD`: [performance flag](e4s/src/index.ts:127)
- HTML entry
  - DOM mount point and external CDNs: [index.html](e4s/index.html:16)
  - Module entry to TS bootstrap: [script type="module"](/src/index.ts) at [index.html](e4s/index.html:27)

Module boundaries (representative)
- Launch and entry
  - Primary launch at [launch component](e4s/src/index.ts:173), v2 launch at [LaunchV2](e4s/src/index.ts:182)
  - Validations wired globally via [VeeValidate](e4s/src/index.ts:42) and custom validators: [validator-extend](e4s/src/index.ts:20)
- Scoreboards and public results
  - Section container: [ScoreboardSection](e4s/src/index.ts:221)
  - Routing tree: [scoreboard-routes](e4s/src/index.ts:222)
- Authentication and guards
  - Guard APIs: [isLoggedInGuard()](e4s/src/index.ts:315), [isLoggedInGuardV2()](e4s/src/index.ts:309), shared flow in [isLoggedInGuardGeneric()](e4s/src/index.ts:321)
  - Anonymous user bootstrap path fetches config: [ConfigData().getAppConfig()](e4s/src/index.ts:353)
- Global store slices (selection)
  - Config module and startup hydration: [CONFIG_STORE_CONST](e4s/src/app.store.ts:32)
  - Auth state and login mutation propagation: [AUTH_STORE_CONST](e4s/src/app.store.ts:15)
  - Results, schedule-public, check-in, scoreboard, cart modules registered at [modules](e4s/src/app.store.ts:123)

Critical flows
- App config hydration
  - Startup dispatch in [beforeCreate()](e4s/src/index.ts:423) triggers config load [CONFIG_ACTIONS_GET_APP_CONFIG](e4s/src/index.ts:428) and applies theme from [window.E4S_GLOBAL_THEME_IDENTIFIER](e4s/index.html:29)
- Title resolution
  - Per-route titles are functions or strings in route meta; applied inside [isLoggedInGuardGeneric()](e4s/src/index.ts:330) with default ["Entry4Sports"](e4s/src/index.ts:343)
- Navigation logging
  - Route transitions logged for tracing: [router.afterEach](e4s/src/index.ts:267)

Design decisions
- Keep Vue 2.7 with class-style compatibility until Vue 3 migration is greenlit; migration plan documented at [vue3-class-component-migration.md](e4s/docs/vue3-class-component-migration.md:245)
- Avoid in-DOM templates; rely on [render(h)](e4s/src/index.ts:447) at the root
- Use Vite’s proxy to target configurable dev host: [server.proxy](e4s/vite.config.ts:47)
- Integrate lint/type checks during dev via [checker()](e4s/vite.config.ts:27), TS overlay off to reduce churn

Risks and mitigations
- Legacy class components and Vue 2 reactivity edge cases
  - Mitigate via conservative dependency upgrades and incremental refactors
- CDN dependency availability (jQuery/Materialize) during development
  - Consider vendoring or moving to public/ assets later
- Env consistency
  - Ensure central config and host resolution remain aligned with Vite env; track via [vite-upgrade-steps.md](e4s/docs/vite-upgrade-steps.md:30)

Operational guidance
- When adding new routes, set a meta.title and rely on [isLoggedInGuardGeneric()](e4s/src/index.ts:321) for document.title
- Prefer code-split dynamic imports for heavy routes; let Vite handle chunking
- Keep store slices self-contained and typed; register in [app.store modules](e4s/src/app.store.ts:123)