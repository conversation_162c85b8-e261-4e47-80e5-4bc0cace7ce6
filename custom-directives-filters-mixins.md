# Custom Directives, Filters, and Mixins Documentation

## Overview
This document provides a comprehensive inventory and analysis of all custom directives, filters, and mixins in the Vue.js application that need to be considered for the Vue 2 to Vue 3 migration.

## Custom Directives

### 1. click-outside Directive

#### File Location
- **Path**: `e4s/src/directives/click-outside.ts`

#### Current Implementation
```typescript
export function nickPurple(el: HTMLElement) {
  el.style.color = "purple";
}

export const clickOutside = {
  bind(element: HTMLElement, binding: any, vnode: any) {
    // @ts-ignore
    element.clickOutsideEvent = function (event: any) {
      // check that click was outside the el and his children
      if (!(element === event.target || element.contains(event.target))) {
        // and if it did, call method provided in attribute value
        vnode.context[binding.expression](event);
        // binding.value(); run the arg
      }
    };
    // @ts-ignore
    document.body.addEventListener("click", element.clickOutsideEvent);
  },
  unbind(element: HTMLElement) {
    // @ts-ignore
    document.body.removeEventListener("click", element.clickOutsideEvent);
  },
};
```

#### Purpose
- **nickPurple**: Simple directive that changes text color to purple
- **clickOutside**: Detects clicks outside an element and triggers a callback

#### Usage in Application
- Registered globally in `index.ts`:
  ```typescript
  Vue.directive("nick-purple", nickPurple);
  Vue.directive("click-outside", clickOutside);
  ```

#### Migration Considerations
1. **API Changes**: Vue 3 directive API is different:
   - `bind` → `beforeMount`
   - `unbind` → `unmounted`
   - New hooks: `mounted`, `updated`, `beforeUnmount`

2. **TypeScript Types**: Need to update directive type definitions

3. **Recommended Vue 3 Implementation**:
   ```typescript
   const clickOutside = {
     beforeMount(el: HTMLElement, binding: any, vnode: any) {
       el.clickOutsideEvent = function (event: any) {
         if (!(el === event.target || el.contains(event.target))) {
           binding.value(event);
         }
       };
       document.body.addEventListener("click", el.clickOutsideEvent);
     },
     unmounted(el: HTMLElement) {
       document.body.removeEventListener("click", el.clickOutsideEvent);
     }
   };
   ```

### 2. form-controller-field Directive

#### File Location
- **Path**: `e4s/src/common/ui/form/form-controller/form-controller-field-directive.ts`

#### Current Implementation
```typescript
import {DirectiveOptions} from "vue";
import {FormController} from "./form-controller";

const directive: DirectiveOptions = {
    bind(value: any) {
        // Preparation work
    },
    inserted(el, node) {
        // Called when element is inserted
    },
    update(el, node) {
        // Called when element is updated
        if (node.value.formController) {
            const formController: FormController = node.value.formController;
            const propPath: string = node.value.propPath;
            const className = formController.isFieldDirty(propPath) ? "" : "";
            el.className = className;
        }
    },
    unbind() {
        // Clean up work
    }
};

export default directive;
```

#### Purpose
- Integrates with FormController to manage form field states
- Applies CSS classes based on field dirty state
- Used for form validation and styling

#### Usage in Application
- Registered globally in `index.ts`:
  ```typescript
  Vue.directive("form-controller-field", formControllerFieldDirective);
  ```

#### Migration Considerations
1. **API Changes**: Need to update directive hooks
2. **FormController Dependency**: Ensure FormController is compatible with Vue 3
3. **Recommended Vue 3 Implementation**:
   ```typescript
   const formControllerField = {
     beforeMount(el, binding) {
       // Preparation work
     },
     mounted(el, binding) {
       // Called when element is mounted
     },
     updated(el, binding) {
       // Called when element is updated
       if (binding.value.formController) {
         const formController = binding.value.formController;
         const propPath = binding.value.propPath;
         const className = formController.isFieldDirty(propPath) ? "" : "";
         el.className = className;
       }
     },
     unmounted(el) {
       // Clean up work
     }
   };
   ```

## Custom Filters

### Analysis Result
- **No custom filters found** in the codebase
- This is beneficial as Vue 3 has removed filter support
- No migration work required for filters

## Custom Mixins

### Analysis Result
- **No custom mixins found** in the codebase
- This simplifies the migration process
- No need to convert mixins to Composition API

## Global Component Registration

### Common Vue Components
Several components are registered globally in `index.ts`:

```typescript
Vue.component("LoadingSpinner", LoadingSpinner);
Vue.component("VueCtkDateTimePicker", VueCtkDateTimePicker);
```

#### Migration Considerations
1. **Global Registration**: Vue 3 handles global registration differently
2. **Recommended Vue 3 Approach**:
   ```typescript
   const app = createApp(App);
   app.component("LoadingSpinner", LoadingSpinner);
   app.component("VueCtkDateTimePicker", VueCtkDateTimePicker);
   ```

## Vue Router Integration

### Route Navigation Guards
The application uses vue-class-component with route navigation guards:

#### File Location
- **Path**: `e4s/src/common/common-vue.ts`

#### Current Implementation
```typescript
import Component from "vue-class-component";

Component.registerHooks([
    "beforeRouteEnter",
    "beforeRouteLeave",
    "beforeRouteUpdate"
]);
```

#### Purpose
- Enables class components to use Vue Router navigation guards
- Allows components to react to route changes

#### Migration Considerations
1. **vue-class-component**: May need to be updated or replaced with Composition API
2. **Vue 3 Router**: Router API has changed slightly
3. **Recommended Vue 3 Approach**:
   ```typescript
   // Using Composition API
   import { onBeforeRouteLeave, onBeforeRouteUpdate } from 'vue-router';
   
   export default {
     setup() {
       onBeforeRouteLeave((to, from) => {
         // Navigation guard logic
       });
     }
   }
   ```

## Custom Validators

### vee-validate Extensions
Custom validators are registered in `validator-extend.ts`:

#### File Location
- **Path**: `e4s/src/common/validator-extend.ts`

#### Current Implementation
```typescript
import { Validator } from "vee-validate";
import {CommonService} from "./common-service";

const commonService: CommonService = new CommonService();

const dateValidator = {
    getMessage(field: any, args: any) {
        return "Please enter date DD/MM/YYYY, e.g. 28/03/1970!";
    },
    validate(value: any, args: any) {
        return commonService.validateDateEntry(value);
    }
};

Validator.extend("dateValidator", dateValidator);

const timeValidator = {
    getMessage(field: any, args: any) {
        return "Please enter time HH:mm, e.g. 09:25, 23:59!";
    },
    validate(value: any, args: any) {
        return commonService.validateTime(value);
    }
};

Validator.extend("timeValidator", timeValidator);
```

#### Purpose
- Extends vee-validate with custom validation rules
- Provides date and time validation with specific formats

#### Migration Considerations
1. **vee-validate Version**: Major changes between v2 and v3/v4
2. **API Changes**: Validation registration and usage has changed significantly
3. **Recommended Vue 3 Approach**:
   ```typescript
   import { configure, defineRule } from 'vee-validate';
   
   defineRule('dateValidator', (value) => {
     return commonService.validateDateEntry(value);
   });
   
   defineRule('timeValidator', (value) => {
     return commonService.validateTime(value);
   });
   ```

## Migration Priority

### High Priority
1. **click-outside directive**: Used throughout the application
2. **form-controller-field directive**: Critical for form functionality
3. **vee-validate extensions**: Essential for validation

### Medium Priority
1. **Global component registration**: Needs update for Vue 3
2. **Route navigation guards**: Important for routing functionality

### Low Priority
1. **nick-purple directive**: Simple styling directive, minimal impact

## Migration Strategy

### Phase 1: Directive Updates (1-2 weeks)
1. Update directive APIs to Vue 3 format
2. Test directive functionality in isolation
3. Update TypeScript definitions

### Phase 2: Validation Migration (1-2 weeks)
1. Update vee-validate to latest version
2. Migrate custom validators to new API
3. Test all validation scenarios

### Phase 3: Global Registration Updates (1 week)
1. Update global component registration
2. Migrate route navigation guards
3. Test application functionality

## Testing Recommendations

### Unit Tests
1. Create unit tests for each directive
2. Test directive lifecycle hooks
3. Test validator functions

### Integration Tests
1. Test directives in component context
2. Test validation in forms
3. Test global component registration

### E2E Tests
1. Test user interactions with directives
2. Test form validation flows
3. Test routing with navigation guards

## Conclusion

The application has a limited number of custom directives and no custom filters or mixins, which simplifies the migration process. The main focus should be on:
1. Updating directive APIs to Vue 3 format
2. Migrating vee-validate custom validators
3. Updating global component registration

With proper planning and testing, these migrations can be completed efficiently as part of the Vue 3 upgrade process.

## Next Steps

1. Begin implementing Vue 3 directive updates
2. Plan vee-validate migration strategy
3. Create test cases for all custom functionality
4. Proceed with identifying third-party library compatibility