# Simple Toast Implementation Plan

## Overview
Replace Materialize toast functionality in `messageDispatchHelper` with a lightweight vanilla JavaScript toast system.

## Current State Analysis
- **Function**: [`messageDispatchHelper(message, level)`](e4s/src/user-message/user-message-store.ts:48)
- **Usage**: 178 occurrences across the codebase
- **Current behavior**: Uses Materialize's `M.toast()` with 7-second display, CSS classes for styling
- **Levels supported**: INFO, WARN, ERROR from [`USER_MESSAGE_LEVEL`](e4s/src/user-message/user-message-models.ts:1)

## Proposed Solution: Minimal CSS/JS Toast System

### 1. Create Toast Utility (`e4s/src/user-message/simple-toast.ts`)
```typescript
/**
 * Simple toast notification system to replace Materialize toasts
 */

interface ToastOptions {
  message: string;
  level: 'INFO' | 'WARN' | 'ERROR';
  duration?: number;
}

let toastContainer: HTMLElement | null = null;

function ensureToastContainer(): HTMLElement {
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.id = 'e4s-toast-container';
    toastContainer.className = 'e4s-toast-container';
    document.body.appendChild(toastContainer);
  }
  return toastContainer;
}

function createToastElement(options: ToastOptions): HTMLElement {
  const toast = document.createElement('div');
  toast.className = `e4s-toast e4s-toast-${options.level.toLowerCase()}`;
  toast.innerHTML = options.message;
  return toast;
}

export function showToast(options: ToastOptions): void {
  const container = ensureToastContainer();
  const toast = createToastElement(options);
  
  // Add to container
  container.appendChild(toast);
  
  // Trigger animation
  setTimeout(() => toast.classList.add('e4s-toast-show'), 10);
  
  // Auto-remove after duration
  const duration = options.duration || 7000;
  setTimeout(() => {
    toast.classList.add('e4s-toast-hide');
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 300); // Wait for fade-out animation
  }, duration);
}
```

### 2. Create Toast Styles (`e4s/src/user-message/simple-toast.css`)
```css
/* Toast Container */
.e4s-toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  pointer-events: none;
}

/* Toast Base Styles */
.e4s-toast {
  background: #323232;
  color: white;
  padding: 12px 24px;
  margin-bottom: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  max-width: 300px;
  word-wrap: break-word;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease-in-out;
  pointer-events: auto;
}

/* Toast Show Animation */
.e4s-toast-show {
  opacity: 1;
  transform: translateX(0);
}

/* Toast Hide Animation */
.e4s-toast-hide {
  opacity: 0;
  transform: translateX(100%);
}

/* Toast Level Styles */
.e4s-toast-info {
  background: #2196F3;
  border-left: 4px solid #1976D2;
}

.e4s-toast-warn {
  background: #FF9800;
  border-left: 4px solid #F57C00;
}

.e4s-toast-error {
  background: #F44336;
  border-left: 4px solid #D32F2F;
}

/* Responsive */
@media (max-width: 480px) {
  .e4s-toast-container {
    left: 10px;
    right: 10px;
    top: 10px;
  }
  
  .e4s-toast {
    max-width: none;
  }
}
```

### 3. Update messageDispatchHelper (`e4s/src/user-message/user-message-store.ts`)

Replace the Materialize toast section (lines 75-99) with:

```typescript
import { showToast } from './simple-toast';

// Replace the existing Materialize toast block with:
showToast({
  message: userMessage.message,
  level: level === "WARN" ? "WARN" : level === "ERROR" ? "ERROR" : "INFO",
  duration: 7000
});
```

### 4. Import Styles (`e4s/index.html`)

Add to the `<head>` section:
```html
<link rel="stylesheet" href="/src/user-message/simple-toast.css">
```

## Implementation Steps

1. **Create toast utility functions** (`simple-toast.ts`)
2. **Create toast CSS styles** (`simple-toast.css`) 
3. **Update messageDispatchHelper** to use new toast system
4. **Import CSS** in main HTML file
5. **Test functionality** with different message levels
6. **Verify no breaking changes** across the application

## Benefits

- ✅ **Zero dependencies** - Pure vanilla JavaScript and CSS
- ✅ **Same API** - No changes needed to existing `messageDispatchHelper` calls
- ✅ **Lightweight** - Minimal code footprint
- ✅ **Responsive** - Works on mobile and desktop
- ✅ **Accessible** - Proper contrast and readable text
- ✅ **Easy to replace** - Self-contained system for future upgrades

## Fallback Strategy

If toast creation fails, the system will fall back to console logging (existing behavior in the current implementation).

## Testing Plan

1. Test INFO level messages
2. Test WARN level messages  
3. Test ERROR level messages
4. Test multiple simultaneous toasts
5. Test on mobile viewport
6. Verify existing functionality still works across the app

## Migration Notes

- No changes required to existing `messageDispatchHelper` calls
- CSS classes match Materialize naming convention where possible
- Duration and behavior match existing Materialize implementation
- Can be easily extended or replaced in the future