# Package.json Backup Instructions

## Current Package.json State

This document contains the current state of package.json before any library upgrades. Please create a backup using the instructions below.

## Current Dependencies

### Dependencies
```json
{
  "name": "e4s",
  "version": "1.0.0",
  "description": "e4s",
  "scripts": {
    "dev": "cross-env NODE_ENV=development webpack-dev-server",
    "safari-dev": "cross-env NODE_ENV=development webpack-dev-server --host 0.0.0.0 --port 9000",
    "test": "cross-env NODE_ENV=development jest --watchAll",
    "integration": "cross-env NODE_ENV=development jest -c jest.config.integration.js",
    "dev-build": "cross-env NODE_ENV=development webpack",
    "build": "cross-env NODE_ENV=production webpack"
  },
  "dependencies": {
    "@vue/composition-api": "1.3.3",
    "@zxing/library": "0.18.3",
    "axios": "0.19.2",
    "core-js": "3.6.5",
    "date-fns": "1.30.1",
    "dotenv-webpack": "1.8.0",
    "qrcode-generator": "1.4.4",
    "ramda": "0.27.0",
    "socket.io-client": "4.0.1",
    "vee-validate": "2.2.2",
    "vue": "2.6.14",
    "vue-class-component": "7.2.3",
    "vue-ctk-date-time-picker": "2.5.0",
    "vue-i18n": "8.17.5",
    "vue-mq": "1.0.1",
    "vue-property-decorator": "8.4.2",
    "vue-router": "3.1.6",
    "vue2-editor": "2.10.2",
    "vuex": "3.1.1",
    "xlsx": "0.16.9"
  },
  "devDependencies": {
    "@babel/core": "7.9.6",
    "@types/babel__traverse": "7.0.15",
    "@types/core-js": "2.5.3",
    "@types/glob": "7.1.4",
    "@types/jest": "25.2.2",
    "@types/minimatch": "3.0.5",
    "@types/node": "14.0.1",
    "@types/prettier": "2.4.1",
    "@types/ramda": "0.27.4",
    "@types/vue2-editor": "2.6.0",
    "@vue/test-utils": "1.0.2",
    "babel-core": "6.26.3",
    "babel-loader": "8.1.0",
    "clean-webpack-plugin": "3.0.0",
    "copy-webpack-plugin": "5.1.1",
    "cross-env": "^7.0.3",
    "cross-env-shell": "^7.0.3",
    "css-loader": "3.5.3",
    "file-loader": "6",
    "fork-ts-checker-webpack-plugin": "4.1.3",
    "html-loader": "1.1.0",
    "html-webpack-plugin": "4.3.0",
    "jest": "26.0.1",
    "jsdom": "16.2.2",
    "jsdom-global": "3.0.2",
    "mini-css-extract-plugin": "0.9.0",
    "minimatch": "3.1.2",
    "prettier": "2.4.1",
    "style-loader": "1.2.1",
    "ts-jest": "26.0.0",
    "ts-loader": "7.0.4",
    "tslint": "6.1.2",
    "typescript": "3.9.2",
    "typescript-tslint-plugin": "0.5.5",
    "vue-jest": "3.0.5",
    "vue-loader": "15.9.2",
    "vue-template-compiler": "2.6.14",
    "webpack": "4.43.0",
    "webpack-bundle-analyzer": "3.7.0",
    "webpack-cli": "^3.3.12",
    "webpack-dev-server": "^3.11.0"
  },
  "resolutions": {
    "@types/babel__traverse": "7.0.15",
    "@types/glob": "7.1.4",
    "@types/minimatch": "3.0.5",
    "minimatch": "3.1.2"
  }
}
```

## Libraries to Upgrade

### Priority 1: Core Utilities
1. **axios**: 0.19.2 → 1.x (latest)
2. **core-js**: 3.6.5 → Latest (3.x)

### Priority 2: Utility Libraries
1. **date-fns**: 1.30.1 → 2.x or 3.x
2. **ramda**: 0.27.0 → Latest
3. **socket.io-client**: 4.0.1 → Latest

## Backup Instructions

### Step 1: Create Git Backup
```bash
# Navigate to project directory
cd e4s

# Create backup commit
git add package.json package-lock.json
git commit -m "Backup before library upgrades - $(date)"
```

### Step 2: Create File Backup
```bash
# Copy package.json to backup file
cp package.json package-backup-$(date +%Y%m%d-%H%M%S).json

# Copy package-lock.json to backup file
cp package-lock.json package-lock-backup-$(date +%Y%m%d-%H%M%S).json
```

### Step 3: Verify Backup
```bash
# Check that backup files exist
ls package-backup-*.json
ls package-lock-backup-*.json
```

## Rollback Instructions

If you need to rollback to the backup:

```bash
# Restore from git
git checkout HEAD -- package.json package-lock.json
npm install

# Or restore from file backup
cp package-backup-YYYYMMDD-HHMMSS.json package.json
cp package-lock-backup-YYYYMMDD-HHMMSS.json package-lock.json
npm install
```

## Next Steps

After creating the backup:

1. Update Node.js to 18.19.0:
   ```bash
   nvm use 18.19.0
   node --version  # Should show 18.19.0
   ```

2. Test current application with new Node.js version:
   ```bash
   npm run dev
   npm run build
   ```

3. Begin library upgrades in priority order

## Testing Protocol

After each library upgrade:

1. Run development server:
   ```bash
   npm run dev
   ```
   - Verify server starts without errors
   - Check browser console for errors
   - Test key application features
   - Ensure build time is reasonable (< 60 seconds)

2. Run production build:
   ```bash
   npm run build
   ```
   - Verify build completes without errors
   - Check for warnings
   - Test built application locally

3. Perform regression testing:
   - Test all major application features
   - Check for breaking changes
   - Verify no performance degradation

## Documentation

Document any issues encountered and solutions in the `library-upgrade-log.md` file.