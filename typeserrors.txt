ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(3,43):
TS2339: Property 'RemovePropertiesOptions' does not exist on type 'typeof import("C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@babel/types/lib/index-legacy")'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(3,43):
TS2694: Namespace '"C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@babel/types/lib/index-legacy"' has no exported member 'RemovePropertiesOptions'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(47,34):
TS2694: Namespace '"C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@babel/types/lib/index-legacy"' has no exported member 'DeprecatedAliases'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(301,7):
TS7039: Mapped object type implicitly has an 'any' template type.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(302,15):
TS2322: Type 'Node' is not assignable to type 'string | number | symbol'.
  Type 'ClassAccessorProperty' is not assignable to type 'string | number | symbol'.
    Type 'ClassAccessorProperty' is not assignable to type 'symbol'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(302,20):
TS2304: Cannot find name 'as'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(302,23):
TS2304: Cannot find name 'N'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(302,36):
TS2693: 'VisitNode' only refers to a type, but is being used as a value here.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(302,46):
TS2304: Cannot find name 'S'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(302,49):
TS2304: Cannot find name 'N'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(302,67):
TS2304: Cannot find name 'N'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(302,81):
TS2304: Cannot find name 'N'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(304,7):
TS2363: The right-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(305,9):
TS2464: A computed property name must be of type 'string', 'number', 'symbol', or 'any'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(305,10):
TS2304: Cannot find name 'K'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(305,15):
TS2304: Cannot find name 'keyof'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(305,23):
TS2339: Property 'Aliases' does not exist on type 'typeof import("C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@babel/types/lib/index-legacy")'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(305,57):
TS2304: Cannot find name 'K'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(307,7):
TS2363: The right-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(308,9):
TS2464: A computed property name must be of type 'string', 'number', 'symbol', or 'any'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(308,10):
TS2304: Cannot find name 'K'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(308,15):
TS2304: Cannot find name 'keyof'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(308,21):
TS2304: Cannot find name 'VirtualTypeAliases'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(308,75):
TS2304: Cannot find name 'K'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(310,7):
TS2363: The right-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(314,10):
TS2304: Cannot find name 'k'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(314,16):
TS2693: 'string' only refers to a type, but is being used as a value here.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(314,26):
TS2693: 'string' only refers to a type, but is being used as a value here.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(748,75):
TS2304: Cannot find name 'NodePathResult'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(749,75):
TS2304: Cannot find name 'NodePathResult'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(749,90):
TS2304: Cannot find name 'ImplGetRecursive'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1097,58):
TS2304: Cannot find name 'VirtualTypeAliases'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1101,61):
TS2304: Cannot find name 'VirtualTypeAliases'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1102,58):
TS2304: Cannot find name 'VirtualTypeAliases'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1109,61):
TS2304: Cannot find name 'VirtualTypeAliases'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1110,67):
TS2304: Cannot find name 'VirtualTypeAliases'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1111,46):
TS2304: Cannot find name 'VirtualTypeAliases'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1113,44):
TS2304: Cannot find name 'VirtualTypeAliases'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1455,18):
TS2304: Cannot find name 'infer'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1455,24):
TS2304: Cannot find name 'N'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1455,34):
TS2693: 'number' only refers to a type, but is being used as a value here.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1463,18):
TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i @types/jquery`.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1463,20):
TS2304: Cannot find name 'infer'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1463,26):
TS2304: Cannot find name 'N'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1463,36):
TS2693: 'number' only refers to a type, but is being used as a value here.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1469,18):
TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i @types/jquery`.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1469,20):
TS2304: Cannot find name 'infer'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1469,26):
TS2304: Cannot find name 'L'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1469,29):
TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i @types/jquery`.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1469,31):
TS2304: Cannot find name 'infer'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/babel__traverse/index.d.ts(1469,37):
TS2686: 'R' refers to a UMD global, but the current file is a module. Consider adding an import instead.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/glob/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/glob/index.d.ts(29,42):
TS2694: Namespace '"C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/minimatch/dist/cjs/index"' has no exported member 'IOptions'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/glob/index.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/@types/glob/index.d.ts(74,30):
TS2694: Namespace '"C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/minimatch/dist/cjs/index"' has no exported member 'IMinimatch'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/minimatch/dist/cjs/ast.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/minimatch/dist/cjs/ast.d.ts(18,9):
TS2304: Cannot find name 're'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/minimatch/dist/cjs/ast.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/minimatch/dist/cjs/ast.d.ts(19,9):
TS2304: Cannot find name 'body'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/minimatch/dist/cjs/ast.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/minimatch/dist/cjs/ast.d.ts(20,9):
TS2304: Cannot find name 'hasMagic'.

ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/minimatch/dist/cjs/ast.d.ts
ERROR in C:/Projects/e4s-heavy/e4s_ie/e4s/node_modules/minimatch/dist/cjs/ast.d.ts(21,9):
TS2304: Cannot find name 'uflag'.
i ｢wdm｣: Failed to compile.