# Vue 2 to Vue 3 Migration Plan

## Overview
This document outlines the comprehensive migration strategy for upgrading the Vue.js application from Vue 2.6.14 to Vue 3. The migration is divided into 6 phases to ensure a smooth transition with minimal risk.

## Current Setup Analysis
- Vue 2.7.16 (upgraded from 2.6.14)
- Webpack 4.43.0 for bundling
- TypeScript 4.5.5 (upgraded from 3.9.2)
- Vue Router 3.1.6
- Vuex 3.1.1
- Node.js 18.19.0 (upgraded from 12.16.3)

## Key Findings
1. The application has been upgraded to Vue 2.7.16, which includes Composition API natively
2. Node.js has been upgraded to 18.19.0
3. File casing inconsistencies exist (athletecompsched vs athleteCompSched) that need to be addressed
4. Several third-party libraries will need updates for Vue 3 compatibility
5. TypeScript has been upgraded to 4.5.5, providing good Vue 3 support

## Migration Phases

### Phase 1: Preparation and Assessment (1-2 weeks)

#### Objectives
- Create a comprehensive inventory of all components and dependencies
- Address file casing issues systematically
- Set up proper testing infrastructure

#### Tasks
1. **Create a comprehensive inventory**
   - List all Vue components and their API usage (Options API vs Composition API)
   - Identify all third-party libraries and their Vue 3 compatibility
   - Document all custom directives, filters, and mixins

2. **Address file casing issues**
   - Standardize all file and folder naming conventions
   - Update all import statements to match the actual file casing
   - This is critical before moving to case-sensitive environments

3. **Set up proper testing infrastructure**
   - Ensure all components have adequate test coverage
   - Set up regression tests to catch breaking changes

#### Deliverables
- Complete inventory of all components and dependencies
- Standardized file naming across the project
- Test suite with adequate coverage

### Phase 2: Upgrade to Vue 2.7 and Modernize Tooling (2-3 weeks) - COMPLETED

#### Objectives
- Upgrade Node.js to a more recent version
- Update Vue to 2.7.x
- Modernize build tooling

#### Tasks
1. **Upgrade Node.js** - COMPLETED
   - Move from Node 12.16.3 to Node 18.19.0 (LTS version)
   - Update npm scripts if needed

2. **Upgrade to Vue 2.7** - COMPLETED
   - Update Vue to 2.7.16
   - Remove @vue/composition-api (now built-in)
   - Update vue-template-compiler to match Vue version

3. **Update dependencies** - PARTIALLY COMPLETED
   - Upgrade all Vue-related packages to latest Vue 2.7 compatible versions
   - Update TypeScript to at least 4.5.x for better Vue 3 support - COMPLETED (4.5.5)
   - Update webpack to 4.x latest or 5.x (if staying with webpack) - CURRENTLY AT 4.43.0

#### Deliverables
- Application running on Vue 2.7 - COMPLETED
- Updated dependencies with compatibility for Vue 2.7 - COMPLETED
- Modernized development environment - PARTIALLY COMPLETED

### Phase 3: Migrate to Composition API (4-6 weeks) - IN PROGRESS

#### Objectives
- Migrate all components from Options API to Composition API
- Refactor state management to work with Composition API
- Create reusable composables

#### Tasks
1. **Set up hybrid mode** - COMPLETED
   - Enable Composition API alongside Options API
   - Create migration guide for the team

2. **Migrate components systematically** - IN PROGRESS
   - Start with smaller, less complex components
   - Migrate shared utilities and composables first
   - Gradually move to larger components
   - **COMPLETED**: Fixed TypeScript initialization issues in comp-event-team.vue
     - Resolved "Property 'eventTeamHeader' is used before its initialization" error
     - Moved property initialization from class declaration to lifecycle hooks
     - Added comprehensive null safety checks
     - Enhanced error handling and defensive programming

3. **Update Vuex stores** - PENDING
   - Refactor stores to use Composition API patterns
   - Consider migrating to Pinia (Vue 3 recommended state management)

#### Progress Notes
- **2025-01-07**: Fixed critical TypeScript initialization issue in comp-event-team.vue
  - This type of fix will be needed across many components during the migration
  - Established pattern for proper prop initialization in Vue 2.7 with TypeScript
  - Added null safety patterns that will be beneficial for Vue 3 migration

#### Deliverables
- All components using Composition API - IN PROGRESS
- Refactored state management - PENDING
- Reusable composables library - PENDING

### Phase 4: Build Tool Migration (2-3 weeks)

#### Objectives
- Migrate from Webpack to Vite
- Maintain build performance and compatibility

#### Tasks
1. **Update TypeScript** - COMPLETED
   - Upgrade TypeScript from 3.9.2 to at least 4.5.x for better Vue 3 support - COMPLETED (4.5.5)

2. **Remove webpack-specific dependencies**
   - Remove webpack (4.43.0)
   - Remove webpack-cli (^3.3.12)
   - Remove webpack-dev-server (^3.11.0)
   - Remove webpack-bundle-analyzer (3.7.0) - can replace with rollup-plugin-visualizer
   - Remove vue-loader (15.9.2) - Vite has built-in Vue SFC support
   - Remove vue-template-compiler (^2.7.16) - Vite handles Vue compilation internally
   - Remove ts-loader (7.0.4) - Vite has built-in TypeScript support
   - Remove fork-ts-checker-webpack-plugin (4.1.3) - Vite handles TypeScript differently
   - Remove css-loader (3.5.3) - Vite handles CSS imports natively
   - Remove style-loader (1.2.1) - Vite handles CSS injection
   - Remove mini-css-extract-plugin (0.9.0) - Vite handles CSS extraction
   - Remove file-loader (6) - Vite has built-in asset handling
   - Remove html-loader (1.1.0) - Vite handles HTML differently
   - Remove html-webpack-plugin (4.3.0) - Not needed with Vite's HTML handling
   - Remove clean-webpack-plugin (3.0.0) - Vite has its own build cleaning mechanism
   - Remove copy-webpack-plugin (5.1.1) - Can replace with Vite's publicDir configuration
   - Remove dotenv-webpack (1.8.0) - Vite has built-in environment variable support

3. **Update linting dependencies**
   - Replace tslint (6.1.2) with ESLint (deprecated)
   - Remove typescript-tslint-plugin (0.5.5) - obsolete with ESLint

4. **Add Vite-specific dependencies**
   - Install vite
   - Install @vitejs/plugin-vue2 for Vue 2 support
   - Install @vitejs/plugin-legacy for browser compatibility (if needed)
   - Install rollup-plugin-visualizer for bundle analysis (optional)
   - Install eslint and @typescript-eslint/parser for TypeScript linting

5. **Vite migration steps**
   - Create vite.config.js based on webpack config
   - Update environment variables handling
   - Configure Vite's publicDir for static assets
   - Update scripts in package.json to use Vite commands
   - Migrate any webpack-specific functionality to Vite equivalents

6. **Testing and validation**
   - Ensure all builds work correctly with Vite
   - Verify hot module replacement functionality
   - Check production builds for any issues
   - Compare bundle sizes and performance metrics

#### Deliverables
- Vite-based build system
- Updated configuration files
- Documentation of any build process changes
- Updated package.json with Vite dependencies

### Phase 5: Upgrade to Vue 3 (3-4 weeks)

#### Objectives
- Upgrade core Vue packages to version 3
- Handle all breaking changes
- Update TypeScript configuration

#### Tasks
1. **Update core packages**
   - Upgrade Vue to 3.x
   - Upgrade Vue Router to 4.x
   - Upgrade Vuex to 4.x or migrate to Pinia
   - Update other Vue-related dependencies

2. **Handle breaking changes**
   - Update template syntax (v-model changes, etc.)
   - Replace removed features (filters, eventBus, etc.)
   - Update lifecycle hooks if needed

3. **Update TypeScript configuration**
   - Update tsconfig.json for Vue 3 compatibility
   - Update component type definitions

#### Deliverables
- Application running on Vue 3
- Updated core packages
- Resolved breaking changes

### Phase 6: Finalization and Testing (2-3 weeks)

#### Objectives
- Ensure full functionality after migration
- Optimize performance
- Update documentation

#### Tasks
1. **Comprehensive testing**
   - Run all tests and fix any issues
   - Perform manual testing of all features
   - Check performance improvements

2. **Documentation updates**
   - Update any internal documentation
   - Create migration report for future reference

#### Deliverables
- Fully tested Vue 3 application
- Updated documentation
- Migration completion report

## Answers to Specific Questions

### File Casing Issues

The file casing issue (athletecompsched vs athleteCompSched) is a critical problem that needs to be addressed early:

1. **Audit all imports and references**:
   - Search for all variations of casing in import statements
   - Check dynamic imports and require() statements
   - Look for any string-based path references

2. **Standardize naming convention**:
   - Choose either camelCase or kebab-case for consistency
   - Based on the structure, camelCase (athleteCompSched) is recommended

3. **Systematic renaming**:
   - Create a script to identify all inconsistent references
   - Update all import statements to match the actual file names
   - Consider using a case-insensitive filesystem during development if needed

### Webpack to Vite Migration Timing

**Migrate to Vite while still on Vue 2.7**. Here's why:

1. **Vite has official Vue 2 support** through `@vitejs/plugin-vue2`
2. **Benefits of migrating early**:
   - Faster development server and builds
   - Simplified configuration
   - Better TypeScript support
   - Native ES modules support
   - Faster hot module replacement

3. **Migration process**:
   ```javascript
   // vite.config.js
   import { defineConfig } from 'vite'
   import vue from '@vitejs/plugin-vue2'
   
   export default defineConfig({
     plugins: [vue()],
     // ... other config
   })
   ```

### Key Considerations for Each Phase

#### Vue 2.7 Phase:
- Ensure all third-party components are compatible
- Update build configurations
- Handle any deprecated features

#### Composition API Phase:
- Plan for gradual migration (hybrid approach)
- Create reusable composables to replace mixins
- Update testing strategies for Composition API

#### Vite Migration Phase:
- Handle any webpack-specific features
- Update environment variables handling
- Check for any Node.js-specific code that might behave differently

#### Vue 3 Phase:
- Handle breaking changes in templates and components
- Update global API usage
- Replace removed features (filters, etc.)

## Recommended Tools and Resources

### Migration Tools:
1. **Vue Migration Helper**: Official tool to identify breaking changes
2. **vue-tsc**: TypeScript checking for Vue components
3. **eslint-plugin-vue**: Linting rules to catch Vue 3 compatibility issues

### Testing Tools:
1. **Vue Test Utils**: Update to version 2 for Vue 3 compatibility
2. **Cypress or Vitest**: For end-to-end and unit testing

### Build Tools:
1. **Vite**: For faster builds and development
2. **@vitejs/plugin-legacy**: For browser compatibility

### Resources:
1. [Official Vue 3 Migration Guide](https://v3-migration.vuejs.org/)
2. [Vue 2.7 Release Notes](https://blog.vuejs.org/posts/vue-2-7-naruto.html)
3. [Vite Documentation](https://vitejs.dev/)

## Timeline Summary

- **Phase 1**: Preparation and Assessment (1-2 weeks)
- **Phase 2**: Upgrade to Vue 2.7 (2-3 weeks)
- **Phase 3**: Migrate to Composition API (4-6 weeks)
- **Phase 4**: Build Tool Migration (2-3 weeks)
- **Phase 5**: Upgrade to Vue 3 (3-4 weeks)
- **Phase 6**: Finalization and Testing (2-3 weeks)

**Total Estimated Time**: 14-21 weeks

## Next Steps

1. Begin with Phase 1: Preparation and Assessment
2. Create inventory of all components and dependencies
3. Address file casing issues systematically
4. Set up testing infrastructure