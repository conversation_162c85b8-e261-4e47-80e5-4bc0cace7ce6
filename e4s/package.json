{"name": "e4s", "version": "1.0.0", "description": "e4s", "scripts": {"test": "cross-env NODE_ENV=development jest --watchAll", "integration": "cross-env NODE_ENV=development jest -c jest.config.integration.js", "lint": "eslint --ext .ts,.js,.vue src --max-warnings=0", "lint:fix": "eslint --ext .ts,.js,.vue src --fix", "vite:dev": "vite", "vite:build": "vite build", "vite:preview": "vite preview --port 5173"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@zxing/library": "0.18.3", "axios": "^0.21.4", "core-js": "^3.44.0", "date-fns": "^1.30.1", "qrcode-generator": "1.4.4", "ramda": "^0.27.0", "socket.io-client": "^4.0.1", "vee-validate": "2.2.2", "vue": "^2.7.16", "vue-class-component": "7.2.3", "vue-ctk-date-time-picker": "2.5.0", "vue-i18n": "8.17.5", "vue-mq": "1.0.1", "vue-property-decorator": "8.4.2", "vue-router": "3.6.5", "vue2-editor": "2.10.2", "vuex": "3.6.2", "xlsx": "0.16.9"}, "devDependencies": {"@babel/core": "7.9.6", "@types/babel__traverse": "7.0.15", "@types/core-js": "2.5.3", "@types/glob": "7.1.4", "@types/jest": "25.2.2", "@types/minimatch": "3.0.5", "@types/node": "^20.11.30", "@types/prettier": "2.4.1", "@types/ramda": "0.27.4", "@types/vue2-editor": "2.6.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue2": "^2.3.3", "@vue/test-utils": "1.0.2", "babel-core": "6.26.3", "cross-env": "^7.0.3", "cross-env-shell": "^7.0.3", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.26.0", "jest": "26.0.1", "jsdom": "16.2.2", "jsdom-global": "3.0.2", "minimatch": "3.1.2", "prettier": "2.4.1", "rollup-plugin-visualizer": "^5.14.0", "ts-jest": "26.5.6", "tslint": "6.1.2", "typescript": "^5.4.5", "typescript-tslint-plugin": "0.5.5", "vite": "^6.3.5", "vite-plugin-checker": "^0.7.2", "vue-eslint-parser": "^9.4.2", "vue-jest": "3.0.5", "vue-template-compiler": "^2.7.16"}, "resolutions": {"@types/babel__traverse": "7.0.15", "@types/glob": "7.1.4", "@types/minimatch": "3.0.5", "minimatch": "3.1.2"}}