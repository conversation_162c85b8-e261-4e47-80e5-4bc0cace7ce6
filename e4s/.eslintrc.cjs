/* ESLint config for Vue 2.7 + TypeScript + Vite */
module.exports = {
  root: true,
  env: {
    browser: true,
    es2020: true,
    node: true,
  },
  parser: "vue-eslint-parser",
  parserOptions: {
    parser: "@typescript-eslint/parser",
    ecmaVersion: 2020,
    sourceType: "module",
    extraFileExtensions: [".vue"],
  },
  plugins: ["vue", "@typescript-eslint"],
  extends: [
    "eslint:recommended",
    "plugin:vue/recommended",
    "plugin:@typescript-eslint/recommended",
  ],
  settings: {
    // Use Vue 2 ruleset behavior
    vue: {
      version: "2.7",
    },
  },
  rules: {
    // Reasonable TS relaxations for migration
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
  },
  ignorePatterns: [
    "dist",
    "dist/**",
    "node_modules",
    // Vite config may use ESM APIs that confuse eslint depending on parser settings
    "vite.config.ts",
  ],
};