// Jest mock for common/config to avoid import.meta in tests.
// Provides minimal runtime values used by code under test.

import type { IConfig } from '../src/common/common-models';

declare const process: any; // avoid TS node types requirement

const E4S_HOST: string = (typeof process !== 'undefined' && process.env && process.env.VITE_E4S_HOST) || 'http://localhost';
const API_URL = E4S_HOST + '/entry/php';
const REST_URL = E4S_HOST + '/wp-json/e4s';
const WP_BASKET = E4S_HOST + '/my-account/basket';
const ORGANISER_AND_PUBLIC_CARD = '/{COMP_ID}/card';
const ORGANISER_REPORT = '/{COMP_ID}/report';

export const POWER_OF_TEN_ATHLETE_LINK = 'https://www.thepowerof10.info/athletes/profile.aspx?ukaurn=';

export const CONFIG = {
  E4S_HOST,
  API_URL,
  REST_URL,
  WP_BASKET,
  ORGANISER_AND_PUBLIC_CARD,
  ORGANISER_REPORT,
} as IConfig;

export const STADIUM_URL_MANCHESTER = CONFIG.E4S_HOST + '/resources/EnglishSchoolsStadiumMap.png';

export const isLocalDevServer = true;