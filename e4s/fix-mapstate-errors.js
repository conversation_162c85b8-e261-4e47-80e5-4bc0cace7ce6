#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix mapState TypeScript errors
 * Addresses TS2769: No overload matches this call errors
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Patterns to identify problematic mapState usage
const PROBLEMATIC_PATTERNS = [
  // Pattern 1: Object literal with incorrect getter syntax
  /mapState\s*\(\s*['"`]([^'"`]+)['"`]\s*,\s*\{\s*([^:]+):\s*\([^)]*\)\s*=>\s*([^}]+)\s*\}/g,
  
  // Pattern 2: Missing proper type annotations
  /mapState\s*\(\s*['"`]([^'"`]+)['"`]\s*,\s*\{\s*([^}]+)\s*\}/g,
  
  // Pattern 3: Incorrect state parameter typing
  /\(state:\s*unknown\)/g,
];

// Correct patterns to replace with
const FIXES = {
  // Convert object literal getter to string array when possible
  objectToStringArray: (moduleName, propName) => 
    `mapState('${moduleName}', ['${propName}'])`,
  
  // Fix getter function with proper typing
  fixGetterFunction: (moduleName, propName, stateAccess) => 
    `mapState('${moduleName}', {\n      ${propName}: (state: any) => ${stateAccess}\n    })`,
  
  // Fix state typing
  fixStateTyping: '(state: any)',
};

/**
 * Process a single Vue file
 */
function processFile(filePath) {
  console.log(`Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  
  // Fix 1: Replace unknown state types with any
  if (content.includes('(state: unknown)')) {
    content = content.replace(/\(state:\s*unknown\)/g, '(state: any)');
    hasChanges = true;
    console.log(`  ✓ Fixed unknown state types`);
  }
  
  // Fix 2: Identify and fix complex mapState patterns
  const mapStateMatches = content.match(/mapState\s*\([^)]+\)/g);
  if (mapStateMatches) {
    mapStateMatches.forEach(match => {
      // Check for problematic object literal patterns
      const objectLiteralMatch = match.match(/mapState\s*\(\s*['"`]([^'"`]+)['"`]\s*,\s*\{\s*([^:]+):\s*\([^)]*\)\s*=>\s*([^}]+)\s*\}/);
      
      if (objectLiteralMatch) {
        const [fullMatch, moduleName, propName, stateAccess] = objectLiteralMatch;
        
        // If it's a simple property access, convert to string array
        if (stateAccess.trim() === `state.${propName.trim()}`) {
          const replacement = `mapState('${moduleName}', ['${propName.trim()}'])`;
          content = content.replace(fullMatch, replacement);
          hasChanges = true;
          console.log(`  ✓ Converted object literal to string array: ${propName.trim()}`);
        } else {
          // Fix the getter function syntax
          const replacement = `mapState('${moduleName}', {\n      ${propName.trim()}: (state: any) => ${stateAccess.trim()}\n    })`;
          content = content.replace(fullMatch, replacement);
          hasChanges = true;
          console.log(`  ✓ Fixed getter function syntax: ${propName.trim()}`);
        }
      }
    });
  }
  
  // Fix 3: Ensure proper TypeScript imports
  if (content.includes('mapState') && !content.includes('import { mapState }')) {
    // Check if there's already a vuex import line
    const vuexImportMatch = content.match(/import\s+\{([^}]+)\}\s+from\s+['"`]vuex['"`]/);
    if (vuexImportMatch) {
      const imports = vuexImportMatch[1];
      if (!imports.includes('mapState')) {
        const newImports = imports.includes('mapState') ? imports : `${imports}, mapState`;
        content = content.replace(vuexImportMatch[0], `import { ${newImports} } from "vuex"`);
        hasChanges = true;
        console.log(`  ✓ Added mapState to existing vuex import`);
      }
    }
  }
  
  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  ✅ File updated: ${filePath}`);
    return true;
  }
  
  return false;
}

/**
 * Main execution
 */
function main() {
  console.log('🔧 Starting mapState error fixes...\n');
  
  // Find all Vue files
  const vueFiles = glob.sync('src/**/*.vue', { cwd: __dirname });
  
  let totalFiles = 0;
  let modifiedFiles = 0;
  
  vueFiles.forEach(file => {
    totalFiles++;
    const fullPath = path.join(__dirname, file);
    
    if (processFile(fullPath)) {
      modifiedFiles++;
    }
  });
  
  console.log(`\n📊 Summary:`);
  console.log(`   Total files processed: ${totalFiles}`);
  console.log(`   Files modified: ${modifiedFiles}`);
  console.log(`   Files unchanged: ${totalFiles - modifiedFiles}`);
  
  if (modifiedFiles > 0) {
    console.log(`\n✅ mapState fixes completed!`);
    console.log(`\n🔍 Next steps:`);
    console.log(`   1. Run: npm run build`);
    console.log(`   2. Test the application`);
    console.log(`   3. Commit changes if everything works`);
  } else {
    console.log(`\n✅ No mapState issues found - all files are already correct!`);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { processFile, FIXES };