{
    "include": ["./src"],
    "exclude": ["**/node_modules", "**/.*/"],
    "compilerOptions": {
        "strict": true,
        "module": "esnext",
        "target": "es5",
        "sourceMap": true,
        "esModuleInterop": true,
        "moduleResolution": "node",
        "allowSyntheticDefaultImports": true,
        "experimentalDecorators": true,
        "noUnusedLocals": false,
        "noImplicitReturns": true,
        "emitDecoratorMetadata": true,
        "strictPropertyInitialization": false,
        //  "noImplicitAny": true,
        "lib": ["dom", "es2015", "es2017", "es2018"],
        "skipLibCheck": true,
        // Additional options for TypeScript 4.5.x
        "noUnusedParameters": false,
        "noFallthroughCasesInSwitch": true,
        "forceConsistentCasingInFileNames": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "declaration": false,
        "removeComments": false,
        "baseUrl": ".",
        "paths": {
            "@/*": ["src/*"]
        }
    },
    "plugins": [
        {
            "name": "typescript-tslint-plugin"
        }
    ]
}
