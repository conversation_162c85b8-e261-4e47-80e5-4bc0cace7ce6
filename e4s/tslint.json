{
    "extends": "tslint:recommended",
    "rules": {
        "max-line-length": {
            "options": [200]
        },
//        "member-access": [true, "no-public"],
        "object-literal-sort-keys": false,
        "new-parens": true,
        "only-arrow-functions": false,
        "no-arg": true,
        "no-bitwise": true,
        "no-conditional-assignment": true,
        "no-consecutive-blank-lines": false,
        "ordered-imports": false,
        "no-console": {
            "severity": "warning",
            "options": [
                "debug",
                "info",
                "log",
                "time",
                "timeEnd",
                "trace"
            ]
        },
        "trailing-comma": [
            true,
            {
                "type": "object",
                "properties": {
                  "multiline": {
                    "anyOf": [
                      {
                        "type": "string",
                        "enum": [
                          "always",
                          "never"
                        ]
                      },
                      {
                        "type": "object",
                        "properties": {
                          "arrays": {
                            "type": "string",
                            "enum": [
                              "always",
                              "never",
                              "ignore"
                            ]
                          },
                          "exports": {
                            "type": "string",
                            "enum": [
                              "always",
                              "never",
                              "ignore"
                            ]
                          },
                          "functions": {
                            "type": "string",
                            "enum": [
                              "always",
                              "never",
                              "ignore"
                            ]
                          },
                          "imports": {
                            "type": "string",
                            "enum": [
                              "always",
                              "never",
                              "ignore"
                            ]
                          },
                          "objects": {
                            "type": "string",
                            "enum": [
                              "always",
                              "never",
                              "ignore"
                            ]
                          },
                          "typeLiterals": {
                            "type": "string",
                            "enum": [
                              "always",
                              "never",
                              "ignore"
                            ]
                          }
                        }
                      }
                    ]
                  },
                  "singleline": {
                    "anyOf": [
                      {
                        "type": "string",
                        "enum": [
                          "always",
                          "never"
                        ]
                      },
                      {
                        "type": "object",
                        "properties": {
                          "arrays": {
                            "type": "string",
                            "enum": [
                              "always",
                              "never",
                              "ignore"
                            ]
                          },
                          "exports": {
                            "type": "string",
                            "enum": [
                              "always",
                              "never",
                              "ignore"
                            ]
                          },
                          "functions": {
                            "type": "string",
                            "enum": [
                              "always",
                              "never",
                              "ignore"
                            ]
                          },
                          "imports": {
                            "type": "string",
                            "enum": [
                              "always",
                              "never",
                              "ignore"
                            ]
                          },
                          "objects": {
                            "type": "string",
                            "enum": [
                              "always",
                              "never",
                              "ignore"
                            ]
                          },
                          "typeLiterals": {
                            "type": "string",
                            "enum": [
                              "always",
                              "never",
                              "ignore"
                            ]
                          }
                        }
                      }
                    ]
                  },
                  "esSpecCompliant": {
                    "type": "boolean"
                  }
                },
                "additionalProperties": false
              }
          ]
    },
    "jsRules": {
        "max-line-length": {
            "options": [120]
        }
    }
}