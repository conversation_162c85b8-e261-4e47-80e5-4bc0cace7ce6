{"header": {"reportVersion": 2, "event": "Allocation failed - JavaScript heap out of memory", "trigger": "FatalE<PERSON>r", "filename": "report.20250703.170742.25836.0.001.json", "dumpEventTime": "2025-07-03T17:07:42Z", "dumpEventTimeStamp": "1751558862054", "processId": 25836, "threadId": null, "cwd": "D:\\Projects\\e4s_3\\e4s", "commandLine": ["C:\\Program Files\\nodejs\\node.exe", "--max-old-space-size=2048", "D:\\Projects\\e4s_3\\e4s\\node_modules\\fork-ts-checker-webpack-plugin\\lib\\service.js"], "nodejsVersion": "v12.16.3", "wordSize": 64, "arch": "x64", "platform": "win32", "componentVersions": {"node": "12.16.3", "v8": "7.8.279.23-node.35", "uv": "1.34.2", "zlib": "1.2.11", "brotli": "1.0.7", "ares": "1.16.0", "modules": "72", "nghttp2": "1.40.0", "napi": "5", "llhttp": "2.0.4", "http_parser": "2.9.3", "openssl": "1.1.1g", "cldr": "36.0", "icu": "65.1", "tz": "2019c", "unicode": "12.1"}, "release": {"name": "node", "lts": "Erbium", "headersUrl": "https://nodejs.org/download/release/v12.16.3/node-v12.16.3-headers.tar.gz", "sourceUrl": "https://nodejs.org/download/release/v12.16.3/node-v12.16.3.tar.gz", "libUrl": "https://nodejs.org/download/release/v12.16.3/win-x64/node.lib"}, "osName": "Windows_NT", "osRelease": "10.0.19045", "osVersion": "Windows 10 Pro", "osMachine": "x86_64", "cpus": [{"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 5419593, "nice": 0, "sys": 5477453, "idle": 278723093, "irq": 403718}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 6250765, "nice": 0, "sys": 5291703, "idle": 278077531, "irq": 236609}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 8447281, "nice": 0, "sys": 7093406, "idle": 274079296, "irq": 249312}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 6345125, "nice": 0, "sys": 4819046, "idle": 278455812, "irq": 190265}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 5581750, "nice": 0, "sys": 4718156, "idle": 279320078, "irq": 363937}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 5647156, "nice": 0, "sys": 5958500, "idle": 278014328, "irq": 1010359}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 5754000, "nice": 0, "sys": 4401343, "idle": 279464640, "irq": 259562}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 6353906, "nice": 0, "sys": 4563843, "idle": 278702234, "irq": 282625}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 5408593, "nice": 0, "sys": 3866562, "idle": 280344828, "irq": 196609}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 5672437, "nice": 0, "sys": 4142109, "idle": 279805437, "irq": 194015}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 10970765, "nice": 0, "sys": 5920593, "idle": 272728609, "irq": 308250}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 4343828, "nice": 0, "sys": 3479562, "idle": 281796578, "irq": 165812}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 5970468, "nice": 0, "sys": 4272921, "idle": 279376562, "irq": 202546}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 5645687, "nice": 0, "sys": 3991312, "idle": 279982968, "irq": 184484}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 15338187, "nice": 0, "sys": 5469000, "idle": 268812781, "irq": 166203}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 4820843, "nice": 0, "sys": 3288765, "idle": 281510359, "irq": 150671}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 7068890, "nice": 0, "sys": 2566062, "idle": 279985000, "irq": 172734}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 6540031, "nice": 0, "sys": 1691281, "idle": 281388640, "irq": 90171}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 7016437, "nice": 0, "sys": 2373078, "idle": 280230421, "irq": 124234}, {"model": "Intel(R) Core(TM) i9-10900K CPU @ 3.70GHz", "speed": 3696, "user": 7623218, "nice": 0, "sys": 1942921, "idle": 280053812, "irq": 116156}], "networkInterfaces": [{"name": "Ethernet", "internal": false, "mac": "24:4b:fe:02:57:bd", "address": "*************", "netmask": "*************", "family": "IPv4"}, {"name": "Loopback Pseudo-Interface 1", "internal": true, "mac": "00:00:00:00:00:00", "address": "::1", "netmask": "ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff", "family": "IPv6", "scopeid": 0}, {"name": "Loopback Pseudo-Interface 1", "internal": true, "mac": "00:00:00:00:00:00", "address": "127.0.0.1", "netmask": "*********", "family": "IPv4"}], "host": "e4s"}, "javascriptStack": {"message": "No stack.", "stack": ["Unavailable."]}, "nativeStack": [{"pc": "0x00007ff72e2c2449", "symbol": "std::basic_ostream<char,std::char_traits<char> >::operator<<+11577"}, {"pc": "0x00007ff72e2c69a9", "symbol": "std::basic_ostream<char,std::char_traits<char> >::operator<<+29337"}, {"pc": "0x00007ff72e2c5828", "symbol": "std::basic_ostream<char,std::char_traits<char> >::operator<<+24856"}, {"pc": "0x00007ff72e3fd8c2", "symbol": "v8::base::CPU::has_sse+68658"}, {"pc": "0x00007ff72ec19bbe", "symbol": "v8::Isolate::ReportExternalAllocationLimitReached+94"}, {"pc": "0x00007ff72ec01c91", "symbol": "v8::SharedArrayBuffer::Externalize+833"}, {"pc": "0x00007ff72eace1ec", "symbol": "v8::internal::Heap::EphemeronKeyWriteBarrierFromCode+1436"}, {"pc": "0x00007ff72ead9420", "symbol": "v8::internal::Heap::ProtectUnprotectedMemoryChunks+1312"}, {"pc": "0x00007ff72ead5f44", "symbol": "v8::internal::Heap::PageFlagsAreConsistent+3204"}, {"pc": "0x00007ff72eacb743", "symbol": "v8::internal::Heap::CollectGarbage+1283"}, {"pc": "0x00007ff72eac9db4", "symbol": "v8::internal::Heap::AddRetainedMap+2452"}, {"pc": "0x00007ff72eaeafbd", "symbol": "v8::internal::Factory::NewFillerObject+61"}, {"pc": "0x00007ff72e851871", "symbol": "v8::internal::interpreter::JumpTableTargetOffsets::iterator::operator=+1665"}, {"pc": "0x00007ff72f066c4d", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+546637"}, {"pc": "0x00007ff72f067b7d", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+550525"}, {"pc": "0x00007ff72f0a3db0", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+796848"}, {"pc": "0x00007ff72f053797", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+467607"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efe4f2c", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+14892"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72f039dc6", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+362694"}, {"pc": "0x00007ff72efe72b3", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+23987"}, {"pc": "0x00007ff72f0cb430", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+958256"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efe4f2c", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+14892"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efebbfc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42748"}, {"pc": "0x00007ff72efe9131", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+31793"}, {"pc": "0x00007ff72efe8d1c", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+30748"}, {"pc": "0x00007ff72eb278a3", "symbol": "v8::internal::Execution::CallWasm+1395"}, {"pc": "0x00007ff72eb27226", "symbol": "v8::internal::Execution::Call+182"}, {"pc": "0x00007ff72ebf829d", "symbol": "v8::Function::Call+605"}, {"pc": "0x00007ff72e47d008", "symbol": "node::CallbackScope::~CallbackScope+776"}, {"pc": "0x00007ff72e47d2c4", "symbol": "node::CallbackScope::~CallbackScope+1476"}, {"pc": "0x00007ff72e476f53", "symbol": "v8::internal::compiler::Operator::EffectOutputCount+179"}, {"pc": "0x00007ff72e33bd00", "symbol": "v8::internal::MicrotaskQueue::microtasks_policy+19936"}, {"pc": "0x00007ff72e33c52c", "symbol": "v8::internal::MicrotaskQueue::microtasks_policy+22028"}, {"pc": "0x00007ff72e337385", "symbol": "v8::internal::MicrotaskQueue::microtasks_policy+1125"}, {"pc": "0x00007ff72e49c462", "symbol": "uv_thread_self+2722"}, {"pc": "0x00007ff72e49c4d7", "symbol": "uv_thread_self+2839"}, {"pc": "0x00007ff72e49f2ef", "symbol": "uv_pipe_pending_type+3471"}, {"pc": "0x00007ff72e4a962c", "symbol": "uv_loop_init+860"}, {"pc": "0x00007ff72e4a9954", "symbol": "uv_run+244"}, {"pc": "0x00007ff72e3b4851", "symbol": "v8::internal::interpreter::BytecodeArrayWriter::source_position_table_builder+31777"}, {"pc": "0x00007ff72e426433", "symbol": "node::Start+275"}, {"pc": "0x00007ff72e296a9c", "symbol": "RC4_options+340380"}, {"pc": "0x00007ff72f11ed38", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+1300536"}, {"pc": "0x00007ffdf4cc7374", "symbol": "BaseThreadInitThunk+20"}, {"pc": "0x00007ffdf5f5cc91", "symbol": "RtlUserThreadStart+33"}], "javascriptHeap": {"totalMemory": 2152353792, "totalCommittedMemory": 2152353792, "usedMemory": 2134896104, "availableMemory": 52283368, "memoryLimit": 2197815296, "heapSpaces": {"read_only_space": {"memorySize": 262144, "committedMemory": 262144, "capacity": 32808, "used": 32808, "available": 0}, "new_space": {"memorySize": 2097152, "committedMemory": 2097152, "capacity": 1047456, "used": 196032, "available": 851424}, "old_space": {"memorySize": 1944203264, "committedMemory": 1944203264, "capacity": 1935629880, "used": 1930706896, "available": 4922984}, "code_space": {"memorySize": 12226560, "committedMemory": 12226560, "capacity": 10845888, "used": 10845888, "available": 0}, "map_space": {"memorySize": 790528, "committedMemory": 790528, "capacity": 681440, "used": 681440, "available": 0}, "large_object_space": {"memorySize": 192724992, "committedMemory": 192724992, "capacity": 192430256, "used": 192430256, "available": 0}, "code_large_object_space": {"memorySize": 49152, "committedMemory": 49152, "capacity": 2784, "used": 2784, "available": 0}, "new_large_object_space": {"memorySize": 0, "committedMemory": 0, "capacity": 1047456, "used": 0, "available": 1047456}}}, "resourceUsage": {"userCpuSeconds": 423.234, "kernelCpuSeconds": 20.312, "cpuConsumptionPercent": 15.2264, "maxRss": 2270879744, "pageFaults": {"IORequired": 1980202, "IONotRequired": 0}, "fsActivity": {"reads": 22248, "writes": 12}}, "libuv": [], "workers": [], "environmentVariables": {"ALLUSERSPROFILE": "C:\\ProgramData", "ANSICON": "120x1000 (120x60)", "ANSICON_DEF": "7", "APPDATA": "C:\\Users\\<USER>\\AppData\\Roaming", "CHECK_SYNTACTIC_ERRORS": "false", "CommonProgramFiles": "C:\\Program Files\\Common Files", "CommonProgramFiles(x86)": "C:\\Program Files (x86)\\Common Files", "CommonProgramW6432": "C:\\Program Files\\Common Files", "COMPILER_OPTIONS": "{}", "COMPUTERNAME": "E4S", "ComSpec": "C:\\WINDOWS\\system32\\cmd.exe", "ConEmuANSI": "ON", "ConEmuAnsiLog": "", "ConEmuArgs": "", "ConEmuArgs2": "", "ConEmuBackHWND": "0x000205D6", "ConEmuBaseDir": "C:\\Program Files\\ConEmu\\ConEmu", "ConEmuBuild": "161022", "ConEmuConfig": "", "ConEmuDir": "C:\\Program Files\\ConEmu", "ConEmuDrawHWND": "0x000205E0", "ConEmuDrive": "C:", "ConEmuHooks": "Enabled", "ConEmuHWND": "0x000205AA", "ConEmuPalette": "<ConEmu>", "ConEmuPID": "21428", "ConEmuPrompt0": "$E[m$E[32m$E]9;8;\"USERNAME\"$E\\@$E]9;8;\"COMPUTERNAME\"$E\\$S", "ConEmuPrompt1": "$E[m$E[32m$E]9;8;\"USERNAME\"$E\\@$E]9;8;\"COMPUTERNAME\"$E\\$S$E[92m$P$E[90m", "ConEmuPrompt2": "$_$E[90m$G", "ConEmuPrompt3": "$E[m$S$E]9;12$E\\", "ConEmuServerPID": "16892", "ConEmuTask": "{Shells::cmd}", "ConEmuWorkDir": "C:\\Users\\<USER>", "ConEmuWorkDrive": "C:", "CONTEXT": "D:\\Projects\\e4s_3\\e4s", "dp0": "D:\\Projects\\e4s_3\\e4s\\node_modules\\.bin\\", "DriverData": "C:\\Windows\\System32\\Drivers\\DriverData", "ESLINT": "false", "ESLINT_OPTIONS": "{}", "HOME": "C:\\Users\\<USER>", "HOMEDRIVE": "C:", "HOMEPATH": "\\Users\\nick", "INIT_CWD": "D:\\Projects\\e4s_3\\e4s", "LOCALAPPDATA": "C:\\Users\\<USER>\\AppData\\Local", "LOGONSERVER": "\\\\E4S", "MEMORY_LIMIT": "2048", "NODE": "C:\\Program Files\\nodejs\\node.exe", "NODE_ENV": "development", "NODE_EXE": "C:\\Program Files\\nodejs\\\\node.exe", "NPM_CLI_JS": "C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js", "npm_config_access": "", "npm_config_allow_same_version": "", "npm_config_also": "", "npm_config_always_auth": "", "npm_config_argv": "{\"remain\":[],\"cooked\":[\"run\",\"dev\"],\"original\":[\"run\",\"dev\"]}", "npm_config_audit": "true", "npm_config_audit_level": "low", "npm_config_auth_type": "legacy", "npm_config_before": "", "npm_config_bin_links": "true", "npm_config_browser": "", "npm_config_ca": "", "npm_config_cache": "C:\\Users\\<USER>\\AppData\\Roaming\\npm-cache", "npm_config_cache_lock_retries": "10", "npm_config_cache_lock_stale": "60000", "npm_config_cache_lock_wait": "10000", "npm_config_cache_max": "Infinity", "npm_config_cache_min": "10", "npm_config_cafile": "", "npm_config_cert": "", "npm_config_cidr": "", "npm_config_color": "true", "npm_config_commit_hooks": "true", "npm_config_depth": "Infinity", "npm_config_description": "true", "npm_config_dev": "", "npm_config_dry_run": "", "npm_config_editor": "notepad.exe", "npm_config_engine_strict": "", "npm_config_fetch_retries": "2", "npm_config_fetch_retry_factor": "10", "npm_config_fetch_retry_maxtimeout": "60000", "npm_config_fetch_retry_mintimeout": "10000", "npm_config_force": "", "npm_config_format_package_lock": "true", "npm_config_fund": "true", "npm_config_git": "git", "npm_config_git_tag_version": "true", "npm_config_global": "", "npm_config_globalconfig": "C:\\Program Files\\nodejs\\etc\\npmrc", "npm_config_globalignorefile": "C:\\Program Files\\nodejs\\etc\\npmignore", "npm_config_global_style": "", "npm_config_group": "", "npm_config_ham_it_up": "", "npm_config_heading": "npm", "npm_config_https_proxy": "", "npm_config_if_present": "", "npm_config_ignore_prepublish": "", "npm_config_ignore_scripts": "", "npm_config_init_author_email": "", "npm_config_init_author_name": "", "npm_config_init_author_url": "", "npm_config_init_license": "ISC", "npm_config_init_module": "C:\\Users\\<USER>\\.npm-init.js", "npm_config_init_version": "1.0.0", "npm_config_json": "", "npm_config_key": "", "npm_config_legacy_bundling": "", "npm_config_link": "", "npm_config_local_address": "", "npm_config_loglevel": "notice", "npm_config_logs_max": "10", "npm_config_long": "", "npm_config_maxsockets": "50", "npm_config_message": "%s", "npm_config_metrics_registry": "https://registry.npmjs.org/", "npm_config_node_gyp": "C:\\Users\\<USER>\\AppData\\Roaming\\nvm\\v12.16.3\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js", "npm_config_node_options": "", "npm_config_node_version": "12.16.3", "npm_config_noproxy": "", "npm_config_offline": "", "npm_config_onload_script": "", "npm_config_only": "", "npm_config_optional": "true", "npm_config_otp": "", "npm_config_package_lock": "true", "npm_config_package_lock_only": "", "npm_config_parseable": "", "npm_config_prefer_offline": "", "npm_config_prefer_online": "", "npm_config_prefix": "C:\\Program Files\\nodejs", "npm_config_preid": "", "npm_config_production": "", "npm_config_progress": "true", "npm_config_proxy": "", "npm_config_read_only": "", "npm_config_rebuild_bundle": "true", "npm_config_registry": "https://registry.npmjs.org/", "npm_config_rollback": "true", "npm_config_save": "true", "npm_config_save_bundle": "", "npm_config_save_dev": "", "npm_config_save_exact": "", "npm_config_save_optional": "", "npm_config_save_prefix": "^", "npm_config_save_prod": "", "npm_config_scope": "", "npm_config_scripts_prepend_node_path": "warn-only", "npm_config_script_shell": "", "npm_config_searchexclude": "", "npm_config_searchlimit": "20", "npm_config_searchopts": "", "npm_config_searchstaleness": "900", "npm_config_send_metrics": "", "npm_config_shell": "C:\\WINDOWS\\system32\\cmd.exe", "npm_config_shrinkwrap": "true", "npm_config_sign_git_commit": "", "npm_config_sign_git_tag": "", "npm_config_sso_poll_frequency": "500", "npm_config_sso_type": "o<PERSON>h", "npm_config_strict_ssl": "true", "npm_config_tag": "latest", "npm_config_tag_version_prefix": "v", "npm_config_timing": "", "npm_config_tmp": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "npm_config_umask": "0000", "npm_config_unicode": "", "npm_config_unsafe_perm": "true", "npm_config_update_notifier": "true", "npm_config_usage": "", "npm_config_user": "", "npm_config_userconfig": "C:\\Users\\<USER>\\.npmrc", "npm_config_user_agent": "npm/6.14.4 node/v12.16.3 win32 x64", "npm_config_version": "", "npm_config_versions": "", "npm_config_viewer": "browser", "npm_execpath": "C:\\Users\\<USER>\\AppData\\Roaming\\nvm\\v12.16.3\\node_modules\\npm\\bin\\npm-cli.js", "npm_lifecycle_event": "dev", "npm_lifecycle_script": "cross-env-shell NODE_ENV=development webpack-dev-server", "npm_node_execpath": "C:\\Program Files\\nodejs\\node.exe", "npm_package_author": "", "npm_package_dependencies_axios": "0.19.2", "npm_package_dependencies_core_js": "3.6.5", "npm_package_dependencies_date_fns": "1.30.1", "npm_package_dependencies_dotenv_webpack": "1.8.0", "npm_package_dependencies_qrcode_generator": "1.4.4", "npm_package_dependencies_ramda": "0.27.0", "npm_package_dependencies_socket_io_client": "4.0.0", "npm_package_dependencies_vee_validate": "2.2.2", "npm_package_dependencies_vue": "2.6.14", "npm_package_dependencies_vue2_editor": "2.10.2", "npm_package_dependencies_vuex": "3.1.1", "npm_package_dependencies_vue_class_component": "7.2.3", "npm_package_dependencies_vue_ctk_date_time_picker": "2.5.0", "npm_package_dependencies_vue_i18n": "8.17.5", "npm_package_dependencies_vue_mq": "1.0.1", "npm_package_dependencies_vue_property_decorator": "8.4.2", "npm_package_dependencies_vue_router": "3.1.6", "npm_package_dependencies_xlsx": "0.16.9", "npm_package_dependencies__vue_composition_api": "1.3.3", "npm_package_dependencies__zxing_library": "0.18.3", "npm_package_description": "e4s", "npm_package_devDependencies_babel_core": "6.26.3", "npm_package_devDependencies_babel_loader": "8.1.0", "npm_package_devDependencies_clean_webpack_plugin": "3.0.0", "npm_package_devDependencies_copy_webpack_plugin": "5.1.1", "npm_package_devDependencies_cross_env": "7.0.2", "npm_package_devDependencies_css_loader": "3.5.3", "npm_package_devDependencies_file_loader": "6", "npm_package_devDependencies_fork_ts_checker_webpack_plugin": "4.1.3", "npm_package_devDependencies_html_loader": "1.1.0", "npm_package_devDependencies_html_webpack_plugin": "4.3.0", "npm_package_devDependencies_jest": "26.0.1", "npm_package_devDependencies_jsdom": "16.2.2", "npm_package_devDependencies_jsdom_global": "3.0.2", "npm_package_devDependencies_mini_css_extract_plugin": "0.9.0", "npm_package_devDependencies_prettier": "2.4.1", "npm_package_devDependencies_style_loader": "1.2.1", "npm_package_devDependencies_tslint": "6.1.2", "npm_package_devDependencies_ts_jest": "26.0.0", "npm_package_devDependencies_ts_loader": "7.0.4", "npm_package_devDependencies_typescript": "3.9.2", "npm_package_devDependencies_typescript_tslint_plugin": "0.5.5", "npm_package_devDependencies_vue_jest": "3.0.5", "npm_package_devDependencies_vue_loader": "15.9.2", "npm_package_devDependencies_vue_template_compiler": "2.6.14", "npm_package_devDependencies_webpack": "4.43.0", "npm_package_devDependencies_webpack_bundle_analyzer": "3.7.0", "npm_package_devDependencies_webpack_cli": "3.3.11", "npm_package_devDependencies_webpack_dev_server": "3.11.0", "npm_package_devDependencies__babel_core": "7.9.6", "npm_package_devDependencies__types_core_js": "2.5.3", "npm_package_devDependencies__types_jest": "25.2.2", "npm_package_devDependencies__types_node": "14.0.1", "npm_package_devDependencies__types_prettier": "2.4.1", "npm_package_devDependencies__types_ramda": "0.27.4", "npm_package_devDependencies__types_vue2_editor": "2.6.0", "npm_package_devDependencies__vue_test_utils": "1.0.2", "npm_package_license": "ISC", "npm_package_name": "e4s", "npm_package_scripts_build": "cross-env-shell NODE_ENV=production webpack", "npm_package_scripts_dev": "cross-env-shell NODE_ENV=development webpack-dev-server", "npm_package_scripts_dev_build": "cross-env-shell NODE_ENV=development webpack", "npm_package_scripts_integration": "cross-env-shell NODE_ENV=development jest -c jest.config.integration.js", "npm_package_scripts_safari_dev": "cross-env-shell NODE_ENV=development webpack-dev-server --host 0.0.0.0 --port 9000", "npm_package_scripts_test": "cross-env-shell NODE_ENV=development jest --watchAll", "npm_package_version": "1.0.0", "NPM_PREFIX_NPM_CLI_JS": "C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js", "NUMBER_OF_PROCESSORS": "20", "NVM_HOME": "C:\\Users\\<USER>\\AppData\\Roaming\\nvm", "NVM_SYMLINK": "C:\\Program Files\\nodejs", "OneDrive": "C:\\Users\\<USER>\\OneDrive", "OneDriveConsumer": "C:\\Users\\<USER>\\OneDrive", "OS": "Windows_NT", "Path": "C:\\Users\\<USER>\\AppData\\Roaming\\nvm\\v12.16.3\\node_modules\\npm\\node_modules\\npm-lifecycle\\node-gyp-bin;D:\\Projects\\e4s_3\\e4s\\node_modules\\.bin;C:\\Program Files\\ConEmu\\ConEmu\\Scripts;C:\\Program Files\\ConEmu;C:\\Program Files\\ConEmu\\ConEmu;C:\\Program Files\\Common Files\\Oracle\\Java\\javapath;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Users\\<USER>\\AppData\\Roaming\\nvm;C:\\Program Files\\nodejs;C:\\Program Files (x86)\\Microsoft SQL Server\\150\\DTS\\Binn\\;C:\\Program Files\\Azure Data Studio\\bin;C:\\Program Files (x86)\\Sennheiser\\SenncomSDK\\;C:\\Program Files\\dotnet\\;C:\\Program Files (x86)\\WinSCP\\;D:\\batch;C:\\Program Files\\Java\\jdk-19\\bin;C:\\Program Files\\PuTTY\\;C:\\Program Files\\Git\\cmd;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Roaming\\nvm;C:\\Users\\<USER>\\AppData\\Local\\gitkraken\\bin;C:\\Program Files\\Azure Data Studio\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Toolbox\\scripts;C:\\Users\\<USER>\\AppData\\Roaming\\nvm;C:\\Program Files\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\EmEditor", "PATHEXT": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JSE;.WSF;.WSH;.MSC", "PROCESSOR_ARCHITECTURE": "AMD64", "PROCESSOR_IDENTIFIER": "Intel64 Family 6 Model 165 Stepping 5, GenuineIntel", "PROCESSOR_LEVEL": "6", "PROCESSOR_REVISION": "a505", "ProgramData": "C:\\ProgramData", "ProgramFiles": "C:\\Program Files", "ProgramFiles(x86)": "C:\\Program Files (x86)", "ProgramW6432": "C:\\Program Files", "PROMPT": "$E[m$E[32m$E]9;8;\"USERNAME\"$E\\@$E]9;8;\"COMPUTERNAME\"$E\\$S$E[92m$P$E[90m$_$E[90m$G$E[m$S$E]9;12$E\\", "PSModulePath": "C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules", "PUBLIC": "C:\\Users\\<USER>", "SESSIONNAME": "<PERSON><PERSON><PERSON>", "SystemDrive": "C:", "SystemRoot": "C:\\WINDOWS", "TEMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "TSCONFIG": "D:\\Projects\\e4s_3\\e4s\\tsconfig.json", "TYPESCRIPT_PATH": "D:\\Projects\\e4s_3\\e4s\\node_modules\\typescript\\lib\\typescript.js", "USERDOMAIN": "E4S", "USERDOMAIN_ROAMINGPROFILE": "E4S", "USERNAME": "nick", "USERPROFILE": "C:\\Users\\<USER>", "USE_INCREMENTAL_API": "false", "VUE": "{\"compiler\":\"vue-template-compiler\",\"enabled\":true}", "WEBPACK_DEV_SERVER": "true", "windir": "C:\\WINDOWS", "ZES_ENABLE_SYSMAN": "1", "_prog": "node", "__PSLockDownPolicy": "0"}, "sharedObjects": ["C:\\Program Files\\nodejs\\node.exe", "C:\\WINDOWS\\SYSTEM32\\ntdll.dll", "C:\\Program Files\\Avast Software\\Avast\\aswhook.dll", "C:\\WINDOWS\\System32\\KERNEL32.DLL", "C:\\WINDOWS\\System32\\KERNELBASE.dll", "C:\\WINDOWS\\System32\\WS2_32.dll", "C:\\WINDOWS\\System32\\RPCRT4.dll", "C:\\WINDOWS\\System32\\PSAPI.DLL", "C:\\WINDOWS\\System32\\ADVAPI32.dll", "C:\\WINDOWS\\SYSTEM32\\dbghelp.dll", "C:\\WINDOWS\\System32\\msvcrt.dll", "C:\\WINDOWS\\System32\\ucrtbase.dll", "C:\\WINDOWS\\System32\\sechost.dll", "C:\\WINDOWS\\SYSTEM32\\IPHLPAPI.DLL", "C:\\WINDOWS\\SYSTEM32\\USERENV.dll", "C:\\WINDOWS\\System32\\bcrypt.dll", "C:\\WINDOWS\\System32\\USER32.dll", "C:\\WINDOWS\\System32\\win32u.dll", "C:\\WINDOWS\\System32\\GDI32.dll", "C:\\WINDOWS\\System32\\gdi32full.dll", "C:\\WINDOWS\\System32\\msvcp_win.dll", "C:\\WINDOWS\\System32\\CRYPT32.dll", "C:\\WINDOWS\\SYSTEM32\\WINMM.dll", "C:\\WINDOWS\\System32\\IMM32.DLL", "C:\\Program Files\\ConEmu\\ConEmu\\ConEmuHk64.dll", "C:\\WINDOWS\\SYSTEM32\\powrprof.dll", "C:\\WINDOWS\\SYSTEM32\\UMPDC.dll", "C:\\WINDOWS\\SYSTEM32\\CRYPTBASE.DLL", "C:\\WINDOWS\\system32\\uxtheme.dll", "C:\\WINDOWS\\System32\\combase.dll", "C:\\WINDOWS\\system32\\mswsock.dll", "C:\\WINDOWS\\SYSTEM32\\kernel.appcore.dll", "C:\\WINDOWS\\System32\\bcryptprimitives.dll", "C:\\WINDOWS\\System32\\NSI.dll", "C:\\WINDOWS\\SYSTEM32\\dhcpcsvc6.DLL", "C:\\WINDOWS\\SYSTEM32\\dhcpcsvc.DLL", "C:\\WINDOWS\\SYSTEM32\\DNSAPI.dll", "C:\\WINDOWS\\system32\\napinsp.dll", "C:\\WINDOWS\\system32\\pnrpnsp.dll", "C:\\WINDOWS\\system32\\wshbth.dll", "C:\\WINDOWS\\system32\\NLAapi.dll", "C:\\WINDOWS\\System32\\winrnr.dll"]}