# Vite Upgrade: Prioritized Actionable Steps

Scope for this checklist
- Config and scripts: [e4s/package.json](e4s/package.json), [e4s/vite.config.ts](e4s/vite.config.ts), [e4s/index.html](e4s/index.html)
- Entry/runtime: [e4s/src/index.ts](e4s/src/index.ts)
- Output: dist/vite per [e4s/vite.config.ts](e4s/vite.config.ts)

Execution protocol
- Execute steps in order (1 to 8).
- After each step is applied, stop for a manual verification build run (dev/preview/build as appropriate).
- Only proceed to the next step after confirmation.

Status legend
- [ ] Pending
- [-] In progress
- [x] Complete

---

1) Replace http CDN URLs with https to prevent mixed-content blocking
- Files: [e4s/index.html](e4s/index.html)
- Actions:
  - Update CDN links for jQuery UI CSS and jQuery scripts from http to https.
- Acceptance:
  - Dev server runs without mixed content warnings on HTTPS.
  - Preview/build renders without console security errors.
- Notes:
  - Targets observed in [e4s/index.html](e4s/index.html): CSS include and two script tags using http.

2) Migrate environment variables to Vite conventions
- Files: [e4s/vite.config.ts](e4s/vite.config.ts), [e4s/src/index.ts](e4s/src/index.ts)
- Actions:
  - Add .env files under e4s/ with VITE_ prefix for variables to be exposed to client.
  - Replace usages of process.env in the app code with the Vite env API where applicable.
  - Specifically review the NODE_ENV usage in [e4s/src/index.ts](e4s/src/index.ts) around the performance flag and align with Vite’s mode handling.
  - Once migrated, remove the define shim for process.env from [e4s/vite.config.ts](e4s/vite.config.ts) to avoid masking.
- Acceptance:
  - Dev/preview/build succeed and expected environment-driven behaviors remain correct.

3) Remove webpack-specific dependencies and scripts
- Files: [e4s/package.json](e4s/package.json)
- Actions:
  - Remove webpack, webpack-cli, webpack-dev-server and associated loaders/plugins:
    - webpack, webpack-cli, webpack-dev-server
    - vue-loader, vue-template-compiler
    - ts-loader, fork-ts-checker-webpack-plugin
    - css-loader, style-loader, mini-css-extract-plugin
    - file-loader, html-loader, html-webpack-plugin, clean-webpack-plugin, copy-webpack-plugin
    - webpack-bundle-analyzer (superseded by rollup-plugin-visualizer)
    - dotenv-webpack (replace with Vite .env handling)
  - Remove legacy webpack scripts (dev, build, dev-build, safari-dev).
- Acceptance:
  - npm install completes without removed packages.
  - vite:dev, vite:preview, and vite:build still work.

4) Establish linting and type checking under Vite
- Files: project root configs; source files for lint/type-check
- Actions:
  - Add ESLint stack: eslint, @typescript-eslint/parser, @typescript-eslint/eslint-plugin, eslint-plugin-vue; create a minimal .eslintrc.
  - Add a type-check path: vue-tsc, or vite-plugin-checker for integrated TS and ESLint checks.
  - Add npm scripts for lint and type-check.
- Acceptance:
  - Lint runs cleanly or reports actionable findings.
  - Type-checking runs independently of bundling and fails builds when types break (if desired).

5) Static assets and public directory alignment
- Files: [e4s/index.html](e4s/index.html), assets directories under [e4s/src/](e4s/src/)
- Actions:
  - Audit any assets previously handled by file/copy loaders.
  - Move truly static assets to e4s/public when appropriate; reference them via root-relative URLs.
  - Keep imported assets within src as module imports.
  - Only set publicDir in Vite config if deviating from defaults.
- Acceptance:
  - Dev/preview/build resolve assets correctly with correct cache-busting in production.

6) Clean residual webpack patterns
- Files: [e4s/src/index.ts](e4s/src/index.ts) and other lazy imports
- Actions:
  - Remove webpack-specific magic comments in dynamic imports that are no-ops under Vite.
- Acceptance:
  - Codebase no longer contains confusing, unused webpack hints.
  - Lazy routes still code-split as expected by Vite.

7) Production build validation and bundle insights
- Files: [e4s/vite.config.ts](e4s/vite.config.ts); generated artifacts in dist/vite
- Actions:
  - Run vite:build and confirm clean output in dist/vite.
  - Open and review dist/vite-stats.html produced by rollup-plugin-visualizer.
  - Compare key bundle sizes versus prior baseline if available; note regressions/improvements.
- Acceptance:
  - Build succeeds with source maps per config.
  - Bundle composition and size are acceptable, or follow-up actions are logged.

8) Update CI/CD and deployment to consume Vite output
- Files: CI pipelines, deployment scripts, hosting configs (paths not in repo may need ops changes)
- Actions:
  - Ensure pipelines run vite:build and publish dist/vite.
  - Align hosting assumptions (base, asset paths, cache headers).
  - Update internal docs for developer workflows and deployment steps.
- Acceptance:
  - CI produces artifacts from dist/vite.
  - Deployment serves Vite-built assets correctly end-to-end.

---

Change control
- After completing each step, tag progress in this file and in the team TODO tracking.
- If a step surfaces blockers, record them here with a brief resolution plan before proceeding.
## Step 2 — Verification checklist and notes

Use this list to validate the environment variable migration before proceeding.

Verification checklist
- Dev server
  - Hard-reload the browser while running `npm run vite:dev`. Confirm no errors about undefined process.env and the app functions normally.
  - Confirm performance flag logic works (dev vs prod) per [e4s/src/index.ts](e4s/src/index.ts).
- Host resolution
  - Confirm API base host is correct via CONFIG centralization in [e4s/src/common/config.ts](e4s/src/common/config.ts) (E4S_HOST = origin in prod, VITE_E4S_HOST in dev).
  - Visit features that rely on E4S_HOST:
    - Cart mini endpoint now uses CONFIG: [e4s/src/cart/cart-data.ts](e4s/src/cart/cart-data.ts), [e4s/src/cart/cart-mini.vue](e4s/src/cart/cart-mini.vue)
    - Auth token endpoint now uses CONFIG: [e4s/src/auth/auth-store.ts](e4s/src/auth/auth-store.ts)
    - Ticket QR code URLs now use CONFIG: [e4s/src/secondary/ticket/ticket-route.vue](e4s/src/secondary/ticket/ticket-route.vue), [e4s/src/secondary/ticket/ticket-user/ticket-order/ticket-order-container.vue](e4s/src/secondary/ticket/ticket-user/ticket-order/ticket-order-container.vue)
- Production build
  - Run `npm run vite:build` and confirm it succeeds. Spot check dist/vite and the generated stats file noted in [e4s/vite.config.ts](e4s/vite.config.ts).

Known dev warning (non-blocker)
- Vite client sourcemap warning:
  - Example: “Failed to load source map for e4s/src/assets/vue-select-3-20-0.css … missing vue-select.css.map”
  - This arises from a sourceMappingURL comment embedded in the CSS file. Options:
    1) Remove the trailing `/*# sourceMappingURL=... */` comment from [e4s/src/assets/vue-select-3-20-0.css](e4s/src/assets/vue-select-3-20-0.css), or
    2) Add a placeholder map file at [e4s/src/assets/vue-select.css.map](e4s/src/assets/vue-select.css.map), or
    3) Tolerate the warning during dev (it is harmless). If desired, we can also disable CSS dev sourcemaps globally by setting `css: { devSourcemap: false }` in [e4s/vite.config.ts](e4s/vite.config.ts).

Blockers to record (if any)
- Paste any build or runtime errors here so they can be addressed before Step 3.