# Vue 3 migration: Handling Vue 2 class-based components

This document explains how we will handle the large number of Vue 2 class-based Single File Components (SFCs) during the upgrade to Vue 3. It provides a safe phased strategy, concrete conversion rules, examples, batching guidance, and automation tips.

## Why this is needed

The codebase contains many class-style components built with `vue-class-component` and `vue-property-decorator`. A quick sample:
- [common-vue.ts](../src/common/common-vue.ts)
- [loading-spinner.vue](../src/common/ui/loading-spinner.vue)
- [user-validation-messages.vue](../src/user-message/user-validation-messages.vue)
- [team-entry.vue](../src/team/entry/team-entry.vue)
- [html-editor-quill.vue](../src/common/ui/html-editor/html-editor-quill.vue)

We found 260+ matches that import class decorators or extend a Vue base class. A full one-shot conversion would be high risk and would block the Vue 3 upgrade.

## Strategy overview

We will take a 3-phase approach:

1) Short-term: keep class components working under Vue 3 compat
- Upgrade to Vue 3 and enable the Vue 3 compatibility build.
- Switch the root bootstrap (createApp), upgrade the router to vue-router 4, and keep class-style components running via compat.
- This buys time to convert components incrementally without blocking the app.

2) Medium-term: convert class components module-by-module
- Convert class-style SFCs to standard Options API or to the Composition API.
- Defer components that depend on plugins that also require upgrades (vee-validate v2, vue2-editor, old date-time picker, etc.) to when we replace those plugins.

3) Long-term: remove class libs and disable compat
- Drop `vue-class-component` and `vue-property-decorator`.
- Disable Vue 3 compat flags.

## Prerequisites and compat runtime

- Create a branch: `feature/vue3-migration`.
- Move to Vue 3 runtime using the compatibility build and Vite plugin for Vue 3:
  - Add: `vue ^3`, `@vitejs/plugin-vue`, `@vue/compiler-sfc`.
  - Remove: `@vitejs/plugin-vue2`, `vue-template-compiler`.
- In `vite.config.ts`:
  - Use `@vitejs/plugin-vue`.
  - Add a resolve alias `vue` to `@vue/compat` initially.
- Keep `vue-class-component` v7 and `vue-property-decorator` for now (they surface as Options internally, which compat supports).
- Upgrade state to Vuex 4 (minimal churn) so that Vue 3 + store works smoothly during the transition. Pinia can be adopted later if desired.

We will remove the alias to `@vue/compat` only after converting class components and finishing global API updates.

## Router hooks registration

The project uses a shared registration for class-based router hooks:
- [common-vue.ts](../src/common/common-vue.ts)

Under Vue 3 we will:
- Remove the centralized `Component.registerHooks([...])`.
- For each SFC that needs route hooks:
  - Options API: define `beforeRouteEnter`, `beforeRouteUpdate`, `beforeRouteLeave` directly in the component options.
  - Composition API: use `onBeforeRouteUpdate` and friends from `vue-router`.

This avoids global patching and aligns with Vue 3 patterns.

## Conversion cookbook (class decorators ➜ Options or Composition)

Use these consistent rules when refactoring a class-based component.

1) Imports
- Remove `import Vue from "vue"`.
- Remove `vue-class-component` and `vue-property-decorator` imports.
- Prefer:
  - Options API: `import { defineComponent } from "vue"`
  - Composition API (script-setup): `<script setup lang="ts">`

2) @Component metadata
- Move the object passed into `@Component({ ... })` to `defineComponent({ ... })`, including `name`, `components`, `directives`, and any local options.

3) @Prop
- Options API: define in `props: { propName: { type, required, default } }`.
- Composition API: use `defineProps<{ propName: Type }>()` and default with `withDefaults`.

4) Class fields ➜ data or refs
- If the value is mutable per instance:
  - Options API: return it from `data()`.
  - Composition API: use `ref()` or `reactive()`.
- If it’s a constant per component instance, keep as a const in `setup()`.

5) Getters ➜ computed
- Options API: `computed: { someValue() { ... } }`.
- Composition API: `const someValue = computed(() => ...)`.

6) Methods
- Options API: under `methods: { ... }`.
- Composition API: normal functions inside `setup()` or module-level if no instance context is needed.

7) @Watch
- Options API: `watch: { foo(newVal, oldVal) { ... } }`.
- Composition API: `watch(() => foo.value, (newVal, oldVal) => { ... })`.

8) Lifecycle
- Options API: `created, mounted, beforeUnmount` etc.
- Composition API: `onMounted, onBeforeUnmount`, etc.

9) Emits
- Options API: add `emits: ['eventName']`.
- Composition API: `const emit = defineEmits<{(e: 'eventName', payload: T): void}>()`.

10) Vuex usage
- Options API:
  - You can keep `mapState`, `mapGetters`, `mapActions`, `mapMutations` helpers for minimal churn (with Vuex 4).
- Composition API:
  - `const store = useStore()`
  - Then use `computed(() => store.state.module.prop)` and `store.dispatch('module/action', payload)`.

11) Router hooks
- Options API: place `beforeRouteEnter` etc. in component options.
- Composition API: use `onBeforeRouteUpdate`, `useRoute`, `useRouter`.

12) Templates
- Remove Vue 2 filters. Replace with computed or methods.
- Replace `.sync` with `v-model:propName`.
- Ensure slots use Vue 3 syntax.

## Example A: Convert a leaf component (loading-spinner)

Source (class-based):
- [loading-spinner.vue](../src/common/ui/loading-spinner.vue)

Refactor to Options API:

- Keep the template identical.
- Replace script with an Options API SFC using `defineComponent({ name })`.
- No data, props, or methods required.

Alternatively, use script-setup and `defineOptions({ name: "loading-spinner" })`. Either path is acceptable; choose a single style team-wide.

## Example B: Convert a simple props component (user-validation-messages)

Source:
- [user-validation-messages.vue](../src/user-message/user-validation-messages.vue)
- Has a single prop `validationMessages` with a default array.

Refactor steps:
- Define `props: { validationMessages: { type: Array as PropType<IValidationResult[]>, default: () => [] } }`.
- Remove decorator code entirely.
- The template is unchanged.

## Components with external plugin dependencies

These should be converted only after their plugin upgrades, to avoid double-work:

- vee-validate v2 based forms
  - Example: [team-entry.vue](../src/team/entry/team-entry.vue)
  - Plan: Replace vee-validate v2 with v4 (useForm/useField) during the form conversion, then convert the component to Composition API in the same PR.

- vue2-editor (Quill)
  - Example: [html-editor-quill.vue](../src/common/ui/html-editor/html-editor-quill.vue)
  - Plan: Replace with `@vueup/vue-quill` or a preferred Vue 3 editor, then convert the component.

- vue-mq, vue-ctk-date-time-picker
  - Replace with Vue 3 equivalents before converting consuming components.

## Batching plan

1) Start with leaf and reusable UI components in `src/common/ui/**` that do not depend on deprecated plugins.
2) Convert route-level shells that have minimal logic.
3) Convert feature modules one-by-one:
   - Choose a module boundary (e.g., competition scoreboard) and convert all class components inside that module.
4) Defer components that rely on vee-validate v2, vue2-editor, or old date pickers until those replacements are in place.

Within each batch:
- Pick 3 pilot components first (one leaf, one container, one route view) to validate patterns.
- Write the conversions, run dev build, fix type errors, smoke-test routes.
- Land the batch, proceed to the next module.

## Automation and codemod assists

While a perfect codemod is hard due to decorators and varied patterns, the following help:

1) Mechanical import cleanup (regex)
- Remove `import Vue from "vue"`.
- Replace `import Component from "vue-class-component"` usage.
- Remove `import { Prop, Watch } from "vue-property-decorator"` and translate to `props` and `watch`.

2) Scaffolding for Options API
- Insert `export default defineComponent({ name: 'ComponentName' })` with existing `components`, `directives`, and computed/actions migrated from the `@Component` object.

3) Watchers
- Translate `@Watch('x') handler(newVal, oldVal) { ... }` to either Options `watch: { x(newVal, oldVal) { ... } }` or Composition `watch`.

4) Route hooks
- If component contains class methods `beforeRouteEnter` et al., move them into Options or to router composables and delete [common-vue.ts](../src/common/common-vue.ts) once all are migrated.

5) Vuex
- Short-term keep map helpers for Options API components.
- For Composition API, migrate to `useStore()` with `computed`.

6) Unit tests
- For components converted to Vue 3 API, ensure tests use `@vue/test-utils` v2 and adapt mounting accordingly.
- Consider migrating test runner to Vitest to speed feedback during this batch work.

## Known gotchas

- `$listeners` and `$scopedSlots` no longer exist in Vue 3; use `attrs` and slots destructuring.
- Filter pipes in templates are removed; replace with computed or methods.
- `v-model` modifiers and multi-models use the `v-model:prop` syntax with `emits`.
- Global APIs like `Vue.use`, `Vue.component`, `Vue.directive` move to `app.use`, `app.component`, `app.directive`.
- Remove `Component.registerHooks` and place route hooks per-component.
- Ensure Vuex 4 (or Pinia) is present before converting Composition API components that access store.

## When to remove class libs and disable compat

- After all class components are converted and plugin replacements are done:
  - Remove `vue-class-component` and `vue-property-decorator`.
  - Remove the alias to `@vue/compat` in Vite and switch to plain `vue`.
  - Replace any remaining global APIs with application instance APIs.
  - Run a full smoke and regression test.

## Work breakdown and acceptance criteria

- A component is considered migrated when:
  - No import from `vue-class-component` or `vue-property-decorator`.
  - Uses Options or Composition API.
  - Route hooks live in Options or composables, not in [common-vue.ts](../src/common/common-vue.ts).
  - All dependent plugins are Vue 3 compatible.
  - Unit tests pass using Vue 3 tooling.

- A module is considered migrated when:
  - All SFCs in the module are converted.
  - Module entry routes smoke-test successfully.
  - No class-based imports remain in module directories.

- The project is considered migrated when:
  - No usage of class libs project-wide.
  - `@vue/compat` alias removed.
  - All critical flows pass QA.
## Migration options snapshot (reference)

Context
- Class components can continue to run under Vue 3 using the compatibility build. We will refactor gradually, not in one shot.
- vee-validate v2 is a hard blocker for Vue 3 and must be replaced during the migration.
- Current Vue 2-only APIs to keep in mind:
  - Root bootstrap with [new Vue()](../src/index.ts:416) that must move to createApp
  - Global class router hook registration via [Component.registerHooks()](../src/common/common-vue.ts:3)
  - Form-level vee-validate v2 usage including [Validator.extend()](../src/team/entry/team-entry.vue:61) and v-validate directives in templates

Option A: Single-pass to Vue 3 compat now (recommended when aiming for quicker convergence)
- Core:
  - Add Vue 3 + compat, Vite plugin for Vue 3, and alias vue to @vue/compat in vite config
  - Migrate router to vue-router 4 (dedicated router file) and switch to createApp replacing [new Vue()](../src/index.ts:416)
  - Bump state to Vuex 4 for minimal churn (Pinia can be adopted later)
- Plugins:
  - Replace vee-validate v2 with vee-validate 4 (Composition API) in a focused sweep
  - Swap vue2-editor for a Vue 3 editor (e.g., @vueup/vue-quill)
  - Replace vue-ctk-date-time-picker with a Vue 3 compatible date-time picker
  - Replace vue-mq with a Vue 3 alternative (vue3-mq or composable-based approach)
- Components:
  - Keep class components running under compat
  - Convert class components incrementally by module to Options/Composition

Option B: Two-phase to reduce risk on forms-heavy areas (more incremental)
- Phase 1 (still on Vue 2.7):
  - Remove vee-validate v2 by migrating forms to either local validation or vee-validate 3
  - Replace vue2-editor/date-picker/vue-mq with cross-compatible or Vue 3-ready choices if possible
  - Start converting low-risk class components to Options API (no plugin blockers)
- Phase 2 (switch to Vue 3 compat):
  - Move to Vue 3 + @vue/compat, upgrade router to 4, Vuex to 4, and finalize remaining component conversions
  - Complete any plugin swaps that could not be done in Phase 1
- Trade-off: more steps, but forms remain stable during early refactors

Option C: Hybrid rollout by route/module (progressively open the app)
- Switch to Vue 3 compat + router 4 + Vuex 4 now
- Limit the active routes initially while migrating vee-validate v2 forms and other Vue 2-only plugins
- Open modules/areas as they are completed
- Useful for staged releases where partial functionality is acceptable

Key hotspots to address
- Root bootstrap: [new Vue()](../src/index.ts:416) ➜ createApp; move global app registrations (components/directives) to app.component/app.directive
- Global class router hooks: remove [Component.registerHooks()](../src/common/common-vue.ts:3), use per-component Options/Composition or router composables
- Forms: Replace vee-validate v2 (v-validate directives and [Validator.extend()](../src/team/entry/team-entry.vue:61)) with vee-validate 4 (useForm/useField) and update error handling
- Editors/Date pickers/MQ: Replace Vue 2-only packages with Vue 3 compatible alternatives before converting their consumers

Trade-offs summary
- Option A: Fastest path to Vue 3; requires a focused sweep on forms/plugins shortly after enabling compat
- Option B: Safest for forms-heavy areas; longer total duration; additional interim work on Vue 2.7
- Option C: Enables incremental releases; requires stronger route/module gating and coordination

Next decision required
- Choose Option A, B, or C for the program plan and sequencing. Once chosen, we will lock scope for Phase 1 and create the execution checklist and PR plan.

References
- Root entry: [index.ts](../src/index.ts)
- Class hooks registration: [common-vue.ts](../src/common/common-vue.ts)
- Representative form with vee-validate v2: [team-entry.vue](../src/team/entry/team-entry.vue)