# E4S Project Guidelines

## Component Structure
- Use Vue composition API with TypeScript for new components
- Follow naming convention: `ComponentName.vue` for newer components
- Organize components with clear template/script/style sections
- create useFunction.ts for complex component logic

## UI Components
- Use V2 components from common/ui/layoutV2 for consistency
- Prefer FormGenericV2 components for form layouts
- Use ButtonGenericV2 for standard button styling

## TypeScript Best Practices
- Define proper prop types using PropType
- Create factory methods for complex objects
- Use interfaces for model definitions
- Leverage TypeScript for type safety

## State Management
- Use controller pattern for complex component logic
- Implement validation in dedicated services
- Follow the pattern in useResultsImportController for state management

## Code Style
- Follow tslint:recommended rules defined in tslint.json
- Keep functions small and focused
- Use proper naming conventions for variables and functions

## Form Handling
- Use v-model for form inputs
- Implement validation with clear error messages
- Follow patterns in existing form components

## Project Structure
- Organize related components in feature folders
- Keep services separate from components
- Use models.ts files for type definitions
- Use feature sliced design folder structures where possible
