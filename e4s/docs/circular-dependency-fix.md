# Circular Dependency Fix - Entry4Sports (E4S)

## Problem Summary

The E4S codebase had a critical circular dependency that prevented Vite builds from completing successfully. The dependency chain involved:

1. **35+ Data Service Classes** → extend `ResourceData<T>`
2. **ResourceData** → imports `https` from `../https`
3. **https.ts** → imports `appStore` + `router` from app files
4. **app.store.ts** → imports `entryStore` and other stores
5. **Stores** → instantiate Data Services (completing the cycle)

## Solution Implemented

### Minimal Fix: Runtime Context Pattern

**Files Created:**
- `src/common/runtime-context.ts` - Dependency injection utility

**Files Modified:**
- `src/common/https.ts` - Removed direct imports, uses lazy access
- `src/index.ts` - Initializes runtime context after store/router creation

### Key Changes

#### 1. Runtime Context Utility (`src/common/runtime-context.ts`)
```typescript
export interface IRuntimeContext {
  store?: Store<IRootState>;
  router?: VueRouter;
}

let runtimeContext: IRuntimeContext = {};

export function setRuntimeContext(context: IRuntimeContext): void {
  runtimeContext = { ...context };
}

export function getStore(): Store<IRootState> | null {
  return runtimeContext.store || null;
}

export function getRouter(): VueRouter | null {
  return runtimeContext.router || null;
}
```

#### 2. HTTP Client Refactor (`src/common/https.ts`)
**Before:**
```typescript
import { appStore } from "../app.store";
import { router } from "../index";

// Direct usage in interceptors
const isUserLoggedIn = appStore.state[AUTH_STORE_CONST.AUTH_CONST_MODULE_NAME].isLoggedIn;
```

**After:**
```typescript
import { getStore, getRouter } from "./runtime-context";

// Lazy access in interceptors
const store = getStore();
if (store) {
  const isUserLoggedIn = store.state[AUTH_STORE_CONST.AUTH_CONST_MODULE_NAME].isLoggedIn;
}
```

#### 3. Context Initialization (`src/index.ts`)
```typescript
import { setRuntimeContext } from "./common/runtime-context";

export const router = new VueRouter(routerConfig as any);

// Initialize runtime context to break circular dependencies
setRuntimeContext({
  store: appStore,
  router: router
});
```

## Results

✅ **Production build works**: `npm run vite:build` completes successfully  
✅ **Development server works**: `npm run vite:dev` starts without errors  
✅ **No breaking changes**: All existing functionality preserved  
✅ **Runtime safety**: Graceful handling when dependencies aren't available yet  

## Remaining Secondary Circular Dependencies

### 1. User Message Store Cycle (76 files affected)
- `user-message-store.ts` imports `appStore` directly
- Used by `messageDispatchHelper` across the codebase
- **Impact**: Non-blocking, but creates tight coupling

### 2. Router Guards Cycle
- `router/guards.ts` imports `appStore` directly  
- Used in `isLoggedInGuardGeneric` for authentication
- **Impact**: Authentication flow dependency

### 3. Launch Routes Cycle
- `launch/launch-routes.ts` imports `appStore` directly
- **Impact**: Navigation logic coupling

## Future Refactoring Strategy

### Phase 1: Address Secondary Cycles (1-2 weeks)
- Apply runtime context pattern to user-message-store
- Refactor router guards to use dependency injection
- Update launch routes to use lazy access

### Phase 2: Service Locator Pattern (2-4 weeks)
- Create centralized service container
- Refactor all 35+ data service classes
- Implement lazy service instantiation

### Phase 3: Full Dependency Injection (4-6 weeks)
- HTTP service abstraction
- Service registration system
- Prepare for Vue 3 migration

## Maintenance Guidelines

### When Adding New Services
1. Use the runtime context pattern for store/router access
2. Avoid direct imports of `appStore` or `router`
3. Register services in the service container (when implemented)

### When Modifying HTTP Client
1. Always use `getStore()` and `getRouter()` for lazy access
2. Check for null before using store/router instances
3. Maintain graceful degradation when dependencies aren't available

### Monitoring
- Watch for new circular dependency warnings during builds
- Test both development and production builds after changes
- Verify authentication flows work correctly with lazy access

## Technical Debt

### Immediate (Next Sprint)
- [ ] Apply runtime context pattern to user-message-store
- [ ] Refactor router guards dependency
- [ ] Update launch routes pattern

### Medium Term (Next Quarter)
- [ ] Implement service locator pattern
- [ ] Refactor all data service instantiation
- [ ] Create service registration system

### Long Term (Next 6 months)
- [ ] Full dependency injection architecture
- [ ] Prepare for Vue 3 migration
- [ ] Service container with type safety

## Risk Assessment

### Low Risk ✅
- Current fix is minimal and safe
- No breaking changes to existing APIs
- Graceful fallback when dependencies unavailable

### Medium Risk ⚠️
- Secondary circular dependencies still exist
- Tight coupling in user message system
- Authentication flow dependencies

### Mitigation
- Monitor production for any runtime issues
- Implement comprehensive testing for authentication flows
- Plan incremental refactoring to avoid big-bang changes

---

**Date**: 2025-09-23  
**Author**: Kilo Code  
**Status**: ✅ Critical fix implemented, builds working  
**Next Review**: After secondary cycles are addressed