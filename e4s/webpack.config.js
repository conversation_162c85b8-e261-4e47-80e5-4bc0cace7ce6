const path = require('path');
const ForkTsCheckerWebpackPlugin = require("fork-ts-checker-webpack-plugin");
const VueLoaderPlugin = require("vue-loader/lib/plugin");
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const Dotenv = require('dotenv-webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyWebpackPlugin = require("copy-webpack-plugin");
const {CleanWebpackPlugin} = require('clean-webpack-plugin');
// const HtmlWebpackPlugin = require('html-webpack-plugin');

module.exports = {
    mode: process.env.NODE_ENV,
    entry: {
        app: './src/index.ts'
    },
    output: {
        filename: process.env.NODE_ENV === "production" ? "[name][hash].js" : "[name].js",
        path: path.resolve(__dirname, 'dist'),
        publicPath: process.env.NODE_ENV === "production" ? "/app/" : "",
        devtoolModuleFilenameTemplate: (info) => {
            let $filename = `sources://${info.resourcePath}`;
            if (info.resourcePath.match(/\.vue$/) && !info.query.match(/type=script/)) {
                $filename = `webpack-generated:///${info.resourcePath}?${info.hash}`;
            }
            return $filename;
        },
        devtoolFallbackModuleFilenameTemplate: "webpack:///[resource-path]?[hash]"
    },
    optimization: {
        splitChunks: {
            cacheGroups: {
                commons: {
                    test: /[\\/]node_modules[\\/]/,
                    name: "vendor",
                    chunks: "all"
                }
            }
        }
    },
    //   output: {
    //       path: path.resolve(__dirname, 'dist'),
    //       filename: '[name].[contenthash].js',
    //   },
    //   optimization: {
    //       splitChunks: {
    //           chunks: 'all',
    //       },
    //   },
    devtool: process.env.NODE_ENV === "production" ? "source-map" : "eval-source-map",   // "eval-source-map", "eval-cheap-module-source-map", "cheap-module-eval-source-map",
    module: {
        rules: [
            {
                // Fonts
                test: /\.(eot|svg|ttf|woff|woff2)(\?\S*)?$/,
                use: {
                    loader: "file-loader",
                    options: {
                        name: "[name].[ext]",
                        outputPath: "fonts/"
                    }
                }
            },
            {
                // Images
                test: /\.(png|jpg|gif|ico)$/,
                use: {
                    loader: "file-loader",
                    options: {
                        name: "[name].[ext]",
                        outputPath: "images/",
                        esModule: false
                    }
                }
            },
            {
                // HTML
                // exclude index.html which is processed by HtmlWebpackPlugin
                test: /\.html$/,
                exclude: /index\.html$/,
                use: "html-loader"
            },
            {
                // CSS
                test: /\.css$/,
                use: [
                    MiniCssExtractPlugin.loader,
                    "css-loader"
                ]
            },
            {
                // Vue single file components
                test: /\.vue$/,
                use: {
                    loader: "vue-loader"
                }
            },
            {
                // TypeScript
                test: /\.ts$/,
                use: {
                    loader: "ts-loader",
                    options: {
                        appendTsSuffixTo: [/\.vue$/],
                        transpileOnly: true
                    }
                }
            }
        ]
    },
    resolve: {
        alias: {
            "vue$": "vue/dist/vue.common.js",
            "axios$": "axios/dist/axios.js",
            "vue-cool-select$": "vue-cool-select/dist/vue-cool-select.common.js"
        },
        extensions: [".ts", ".js", ".vue"]
    },
    plugins: [
        // new HtmlWebpackPlugin(),
        new CleanWebpackPlugin(),
        new MiniCssExtractPlugin({
            filename:  process.env.NODE_ENV === "production" ? "[name][hash].css" : "[name].css"
        }),
        new VueLoaderPlugin(),
        new ForkTsCheckerWebpackPlugin({
            async: false,
            vue: true,
            tslint: true
        }),
        new Dotenv({
            path: `config/${process.env.NODE_ENV}.env`,  //  path.resolve(__dirname, 'project.env'),
            safe: "config/example.env",
            systemvars: false,
            silent: false
        }),
        new BundleAnalyzerPlugin({
            analyzerMode: "production" === process.env.NODE_ENV ? "static" : "disabled",
            openAnalyzer: false
        }),
        new CopyWebpackPlugin([
            {from: "./src/index.html"}
        ])
    ],
    devServer: {
        contentBase: path.join(__dirname, 'dist'),
        compress: true,
        port: 9002
    },
    stats: {
        logging: "warn"
    }

};
