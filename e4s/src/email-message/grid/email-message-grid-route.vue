<template>
    <div>
        <div class="e4s-section-padding-separator"></div>
        <EmailMessageGridCont
            :email-messages-params="emailMessagesParams"
        />
    </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import EmailMessageGridCont from "./email-message-grid-cont.vue";
import {EmailMessageService} from "../email-message-service";
import {IEmailMessagesParams} from "../email-message-models";
import {ICompetitionSummaryPublic} from "../../competition/competition-models";
import {useHttpResponseController} from "../../common/handle-http-reponse";
import {CompetitionService} from "../../competition/competiton-service";

const emailMessageService: EmailMessageService = new EmailMessageService();

@Component({
    name: "email-message-grid-route",
    components: {
        EmailMessageGridCont

    }
})
export default class EmailMessageGridRoute extends Vue {

    public competitionService = new CompetitionService();
    public emailMessagesParams: IEmailMessagesParams = emailMessageService.factoryEmailMessagesParams();
    public httpResponseControllerComp = useHttpResponseController<ICompetitionSummaryPublic>(this.competitionService.factorySummaryPublic())

    public created() {
        this.emailMessagesParams.compId = isNaN(Number(this.$route.params.compId)) ?
            null :
            parseInt(this.$route.params.compId, 0);
    }
}
</script>
