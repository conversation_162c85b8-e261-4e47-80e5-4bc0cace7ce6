<template>
  <div>
    <div v-show="showSection === sections.GRID">
      <div class="row">
        <div class="col s12 m12 l12">
          <a href="#" v-on:click.prevent="getMessageData">
            <i class="material-icons" style="vertical-align: middle">refresh</i>
          </a>

          <button
            v-if="canCreateMessage"
            class="e4s-button e4s-button--pad e4s-button--green"
            v-on:click="createEmailMessage"
          >
            Create
          </button>

          <CompetitionGoToSummary
            class="right"
            v-if="emailMessagesParams.compId"
            :comp-id="emailMessagesParams.compId"
          />
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>

      <div
        v-if="
          httpResponseControllerComp.data &&
          httpResponseControllerComp.data.compId > 0
        "
      >
        <div class="row">
          <div class="col s12 m12 l12">
            Message for competition
            <span v-text="httpResponseControllerComp.data.compId"></span>:&nbsp;
            <span v-text="httpResponseControllerComp.data.compName"></span>
          </div>
        </div>

        <div class="e4s-section-padding-separator"></div>
      </div>


      <div class="row">
        <div class="col s12 m12 l12">
          {{e4sEmailMessages.length}}: messages.
        </div>
      </div>
      <EmailMessageGrid
        :e4s-email-messages="e4sEmailMessages"
        v-on:openMessage="openMessage"
        v-on:markMessageUnread="markMessageUnread"
        v-on:deleteMessage="deleteMessage"
      ></EmailMessageGrid>
    </div>

    <div v-if="showSection === sections.MESSAGE">
      <EmailMessageForm
        :e4s-email-message="e4sEmailMessageInternal"
        v-on:closeMessage="closeMessage"
        v-on:markMessage="markMessage"
        v-on:deleteMessage="deleteMessage"
      ></EmailMessageForm>
    </div>

    <div v-if="showSection === sections.CREATE">
      <EmailMessageCreateForm
        :e4s-email-message-create="e4sEmailMessageCreate"
        v-on:closeMessage="closeMessageCreate"
        v-on:sentMessage="sentMessage"
      />
    </div>

    <LoadingSpinnerModal :show-it="getIsAnythingLoading" />
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import type {
  IE4sEmailMessageApp,
  IE4sEmailMessageComp,
  IE4sEmailMessageCreate,
  IEmailMessagesParams,
} from "../email-message-models";
import { EmailMessageService } from "../email-message-service";
import { EmailMessageData } from "../email-message-data";
import { useHttpResponseController } from "../../common/handle-http-reponse";
import EmailMessageGrid from "./email-message-grid.vue";
import EmailMessageForm from "../email-message-form/email-message-form.vue";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
} from "../../config/config-store";
import type { IConfigApp } from "../../config/config-app-models";
import { CommonService } from "../../common/common-service";
import { format } from "date-fns";
import LoadingSpinnerModal from "../../common/ui/modal/loading-spinner-modal.vue";
import CompetitionGoToSummary from "../../competition/ui/competition-go-to-summary.vue";
import { ConfigService } from "../../config/config-service";
import type { ICompetitionSummaryPublic } from "../../competition/competition-models";
import { CompetitionService } from "../../competition/competiton-service";
import { CompetitionData } from "../../competition/competition-data";

const emailMessageService: EmailMessageService = new EmailMessageService();
const competitionService: CompetitionService = new CompetitionService();

@Component({
  name: "email-message-grid-cont",
  components: {
    EmailMessageCreateForm: () => {
      return import("../email-message-create/email-message-create-form.vue");
    },
    CompetitionGoToSummary,
    LoadingSpinnerModal,
    EmailMessageForm,
    EmailMessageGrid,
  },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
    }),
  },
})
export default class EmailMessageGridCont extends Vue {
  public readonly isAdmin!: boolean;
  public readonly configApp!: IConfigApp;

  @Prop({
    default: () => {
      return competitionService.factorySummaryPublic();
    },
  })
  public readonly competitionSummaryPublic: ICompetitionSummaryPublic;

  @Prop({
    default: () => {
      return emailMessageService.factoryEmailMessagesParams();
    },
  })
  public readonly emailMessagesParams: IEmailMessagesParams;

  @Watch("emailMessagesParams")
  public onMessageChanged(
    newValue: IEmailMessagesParams
  ) {
    this.init(newValue);
  }

  public emailMessagesParamsInternal: IEmailMessagesParams =
    emailMessageService.factoryEmailMessagesParams();
  public emailMessageData: EmailMessageData = new EmailMessageData();

  public competitionService = new CompetitionService();

  public httpResponseControllerComp =
    useHttpResponseController<ICompetitionSummaryPublic>(
      this.competitionService.factorySummaryPublic()
    );
  public httpResponseControllerMessages = useHttpResponseController<
    IE4sEmailMessageApp[]
  >([] as IE4sEmailMessageApp[]);
  public httpResponseControllerMessage =
    useHttpResponseController<IE4sEmailMessageApp>(
      emailMessageService.factoryE4sEmailMessageApp()
    );
  public e4sEmailMessageInternal: IE4sEmailMessageApp =
    emailMessageService.factoryE4sEmailMessageApp();
  public commonService: CommonService = new CommonService();
  public configService = new ConfigService();

  public e4sEmailMessages: IE4sEmailMessageApp[] = [];
  public e4sEmailMessageCreate: IE4sEmailMessageCreate =
    emailMessageService.factoryE4sEmailMessageCreate();

  public isLoading = false;

  public sections = {
    GRID: "GRID",
    MESSAGE: "MESSAGE",
    CREATE: "CREATE",
  };
  public showSection: string = this.sections.GRID;

  public created() {
    this.init(this.emailMessagesParams);
  }

  public init(emailMessagesParams: IEmailMessagesParams) {
    this.emailMessagesParamsInternal = R.clone(emailMessagesParams);

    if (
      this.emailMessagesParamsInternal.compId &&
      this.emailMessagesParamsInternal.compId > 0
    ) {
      this.httpResponseControllerComp.getData(
        new CompetitionData().getCompById(
          this.emailMessagesParamsInternal.compId
        )
      );
    }

    this.getMessageData();
  }

  public getMessageData() {
    this.httpResponseControllerMessages
      .getData(
        this.isAdmin
          ? this.emailMessageData.getEmailMessagesAdmin(
              this.emailMessagesParamsInternal
            )
          : this.emailMessageData.getEmailMessages(
              this.emailMessagesParamsInternal
            )
      )
      .then(() => {
        this.e4sEmailMessages = this.httpResponseControllerMessages.data
          ? this.httpResponseControllerMessages.data
          : [];
      });
  }

  public openMessage(e4sEmailMessage: IE4sEmailMessageApp) {
    this.httpResponseControllerMessage
      .getData(this.emailMessageData.getEmailMessage(e4sEmailMessage.id))
      .then(() => {
        if (this.httpResponseControllerMessage.data) {
          this.e4sEmailMessageInternal =
            this.httpResponseControllerMessage.data;
          this.showSection = this.sections.MESSAGE;
        }
      });
  }

  public closeMessage(e4sEmailMessage: IE4sEmailMessageApp) {
    //  TODO, should we reload or just mark it client side? or get the indiv message
    this.showSection = this.sections.GRID;

    this.e4sEmailMessages = this.e4sEmailMessages.map((message) => {
      if (message.id === e4sEmailMessage.id) {
        // message.dateReadISO = format(new Date(), "YYYY-MM-DD[T]HH:mm:ssZ");
        const forUser = emailMessageService.factoryE4sEmailMessageCompUser();
        forUser.status.dateReadISO = format(
          new Date(),
          "YYYY-MM-DD[T]HH:mm:ssZ"
        );
        message.forUsers.push(forUser);
      }
      return message;
    });

    this.$store.commit(
      CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_MUTATIONS_SET_USER_MESSAGES,
      this.e4sEmailMessages
    );
  }

  public markMessageUnread(e4sEmailMessage: IE4sEmailMessageApp) {
    this.isLoading = true;
    const contr = useHttpResponseController<IE4sEmailMessageApp>(
      emailMessageService.factoryE4sEmailMessageApp()
    );
    contr
      .getData(this.emailMessageData.markEmailMessageUnread(e4sEmailMessage))
      .finally(() => {
        this.isLoading = false;
      });
  }

  public deleteMessage(
    e4sEmailMessage: IE4sEmailMessageApp | IE4sEmailMessageComp
  ) {
    this.showSection = this.sections.GRID;
    this.isLoading = true;
    const contr = useHttpResponseController<IE4sEmailMessageApp>(
      emailMessageService.factoryE4sEmailMessageApp()
    );
    contr
      .getData(this.emailMessageData.softDeleteEmailMessage(e4sEmailMessage))
      .then(this.getMessageData)
      .finally(() => {
        this.isLoading = false;
      });
  }

  public markMessage() {
    this.showSection = this.sections.GRID;
  }

  public get getIsAnythingLoading() {
    return (
      this.httpResponseControllerComp.isLoading ||
      this.httpResponseControllerMessages.isLoading ||
      this.httpResponseControllerMessage.isLoading ||
      this.isLoading
    );
  }

  public get canCreateMessage(): boolean {
    if (this.isAdmin) {
      return true;
    }
    const comp = this.httpResponseControllerComp.data;
    if (comp && comp.compId > 0) {
      return this.configService.hasBuilderPermissionForComp(
        this.configApp.userInfo,
        comp.compOrgId,
        comp.compId
      );
    }
    return false;
  }

  public createEmailMessage() {
    const comp = this.httpResponseControllerComp.data;
    const e4sEmailMessageCreate =
      comp && comp.compId > 0
        ? emailMessageService.factoryE4sEmailMessageCreateForCompAllAthletes(
            comp.compId
          )
        : emailMessageService.factoryE4sEmailMessageCreate();

    this.e4sEmailMessageCreate = e4sEmailMessageCreate;
    this.showSection = this.sections.CREATE;
  }

  public closeMessageCreate() {
    this.showSection = this.sections.GRID;
  }

  public sentMessage() {
    this.showSection = this.sections.GRID;
    this.getMessageData();
  }
}
</script>
