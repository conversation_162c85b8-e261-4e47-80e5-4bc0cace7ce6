<template>
  <EmailMessageGridContV2
    :email-messages-params="emailMessagesParams"
    class="e4s-content-wrapper e4s-flex-column e4s-width-controller"
  />
</template>

<script lang="ts">
import { defineComponent, SetupContext, ref } from "vue";
import { useRoute } from "../../../router/migrateRouterVue3";
import EmailMessageGridContV2 from "./email-message-grid-cont-v2.vue";
import { EmailMessageService } from "../../email-message-service";

const emailMessageService: EmailMessageService = new EmailMessageService();

export default defineComponent({
  name: "email-message-grid-route-v2",
  components: { EmailMessageGridContV2 },
  props: {},
  setup(props: any, context: SetupContext) {
    const route = useRoute();
    const isLoading = ref(false);
    const emailMessagesParams = ref(
      emailMessageService.factoryEmailMessagesParams()
    );

    const compId = isNaN(Number(route.params.compId))
      ? 0
      : parseInt(route.params.compId, 0);
    if (compId > 0) {
      emailMessagesParams.value.compId = compId;
    }

    return {
      emailMessagesParams,
      isLoading,
    };
  },
});
</script>
