<template functional>
  <div class="e4s-flex-column">
    <div
      class="e4s-flex-column email-message--row"
      :class="
        $options.methods.isUnread(props.e4sEmailMessage)
          ? 'email-message--unread'
          : ''
      "
      v-on:click.prevent="listeners.openMessage(props.e4sEmailMessage)"
      v-if="props.screenMqSize !== props.vueMqSizes.MOBILE.name"
    >
      <div class="e4s-flex-row e4s-justify-flex-space-between">
        <div class="e4s-flex-row" style="width: 80%">
          <div class="e4s-flex-row" style="width: 25%">
            <i
              class="material-icons e4s-icon"
              v-text="props.e4sEmailMessage.type === 'E' ? 'email' : 'message'"
            ></i>
            <span
              class="e4s-truncate-text-v2"
              v-text="$options.methods.getFrom(props.e4sEmailMessage)"
            ></span>
          </div>

          <div style="width: 75%">
            <div
              v-text="$options.methods.getSubject(props.e4sEmailMessage)"
              class="e4s-truncate-text-v2"
            ></div>
          </div>
        </div>

        <div
          v-text="$options.methods.getCreatedTime(props.e4sEmailMessage)"
        ></div>
      </div>

    </div>

    <div
      class="e4s-flex-column email-message--row"
      :class="
        $options.methods.isUnread(props.e4sEmailMessage)
          ? 'email-message--unread'
          : ''
      "
      v-on:click.prevent="listeners.openMessage(props.e4sEmailMessage)"
      v-if="props.screenMqSize === props.vueMqSizes.MOBILE.name"
    >
      <div class="e4s-flex-column">
        <div class="e4s-flex-row e4s-justify-flex-space-between">
          <span class="e4s-truncate-text">
            <i
              class="material-icons e4s-icon"
              v-text="props.e4sEmailMessage.type === 'E' ? 'email' : 'message'"
            ></i>
            <span
              v-text="$options.methods.getFrom(props.e4sEmailMessage)"
            ></span>
          </span>

          <span
            v-text="$options.methods.getCreatedTime(props.e4sEmailMessage)"
          ></span>
        </div>

        <div
          v-text="$options.methods.getSubject(props.e4sEmailMessage)"
          class="e4s-truncate-text"
        ></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { format, isToday, parse } from "date-fns";
import { EmailMessageService } from "../../email-message-service";
import {
  IE4sEmailMessageApp,
  IE4sEmailMessageComp,
} from "../../email-message-models";

// const commonService: CommonService = new CommonService();
const emailMessageService = new EmailMessageService();

export default {
  props: ["e4sEmailMessage", "canDeleteMessage", "vueMqSizes", "screenMqSize"],
  components: {
    // "athlete-po10-link": AthletePo10Link
  },
  methods: {
    getCreatedTime: (
      e4sEmailMessage: IE4sEmailMessageApp | IE4sEmailMessageComp
    ) => {
      const date =
        e4sEmailMessage.dateSentISO.length > 0
          ? e4sEmailMessage.dateSentISO
          : e4sEmailMessage.dateCreatedISO;
      const dateFns = parse(date);
      return isToday(dateFns)
        ? format(dateFns, "HH:mm")
        : format(dateFns, "D MMM");
    },
    getSubject: (
      e4sEmailMessage: IE4sEmailMessageApp | IE4sEmailMessageComp
    ) => {
      const subjectBody =
        e4sEmailMessage.subject + " - " + e4sEmailMessage.body;
      const maxLength = 5000;
      return subjectBody.length > maxLength
        ? subjectBody.slice(0, maxLength) + "..."
        : subjectBody;
    },
    getFrom: (e4sEmailMessage: IE4sEmailMessageApp | IE4sEmailMessageComp) => {
      return (
        e4sEmailMessage.type +
        ": " +
        emailMessageService.getFrom(e4sEmailMessage)
      );
    },
    isUnread: (e4sEmailMessage: IE4sEmailMessageApp | IE4sEmailMessageComp) => {
      return emailMessageService.isUnread(e4sEmailMessage);
    },
  },
};
</script>

<style>
.email-message--row {
  background: rgba(242, 245, 245, 0.8);
  color: #202124;
  border-top: 1px solid rgb(170, 170, 170);
  padding: 4px 0 4px 0;
  cursor: pointer;
}
.email-message--unread {
  font-weight: 700;
  background: rgba(255, 255, 255, 0.5);
}
</style>
