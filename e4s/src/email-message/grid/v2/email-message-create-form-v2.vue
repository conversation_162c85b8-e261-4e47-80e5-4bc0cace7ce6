<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <div class="e4s-flex-row e4s-justify-flex-space-between">
      <div class="e4s-flex-row e4s-gap--standard">
        <span>To:</span>
        <div v-text="getTo"></div>
      </div>
      <ButtonGenericBackV2 v-on:click="closeMessage" />
    </div>

    <!--    <div class="e4s-flex-row">-->
    <!--      <FormGenericInputTemplateV2 form-label="Type">-->
    <!--        <select-->
    <!--          slot="field"-->
    <!--          class="browser-default e4s-input-field e4s-input-field&#45;&#45;primary"-->
    <!--          v-model="e4sEmailMessageCreateInternal.type"-->
    <!--          v-if="!isSendingToAllUsers"-->
    <!--        >-->
    <!--          <option value="E">Email</option>-->
    <!--          <option value="M">Message</option>-->
    <!--        </select>-->
    <!--      </FormGenericInputTemplateV2>-->
    <!--    </div>-->

    <FormGenericInputTextV2
      form-label="Subject"
      v-model="e4sEmailMessageCreateInternal.subject"
      :error-message="
        errorMessages.subject
      "
    />

    <HtmlEditor
      :content="e4sEmailMessageCreateInternal.body"
      v-on:onContentChange="onHtmlEdited"
      v-on:onCancel="showHtmlEditor = false"
    >
      <div slot="buttons"></div>
    </HtmlEditor>

    <div class="e4s-section-padding-separator"></div>

    <div class="e4s-flex-row e4s-full-width e4s-justify-flex-end">
      <ButtongroupOkConfirmV2
        ok-text="Send"
        :is-loading="httpResponseControllerMessage.isLoading"
        :show-cancel-only-when-confirming="true"
        :cancel-first="false"
        v-on:ok="sendMessage"
      />
    </div>

    <LoadingSpinnerV2 v-if="isLoading" />
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType, reactive,
  ref,
  SetupContext,
  watch,
} from "vue"
import { IE4sEmailMessageCreate } from "../../email-message-models";
import { useHttpResponseController } from "../../../common/handle-http-reponse";
import {
  getE4sStandardHumanDateTimeOutPut,
  simpleClone,
} from "../../../common/common-service-utils";
import { IsoDateTime } from "../../../common/common-models";
import { EmailMessageService } from "../../email-message-service";
import { EmailMessageData } from "../../email-message-data";
import ButtonGenericBackV2 from "../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import HtmlEditor from "../../../common/ui/html-editor/html-editor.vue";
import ButtongroupOkConfirmV2 from "../../../common/ui/layoutV2/buttons/buttongroup-ok-confirm-v2.vue";
import FormGenericInputTemplateV2 from "../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import FormGenericInputTextV2 from "../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";

const emailMessageService = new EmailMessageService();

export default defineComponent({
  name: "email-message-create-form-v2",
  components: {
    LoadingSpinnerV2,
    FormGenericInputTextV2,
    FormGenericInputTemplateV2,
    ButtongroupOkConfirmV2,
    HtmlEditor,
    ButtonGenericBackV2,
  },
  props: {
    e4sEmailMessageCreate: {
      type: Object as PropType<IE4sEmailMessageCreate>,
      default: () => {
        return emailMessageService.factoryE4sEmailMessageApp();
      },
    },
  },
  setup(
    props: { e4sEmailMessageCreate: IE4sEmailMessageCreate },
    context: SetupContext
  ) {
    const e4sEmailMessageCreateInternal = ref(
      emailMessageService.factoryE4sEmailMessageCreate()
    );

    const httpResponseControllerMessage =
      useHttpResponseController<IE4sEmailMessageCreate>(
        emailMessageService.factoryE4sEmailMessageCreate()
      );

    const errorMessages = reactive( {
      subject: "",
      body: ""
    })

    watch(
      () => props.e4sEmailMessageCreate,
      (newValue: IE4sEmailMessageCreate) => {
        e4sEmailMessageCreateInternal.value = simpleClone(newValue);
      },
      {
        immediate: true,
      }
    );

    const getDateTimeOutput = computed((dateTime: IsoDateTime) => {
      return dateTime.length === 0
        ? ""
        : getE4sStandardHumanDateTimeOutPut(dateTime);
    });

    function onHtmlEdited(content: string) {
      e4sEmailMessageCreateInternal.value.body = content;
    }

    const getTo = computed(() => {
      return emailMessageService
        .getHumanReadableSendTos(e4sEmailMessageCreateInternal.value.to)
        .join(", ");
    });

    const isSendingToAllUsers = computed(() => {
      return emailMessageService.isSendingToAllUsers(
        e4sEmailMessageCreateInternal.value.to
      );
    });

    function closeMessage(e4sEmailMessage: IE4sEmailMessageCreate) {
      context.emit(
        "closeMessage",
        simpleClone(e4sEmailMessageCreateInternal.value)
      );
    }

    function sendMessage() {
      errorMessages.subject = "";
      errorMessages.body = "";
      if (e4sEmailMessageCreateInternal.value.subject.length === 0) {
        errorMessages.subject = "Required"
        return;
      }
      isLoading.value = true;
      httpResponseControllerMessage
        .getData(
          new EmailMessageData().createMessage(
            e4sEmailMessageCreateInternal.value
          )
        )
        .then(() => {
          isLoading.value = false;
          context.emit(
            "sentMessage",
            simpleClone(e4sEmailMessageCreateInternal.value)
          );
        });
    }

    const isLoading = ref(false);

    return {
      e4sEmailMessageCreateInternal,
      httpResponseControllerMessage,
      getDateTimeOutput,
      getTo,
      isSendingToAllUsers,
      onHtmlEdited,
      closeMessage,
      sendMessage,
      errorMessages,
      isLoading
    };
  },
});
</script>
