<template>
  <div>
    <div
      v-show="showSection === sections.GRID"
      class="e4s-flex-column e4s-gap--standard"
    >
      <div class="e4s-flex-row e4s-justify-flex-space-between e4s-flex-end">
        <div class="e4s-flex-row e4s-gap--standard">
          <span v-text="'[' + e4sEmailMessages.length + ']'"></span>
          message<template v-if="e4sEmailMessages.length > 1">(s)</template>
          <template
            class="e4s-flex-row e4s-gap--standard"
            v-if="
              httpResponseControllerComp.data &&
              httpResponseControllerComp.data.compId > 0
            "
          >
            for competition
            <span v-text="httpResponseControllerComp.data.compId + ':'"></span>
            <span v-text="httpResponseControllerComp.data.compName"></span>
          </template>
        </div>

        <div class="e4s-flex-row e4s-gap--standard">
          <ButtonGenericV2
            text="Reload"
            button-type="tertiary"
            v-on:click="getMessageData"
          />

          <ButtonGenericV2
            v-if="canCreateMessage"
            text="Create"
            v-on:click="createEmailMessage"
          />
        </div>
      </div>

      <EmailMessageGridV2
        :e4s-email-messages="e4sEmailMessages"
        v-on:openMessage="openMessage"
        v-on:markMessageUnread="markMessageUnread"
        v-on:deleteMessage="deleteMessage"
      />
    </div>

    <div v-if="showSection === sections.MESSAGE">
      <EmailMessageFormV2
        :e4s-email-message="e4sEmailMessageInternal"
        v-on:closeMessage="closeMessage"
        v-on:markMessage="markMessage"
        v-on:deleteMessage="deleteMessage"
      ></EmailMessageFormV2>
    </div>

    <div v-if="showSection === sections.CREATE">
      <EmailMessageCreateFormV2
        :e4s-email-message-create="e4sEmailMessageCreate"
        v-on:closeMessage="closeMessageCreate"
        v-on:sentMessage="sentMessage"
      />
    </div>

    <LoadingSpinnerV2 v-if="getIsAnythingLoading" />
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { EmailMessageService } from "../../email-message-service";
import { CompetitionService } from "../../../competition/competiton-service";
import EmailMessageGridV2 from "./email-message-grid-v2.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import { mapGetters, mapState } from "vuex";
import { IConfigApp } from "../../../config/config-app-models";
import { ICompetitionSummaryPublic } from "../../../competition/competition-models";
import {
  IE4sEmailMessageApp,
  IE4sEmailMessageComp,
  IE4sEmailMessageCreate,
  IEmailMessagesParams,
} from "../../email-message-models";
import { EmailMessageData } from "../../email-message-data";
import { useHttpResponseController } from "../../../common/handle-http-reponse";
import { CommonService } from "../../../common/common-service";
import { ConfigService } from "../../../config/config-service";
import { CompetitionData } from "../../../competition/competition-data";
import { format } from "date-fns";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import EmailMessageFormV2 from "./email-message-form-v2.vue";
import EmailMessageCreateFormV2 from "./email-message-create-form-v2.vue";

const emailMessageService: EmailMessageService = new EmailMessageService();
const competitionService: CompetitionService = new CompetitionService();

@Component({
  name: "email-message-grid-cont-v2",
  components: {
    EmailMessageCreateFormV2,
    EmailMessageFormV2,
    ButtonGenericV2,
    LoadingSpinnerV2,
    EmailMessageGridV2,
  },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
    }),
  },
})
export default class EmailMessageGridCont extends Vue {
  public readonly isAdmin!: boolean;
  public readonly configApp!: IConfigApp;

  @Prop({
    default: () => {
      return competitionService.factorySummaryPublic();
    },
  })
  public readonly competitionSummaryPublic: ICompetitionSummaryPublic;

  @Prop({
    default: () => {
      return emailMessageService.factoryEmailMessagesParams();
    },
  })
  public readonly emailMessagesParams: IEmailMessagesParams;

  @Watch("emailMessagesParams")
  public onMessageChanged(
    newValue: IEmailMessagesParams,
    oldValue: IEmailMessagesParams
  ) {
    this.init(newValue);
  }

  public emailMessagesParamsInternal: IEmailMessagesParams =
    emailMessageService.factoryEmailMessagesParams();
  public emailMessageData: EmailMessageData = new EmailMessageData();

  public competitionService = new CompetitionService();

  public httpResponseControllerComp =
    useHttpResponseController<ICompetitionSummaryPublic>(
      this.competitionService.factorySummaryPublic()
    );
  public httpResponseControllerMessages = useHttpResponseController<
    IE4sEmailMessageApp[]
  >([] as IE4sEmailMessageApp[]);
  public httpResponseControllerMessage =
    useHttpResponseController<IE4sEmailMessageApp>(
      emailMessageService.factoryE4sEmailMessageApp()
    );
  public e4sEmailMessageInternal: IE4sEmailMessageApp =
    emailMessageService.factoryE4sEmailMessageApp();
  public commonService: CommonService = new CommonService();
  public configService = new ConfigService();

  public e4sEmailMessages: IE4sEmailMessageApp[] = [];
  public e4sEmailMessageCreate: IE4sEmailMessageCreate =
    emailMessageService.factoryE4sEmailMessageCreate();

  public isLoading = false;

  public sections = {
    GRID: "GRID",
    MESSAGE: "MESSAGE",
    CREATE: "CREATE",
  };
  public showSection: string = this.sections.GRID;

  public created() {
    this.init(this.emailMessagesParams);
  }

  public init(emailMessagesParams: IEmailMessagesParams) {
    this.emailMessagesParamsInternal = R.clone(emailMessagesParams);

    if (
      this.emailMessagesParamsInternal.compId &&
      this.emailMessagesParamsInternal.compId > 0
    ) {
      this.httpResponseControllerComp.getData(
        new CompetitionData().getCompById(
          this.emailMessagesParamsInternal.compId
        )
      );
    }

    this.getMessageData();
  }

  public getMessageData() {
    this.httpResponseControllerMessages
      .getData(
        this.isAdmin
          ? this.emailMessageData.getEmailMessagesAdmin(
              this.emailMessagesParamsInternal
            )
          : this.emailMessageData.getEmailMessages(
              this.emailMessagesParamsInternal
            )
      )
      .then(() => {
        this.e4sEmailMessages = this.httpResponseControllerMessages.data
          ? this.httpResponseControllerMessages.data
          : [];
      });
  }

  public openMessage(e4sEmailMessage: IE4sEmailMessageApp) {
    this.httpResponseControllerMessage
      .getData(this.emailMessageData.getEmailMessage(e4sEmailMessage.id))
      .then(() => {
        if (this.httpResponseControllerMessage.data) {
          this.e4sEmailMessageInternal =
            this.httpResponseControllerMessage.data;
          this.showSection = this.sections.MESSAGE;
        }
      });
  }

  public closeMessage(e4sEmailMessage: IE4sEmailMessageApp) {
    //  TODO, should we reload or just mark it client side? or get the indiv message
    this.showSection = this.sections.GRID;

    this.e4sEmailMessages = this.e4sEmailMessages.map((message) => {
      if (message.id === e4sEmailMessage.id) {
        // message.dateReadISO = format(new Date(), "YYYY-MM-DD[T]HH:mm:ssZ");
        const forUser = emailMessageService.factoryE4sEmailMessageCompUser();
        forUser.status.dateReadISO = format(
          new Date(),
          "YYYY-MM-DD[T]HH:mm:ssZ"
        );
        message.forUsers.push(forUser);
      }
      return message;
    });

    this.$store.commit(
      CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_MUTATIONS_SET_USER_MESSAGES,
      this.e4sEmailMessages
    );
  }

  public markMessageUnread(e4sEmailMessage: IE4sEmailMessageApp) {
    this.isLoading = true;
    const contr = useHttpResponseController<IE4sEmailMessageApp>(
      emailMessageService.factoryE4sEmailMessageApp()
    );
    contr
      .getData(this.emailMessageData.markEmailMessageUnread(e4sEmailMessage))
      .finally(() => {
        this.isLoading = false;
      });
  }

  public deleteMessage(
    e4sEmailMessage: IE4sEmailMessageApp | IE4sEmailMessageComp
  ) {
    this.showSection = this.sections.GRID;
    this.isLoading = true;
    const contr = useHttpResponseController<IE4sEmailMessageApp>(
      emailMessageService.factoryE4sEmailMessageApp()
    );
    contr
      .getData(this.emailMessageData.softDeleteEmailMessage(e4sEmailMessage))
      .then(this.getMessageData)
      .finally(() => {
        this.isLoading = false;
      });
  }

  public markMessage() {
    this.showSection = this.sections.GRID;
  }

  public get getIsAnythingLoading() {
    return (
      this.httpResponseControllerComp.isLoading ||
      this.httpResponseControllerMessages.isLoading ||
      this.httpResponseControllerMessage.isLoading ||
      this.isLoading
    );
  }

  public get canCreateMessage(): boolean {
    if (this.isAdmin) {
      return true;
    }
    const comp = this.httpResponseControllerComp.data;
    if (comp && comp.compId > 0) {
      return this.configService.hasBuilderPermissionForComp(
        this.configApp.userInfo,
        comp.compOrgId,
        comp.compId
      );
    }
    return false;
  }

  public createEmailMessage() {
    const comp = this.httpResponseControllerComp.data;
    const e4sEmailMessageCreate =
      comp && comp.compId > 0
        ? emailMessageService.factoryE4sEmailMessageCreateForCompAllAthletes(
            comp.compId
          )
        : emailMessageService.factoryE4sEmailMessageCreate();

    this.e4sEmailMessageCreate = e4sEmailMessageCreate;
    this.showSection = this.sections.CREATE;
  }

  public closeMessageCreate() {
    this.showSection = this.sections.GRID;
  }

  public sentMessage() {
    this.showSection = this.sections.GRID;
    this.getMessageData();
  }
}
</script>
