<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <div class="e4s-flex-row e4s-justify-flex-space-between">
      <div class="e4s-flex-column">
        <span v-text="getFrom"></span>
        <span
          v-text="getDateTimeOutput(e4sEmailMessageInternal.dateCreatedISO)"
        ></span>
      </div>

      <div class="e4s-flex-row e4s-gap--standard">
        <ButtonGenericBackV2 v-on:click="closeMessage" />
      </div>
    </div>

    <hr class="dat-e4s-hr-small" />

    <div
      v-text="e4sEmailMessageInternal.subject"
      class="email-message-form--subject"
    ></div>

    <!--    <hr class="dat-e4s-hr-small">-->

    <div v-html="e4sEmailMessageInternal.body" id="message-body"></div>

    <div class="e4s-flex-row e4s-full-width e4s-justify-flex-end">
      <ButtongroupOkConfirmV2
        ok-text="Delete"
        :show-cancel-only-when-confirming="true"
        :cancel-first="false"
        v-on:ok="deleteMessage"
      />
    </div>

  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { EmailMessageService } from "../../email-message-service";
import ButtonGenericBackV2 from "../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import { IE4sEmailMessageApp } from "../../email-message-models";
import { CommonService } from "../../../common/common-service";
import { IsoDateTime } from "../../../common/common-models";
import ButtongroupOkConfirmV2 from "../../../common/ui/layoutV2/buttons/buttongroup-ok-confirm-v2.vue";

const emailMessageService = new EmailMessageService();

@Component({
  name: "email-message-form-v2",
  components: {
    ButtongroupOkConfirmV2,
    ButtonGenericBackV2,
  },
})
export default class EmailMessageForm extends Vue {
  @Prop({
    default: () => {
      return emailMessageService.factoryE4sEmailMessageApp();
    },
  })
  public readonly e4sEmailMessage: IE4sEmailMessageApp;

  @Watch("e4sEmailMessage", {
    immediate: true,
  })
  public onMessageChanged(
    newValue: IE4sEmailMessageApp,
    oldValue: IE4sEmailMessageApp
  ) {
    this.e4sEmailMessageInternal = R.clone(newValue);
  }

  public e4sEmailMessageInternal: IE4sEmailMessageApp =
    emailMessageService.factoryE4sEmailMessageApp();
  public commonService: CommonService = new CommonService();

  public getDateTimeOutput(dateTime: IsoDateTime): string {
    return dateTime.length === 0
      ? ""
      : this.commonService.getE4sStandardHumanDateTimeOutPut(dateTime);
  }

  public closeMessage(e4sEmailMessage: IE4sEmailMessageApp) {
    this.$emit("closeMessage", R.clone(this.e4sEmailMessageInternal));
  }

  public get getFrom() {
    return emailMessageService.getFrom(this.e4sEmailMessageInternal);
  }

  public deleteMessage() {
    this.$emit("deleteMessage", R.clone(this.e4sEmailMessageInternal));
  }

  public markMessage() {
    this.$emit("markMessage", R.clone(this.e4sEmailMessageInternal));
  }
}
</script>

<style scoped>
.email-message-form--from {
  font-weight: 700;
}
.email-message-form--subject {
  font-size: 1.5em;
}
</style>
