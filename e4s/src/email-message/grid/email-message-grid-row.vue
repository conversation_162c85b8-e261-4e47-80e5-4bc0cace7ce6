<template functional>
    <div>
        <div
            class="row email-message--row"
            :class="$options.methods.isUnread(props.e4sEmailMessage) ? 'email-message--unread' : ''"
            v-on:click.prevent="listeners.openMessage(props.e4sEmailMessage)"
            v-if="props.screenMqSize !== props.vueMqSizes.MOBILE.name"
        >

            <div class="col m2 l2">
            <span class="e4s-truncate-text">
                <i class='material-icons e4s-icon' v-text="props.e4sEmailMessage.type === 'E' ? 'email' : 'message'"></i>
                <span v-text="$options.methods.getFrom(props.e4sEmailMessage)"></span>
            </span>
            </div>

            <div class="col m8 l8">
                <span v-text="$options.methods.getSubject(props.e4sEmailMessage)" class="e4s-truncate-text"></span>
            </div>

            <div class="col m2 l2">
                <span v-text="$options.methods.getCreatedTime(props.e4sEmailMessage)" class="right"></span>
            </div>

            <!--        <div class="col s2 m2 l2">-->
            <!--            <div class="right">-->
            <!--                <a href="#"-->
            <!--                   v-if="!$options.methods.isUnread(props.e4sEmailMessage)"-->
            <!--                   v-on:click.prevent="listeners.markMessageUnread(props.e4sEmailMessage)">-->
            <!--                    <i class='material-icons'>markunread</i>-->
            <!--                </a>-->
            <!--                <a href="#"-->
            <!--                   v-on:click.prevent="listeners.openMessage(props.e4sEmailMessage)">-->
            <!--                    <i class='material-icons'>open_in_browser</i>-->
            <!--                </a>-->
            <!--                <a href="#" v-if="props.canDeleteMessage"-->
            <!--                   v-on:click.prevent="listeners.deleteMessage(props.e4sEmailMessage)">-->
            <!--                    <i class='material-icons red-text'>delete_forever</i>-->
            <!--                </a>-->
            <!--            </div>-->
            <!--        </div>-->
        </div>

        <div
            class="row email-message--row"
            :class="$options.methods.isUnread(props.e4sEmailMessage) ? 'email-message--unread' : ''"
            v-on:click.prevent="listeners.openMessage(props.e4sEmailMessage)"
            v-if="props.screenMqSize === props.vueMqSizes.MOBILE.name"
        >

            <div class="col s6">
                <span class="e4s-truncate-text">
                    <i class='material-icons e4s-icon' v-text="props.e4sEmailMessage.type === 'E' ? 'email' : 'message'"></i>
                    <span v-text="$options.methods.getFrom(props.e4sEmailMessage)"></span>
                </span>
            </div>

            <div class="col s6">
                <span v-text="$options.methods.getCreatedTime(props.e4sEmailMessage)" class="right"></span>
            </div>

            <div class="col s12">
                <span v-text="$options.methods.getSubject(props.e4sEmailMessage)" class="e4s-truncate-text"></span>
            </div>

            <!--        <div class="col s2 m2 l2">-->
            <!--            <div class="right">-->
            <!--                <a href="#"-->
            <!--                   v-if="!$options.methods.isUnread(props.e4sEmailMessage)"-->
            <!--                   v-on:click.prevent="listeners.markMessageUnread(props.e4sEmailMessage)">-->
            <!--                    <i class='material-icons'>markunread</i>-->
            <!--                </a>-->
            <!--                <a href="#"-->
            <!--                   v-on:click.prevent="listeners.openMessage(props.e4sEmailMessage)">-->
            <!--                    <i class='material-icons'>open_in_browser</i>-->
            <!--                </a>-->
            <!--                <a href="#" v-if="props.canDeleteMessage"-->
            <!--                   v-on:click.prevent="listeners.deleteMessage(props.e4sEmailMessage)">-->
            <!--                    <i class='material-icons red-text'>delete_forever</i>-->
            <!--                </a>-->
            <!--            </div>-->
            <!--        </div>-->
        </div>

    </div>
</template>

<script lang="ts">

import {IE4sEmailMessageApp, IE4sEmailMessageComp} from "../email-message-models";
import {EmailMessageService} from "../email-message-service";
import {format, isToday, parse} from "date-fns";

// const commonService: CommonService = new CommonService();
const emailMessageService = new EmailMessageService()

export default {
    props: ["e4sEmailMessage", "canDeleteMessage", "vueMqSizes", "screenMqSize"],
    components: {
        // "athlete-po10-link": AthletePo10Link
    },
    methods: {
        getCreatedTime: (e4sEmailMessage: IE4sEmailMessageApp | IE4sEmailMessageComp) => {

            const date = e4sEmailMessage.dateSentISO.length > 0 ? e4sEmailMessage.dateSentISO : e4sEmailMessage.dateCreatedISO;
            const dateFns = parse(date);
            return isToday(dateFns) ? format(dateFns, "HH:mm") : format(dateFns, "D MMM");
        },
        getSubject: (e4sEmailMessage: IE4sEmailMessageApp | IE4sEmailMessageComp) => {
            const subjectBody = e4sEmailMessage.subject + " - " + e4sEmailMessage.body;
            const maxLength = 5000;
            return subjectBody.length > maxLength ? subjectBody.slice(0, maxLength) + "..." : subjectBody;
        },
        getFrom: (e4sEmailMessage: IE4sEmailMessageApp | IE4sEmailMessageComp) => {
            return e4sEmailMessage.type + ": " + emailMessageService.getFrom(e4sEmailMessage);
        },
        isUnread: (e4sEmailMessage: IE4sEmailMessageApp | IE4sEmailMessageComp) => {
            return emailMessageService.isUnread(e4sEmailMessage);
        }
    }
};
</script>

<style>
.email-message--row {
    background: rgba(242,245,245,0.8);
    color: #202124;
    border-top: 1px solid rgb(170,170,170);
    padding: 4px 0 4px 0;
    cursor: pointer;
}
.email-message--unread {
    font-weight: 700;
    background: rgba(255,255,255,0.5);
}
</style>
