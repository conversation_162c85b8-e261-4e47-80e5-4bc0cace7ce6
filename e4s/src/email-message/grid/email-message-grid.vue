<template>
    <div>
        <EmailMessageGridRow
            v-for="e4sEmailMessage in e4sEmailMessagesInternal"
            :e4s-email-message="e4sEmailMessage"
            :key="e4sEmailMessage.id"
            :can-delete-message="true"
            :vue-mq-sizes="VUE_MQ_SIZES"
            :screen-mq-size="$mq"
            v-on:openMessage="openMessage"
            v-on:markMessageUnread="markMessageUnread"
            v-on:deleteMessage="deleteMessage"
        ></EmailMessageGridRow>
    </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {IE4sEmailMessageApp, IE4sEmailMessageComp} from "../email-message-models";
import EmailMessageGridRow from "./email-message-grid-row.vue";

import {VUE_MQ_SIZES} from "../../index";

@Component({
    name: "email-message-grid",
    components: {
        EmailMessageGridRow
    }
})
export default class EmailMessageGrid extends Vue {
    @Prop({default: ()=>{return []}})
    public readonly e4sEmailMessages: IE4sEmailMessageApp[] | IE4sEmailMessageComp[];

    @Watch("e4sEmailMessages")
    public onMessageChanged(newValue: IE4sEmailMessageApp[], oldValue: IE4sEmailMessageApp[]) {
        this.e4sEmailMessagesInternal = R.clone(newValue);
    }

    public e4sEmailMessagesInternal: IE4sEmailMessageApp[] | IE4sEmailMessageComp[] = [];
    public VUE_MQ_SIZES = VUE_MQ_SIZES;
    public $mq: any;

    public created() {
        this.e4sEmailMessagesInternal = R.clone(this.e4sEmailMessages);
    }

    public openMessage(e4sEmailMessage: IE4sEmailMessageApp | IE4sEmailMessageComp) {
        this.$emit("openMessage", R.clone(e4sEmailMessage));
    }

    public markMessageUnread(e4sEmailMessage: IE4sEmailMessageApp | IE4sEmailMessageComp) {
        this.$emit("markMessageUnread", R.clone(e4sEmailMessage));
    }

    public deleteMessage(e4sEmailMessage: IE4sEmailMessageApp | IE4sEmailMessageComp) {
        this.$emit("deleteMessage", R.clone(e4sEmailMessage));
    }
}
</script>
