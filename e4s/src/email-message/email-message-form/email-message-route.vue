<template>
    <div>
        <div class="e4s-section-padding-separator"></div>
        <LoadingSpinnerModal :show-it="httpResponseControllerMessage.isLoading"></LoadingSpinnerModal>
        <EmailMessageForm
            :e4s-email-message="e4sEmailMessageInternal"
            v-on:closeMessage="closeMessage"
        ></EmailMessageForm>
    </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import {EmailMessageData} from "../email-message-data";
import {useHttpResponseController} from "../../common/handle-http-reponse";
import {IE4sEmailMessageApp} from "../email-message-models";
import {EmailMessageService} from "../email-message-service";
import EmailMessageForm from "./email-message-form.vue";
import LoadingSpinnerModal from "../../common/ui/modal/loading-spinner-modal.vue";
import {RawLocation} from "vue-router";
import {LAUNCH_ROUTES_PATHS} from "../../launch/launch-routes";

const emailMessageService: EmailMessageService = new EmailMessageService();

@Component({
    name: "email-message-route",
    components: {
        LoadingSpinnerModal,
        EmailMessageForm
    }
})
export default class EmailMessageRoute extends Vue {
    public emailMessageData: EmailMessageData = new EmailMessageData();

    public httpResponseControllerMessage = useHttpResponseController<IE4sEmailMessageApp>(emailMessageService.factoryE4sEmailMessageApp())
    public e4sEmailMessageInternal: IE4sEmailMessageApp = emailMessageService.factoryE4sEmailMessageApp();

    public created() {
        const id: number = isNaN(Number(this.$route.params.id)) ? 0 : parseInt(this.$route.params.id, 0);

        this.httpResponseControllerMessage.getData(
            this.emailMessageData.getEmailMessage(id)
        )
            .then(() => {
                if (this.httpResponseControllerMessage.data) {
                    this.e4sEmailMessageInternal = this.httpResponseControllerMessage.data;
                }
            })
    }

    public closeMessage() {
        let location: RawLocation;
        location = {
            path: "/" + LAUNCH_ROUTES_PATHS.EMAIL_MESSAGES
        };
        this.$router.push(location);
    }
}
</script>
