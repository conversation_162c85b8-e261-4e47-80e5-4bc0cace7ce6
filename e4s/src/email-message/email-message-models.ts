import {ContentStringHtml, IBase<PERSON>oncrete, IBaseRaw, IsoDateTime} from "../common/common-models";

export type E4sEmailType = "E" | "M";

export interface IE4sEmailMessageBase {
    to: string[];                   //  ["_COMP123_"] ["_ALL_"]  All only message
    subject: string;
    type: E4sEmailType;
    body: ContentStringHtml;
}

export interface IE4sEmailMessageCreate extends IE4sEmailMessageBase {
    //  https://stackoverflow.com/questions/51445767/how-to-define-a-regex-matched-string-type-in-typescript   "_COMP123_"
    easyFilter: "P" | "T" | "F";    // Helper prop.
                                    // P = pick your own EventGroups
                                    // T = UI will select all track
                                    // F = Ui will pick all field
    eventGroups: IBaseConcrete[];   //  Only available if _COMP123_, E.g. send to all field events or
}

export interface IE4sEmailMessage extends IBaseRaw, IE4sEmailMessageBase {
    sendFrom: string;
    cc: string[];
    bcc: string[];
    priority: 1 | 2;
    error: string;
    dateSentISO: IsoDateTime;
    dateCreatedISO: IsoDateTime;
    user: {
        id: number;
        name: string;
        email: string;
    };
}

export interface IE4sEmailMessageApp extends IE4sEmailMessage {
    // dateReadISO: IsoDateTime;
    // dateDeletedISO: IsoDateTime;
    forUsers: IE4sEmailMessageCompUser[];
}

export type IE4sEmailMessageComp = Omit<IE4sEmailMessageApp, "dateReadISO"> & {
    forUsers: IE4sEmailMessageCompUser[];
}

export interface IE4sEmailMessageCompUser {
    muId: number;
    id: number
    email: string
    name: string
    status: {
        dateDeletedISO: IsoDateTime
        dateReadISO: IsoDateTime
    }
}

export interface IEmailMessagesParams {
    messages: boolean;
    emails: boolean;
    read: boolean | null;           //  null = give me all.
    deleted: boolean;               //  Has been deleted
    compId: number | null;
}
