<template>
    <div>
        <div class="e4s-section-padding-separator"></div>
        <LoadingSpinnerModal :show-it="httpResponseControllerMessage.isLoading"></LoadingSpinnerModal>
        <EmailMessageCreateForm
            :e4s-email-message-create="e4sEmailMessageCreate"
            v-on:closeMessage="closeMessage"
        />
    </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import {EmailMessageData} from "../email-message-data";
import {useHttpResponseController} from "../../common/handle-http-reponse";
import {IE4sEmailMessageApp, IE4sEmailMessageCreate} from "../email-message-models";
import {EmailMessageService} from "../email-message-service";
import EmailMessageForm from "../email-message-form/email-message-form.vue";
import LoadingSpinnerModal from "../../common/ui/modal/loading-spinner-modal.vue";
import {RawLocation} from "vue-router";
import {LAUNCH_ROUTES_PATHS} from "../../launch/launch-routes";
import EmailMessageCreateForm from "./email-message-create-form.vue";

const emailMessageService: EmailMessageService = new EmailMessageService();

@Component({
    name: "email-message-route",
    components: {
        EmailMessageCreateForm,
        LoadingSpinnerModal,
        EmailMessageForm
    }
})
export default class EmailMessageRoute extends Vue {
    public emailMessageData: EmailMessageData = new EmailMessageData();

    public httpResponseControllerMessage = useHttpResponseController<IE4sEmailMessageApp>(emailMessageService.factoryE4sEmailMessageApp())
    public e4sEmailMessageCreate: IE4sEmailMessageCreate = emailMessageService.factoryE4sEmailMessageCreate();

    public compId: number = 0;

    public created() {
        this.compId = isNaN(Number(this.$route.params.compId)) ? 0 : parseInt(this.$route.params.compId, 0);
        this.e4sEmailMessageCreate = this.compId > 0 ?
            emailMessageService.factoryE4sEmailMessageCreateForCompAllAthletes(this.compId) :
            emailMessageService.factoryE4sEmailMessageCreate();
    }

    public closeMessage() {
        let location: RawLocation;
        location = {
            path: "/" + LAUNCH_ROUTES_PATHS.COMP_ADMIN + "/" + this.compId
        };
        this.$router.push(location);
    }
}
</script>
