import { computed, reactive } from "vue";
import { OnboardingGridControllerState } from "./onboarding-grid-models";
import { OrgData } from "../../../org-data";
import { IOrg } from "../../../org-models";
import { simpleClone } from "../../../../common/common-service-utils";
import { messageDispatchHelper } from "../../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../../user-message/user-message-models";

export function useOnboardingGridController() {
  const state = reactive<OnboardingGridControllerState>({
    isLoading: false,
    orgs: [],
    orgEditing: null,
    ui: {
      showSection: "grid",
    },
  });

  const orgData = new OrgData();

  function init() {
    getUnapprovedOrgs();
  }

  function getUnapprovedOrgs() {
    state.isLoading = true;
    orgData
      .getOrgsByStatus("New")
      .then((response) => {
        state.orgs = response.data;
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  function onApproveGetConfirmation(org: IOrg) {
    state.orgEditing = simpleClone(org);
    state.ui.showSection = "approve";
  }

  function onApproveOrg() {
    if (!state.orgEditing) {
      return;
    }
    state.isLoading = true;
    orgData
      .approveOrg(state.orgEditing.id)
      .then(() => {
        state.ui.showSection = "grid";
        messageDispatchHelper(
          "Organisation approved successfully",
          USER_MESSAGE_LEVEL.INFO
        );
        getUnapprovedOrgs();
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  function onCancelApproveOrg() {
    state.ui.showSection = "grid";
    state.orgEditing = null;
  }

  const displayName = computed(() => {
    if (state.orgEditing) {
      return state.orgEditing.name + " (" + state.orgEditing.id + ")";
    }
    return "";
  });

  return {
    state,
    init,
    getUnapprovedOrgs,
    onApproveGetConfirmation,
    onApproveOrg,
    onCancelApproveOrg,

    displayName,
  };
}
