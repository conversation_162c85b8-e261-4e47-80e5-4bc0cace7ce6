<template>
    <!--<div>-->
        <!--<iframe :src="url" style="width: 100%; border: none; height: 63vh;">></iframe>-->
    <!--</div>-->
    <iframe :src="url">></iframe>
</template>


<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop, Watch} from "vue-property-decorator";
    import {CONFIG} from "../common/config";
    import {type ICompetition} from "../competition/competition-models";

    @Component({
    })
    export default class ShowEntries extends Vue {

        @Prop() public competition: ICompetition;

        public url: string = "";

        public created() {
            this.url = CONFIG.E4S_HOST + "/entry/v5/competition/showEntriesV2.php?compid=" + this.competition.id;
        }

        @Watch("competition")
        public onCompetitionChanged(newValue: ICompetition) {
            if (newValue && newValue.id) {
                this.url = CONFIG.E4S_HOST + "/entry/v5/competition/showEntriesV2.php?compid=" + newValue.id;
            } else {
                this.url = "";
            }
        }

    }
</script>
