/* Toast Container */
.e4s-toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  pointer-events: none;
}

/* Toast Base Styles */
.e4s-toast {
  background: #323232;
  color: white;
  padding: 12px 24px;
  margin-bottom: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  max-width: 300px;
  word-wrap: break-word;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease-in-out;
  pointer-events: auto;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  font-size: 14px;
  line-height: 1.4;
}

/* Toast Show Animation */
.e4s-toast-show {
  opacity: 1;
  transform: translateX(0);
}

/* Toast Hide Animation */
.e4s-toast-hide {
  opacity: 0;
  transform: translateX(100%);
}

/* Toast Level Styles */
.e4s-toast-info {
  background: #2196F3;
  border-left: 4px solid #1976D2;
}

.e4s-toast-warn {
  background: #FF9800;
  border-left: 4px solid #F57C00;
}

.e4s-toast-error {
  background: #F44336;
  border-left: 4px solid #D32F2F;
}

/* Responsive */
@media (max-width: 480px) {
  .e4s-toast-container {
    left: 10px;
    right: 10px;
    top: 10px;
  }
  
  .e4s-toast {
    max-width: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .e4s-toast {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .e4s-toast {
    transition: opacity 0.1s ease-in-out;
    transform: none;
  }
  
  .e4s-toast-show {
    transform: none;
  }
  
  .e4s-toast-hide {
    transform: none;
  }
}