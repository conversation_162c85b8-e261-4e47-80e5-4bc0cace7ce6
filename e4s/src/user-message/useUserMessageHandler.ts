export function useUserMessageHandler() {
  function send(message: string, level?: "INFO" | "WARN") {
    const tryToast = () => {
      const M = (window as any).M;
      if (M && typeof M.toast === "function") {
        M.toast({
          html: message, // "<div>Drag left or right to close.</div>"
          classes: level === "WARN" ? "toast-warning" : "toast-info",
          displayLength: 7000,
          inDuration: 500,
          outDuration: 500,
        });
      } else {
        // Fallback to console if Materialize not ready yet
        console.warn("Toast (Materialize not ready):", message);
      }
    };

    const M = (window as any).M;
    if (M && typeof M.toast === "function") {
      tryToast();
    } else {
      setTimeout(tryToast, 50);
    }
  }

  return {
    send,
  };
}
