import { Modu<PERSON> } from "vuex";
import { getStore } from "../common/runtime-context";

import { IUserMessage, USER_MESSAGE_LEVEL } from "./user-message-models";
import { showToast } from "./simple-toast";

export enum USER_MESSAGE_STORE_CONST {
  USER_MESSAGE_CONST_MODULE_NAME = "USER_MESSAGE_CONST_MODULE_NAME",

  // <MUTATIONS>
  USER_MESSAGE_MUTATIONS_ADD_MESSAGE = "USER_MESSAGE_MUTATIONS_ADD_MESSAGE",
  // </MUTATIONS>
}

export interface IUserMessageStoreState {
  userMessages: IUserMessage[];
  currentMessage: IUserMessage;
}

const mutations = {
  [USER_MESSAGE_STORE_CONST.USER_MESSAGE_MUTATIONS_ADD_MESSAGE](
    state: IUserMessageStoreState,
    userMessage: IUserMessage
  ) {
    //  create an id that is a 36 char random string.
    const id =
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15);

    userMessage = {
      ...userMessage,
      id,
    };
    state.userMessages.push(userMessage);
    state.currentMessage = userMessage;
  },
};

export const userMessageStore: Module<IUserMessageStoreState, any> = {
  namespaced: true,
  state: {
    userMessages: [],
    currentMessage: {} as IUserMessage,
  },
  mutations,
};

export const messageDispatchHelper = (
  message: string,
  level?: string | USER_MESSAGE_LEVEL
) => {
  if (!level || level.toString().length === 0) {
    level = USER_MESSAGE_LEVEL.INFO;
  }

  const id =
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15);

  const userMessage: IUserMessage = {
    id,
    level: level
      ? USER_MESSAGE_LEVEL.INFO
      : (level as any as USER_MESSAGE_LEVEL),
    message,
  };

  const store = getStore();
  if (store) {
    store.commit(
      USER_MESSAGE_STORE_CONST.USER_MESSAGE_CONST_MODULE_NAME +
        "/" +
        USER_MESSAGE_STORE_CONST.USER_MESSAGE_MUTATIONS_ADD_MESSAGE,
      userMessage
    );
  }

  // Use simple toast system instead of Materialize
  showToast({
    message: userMessage.message,
    level: level === "WARN" ? "WARN" : level === "ERROR" ? "ERROR" : "INFO",
    duration: 7000
  });
};

export const messageDispatchHelperAdmin = (
  message: string,
  level?: string | USER_MESSAGE_LEVEL
) => {
  if (!level || level.toString().length === 0) {
    level = USER_MESSAGE_LEVEL.INFO;
  }

  const store = getStore();
  if (store) {
    const isAdmin =
      store.getters["CONFIG_CONST_MODULE_NAME/CONFIG_GETTERS_IS_ADMIN"];
    if (isAdmin) {
      messageDispatchHelper(message, level);
    }
  }
};
