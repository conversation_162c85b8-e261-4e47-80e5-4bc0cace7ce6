/**
 * Simple toast notification system to replace Materialize toasts
 * Maintains the same API and behavior as the original messageDispatchHelper
 */

interface ToastOptions {
  message: string;
  level: 'INFO' | 'WARN' | 'ERROR';
  duration?: number;
}

let toastContainer: HTMLElement | null = null;

function ensureToastContainer(): HTMLElement {
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.id = 'e4s-toast-container';
    toastContainer.className = 'e4s-toast-container';
    document.body.appendChild(toastContainer);
  }
  return toastContainer;
}

function createToastElement(options: ToastOptions): HTMLElement {
  const toast = document.createElement('div');
  toast.className = `e4s-toast e4s-toast-${options.level.toLowerCase()}`;
  toast.innerHTML = options.message;
  return toast;
}

export function showToast(options: ToastOptions): void {
  try {
    const container = ensureToastContainer();
    const toast = createToastElement(options);
    
    // Add to container
    container.appendChild(toast);
    
    // Trigger animation
    setTimeout(() => toast.classList.add('e4s-toast-show'), 10);
    
    // Auto-remove after duration
    const duration = options.duration || 7000;
    setTimeout(() => {
      toast.classList.add('e4s-toast-hide');
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300); // Wait for fade-out animation
    }, duration);
  } catch (error) {
    // Fallback to console if toast creation fails
    console.warn('Toast message (fallback):', options.message);
  }
}