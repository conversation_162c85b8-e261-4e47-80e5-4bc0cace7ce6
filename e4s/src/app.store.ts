import Vue from "vue";
import Vuex, { Store, StoreOptions } from "vuex";

import { compEventTeamStore } from "./athleteCompSched/comp-event-teams/comp-event-store";
import { athCompSchedStore } from "./athleteCompSched/store/athleteCompSched-store";
import { authStore } from "./auth/auth-store";
import { builderStore } from "./builder/builder-store";
import { cartStore } from "./cart/cart-store";
import { clubStore } from "./club/club-store";
import { configStore } from "./config/config-store";
import { entryStore } from "./entry/entry-store";
import {
  CLUB_STORE_CONST,
  CONFIG_STORE_CONST,
  ENTRY_STORE_CONST,
  USER_MESSAGE_STORE_CONST,
  AUTH_STORE_CONST,
  TEAM_STORE_CONST,
  CART_STORE_CONST,
  BUILDER_STORE_CONST,
  ATH_COMP_SCHED_STORE_CONST,
  COMP_EVENT_TEAMS_STORE_CONST,
  PUBLIC_COMPS_STORE_CONST,
  PERMISSION_STORE_CONST,
  FIELD_HELP_STORE_CONST,
  CHECKIN_STORE_CONST,
  SCOREBOARD_STORE_CONST,
  SECONDARY_STORE_CONST,
  SECONDARY_CUST_STORE_CONST,
  RESULTS_STORE_CONST,
  SCHEDULE_COMP_PUBLIC_STORE_CONST,
} from "./store/store-constants";
import { teamStore } from "./team/team-store";
import { userMessageStore } from "./user-message/user-message-store";
import { publicCompStore } from "./public/entry-public/public-comps-store";
import { permissionStore } from "./admin/permissions/permission-store";
import { fieldHelpStore } from "./common/ui/field/field-help/field-help-store";
import { checkInStore } from "./competition/checkin/checkin-store";
import { scoreboardStore } from "./competition/scoreboard/scorboard-store";
import { secondaryStore } from "./secondary/secondary-store";
import { secondaryCustStore } from "./secondary/cust/secondary-cust-store";
import { resultsStore } from "./competition/results/results-store";
import { scheduleCompPublicStore } from "./competition/results/public/schedule/schedule-comp-public-store";

Vue.use(Vuex);

// Import IRootState from separate file to avoid circular dependencies
import type { IRootState } from "./store/root-state";
export type { IRootState };

// https://stackoverflow.com/questions/47163495/vuex-getter-with-argument-written-in-typescript
const store: StoreOptions<IRootState> = {
  strict: true,
  modules: {
    [CLUB_STORE_CONST.CLUB_STORE_CONST_MODULE_NAME]: clubStore,
    [USER_MESSAGE_STORE_CONST.USER_MESSAGE_CONST_MODULE_NAME]: userMessageStore,
    [ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME]:
      athCompSchedStore,
    [CART_STORE_CONST.CART_CONST_MODULE_NAME]: cartStore,
    [AUTH_STORE_CONST.AUTH_CONST_MODULE_NAME]: authStore,
    [ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME]: entryStore,
    [TEAM_STORE_CONST.TEAM_STORE_CONST_MODULE_NAME]: teamStore,
    [CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME]: configStore,
    [BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME]: builderStore,
    [COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME]:
      compEventTeamStore,
    [PUBLIC_COMPS_STORE_CONST.PUBLIC_COMPS_CONST_MODULE_NAME]: publicCompStore,
    [PERMISSION_STORE_CONST.PERMISSION_CONST_MODULE_NAME]: permissionStore,
    [FIELD_HELP_STORE_CONST.CONST_MODULE_NAME]: fieldHelpStore,
    [CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME]: checkInStore,
    [SCOREBOARD_STORE_CONST.SCOREBOARD_CONST_MODULE_NAME]: scoreboardStore,
    [SECONDARY_STORE_CONST.SECONDARY_CONST_MODULE_NAME]: secondaryStore,
    [SECONDARY_CUST_STORE_CONST.SECONDARY_CUST_CONST_MODULE_NAME]:
      secondaryCustStore,
    [RESULTS_STORE_CONST.RESULTS_CONST_MODULE_NAME]: resultsStore,
    [SCHEDULE_COMP_PUBLIC_STORE_CONST.SCHEDULE_COMP_PUBLIC_MODULE_NAME]:
      scheduleCompPublicStore,
  },
};

export const appStore = new Vuex.Store<IRootState>(store);

export interface IAppStoreDispatchHelper {
  actionName: string;
  dataProp: string;
  loadingProp: string;
}

//  Until v3, use this
export function useStore(): Store<IRootState> {
  return appStore;
}
