<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <LoadingSpinnerV2 v-if="multiEVentOptionsController.uiState.isLoading" />
    <FormGenericInputTemplateV2 form-label="Add Discipline">
      <EventDefTypeAheadV2
        slot="field"
        :reset-on-selected="true"
        @input="addDiscipline"
      />
    </FormGenericInputTemplateV2>

    <FormGenericInputTemplateV2 form-label="Events Selected">
      <MultiEventOptionsTable
        slot="field"
        :child-events="
          multiEVentOptionsController.state.multiEventOptions.childEvents
        "
        @remove="removeDiscipline"
      />
    </FormGenericInputTemplateV2>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  SetupContext,
  watch,
} from "vue";
import {
  IMultiEventEventDef,
  IMultiEventOptions,
} from "../athletecompsched-models";

import FormGenericInputTemplateV2 from "../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import { useMultiEVentOptionsController } from "./useMultiEVentOptionsController";
import MultiEventOptionsTable from "./MultiEventOptionsTable.vue";
import EventDefTypeAheadV2 from "../../eventdef/v2/EventDefTypeAheadV2.vue";
import { IEventDef } from "../../eventdef/eventdef-models";
import LoadingSpinnerV2 from "../../common/ui/loading-spinner-v2.vue";

export default defineComponent({
  name: "MultiEventOptions",
  components: {
    LoadingSpinnerV2,
    EventDefTypeAheadV2,
    MultiEventOptionsTable,
    FormGenericInputTemplateV2,
  },
  props: {
    value: {
      type: Object as PropType<IMultiEventOptions>,
      default: () => {
        const data: IMultiEventOptions = {
          childEvents: [],
        };
        return data;
      },
    },
  },
  setup(props: { value: IMultiEventOptions }, context: SetupContext) {
    const multiEVentOptionsController = useMultiEVentOptionsController({
      genders: [],
    });

    watch(
      () => props.value,
      (newValue: IMultiEventOptions) => {
        multiEVentOptionsController.setMultiOptions(newValue);
      },
      {
        immediate: true,
      }
    );

    function addDiscipline(eventDef: IEventDef) {
      multiEVentOptionsController.addDiscipline(eventDef);
      context.emit(
        "input",
        multiEVentOptionsController.state.multiEventOptions
      );
      context.emit(
        "changed",
        multiEVentOptionsController.state.multiEventOptions
      );
    }

    function removeDiscipline(multiEventEventDef: IMultiEventEventDef) {
      multiEVentOptionsController.removeEvents(multiEventEventDef).then(() => {
        context.emit(
          "input",
          multiEVentOptionsController.state.multiEventOptions
        );
        context.emit(
          "changed",
          multiEVentOptionsController.state.multiEventOptions
        );
      });
    }

    return {
      multiEVentOptionsController,
      addDiscipline,
      removeDiscipline,
    };
  },
});
</script>
