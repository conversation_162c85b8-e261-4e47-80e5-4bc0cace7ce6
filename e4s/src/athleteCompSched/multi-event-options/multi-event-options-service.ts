import {
  EventGroupIdNumber,
  GenderType,
  IBaseConcrete,
} from "../../common/common-models";
import { ICompEvent } from "../../compevent/compevent-models";
import { uniqueBy } from "../../common/common-service-utils";
import { IMultiEventEventDef } from "../athletecompsched-models";

export interface IChildEventGenderMerged extends IBaseConcrete {
  genders: GenderType[];
}

export function filterCompEventsByEventGroupId(
  eventGroupId: EventGroupIdNumber,
  compEvents: ICompEvent[]
): ICompEvent[] {
  return compEvents.filter((ce) => ce.eventGroupSummary.id === eventGroupId);
}

export function getAllChildEvents(
  compEvents: ICompEvent[]
): IMultiEventEventDef[] {
  return compEvents.reduce((accum, compEvent) => {
    return accum.concat(compEvent.options.multiEventOptions.childEvents);
  }, [] as IMultiEventEventDef[]);
}

export function getMultiChildEventsFromAllCompEvents(
  eventGroupId: EventGroupIdNumber,
  compEvents: ICompEvent[]
): IMultiEventEventDef[] {
  const eventsFiltered: ICompEvent[] = filterCompEventsByEventGroupId(
    eventGroupId,
    compEvents
  );

  const allChildEventsFiltered = getAllChildEvents(eventsFiltered);
  return uniqueBy(allChildEventsFiltered, "eventDefId");
}
