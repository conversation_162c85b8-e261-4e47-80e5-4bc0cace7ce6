<template>
  <div>
      <span>Max Events:</span>
      <span v-text="rules.maxEvents"></span>

      <span>Max Comp Events:</span>
      <span v-text="rules.maxCompEvents"></span>

      <span>Max Field:</span>
      <span v-text="rules.maxField"></span>

      <span>Max Track:</span>
      <span v-text="rules.maxTrack"></span>

      Other: 
      <span v-for="uni in rules.unique" :key="uni.text">{{uni.text}}</span>
  </div>
</template>

<script lang="ts">

import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";

import type {ICompShedRuleOptions} from "./athletecompsched-models";

@Component({
    name: "comp-rules"
})
export default class CompRules extends Vue {
    @Prop({
        default: () => {
            return {} as ICompShedRuleOptions;
        },
    })
    public readonly rules: ICompShedRuleOptions;
}
</script>