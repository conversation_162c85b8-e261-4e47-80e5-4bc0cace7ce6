<template>
    <div>
        <div class="row">
            <div class="col s12 m12 l12">
                <SectionAlert :header-title="getCompEventName">
                    <div slot="content">
                        <div v-html="getSubscriptionSummary"></div>
                        <div
                            v-if="selectedCompetition.options.subscription.organiserMessage.length > 0"
                            v-html="selectedCompetition.options.subscription.organiserMessage"
                        >
                        </div>
                        <div class="e4s-section-padding-separator"></div>
                        <div v-html="selectedCompetition.options.subscription.e4sMessage"></div>
                    </div>
                </SectionAlert>
            </div>
        </div>

        <div class="e4s-section-padding-separator"></div>

        <div class="row">
            <div class="col s12 m12 l12">
                <div class="right">
                    <button class="e4s-button e4s-button--red e4s-button--10-wide"
                            v-on:click.stop="onCancel">
                        <span>Cancel</span>
                    </button>

                    <button class="e4s-button e4s-button--green e4s-button--10-wide"
                            v-on:click.stop="onContinue">
                        <span>Continue</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import {IAthleteCompSchedRuleEvent} from "../athletecompsched-models"
import {AthleteCompSchedService} from "../athletecompsched-service"
import SectionAlert from "../../common/ui/section-alert/section-alert.vue"
import {CompetitionService} from "../../competition/competiton-service"
import {ICompetitionInfo} from "../../competition/competition-models"
import {IConfigApp} from "../../config/config-app-models"
import {IAthlete} from "../../athlete/athlete-models"
import {AthleteService} from "../../athlete/athlete-service"
import SectionDividerLine from "../../common/ui/section-divider-line.vue"
import {CommonService} from "../../common/common-service"

const athleteCompSchedService: AthleteCompSchedService = new AthleteCompSchedService();

@Component({
    name: "subscription-message",
    components: {SectionDividerLine, SectionAlert}
})
export default class SubscriptionMessage extends Vue {

    @Prop({
        default: () => {
            return athleteCompSchedService.factoryAthleteCompSchedRuleEvent();
        }
    })
    public readonly compEvent!: IAthleteCompSchedRuleEvent;

    @Prop({
        default: () => {
            return new CompetitionService().factoryCompetitionInfo();
        }
    })
    public readonly selectedCompetition!: ICompetitionInfo;

    @Prop({
        default: () => {
            return new AthleteService().factoryGetAthlete();
        }
    })
    public readonly selectedAthlete!: IAthlete;

    @Prop({
        required: true
    })
    public readonly configApp!: IConfigApp;


    public athleteCompSchedService = athleteCompSchedService;

    public get getCompEventName() {
        return this.selectedCompetition.name + " - " + this.athleteCompSchedService.getCompEventName(this.compEvent);
    }

    public get getSubscriptionSummary(): string {
        const message: string[] = [];

        const paidCount = this.compEvent.entryInfo.paidCount;
        const maxAthletes = this.compEvent.maxathletes;
        const unpaidCount = this.compEvent.entryInfo.unpaidCount;
        const currentPosition = paidCount + unpaidCount + 1;
        const waitingListPosition = currentPosition - maxAthletes;

        const commonService: CommonService = new CommonService();

        message.push("This event has a limit of " + maxAthletes + " entries and is full.");
        if (paidCount > maxAthletes) {
            message.push(" " + (paidCount - maxAthletes) + " paid entry(s) on the waiting list already.");
        }

        if (unpaidCount > 0) {
            message.push(" " + unpaidCount + " entr" + (unpaidCount > 1 ? "ies are" : "y is") + " being processed and awaiting payment.");
        }

        message.push(" " +  this.selectedAthlete.firstName + " " + this.selectedAthlete.surName +
            " would be assigned waiting list position " + waitingListPosition + ".");

        message.push("Please note: Athletes on the waiting list will receive an email if they get moved into " +
            "the event, but it is your responsibility to check the entry list before making " +
            "your way to the competition.");

        const currency = this.configApp.currency;

        message.push("N.B: The entry fee for this event is " + commonService.getAmountAsCurrency(this.compEvent.price.curPrice, currency) +
            ".  When entries close, athletes still on the waiting list will receive a refund of " +
            (commonService.getAmountAsCurrency(this.compEvent.price.curPrice - this.compEvent.price.curFee, currency)) +
            " i.e. the charge to go on the waiting list for this event is " + commonService.getAmountAsCurrency(this.compEvent.price.curFee, currency));

        message.push(" Click 'Continue' if you agree to the above and wish to be put on the waiting list.");

        return message.join("<br><br>");
    }

    public onContinue() {
        this.$emit("continue");
    }

    public onCancel() {
        this.$emit("cancel");
    }

}
</script>
