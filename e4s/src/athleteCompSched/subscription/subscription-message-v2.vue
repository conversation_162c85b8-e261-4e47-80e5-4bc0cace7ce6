<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <InfoSectionV2>
      <div slot="default" class="e4s-flex-column e4s-gap--standard">

        <div v-text="getCompEventName"></div>

        <div v-html="getSubscriptionSummary"></div>
        <div
          v-if="
              selectedCompetition.options.subscription.organiserMessage.length >
              0
            "
          v-html="selectedCompetition.options.subscription.organiserMessage"
        ></div>
        <div class="e4s-section-padding-separator"></div>
        <div
          v-html="selectedCompetition.options.subscription.e4sMessage"
        ></div>
      </div>
    </InfoSectionV2>

    <div class="e4s-flex-row e4s-justify-flex-end e4s-gap--standard">
      <ButtonGenericV2 text="Cancel" button-type="tertiary" v-on:click="onCancel"/>
      <ButtonGenericV2 text="Continue" v-on:click="onContinue"/>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "vue";
import { IAthleteCompSchedRuleEvent } from "../athletecompsched-models";
import { AthleteCompSchedService } from "../athletecompsched-service";
import { IAthlete } from "../../athlete/athlete-models";
import { AthleteService } from "../../athlete/athlete-service";
import { IConfigApp } from "../../config/config-app-models";
import { ICompetitionSummaryPublic } from "../../competition/competition-models";
import { CommonService } from "../../common/common-service";
import InfoSectionV2 from "../../common/ui/layoutV2/info-section-v2.vue";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue"

const athleteCompSchedService = new AthleteCompSchedService();

export default defineComponent({
  name: "subscription-message-v2",
  components: {ButtonGenericV2, InfoSectionV2 },
  props: {
    selectedCompetition: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      required: true,
    },
    compEvent: {
      type: Object as PropType<IAthleteCompSchedRuleEvent>,
      default: () => {
        return athleteCompSchedService.factoryAthleteCompSchedRuleEvent();
      },
    },
    selectedAthlete: {
      type: Object as PropType<IAthlete>,
      default: () => {
        return new AthleteService().factoryGetAthlete();
      },
    },
    configApp: {
      type: Object as PropType<IConfigApp>,
      required: true,
    },
  },
  setup(
    props: {
      selectedCompetition: ICompetitionSummaryPublic;
      compEvent: IAthleteCompSchedRuleEvent;
      selectedAthlete: IAthlete;
      configApp: IConfigApp;
    },
    context: SetupContext
  ) {
    /*
    watch(
      () => props.athlete,
      (newValue: IAthlete) => {
        athleteForm.init(newValue);
      },
      {
        immediate: true,
      }
    );
     */

    const getCompEventName = computed(() => {
      return (
        props.selectedCompetition.compName +
        " - " +
        athleteCompSchedService.getCompEventName(props.compEvent)
      );
    });

    const getSubscriptionSummary = computed(() => {
      const message: string[] = [];

      const paidCount = props.compEvent.entryInfo.paidCount;
      const maxAthletes = props.compEvent.maxathletes;
      const unpaidCount = props.compEvent.entryInfo.unpaidCount;
      const currentPosition = paidCount + unpaidCount + 1;
      const waitingListPosition = currentPosition - maxAthletes;

      const commonService: CommonService = new CommonService();

      message.push(
        "This event has a limit of " + maxAthletes + " entries and is full."
      );
      if (paidCount > maxAthletes) {
        message.push(
          " " +
            (paidCount - maxAthletes) +
            " paid entry(s) on the waiting list already."
        );
      }

      if (unpaidCount > 0) {
        message.push(
          " " +
            unpaidCount +
            " entr" +
            (unpaidCount > 1 ? "ies are" : "y is") +
            " being processed and awaiting payment."
        );
      }

      message.push(
        " " +
        props.selectedAthlete.firstName +
          " " +
        props.selectedAthlete.surName +
          " would be assigned waiting list position " +
          waitingListPosition +
          "."
      );

      message.push(
        "Please note: Athletes on the waiting list will receive an email if they get moved into " +
          "the event, but it is your responsibility to check the entry list before making " +
          "your way to the competition."
      );

      const currency = props.configApp.currency;

      message.push(
        "N.B: The entry fee for this event is " +
          commonService.getAmountAsCurrency(
            props.compEvent.price.curPrice,
            currency
          ) +
          ".  When entries close, athletes still on the waiting list will receive a refund of " +
          commonService.getAmountAsCurrency(
            props.compEvent.price.curPrice - props.compEvent.price.curFee,
            currency
          ) +
          " i.e. the charge to go on the waiting list for this event is " +
          commonService.getAmountAsCurrency(props.compEvent.price.curFee, currency)
      );

      message.push(
        " Click 'Continue' if you agree to the above and wish to be put on the waiting list."
      );

      return message.join("<br><br>");
    });

    function onContinue() {
      context.emit("continue");
    }

    function onCancel() {
      context.emit("cancel");
    }

    return { getCompEventName, getSubscriptionSummary, onContinue, onCancel };
  },
});
</script>
