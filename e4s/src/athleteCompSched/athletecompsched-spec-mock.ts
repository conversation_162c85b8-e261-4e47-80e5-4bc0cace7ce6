import {IAthleteCompSched} from "./athletecompsched-models"
import {ICompetition} from "../competition/competition-models"
import {IAthlete} from "../athlete/athlete-models"
import {IUserApplication, IUserInfo} from "../config/config-app-models"

export const compUniqueEvents = {
    "date": "2023-02-28T00:00:00",
    "today": "2023-02-14",
    "systemtime": "2023-02-14 17:53:25",
    "compDate": "2023-02-28",
    "visibledate": "2023-02-28",
    "lastentrymod": "2023-02-14 17:41:44",
    "locationid": 20,
    "link": "",
    "yearFactor": 0,
    "compOrgId": 54,
    "entriesClose": "2023-02-27T00:00:00",
    "options": {
        "sockets": true,
        "allowExpiredRegistration": true,
        "useTeamBibs": false,
        "bacs": {
            "enabled": false,
            "msg": ""
        },
        "scoreboard": {
            "image": "/results/blank.png"
        },
        "seqEventNo": true,
        "disabled": false,
        "stripeMandatory": false,
        "resultsAvailable": false,
        "paymentCode": "",
        "homeInfo": "",
        "shortCode": "",
        "priority": {
            "required": false,
            "code": "",
            "dateTime": "",
            "message": ""
        },
        "bibNos": 1,
        "bibSort1": "surname",
        "bibSort2": "firstname",
        "bibSort3": "dob",
        "heatOrder": "s",
        "stadium": "",
        "adjustEventNo": 0,
        "pfTargetDirectory": "",
        "athleteType": "A",
        "tickets": {
            "enabled": false
        },
        "contact": {
            "id": 30,
            "userName": "Nuneaton Open Events",
            "tel": "02476 344 429",
            "email": "<EMAIL>",
            "visible": true
        },
        "cardInfo": {
            "enabled": true,
            "availableFrom": ""
        },
        "subscription": {
            "enabled": false,
            "timeCloses": "",
            "organiserMessage": "",
            "e4sMessage": "",
            "refunded": "",
            "process": true,
            "processRefundTime": ""
        },
        "checkIn": {
            "enabled": false,
            "checkInDateTimeOpens": "",
            "defaultFrom": 180,
            "defaultTo": 60,
            "qrCode": true,
            "text": "",
            "terms": "",
            "seedOnEntries": false
        },
        "school": false,
        "orgFreeEntry": false,
        "cheques": {
            "allow": false,
            "ends": ""
        },
        "allowAdd": {
            "unregistered": false,
            "registered": true
        },
        "timetable": "Provisional",
        "helpText": {
            "schedule": "",
            "teams": "",
            "cart": ""
        },
        "showTeamAthletes": true,
        "singleAge": false,
        "stopReport": false,
        "showAthleteAgeInEntries": false,
        "report": {
            "summary": true,
            "athletes": true,
            "ttathletes": true,
            "ttentries": true,
            "individual_entries": true,
            "teams": true,
            "subscriptions": true,
            "orders": true,
            "events": true
        },
        "athleteSecurity": {
            "areas": [],
            "clubs": [],
            "onlyClubsUpTo": ""
        },
        "ui": {
            "enterButtonText": "Enter",
            "entryDefaultPanel": "SCHEDULE",
            "ticketComp": 0,
            "ticketCompButtonText": "Buy Tickets"
        },
        "athleteQrData": false,
        "disabledReason": "",
        "laneCount": 8,
        "compLimits": {
            "athletes": 0,
            "entries": 0,
            "teams": 0
        },
        "cancelEvent": {
            "hrsBeforeClose": 48,
            "refund": {
                "allow": false,
                "type": "E4S_FEES"
            },
            "credit": {
                "allow": false
            }
        },
        "autoEntries": {
            "selectedTargetComp": {
                "id": 0,
                "name": ""
            },
            "targetable": {
                "allowedSources": [],
                "enabled": false
            }
        },
        "level": "",
        "dates": [
            "2023-02-28"
        ],
        "categoryId": 3854,
        "selfService": []
    },
    "logo": "/resources/nuneaton_opens_logo.jpg",
    "areaid": 0,
    "teamid": 0,
    "ctcid": null,
    "maxmale": null,
    "name": "test 2",
    "tandc": {
        "link": "",
        "description": "I agree that entry fees paid are non transferable and non refundable for this event."
    },
    "areaname": "",
    "entity": "",
    "entityid": 0,
    "ctc": {
        "ctcid": null,
        "maxMale": null,
        "maxFemale": null,
        "maxTeams": null,
        "maxAthletes": null,
        "maxAgeGroup": null,
        "uniqueEvents": null,
        "singleAgeGroup": null
    },
    "compRules": [],
    "id": 350,
    "indivEvents": true,
    "hasSecureEvents": false,
    "teamEvents": false
} as any as ICompetition;

export const athleteUniqueEvent: IAthlete = {
    "id": 165985,
    "firstName": "Elliot,max",
    "surName": "",
    "aocode": "",
    "URN": null,
    "pof10id": null,
    "dob": "2000-01-01",
    "gender": "M",
    "classification": 0,
    "email": null,
    "schoolid": null,
    "clubid": 9999,
    "club2id": 0,
    "type": "A",
    "options": {
        "noEntryReason": "",
        "emergency": {
            "name": "",
            "tel": "",
            "relationship": ""
        }
    },
    "activeEndDate": null,
    "activeEndDate2": null,
    "image": "https://www.thepowerof10.info/athletes/profilepic.aspx?athleteid=unknown",
    "club": "Unattached",
    "clubId": 9999,
    "club2Id": 0,
    "schoolId": 0,
    "club2": "",
    "school": "",
    "pbInfo": [
        {
            "eventid": 668,
            "pb": 0,
            "pbid": 0,
            "pbText": "",
            "pof10pb": 0,
            "sb": 0,
            "trackSb": false,
            "eventName": "10000m",
            "options": {
                "maxInHeat": 16,
                "min": 100,
                "max": 9999
            },
            "min": 100,
            "max": 9999,
            "uomInfo": {
                "id": 3,
                "type": "T",
                "options": [
                    {
                        "pattern": "m.ss.SS",
                        "text": "mins",
                        "short": "m"
                    },
                    {
                        "pattern": "m:ss.SS",
                        "text": "mins",
                        "short": "m"
                    }
                ]
            }
        },
        {
            "eventid": 4,
            "pb": 0,
            "pbid": 0,
            "pbText": "",
            "pof10pb": 0,
            "sb": 0,
            "trackSb": false,
            "eventName": "100m",
            "options": {
                "maxInHeat": 8,
                "min": 9.6,
                "max": 20,
                "helpText": "",
                "wind": "E"
            },
            "min": 9.6,
            "max": 20,
            "uomInfo": {
                "id": 1,
                "type": "T",
                "options": [
                    {
                        "pattern": "s.SS",
                        "text": "seconds",
                        "short": "secs"
                    }
                ]
            }
        },
        {
            "eventid": 15,
            "pb": 0,
            "pbid": 0,
            "pbText": "",
            "pof10pb": 0,
            "sb": 0,
            "trackSb": false,
            "eventName": "1500m",
            "options": {
                "maxInHeat": 16,
                "min": 150,
                "max": 420
            },
            "min": 150,
            "max": 420,
            "uomInfo": {
                "id": 3,
                "type": "T",
                "options": [
                    {
                        "pattern": "m.ss.SS",
                        "text": "mins",
                        "short": "m"
                    },
                    {
                        "pattern": "m:ss.SS",
                        "text": "mins",
                        "short": "m"
                    }
                ]
            }
        },
        {
            "eventid": 6,
            "pb": 22,
            "pbid": 72014,
            "pbText": 22,
            "pof10pb": 0,
            "sb": 0,
            "trackSb": false,
            "eventName": "200m",
            "options": {
                "maxInHeat": 8,
                "min": 20,
                "max": 60,
                "wind": "E"
            },
            "min": 20,
            "max": 60,
            "uomInfo": {
                "id": 1,
                "type": "T",
                "options": [
                    {
                        "pattern": "s.SS",
                        "text": "seconds",
                        "short": "secs"
                    }
                ]
            }
        },
        {
            "eventid": 17,
            "pb": 135.41,
            "pbid": 72037,
            "pbText": "2.15.41",
            "pof10pb": 0,
            "sb": 0,
            "trackSb": false,
            "eventName": "800m",
            "options": {
                "maxInHeat": 8,
                "min": 80,
                "max": 240
            },
            "min": 80,
            "max": 240,
            "uomInfo": {
                "id": 3,
                "type": "T",
                "options": [
                    {
                        "pattern": "m.ss.SS",
                        "text": "mins",
                        "short": "m"
                    },
                    {
                        "pattern": "m:ss.SS",
                        "text": "mins",
                        "short": "m"
                    }
                ]
            }
        },
        {
            "eventid": 39,
            "pb": 12,
            "pbid": 72006,
            "pbText": 12,
            "pof10pb": 0,
            "sb": 0,
            "trackSb": false,
            "eventName": "Javelin",
            "options": {
                "min": 1,
                "max": 100,
                "unique": [
                    {
                        "e": 41
                    }
                ]
            },
            "min": 1,
            "max": 110,
            "uomInfo": {
                "id": 5,
                "type": "D",
                "options": [
                    {
                        "pattern": 0.99,
                        "text": "metres",
                        "short": "mt"
                    }
                ]
            }
        }
    ]
} as any as IAthlete;

export const uniqueEvents: IAthleteCompSched[] = [
    {
        "ceid": 36924,
        "ageGroupId": 9,
        "ageGroupName": "Under 15",
        "description": "test",
        "startdate": "2023-02-28T00:00:00+00:00",
        "IsOpen": 1,
        "tf": "T",
        "uomType": "T",
        "price": {
            "id": 475,
            "priceName": "",
            "stdPrice": 5,
            "curPrice": 5,
            "curFee": 0.6,
            "salePrice": 5,
            "saleDate": "+00:00"
        },
        "eventid": 17,
        "multiid": 0,
        "Name": "800m",
        "eventGroup": "800m",
        "egOptions": "{\"checkIn\":{\"from\":-1,\"to\":-1,\"seedOnEntries\":false,\"checkInMins\":60}}",
        "split": 0,
        "maxgroup": 5270,
        "maxathletes": 0,
        "ceoptions": {
            "min": 80,
            "max": 240,
            "helpText": "",
            "registeredAthletes": true,
            "unregistered": false,
            "registered": true,
            "isTeamEvent": false,
            "wind": "",
            "excludeFromCntRule": false,
            "unique": [],
            "eventTeam": {
                "min": 0,
                "max": 0,
                "mustBeIndivEntered": false,
                "minTargetAgeGroupCount": 0,
                "maxOtherAgeGroupCount": 0,
                "teamPositionLabel": "",
                "maxEventTeams": 0,
                "currCount": 0,
                "singleClub": false,
                "teamNameFormat": "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
                "teamSubstituteLabel": "athlete",
                "showForm": false,
                "formType": "DEFAULT",
                "price": ""
            },
            "rowOptions": {
                "autoExpandHelpText": false,
                "showPB": true,
                "showPrice": false,
                "showEntryCount": true
            },
            "maxInHeat": 8,
            "heatInfo": {
                "useLanes": "A",
                "heatDurationMins": 0
            },
            "xiText": "",
            "xeText": "",
            "xbText": "",
            "xrText": "",
            "warningMessage": "",
            "mandatoryPB": false,
            "trialInfo": "",
            "reportInfo": "",
            "ageGroups": [],
            "singleAge": false,
            "security": {},
            "athleteSecurity": {},
            "checkIn": {
                "from": -1,
                "to": -1,
                "seedOnEntries": false,
                "checkInMins": 60
            },
            "uniqueEventGroups": [
                {
                    "optionsSet": true,
                    "id": 5270,
                    "compId": 350,
                    "eventNo": 1,
                    "typeNo": "T1",
                    "bibSortNo": null,
                    "name": "800m",
                    "options": {
                        "trialInfo": "",
                        "reportInfo": "",
                        "isTeamEvent": false,
                        "maxathletes": 0,
                        "maxInHeat": 0,
                        "heatDurationMins": 0,
                        "entriesFrom": 0,
                        "seed": {
                            "firstLane": 1,
                            "laneCount": 0,
                            "gender": false,
                            "age": false,
                            "type": "O",
                            "waiting": false,
                            "qualifyToEg": {
                                "id": 0,
                                "compId": 0,
                                "name": ""
                            }
                        },
                        "athleteSecurity": {
                            "clubs": []
                        },
                        "checkIn": {
                            "from": -1,
                            "to": -1,
                            "seedOnEntries": false,
                            "checkInMins": 60
                        }
                    },
                    "forceClose": 0,
                    "notes": null,
                    "startDate": "2023-02-28 00:00:00"
                },
                {
                    "optionsSet": true,
                    "id": 5271,
                    "compId": 350,
                    "eventNo": 2,
                    "typeNo": "T2",
                    "bibSortNo": null,
                    "name": "1500m",
                    "options": {
                        "trialInfo": "",
                        "reportInfo": "",
                        "isTeamEvent": false,
                        "maxathletes": 0,
                        "maxInHeat": 0,
                        "heatDurationMins": 0,
                        "entriesFrom": 0,
                        "seed": {
                            "firstLane": 1,
                            "laneCount": 0,
                            "gender": false,
                            "age": false,
                            "type": "O",
                            "waiting": false,
                            "qualifyToEg": {
                                "id": 0,
                                "compId": 0,
                                "name": ""
                            }
                        },
                        "athleteSecurity": {
                            "clubs": []
                        },
                        "checkIn": {
                            "from": -1,
                            "to": -1,
                            "seedOnEntries": false,
                            "checkInMins": 60
                        }
                    },
                    "forceClose": 0,
                    "notes": null,
                    "startDate": "2023-02-28 00:00:00"
                },
                {
                    "optionsSet": true,
                    "id": 5270,
                    "compId": 350,
                    "eventNo": 1,
                    "typeNo": "T1",
                    "bibSortNo": null,
                    "name": "800m",
                    "options": {
                        "trialInfo": "",
                        "reportInfo": "",
                        "isTeamEvent": false,
                        "maxathletes": 0,
                        "maxInHeat": 0,
                        "heatDurationMins": 0,
                        "entriesFrom": 0,
                        "seed": {
                            "firstLane": 1,
                            "laneCount": 0,
                            "gender": false,
                            "age": false,
                            "type": "O",
                            "waiting": false,
                            "qualifyToEg": {
                                "id": 0,
                                "compId": 0,
                                "name": ""
                            }
                        },
                        "athleteSecurity": {
                            "clubs": []
                        },
                        "checkIn": {
                            "from": -1,
                            "to": -1,
                            "seedOnEntries": false,
                            "checkInMins": 60
                        }
                    },
                    "forceClose": 0,
                    "notes": null,
                    "startDate": "2023-02-28 00:00:00"
                }
            ],
            "unregisteredAthletes": true,
            "availableTo": "2023-02-28T00:00:00+00:00",
            "availableToStatus": -1
        },
        "eoptions": {
            "maxInHeat": 8,
            "min": 80,
            "max": 240
        },
        "ageGroup": {
            "id": 9,
            "minAge": 13,
            "minAtDay": 31,
            "minAtMonth": 8,
            "keyName": "Under 15",
            "options": [
                {
                    "aocode": "EA",
                    "default": true,
                    "base": 1
                }
            ],
            "name": "Under 15",
            "maxAge": 14,
            "maxAtDay": 31,
            "maxAtMonth": 8,
            "maxAtYear": 0,
            "minAtYear": 0,
            "shortName": "Under 15"
        },
        "entered": false,
        "entryId": 0,
        "paid": 1,
        "athleteid": 107169,
        "entryInfo": {
            "unpaidCount": 2,
            "paidCount": 0,
            "totalCount": 2,
            "entryPosition": 3,
            "entryCreated": "",
            "entryPositionDate": "",
            "maxAthletes": 0
        },
        "entrycnt": 0,
        "compName": "test 2",
        "user": {
            "userId": 0,
            "userName": "",
            "userEmail": ""
        },
        "order": {
            "orderId": 0,
            "productId": 99999,
            "e4sLineValue": 0,
            "wcLineValue": 0,
            "isRefund": false,
            "refundValue": 0,
            "isCredit": false,
            "creditValue": 0,
            "discountId": 0,
            "dateOrdered": ""
        },
        "firstName": "El-Ezer",
        "surName": "Ablorh",
        "club": "Unattached",
        "clubId": 9999,
        "uom": [
            {
                "pattern": "m.ss.SS",
                "text": "mins",
                "short": "m",
                "uomType": "T"
            },
            {
                "pattern": "m:ss.SS",
                "text": "mins",
                "short": "m",
                "uomType": "T"
            }
        ]
    } as any as IAthleteCompSched,
    {
        "ceid": 36944,
        "ageGroupId": 9,
        "ageGroupName": "Under 15",
        "description": "test",
        "startdate": "2023-02-28T00:00:00+00:00",
        "IsOpen": 1,
        "tf": "T",
        "uomType": "T",
        "price": {
            "id": 475,
            "priceName": "",
            "stdPrice": 5,
            "curPrice": 5,
            "curFee": 0.6,
            "salePrice": 5,
            "saleDate": "+00:00"
        },
        "eventid": 15,
        "multiid": 0,
        "Name": "1500m",
        "eventGroup": "1500m",
        "egOptions": "{\"trialInfo\":\"\",\"reportInfo\":\"\",\"isTeamEvent\":false,\"maxathletes\":0,\"maxInHeat\":0,\"heatDurationMins\":0,\"entriesFrom\":0,\"seed\":{\"firstLane\":1,\"laneCount\":0,\"gender\":false,\"age\":false,\"type\":\"O\",\"waiting\":false,\"qualifyToEg\":{\"id\":0,\"compId\":0,\"name\":\"\"}},\"athleteSecurity\":{\"clubs\":[]},\"checkIn\":{\"from\":-1,\"to\":-1,\"seedOnEntries\":false,\"checkInMins\":60}}",
        "split": 0,
        "maxgroup": 5271,
        "maxathletes": 0,
        "ceoptions": {
            "min": 150,
            "max": 420,
            "helpText": "",
            "registeredAthletes": true,
            "unregistered": false,
            "registered": true,
            "isTeamEvent": false,
            "wind": "",
            "excludeFromCntRule": false,
            "unique": [],
            "eventTeam": {
                "min": 0,
                "max": 0,
                "mustBeIndivEntered": false,
                "minTargetAgeGroupCount": 0,
                "maxOtherAgeGroupCount": 0,
                "teamPositionLabel": "",
                "maxEventTeams": 0,
                "currCount": 0,
                "singleClub": false,
                "teamNameFormat": "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
                "teamSubstituteLabel": "athlete",
                "showForm": false,
                "formType": "DEFAULT",
                "price": ""
            },
            "rowOptions": {
                "autoExpandHelpText": false,
                "showPB": true,
                "showPrice": false,
                "showEntryCount": true
            },
            "maxInHeat": 16,
            "heatInfo": {
                "useLanes": "A",
                "heatDurationMins": 0
            },
            "xiText": "",
            "xeText": "",
            "xbText": "",
            "xrText": "",
            "warningMessage": "",
            "mandatoryPB": false,
            "trialInfo": "",
            "reportInfo": "",
            "ageGroups": [],
            "singleAge": false,
            "security": {},
            "athleteSecurity": {},
            "checkIn": {
                "from": -1,
                "to": -1,
                "seedOnEntries": false,
                "checkInMins": 60
            },
            "uniqueEventGroups": [
                {
                    "optionsSet": true,
                    "id": 5270,
                    "compId": 350,
                    "eventNo": 1,
                    "typeNo": "T1",
                    "bibSortNo": null,
                    "name": "800m",
                    "options": {
                        "trialInfo": "",
                        "reportInfo": "",
                        "isTeamEvent": false,
                        "maxathletes": 0,
                        "maxInHeat": 0,
                        "heatDurationMins": 0,
                        "entriesFrom": 0,
                        "seed": {
                            "firstLane": 1,
                            "laneCount": 0,
                            "gender": false,
                            "age": false,
                            "type": "O",
                            "waiting": false,
                            "qualifyToEg": {
                                "id": 0,
                                "compId": 0,
                                "name": ""
                            }
                        },
                        "athleteSecurity": {
                            "clubs": []
                        },
                        "checkIn": {
                            "from": -1,
                            "to": -1,
                            "seedOnEntries": false,
                            "checkInMins": 60
                        }
                    },
                    "forceClose": 0,
                    "notes": null,
                    "startDate": "2023-02-28 00:00:00"
                }
            ],
            "unregisteredAthletes": true,
            "availableTo": "2023-02-28T00:00:00+00:00",
            "availableToStatus": -1
        },
        "eoptions": {
            "maxInHeat": 16,
            "min": 150,
            "max": 420
        },
        "ageGroup": {
            "id": 9,
            "minAge": 13,
            "minAtDay": 31,
            "minAtMonth": 8,
            "keyName": "Under 15",
            "options": [
                {
                    "aocode": "EA",
                    "default": true,
                    "base": 1
                }
            ],
            "name": "Under 15",
            "maxAge": 14,
            "maxAtDay": 31,
            "maxAtMonth": 8,
            "maxAtYear": 0,
            "minAtYear": 0,
            "shortName": "Under 15"
        },
        "entered": false,
        "entryId": 0,
        "paid": 0,
        "athleteid": 107169,
        "entryInfo": {
            "unpaidCount": 1,
            "paidCount": 0,
            "totalCount": 1,
            "entryPosition": 2,
            "entryCreated": "",
            "entryPositionDate": "",
            "maxAthletes": 0
        },
        "entrycnt": 0,
        "compName": "test 2",
        "user": {
            "userId": 0,
            "userName": "",
            "userEmail": ""
        },
        "order": {
            "orderId": 0,
            "productId": 0,
            "e4sLineValue": 0,
            "wcLineValue": 0,
            "isRefund": false,
            "refundValue": 0,
            "isCredit": false,
            "creditValue": 0,
            "discountId": 0,
            "dateOrdered": ""
        },
        "firstName": "El-Ezer",
        "surName": "Ablorh",
        "club": "Unattached",
        "clubId": 9999,
        "uom": [
            {
                "pattern": "m.ss.SS",
                "text": "mins",
                "short": "m",
                "uomType": "T"
            },
            {
                "pattern": "m:ss.SS",
                "text": "mins",
                "short": "m",
                "uomType": "T"
            }
        ]
    } as any as IAthleteCompSched
];



export const userInfoUniqueEvents: IUserInfo = {
    "e4s_pagessize": 25,
    "e4s_schedInfoState": 1,
    "e4s_user": 1,
    "e4s_credit": 0,
    "e4s_useraccess": 1,
    "e4s_stripe_approved_date": "2020-01-01 12:00:00",
    "e4s_stripe_connected_date": "2020-01-01 12:00:00",
    "orgs": [
        {
            "id": 38,
            "name": "National",
            "locations": []
        }
    ],
    "areas": [
        {
            "areaid": 9,
            "areaname": "Leinster",
            "areashortname": "Len",
            "areaparentid": 6,
            "entityLevel": 3,
            "entitylevel": 3,
            "entityName": "Region"
        }
    ],
    "clubs": [
        {
            "id": 13,
            "externid": 36,
            "Clubname": "Aberystwyth A.C.",
            "shortcode": null,
            "Region": "West Wales",
            "Country": "Wales",
            "areaid": 49,
            "clubtype": "C",
            "active": 1,
            "options": "",
            "entityName": "Club",
            "entityLevel": 1
        },
        {
            "id": 1198,
            "externid": 839,
            "Clubname": "Nuneaton Harriers",
            "shortcode": "Nun",
            "Region": "",
            "Country": "United Kingdom",
            "areaid": 4,
            "clubtype": "C",
            "active": 1,
            "options": null,
            "entityName": "Club",
            "entityLevel": 1
        },
        {
            "id": 11013,
            "externid": 32923,
            "Clubname": "Paxcroft Road Runners ",
            "shortcode": null,
            "Region": "Wiltshire",
            "Country": "England",
            "areaid": 137,
            "clubtype": "C",
            "active": 0,
            "options": null,
            "entityName": "Club",
            "entityLevel": 1
        }
    ],
    "user": {
        "id": 1,
        "user_login": "E4SAdmin",
        "user_nicename": "e4sadmin",
        "user_email": "<EMAIL>",
        "display_name": "E4S Admin",
        "role": "E4SUSER",
        "google_email": "",
        "impersonating": false,
        "e4sCredit": [
            0
        ],
        "permissions": [
            {
                "id": 5,
                "userid": 1,
                "role": {
                    "id": 0,
                    "name": "All"
                },
                "comp": {
                    "id": 0,
                    "name": ""
                },
                "org": {
                    "id": 0,
                    "name": "All"
                },
                "permLevels": []
            }
        ],
        "version": {
            "current": "v2",
            "toggle": true
        }
    },
    "wp_role": "administrator",
    "security": {
        "permissions": [
            {
                "id": 5,
                "userid": 1,
                "roleid": 0,
                "orgid": 0,
                "compid": 0,
                "role": "all",
                "orgname": "All"
            }
        ],
        "permLevels": {
            "": []
        }
    }
} as any as IUserInfo;



export const userUniqueEvent: IUserApplication = {
    "id": 1,
    "user_login": "E4SAdmin",
    "user_nicename": "e4sadmin",
    "user_email": "<EMAIL>",
    "display_name": "E4S Admin",
    "role": "E4SUSER",
    "google_email": "",
    "impersonating": false,
    "e4sCredit": [
        0
    ],
    "permissions": [
        {
            "id": 5,
            "userid": 1,
            "role": {
                "id": 0,
                "name": "All"
            },
            "comp": {
                "id": 0,
                "name": ""
            },
            "org": {
                "id": 0,
                "name": "All"
            },
            "permLevels": []
        }
    ],
    "version": {
        "current": "v2",
        "toggle": true
    }
} as any as IUserApplication;
