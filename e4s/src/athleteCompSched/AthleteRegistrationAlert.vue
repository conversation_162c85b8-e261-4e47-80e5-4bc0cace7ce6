<template>
  <div class="athlete-registration-alert">
    <div class="athlete-registration-alert--content">
      <p>
        The registration for <span v-text="getAthleteName"></span> we have
        received from your country's registration system has indicated that the
        membership expired on the <span v-text="getExpiryDate"></span>.
      </p>

      <p>
        If this is not correct, you may continue with your entry but please
        contact your country's registration system to correct/amend <span v-text="getAthleteFirstName"></span>'s
        details.
      </p>

      <p style="font-weight: bold;">
        If it is confirmed <span v-text="getAthleteFirstName"></span>'s has expired, the organiser may revoke your
        entry and no refund given.
      </p>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import type { IAthlete } from "../athlete/athlete-models";
import {getE4sStandardHumanDateOutPut} from "../common/common-service-utils"

@Component({
  name: "athlete-registration-alert",
})
export default class AthleteRegistrationAlert extends Vue {
  @Prop({
    default: () => {
      return {} as IAthlete;
    },
  })
  public readonly athlete!: IAthlete;

  public get getAthleteName(): string {
    return this.athlete.firstName + " " + this.athlete.surName;
  }

  public get getAthleteFirstName(): string {
    return this.athlete.firstName;
  }

  public get getExpiryDate(): string {
    return getE4sStandardHumanDateOutPut(this.athlete.activeEndDate);
  }
}
</script>

<style>
.athlete-registration-alert {
  border-radius: 4px;
  border: solid 2px darkred;
}

.athlete-registration-alert--content {
  padding: 6px;
  background-color: pink;
  display: flex;
  flex-direction: column;
  gap: 6px;
}
</style>
