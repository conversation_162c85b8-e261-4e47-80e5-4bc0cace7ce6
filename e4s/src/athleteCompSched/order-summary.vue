<template>
  <div class="header-action">
      <span>Athlete(s):</span>
      <span v-text="orderSummary.athleteCount"></span>

      <span>Event(s):</span>
      <span v-text="orderSummary.eventCount"></span>

      <!--<span>Price:</span>-->
      <!--<span v-text="orderSummary.totalPrice"></span>-->

  </div>
</template>

<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop} from "vue-property-decorator";
    import type {IOrderSummary} from "./athletecompsched-models";

    @Component({
    })
    export default class OrderSummary extends Vue {
        @Prop({
            default: () => {
                return {} as IOrderSummary;
            },
        })
        public readonly orderSummary: IOrderSummary;
    }
</script>