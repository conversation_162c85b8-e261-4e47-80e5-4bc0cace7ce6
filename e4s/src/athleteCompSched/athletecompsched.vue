<template>
    <div>
        <div v-if="isLoadingInternal" class="schedule-loading-wrapper">
            <loading-spinner></loading-spinner>
        </div>
        <div class="schedule-grid-wrapper">
            <events-card-grid
                    :isLoading="isLoading"
                    :compEvents="eventsProcessed"
                    :selected-athlete="selectedAthlete"
                    :selected-competition="selectedCompetition"
                    :events-server-response="eventsServerResponse"
                    v-on:onSelectedEventGrid="onSelectedGrid"
                    v-on:openShop="openShop"
            >
            </events-card-grid>
        </div>
    </div>
</template>

<script lang="ts">

import Vue from "vue";
import Component from "vue-class-component";
import {Prop, Watch} from "vue-property-decorator";
import {mapState} from "vuex";

import type {IAthlete} from "../athlete/athlete-models";
import {AthleteService} from "../athlete/athlete-service";
import LoadingSpinner from "../common/ui/loading-spinner.vue";
import type {ICompetition, ICompetitionInfo} from "../competition/competition-models"
import AgeInfo from "./ageinfo.vue";
import type {
    IAgeInfo,
    IAthleteCompSchedResponse,
    IAthleteCompSchedRuleEvent,
    ICompShedRuleOptions,
    IPbKey
} from "./athletecompsched-models";
import {AthleteCompSchedService} from "./athletecompsched-service";
import CompRules from "./comp-rules.vue";
import EventsCardGrid from "./events-card-grid.vue";
import {ATH_COMP_SCHED_STORE_CONST, IAthCompSchedStoreState} from "./store/athleteCompSched-store";
import {ENTRY_STORE_CONST, IEntryStoreState} from "../entry/entry-store";

@Component({
    name: "athlete-comp-sched-grid",
    computed: {
        ...mapState(ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME,
            {
                eventsSelected: (state: any) => state.eventsSelected,
                eventsProcessed: (state: any) => state.eventsProcessed,
                isLoading: (state: any) => state.eventsLoading,
                eventsServerResponse: (state: any) => state.eventsServerResponse
            }
        ),
        ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
            selectedAthlete: (state: any) => state.entryForm.selectedAthlete,
            selectedCompetition: (state: any) => state.entryForm.selectedCompetition
        })
    },
    components: {
        "loading-spinner": LoadingSpinner,
        "events-card-grid": EventsCardGrid,
        "comp-rules": CompRules,
        "ageinfo": AgeInfo
    }
})
export default class AthleteCompSchedGrid extends Vue {
    public readonly isLoading: boolean;
    public readonly eventsServerResponse: IAthleteCompSchedResponse;
    public readonly eventsProcessed: IAthleteCompSchedRuleEvent[];
    public readonly eventsSelected: IAthleteCompSchedRuleEvent[];
    public readonly selectedCompetition: ICompetitionInfo;
    public readonly selectedAthlete: IAthlete;

    @Prop() public competition: ICompetition | null;
    @Prop() public athlete: IAthlete | null;

    public edited: boolean = false;
    public isLoadingInternal: boolean = false;
    public athleteCompSchedService: AthleteCompSchedService | null = null;
    public athleteService: AthleteService | null = null;
    public pbKey: IPbKey = {} as IPbKey;
    public athleteCompSchedResponse: IAthleteCompSchedResponse = {} as IAthleteCompSchedResponse;
    public ageInfo: IAgeInfo = {
        ageGroup: "",
        ageGroupId: 0,
        currentAge: 0,
        vetAgeGroup: "",
        vetAgeGroupId: 0
    };
    public compShedRuleOptions: ICompShedRuleOptions = {} as ICompShedRuleOptions;

    public athleteCompSchedRuleEvents: IAthleteCompSchedRuleEvent[] = [];

    public mounted() {
        // Initialize services
        this.athleteCompSchedService = new AthleteCompSchedService();
        this.athleteService = new AthleteService();
        
        // Initialize component state
        this.pbKey = {} as IPbKey;
        this.athleteCompSchedResponse = {} as IAthleteCompSchedResponse;
        this.ageInfo = {
            ageGroup: "",
            currentAge: 0,
            vetAgeGroup: ""
        } as IAgeInfo;
        this.compShedRuleOptions = {} as ICompShedRuleOptions;
    }

    @Watch("competition")
    public onCompetitionChanged(newValue: ICompetition, oldValue: ICompetition) {
        if (newValue && oldValue && newValue.id === oldValue.id) {
            return;
        }
        this.getAthleteCompSched(newValue ? newValue.id : 0, this.athlete ? this.athlete.id : 0);
    }

    @Watch("athlete")
    public onAthleteChanged(athleteNew: IAthlete, athleteOld: IAthlete) {
        if (athleteNew && athleteOld && athleteNew.id === athleteOld.id) {
            return;
        }
        this.getAthleteCompSched(this.competition ? this.competition.id : 0, athleteNew ? athleteNew.id : 0);
    }

    @Watch("isLoading")
    public onIsLoadingChanged(newValue: boolean) {
        console.log("..............AthleteCompSchedGrid.isLoading: " + newValue);
    }

    public getAthleteCompSched(competitionId: number, athleteId: number) {

        if (competitionId === undefined || athleteId === undefined ) {
            return;
        }
        if (this.edited) {
            //  TODO reloading, you will lsoe any current chnages.
            //  e.g. this.userMessage()...which will allow user to continue, set edited = false;
        }
        this.$store.dispatch(
            ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
            ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_INITIALISE_EVENTS
        );

    }

    public onSelectedGrid(compEvent: IAthleteCompSchedRuleEvent) {
        this.edited = true; //  If user browses away, detect here.

        // const isEventAlreadySelected = compEvent.userSelected;
        // const athleteCompSchedData: AthleteCompSchedData = new AthleteCompSchedData();

        if (this.athleteCompSchedService && !this.athleteCompSchedService.isEventAvailableForClicking(compEvent)) {
            console.log("onRowSelected()" + compEvent.ceid + " - " + compEvent.eventid + "...CAN'T");
            return;
        }
        console.log("onRowSelected()" + compEvent.ceid + " - " + compEvent.eventid + "...CAN...userSelected: " + compEvent.userSelected);

        // if (this.athleteCompSchedService.isEventOverSubscribed(compEvent) ) {
        //     //  We can do this from schedule sent up.  If any changes to schedule
        //     //  happen while user "sits" at their screen, response will indicate this.
        //     this.showOverSubscribed = true;
        //     return;
        // }

        this.proceedWithSelection(compEvent);

        // if (isEventAlreadySelected) {
        //
        //     this.isLoadingInternal = true;
        //     this.$store.dispatch(
        //         ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
        //         ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_REMOVE_FROM_SELECTED_NEW,
        //         {
        //             compEvent: compEvent
        //         }
        //     )
        //         .finally(() => {
        //             this.isLoadingInternal = false;
        //         });
        //
        // } else {
        //     this.isLoadingInternal = true;
        //     this.$store.dispatch(
        //         ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
        //         ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_SELECTED,
        //         {
        //             event: compEvent
        //         }
        //     )
        //         .then(() => {
        //             //  Until other parts of local state get moved to.
        //             this.recalc(compEvent)
        //                 .then( () => {
        //                     this.isLoadingInternal = false;
        //                 });
        //         });
        // }
    }

    public proceedWithSelection(compEvent: IAthleteCompSchedRuleEvent) {
        this.edited = true; //  If user browses away, detect here.
        const isEventAlreadySelected = compEvent.userSelected;

        if (isEventAlreadySelected) {

            this.isLoadingInternal = true;
            this.$store.dispatch(
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_REMOVE_FROM_SELECTED_NEW,
                {
                    compEvent: compEvent
                }
            )
                .finally(() => {
                    this.isLoadingInternal = false;
                });

        } else {
            this.isLoadingInternal = true;
            this.$store.dispatch(
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_SELECTED,
                {
                    event: compEvent
                }
            )
                .then(() => {
                    //  Until other parts of local state get moved to.
                    this.recalc(compEvent)
                        .then( () => {
                            this.isLoadingInternal = false;
                        });
                });
        }
    }

    public recalc(compEvent: null | IAthleteCompSchedRuleEvent) {
        console.log("recalc()" + (compEvent ? compEvent.userSelected : "undefined"));
        return this.$store.dispatch(
            ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
            ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_PROCESS_EVENTS,
            {
                compEvent
            }
        );
    }

    public openShop() {
        this.$emit("openShop");
    }

}
</script>
