{"events": [{"ceid": 22222, "description": "", "startdate": "2018-08-25T10:15:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_09.99", "tf": "F", "saleprice": 7, "price": {"curPrice": 10}, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 18, "multiid": 0, "Name": "Discus", "split": -30, "maxgroup": "discus_30", "maxathletes": 16, "ceoptions": "", "eoptions": "{\"min\":\"1.00\", \"max\":\"100.00\",\"unit\":\"mtrs\"}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22233, "description": "", "startdate": "2018-08-25T10:30:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_09.99", "tf": "F", "saleprice": 7, "price": {"curPrice": 12}, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 26, "multiid": 0, "Name": "Triple Jump", "split": 0, "maxgroup": "triple", "maxathletes": 16, "ceoptions": "", "eoptions": "{\"min\":\"3.00\", \"max\":\"25.00\",\"unit\":\"mtrs\"}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22106, "description": "", "startdate": "2018-08-25T11:10:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_09.99", "tf": "T", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 3, "multiid": 0, "Name": "100m", "split": 0, "maxgroup": "100mu15g", "maxathletes": 48, "ceoptions": "{\"xeText\":\" - Heats\", \"xiText\":\" - Heats\"}", "eoptions": "{\"min\":9.60, \"max\":20.00}", "entered": true, "paid": 0, "athleteid": 2}, {"ceid": 22245, "description": "", "startdate": "2018-08-25T11:30:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_09.99", "tf": "F", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 30, "multiid": 0, "Name": "<PERSON>", "split": -3, "maxgroup": "PV_3", "maxathletes": 16, "ceoptions": "", "eoptions": "{\"min\":\"1.00\", \"max\":\"7.00\",\"unit\":\"mtrs\"}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22257, "description": "", "startdate": "2018-08-25T11:45:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_09.99", "tf": "F", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 18, "multiid": 0, "Name": "Discus", "split": 30, "maxgroup": "Discus30", "maxathletes": 16, "ceoptions": "", "eoptions": "{\"min\":\"1.00\", \"max\":\"100.00\",\"unit\":\"mtrs\"}", "entered": true, "paid": 0, "athleteid": 2}, {"ceid": 22269, "description": "", "startdate": "2018-08-25T11:45:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_9.99", "tf": "F", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 22, "multiid": 0, "Name": "Long Jump", "split": -4, "maxgroup": "LJf_4", "maxathletes": 16, "ceoptions": "", "eoptions": "{\"min\":\"1.00\", \"max\":\"10.00\",\"unit\":\"mtrs\",\"class\":\"0,11-13,20,35-47,61-64\", \"unique\":[{\"e\":30}]}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22120, "description": "", "startdate": "2018-08-25T12:25:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_09.99", "tf": "T", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 3, "multiid": 0, "Name": "100m", "split": 0, "maxgroup": "100mu15gfi", "maxathletes": -1, "ceoptions": "{\"xeText\":\" - Finals\", \"fText\":\" - See Round 1\", \"xiText\":\" - Finals\"}", "eoptions": "{\"min\":9.60, \"max\":20.00}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22131, "description": "", "startdate": "2018-08-25T12:45:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_9.09.99", "tf": "T", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 92, "multiid": 0, "Name": "3000m", "split": 0, "maxgroup": "3000m", "maxathletes": 40, "ceoptions": "", "eoptions": "{\"min\":\"5.30.00\", \"max\":\"10.00.00\",\"unit\":\"mins\"}", "entered": true, "paid": 0, "athleteid": 2}, {"ceid": 22142, "description": "", "startdate": "2018-08-25T13:15:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_09.99", "tf": "T", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 89, "multiid": 0, "Name": "75m <PERSON><PERSON><PERSON>", "split": 0, "maxgroup": "75hg", "maxathletes": 24, "ceoptions": "", "eoptions": "{\"min\":\"9.00\", \"max\":\"25.00\",\"unit\":\"secs\"}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22275, "description": "", "startdate": "2018-08-25T13:15:00+01:00", "IsOpen": 0, "pbFormat": "FORMAT_09.99", "tf": "F", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 28, "multiid": 0, "Name": "Hammer", "split": -40, "maxgroup": "Hammer_40", "maxathletes": 16, "ceoptions": "", "eoptions": "{\"min\":\"0.00\", \"max\":\"100.00\",\"unit\":\"mtrs\"}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22287, "description": "", "startdate": "2018-08-25T13:30:00+01:00", "IsOpen": 0, "pbFormat": "FORMAT_0.99", "tf": "F", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 24, "multiid": 0, "Name": "High Jump", "split": 0, "maxgroup": "HJF", "maxathletes": 16, "ceoptions": "", "eoptions": "{\"min\":\"0.05\", \"max\":\"2.50\",\"unit\":\"mtrs\"}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22155, "description": "", "startdate": "2018-08-25T14:15:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_9.09.99", "tf": "T", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 16, "multiid": 0, "Name": "800m", "split": 0, "maxgroup": "800m", "maxathletes": 84, "ceoptions": "", "eoptions": "{\"min\":\"1.20.00\", \"max\":\"4.00.00\",\"unit\":\"mins\"}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22299, "description": "", "startdate": "2018-08-25T14:30:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_09.99", "tf": "F", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 30, "multiid": 0, "Name": "<PERSON>", "split": 3, "maxgroup": "PV3", "maxathletes": 16, "ceoptions": "", "eoptions": "{\"min\":\"1.00\", \"max\":\"7.00\",\"unit\":\"mtrs\"}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22311, "description": "", "startdate": "2018-08-25T14:45:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_09.99", "tf": "F", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 28, "multiid": 0, "Name": "Hammer", "split": 40, "maxgroup": "Hammer40", "maxathletes": 16, "ceoptions": "", "eoptions": "{\"min\":\"0.00\", \"max\":\"100.00\",\"unit\":\"mtrs\"}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22323, "description": "", "startdate": "2018-08-25T14:45:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_09.99", "tf": "F", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 20, "multiid": 0, "Name": "Shotput", "split": 20, "maxgroup": "SP_10", "maxathletes": 16, "ceoptions": "", "eoptions": "{\"min\": 1.00, \"max\": 30.00,\"unit\":\"mtrs\",\"unique\":[{\"e\":36},{\"e\":48}]}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22179, "description": "", "startdate": "2018-08-25T15:30:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_09.99", "tf": "T", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 5, "multiid": 0, "Name": "200m", "split": 0, "maxgroup": "200m", "maxathletes": 80, "ceoptions": "", "eoptions": "{\"min\":\"20.00\", \"max\":\"60.00\",\"unit\":\"secs\"}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22341, "description": "", "startdate": "2018-08-25T15:45:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_9.99", "tf": "F", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 22, "multiid": 0, "Name": "Long Jump", "split": 4, "maxgroup": "LJf4", "maxathletes": 16, "ceoptions": "", "eoptions": "{\"min\":\"1.00\", \"max\":\"10.00\",\"unit\":\"mtrs\",\"class\":\"0,11-13,20,35-47,61-64\", \"unique\":[{\"e\":30}]}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22347, "description": "", "startdate": "2018-08-25T16:15:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_09.99", "tf": "F", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 38, "multiid": 0, "Name": "Javelin", "split": -30, "maxgroup": "Javelin_30", "maxathletes": 16, "ceoptions": "", "eoptions": "{\"min\":\"1.00\", \"max\":\"110.00\",\"unit\":\"mtrs\",\"unique\":[{\"e\":40}]}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22193, "description": "", "startdate": "2018-08-25T16:30:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_9.09.99", "tf": "T", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 14, "multiid": 0, "Name": "1500m", "split": 0, "maxgroup": "1500m", "maxathletes": 100, "ceoptions": "", "eoptions": "{\"min\":\"3.30.00\", \"max\":\"8.00.00\",\"unit\":\"mins\"}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22359, "description": "", "startdate": "2018-08-25T16:30:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_09.99", "tf": "F", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 20, "multiid": 0, "Name": "Shotput", "split": 10, "maxgroup": "SP10", "maxathletes": 16, "ceoptions": "", "eoptions": "{\"min\":\"1.00\", \"max\":\"30.00\",\"unit\":\"mtrs\",\"unique\":[{\"e\":36},{\"e\":48}]}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22205, "description": "", "startdate": "2018-08-25T17:00:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_09.99", "tf": "T", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 7, "multiid": 0, "Name": "300m", "split": 0, "maxgroup": "300m", "maxathletes": 24, "ceoptions": "", "eoptions": "{\"min\":\"30.00\", \"max\":\"80.00\",\"unit\":\"secs\"}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22210, "description": "", "startdate": "2018-08-25T17:15:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_9.09.99", "tf": "T", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 76, "multiid": 0, "Name": "1 Mile", "split": 0, "maxgroup": "mile", "maxathletes": 32, "ceoptions": "", "eoptions": "{\"min\":\"3.30.00\", \"max\":\"8.00.00\",\"unit\":\"mins\"}", "entered": false, "paid": 0, "athleteid": 2}, {"ceid": 22377, "description": "", "startdate": "2018-08-25T17:45:00+01:00", "IsOpen": 1, "pbFormat": "FORMAT_09.99", "tf": "F", "saleprice": 7, "price": 10, "saledate": "2018-08-20T00:00:00+01:00", "name": "_std", "eventid": 38, "multiid": 0, "Name": "Javelin", "split": 30, "maxgroup": "Javelin30", "maxathletes": 16, "ceoptions": "", "eoptions": "{\"min\":\"1.00\", \"max\":\"110.00\",\"unit\":\"mtrs\",\"unique\":[{\"e\":40}]}", "entered": false, "paid": 0, "athleteid": 2}], "ageInfo": {"currentAge": 16, "ageGroupId": 8, "ageGroup": "Under 17", "vetAgeGroupId": 0, "vetAgeGroup": ""}, "rules": {"id": 917, "options": {"maxEvents": 6, "maxCompEvents": 6, "maxField": 2, "maxTrack": 4, "type": "", "unique": [{"text": "Only 1 of 800m, 1500m or Mile", "ids": "16,17,14,15,76,77"}]}}}