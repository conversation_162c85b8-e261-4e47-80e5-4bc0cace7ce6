<template>
  <div>
    <div v-for="(athletePb, index) in athlete.pbInfo">
      <!--            <AthletePbRow :athlete-pb="athletePb"></AthletePbRow>-->
      <div class="row">
        <div class="col s12 m12 l12">
          <div class="perf-sep"></div>
        </div>
      </div>
      <div class="row">
        <div class="col s12 m12 l12">
          <span
            class="e4s-bold event-label"
            v-text="
              (adminMode ? '(' + athletePb.eventid + ') ' : '') +
              athletePb.eventName
            "
          ></span>
          <button
            class="btn waves-effect waves green right"
            v-on:click.stop="openPbEdit(index)"
          >
            Edit
          </button>
        </div>
        <!--                <div class="col s6 m6 l6">-->
        <!--                    <span class="e4s-bold" v-text="(adminMode ? '(' + athletePb.eventid + ') ' : '') + athletePb.eventName"></span>-->
        <!--                </div>-->
        <!--                <div class="col s3 m3 l3">-->
        <!--                    <div class="">-->
        <!--                        <span class="e4s-bold">EP</span>-->
        <!--                        <span v-text="formatSomePb(athletePb.pb, athletePb)"></span>-->
        <!--                        <span v-text="getDefaultDisplayUom(athletePb)"></span>-->
        <!--                    </div>-->
        <!--                </div>-->
        <!--                <div class="col s3 m3 l3">-->
        <!--                    <button class="btn waves-effect waves green right"-->
        <!--                            v-on:click.stop="openPbEdit(index)">-->
        <!--                        Edit-->
        <!--                    </button>-->
        <!--                </div>-->
      </div>

      <div class="row">
        <div class="col s4 m4 l4">
          <div class="">
            <span class="e4s-bold">EP</span>
            <span v-text="formatSomePb(athletePb.pb, athletePb)"></span>
            <!--                        <span v-text="getDefaultDisplayUom(athletePb)"></span>-->
          </div>
        </div>

        <div v-if="showPo10">
          <div class="col s4 m4 l4">
            <div>
              <span class="e4s-bold">SB</span>
              <span v-text="formatSomePb(athletePb.sb, athletePb)"></span>
              <!--                            <span v-text="getDefaultDisplayUom(athletePb)"></span>-->
            </div>
          </div>
          <div class="col s4 m4 l4">
            <div class="">
              <span class="e4s-bold">PB</span>
              <span v-text="formatSomePb(athletePb.pof10pb, athletePb)"></span>
              <!--                            <span v-text="getDefaultDisplayUom(athletePb)"></span>-->
            </div>
          </div>
        </div>
      </div>

      <!--            <div v-if="showPo10">-->
      <!--                <div class="e4s-section-padding-separator"></div>-->

      <!--                <div class="row">-->
      <!--                    <div class="col s6 m6 l6">-->
      <!--                        <div>-->
      <!--                            <span class="e4s-bold">SB</span>-->
      <!--                            <span v-text="formatSomePb(athletePb.sb, athletePb)"></span>-->
      <!--                            <span v-text="getDefaultDisplayUom(athletePb)"></span>-->
      <!--                        </div>-->
      <!--                    </div>-->
      <!--                    <div class="col s6 m6 l6">-->
      <!--                        <div class="">-->
      <!--                            <span class="e4s-bold">PB</span>-->
      <!--                            <span v-text="formatSomePb(athletePb.pof10pb, athletePb)"></span>-->
      <!--                            <span v-text="getDefaultDisplayUom(athletePb)"></span>-->
      <!--                        </div>-->
      <!--                    </div>-->
      <!--                </div>-->

      <!--            </div>-->

      <div class="row">
        <div class="col s12 m12 l12">
          <div class="perf-sep-bottom"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { IAthletePb, IAthleteSummary } from "../../../athlete/athlete-models";
// import AthletePbRow from "./athlete-pb-row.vue";
import * as R from "ramda";
import { PBService } from "../../pb-service";

const pbService: PBService = new PBService();

@Component({
  name: "athlete-pb-table",
  components: {
    // AthletePbRow
  },
})
export default class AthletePbTable extends Vue {
  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly athlete: IAthleteSummary;

  @Prop({ default: false }) public readonly adminMode: boolean;

  public pbService: PBService = pbService;

  public formatSomePb(pb: number | string, athletePb: IAthletePb): string {
    let pbLocal;
    if (typeof pb === "string") {
      const pbNumber = pb.toString().replace(/\s/g, "");
      pbLocal = parseFloat(pbNumber);

      // const testValue = "\u000011.43";
      //  remove spaces from testValue and convert to number
      // const testValueNumber = parseFloat(
      //   testValue.toString().replace(/\s/g, "")
      // );
    } else {
      pbLocal = pb;
    }

    if (pbLocal === 0) {
      return "N/A";
    }

    if (athletePb.uomInfo.type === "T") {
      const pattern = athletePb.uomInfo.options[0].pattern;
      return (
        this.pbService.convertSecondsToUserFormat(pbLocal, pattern) +
        this.getDefaultDisplayUom(athletePb)
      );
    } else {
      return pb.toString() + this.getDefaultDisplayUom(athletePb);
    }
  }

  public getDefaultDisplayUom(athletePb: IAthletePb): string {
    return athletePb.uomInfo.options[0].short;
  }

  public get showPo10(): boolean {
    return this.pbService.hasAccessToPo10(this.athlete);
  }

  public openPbEdit(index: number) {
    this.$emit("editPb", R.clone(this.athlete.pbInfo[index]));
  }
}
</script>

<style scoped>
.event-label {
  font-size: 1.5em;
  color: #5d5d5d;
}

.perf-sep {
  padding: 0.5rem 0 0.5rem 0;
  border-top: 1px solid #bdd6ef;
}

.perf-sep-bottom {
  padding-bottom: 0.5em;
}
</style>
