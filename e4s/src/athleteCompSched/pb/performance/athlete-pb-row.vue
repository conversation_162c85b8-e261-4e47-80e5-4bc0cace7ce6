<template functional>
    <div class="row">
        <span class="col " v-text="'(' + props.athletePb.eventid + ') ' + props.athletePb.eventName"></span>
        <span class="col " v-text="'(' + props.athletePb.eventid + ') ' + props.athletePb.eventName"></span>
    </div>
</template>

<script lang="ts">
    import {IAthleteSummary} from "../../../athlete/athlete-models"

    export default {
        props: ["athletePb"],
        methods: {
            displayPo10: (athlete: IAthleteSummary) => {
                if (athlete.aocode !== "EA" ) {
                    return false;
                }
                return athlete.URN && athlete.URN.toString().length > 0;
            }
        }
    }
</script>

