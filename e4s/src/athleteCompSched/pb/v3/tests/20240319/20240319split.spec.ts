import { IEditPbV3Props } from "../../../pb-models";
import { factoryPbEditTimeV3 } from "../../time/factory-pb-edit-time-v3";
import { simpleClone } from "../../../../../common/common-service-utils";
import { genericValidationEstimatedPerformance } from "../../pb-service-v3";

const editTimePbV3Props: IEditPbV3Props = {
  athleteCompSchedMini: {
    split: -11.2,
  },
  performance: {
    id: 141219,
    wind: 0,
    info: "",
    ageGroup: "U13",
    eventName: "100m",
    eventType: "T",
    tf: "T",
    min: 9.6,
    max: 20,
    curAgeGroup: "U20",
    athleteId: 160647,
    eventId: 4,
    sb: 0,
    pb: 14.1,
    sbAchieved: "",
    pbAchieved: "2019-07-14",
    pbText: "!#!14.10",
    sbText: "!#!0",
    perf: 14.1,
    trackedPB: true,
    limits: {
      min: 9,
      max: 20,
    },
    uom: [
      {
        pattern: "s.SS",
        text: "seconds",
        short: "s",
        uomType: "T",
      },
    ],
    perfText: "!#!14.10",
  },
} as any as IEditPbV3Props;

describe("factoryPbEditTimeV3", () => {
  test("validate", () => {
    const pbEditTimeV3 = factoryPbEditTimeV3();

    pbEditTimeV3.init(simpleClone(editTimePbV3Props));

    let res;

    pbEditTimeV3.convertUserInputToSeconds();
    res = pbEditTimeV3.validate();

    expect(pbEditTimeV3.state.outputSeconds).toBe(14.1);
    expect(res).toBe(false);
    expect(Object.keys(pbEditTimeV3.state.errors).length).toBe(1);

    pbEditTimeV3.state.convertedSecondsToHMS = {
      hours: 0,
      minutes: 0,
      seconds: 12,
      hundredths: 0,
    };
    pbEditTimeV3.convertUserInputToSeconds();
    res = pbEditTimeV3.validate();
    expect(res).toBe(false);
    expect(Object.keys(pbEditTimeV3.state.errors).length).toBe(1);
    expect(pbEditTimeV3.state.outputSeconds).toBe(12);
    expect(pbEditTimeV3.state.errors.SPLIT_OVER).toBe(
      "EP 12 cannot be greater than: 11.2."
    );

    pbEditTimeV3.state.convertedSecondsToHMS = {
      hours: 0,
      minutes: 0,
      seconds: 11,
      hundredths: 0,
    };
    pbEditTimeV3.convertUserInputToSeconds();
    res = pbEditTimeV3.validate();
    expect(pbEditTimeV3.state.outputSeconds).toBe(11);
    expect(
      pbEditTimeV3.state.editTimePbV3Props.athleteCompSchedMini.split
    ).toBe(-11.2);
    expect(res).toBe(true);
    expect(Object.keys(pbEditTimeV3.state.errors).length).toBe(0);

    const newRes = genericValidationEstimatedPerformance(
      11,
      editTimePbV3Props.athleteCompSchedMini,
      editTimePbV3Props.performance
    );
    // expect(newRes).toBe(222)
    expect(Object.keys(newRes).length).toBe(0);

    pbEditTimeV3.state.convertedSecondsToHMS = {
      hours: 0,
      minutes: 0,
      seconds: 1.91,
      hundredths: 0,
    };
    pbEditTimeV3.convertUserInputToSeconds();
    res = pbEditTimeV3.validate();
    expect(pbEditTimeV3.state.outputSeconds).toBe(1.91);
    expect(
      pbEditTimeV3.state.editTimePbV3Props.athleteCompSchedMini.split
    ).toBe(-11.2);
    expect(res).toBe(false);
    expect(Object.keys(pbEditTimeV3.state.errors).length).toBe(1);

    expect(Object.keys(newRes).length).toBe(0);

    // expect(
    //   genericValidationEstimatedPerformance(
    //     1.91,
    //     editTimePbV3Props.athleteCompSchedMini,
    //     editTimePbV3Props.performance
    //   )
    // ).toBe(11);
    // expect(pbEditTimeV3.state.errors.SPLIT_OVER).toBe(
    //   "PB 12 cannot be greater than split: 11.2."
    // );

    // pbEditTimeV3.state.convertedSecondsToHMS = {
    //   hours: 0,
    //   minutes: 3,
    //   seconds: 12,
    //   hundredths: 0,
    // };
    // pbEditTimeV3.convertUserInputToSeconds();
    // res = pbEditTimeV3.validate();
    // expect(res).toBe(false);
    // expect(Object.keys(pbEditTimeV3.state.errors).length).toBe(1);
    // expect(pbEditTimeV3.state.outputSeconds).toBe(192);
    // expect(pbEditTimeV3.state.errors.MIN_MAX).toBe(
    //   "EP must be between 40.00 and 02:00.00 (40secs and 120secs)."
    // );

    // pbEditTimeV3 = factoryPbEditTimeV3();
    // pbEditTimeV3.init(simpleClone(editTimePbV3Props));
    //
    // pbEditTimeV3.state.convertedSecondsToHMS = {
    //   hours: 0,
    //   minutes: 3,
    //   seconds: 99,
    //   hundredths: 0,
    // };
    // pbEditTimeV3.convertUserInputToSeconds();
    // res = pbEditTimeV3.validate();
    // expect(res).toBe(false);
    // expect(Object.keys(pbEditTimeV3.state.errors).length).toBe(2);
    // expect(pbEditTimeV3.state.outputSeconds).toBe(279);
    // expect(pbEditTimeV3.state.errors.RANGE_SECS).toBe(
    //   "Seconds must be between 0 and 59."
    // );
    // expect(pbEditTimeV3.state.errors.MIN_MAX).toBe(
    //   "EP must be between 40.00 and 02:00.00 (40secs and 120secs)."
    // );

    // pbEditTimeV3 = factoryPbEditTimeV3();
    // pbEditTimeV3.init(simpleClone(editTimePbV3Props));
    //
    // pbEditTimeV3.state.convertedSecondsToHMS = {
    //   hours: 0,
    //   minutes: -3,
    //   seconds: -1,
    //   hundredths: 0,
    // };
    // pbEditTimeV3.convertUserInputToSeconds();
    // res = pbEditTimeV3.validate();
    // expect(res).toBe(false);
    // expect(pbEditTimeV3.state.outputSeconds).toBe(-181);
    // expect(pbEditTimeV3.state.errors.ZERO).toBe("PB must be greater than 0.");
  });

  // test("test split OK", () => {
  //   let pbEditTimeV3 = factoryPbEditTimeV3();
  //   let res;
  //   pbEditTimeV3 = factoryPbEditTimeV3();
  //   pbEditTimeV3.init(simpleClone(editTimePbV3Props));
  //
  //   pbEditTimeV3.state.editTimePbV3Props.athleteCompSchedMini.split = 90;
  //
  //   expect(pbEditTimeV3.state.editTimePbV3Props.performance.limits.min).toBe(
  //     40
  //   );
  //   expect(pbEditTimeV3.state.editTimePbV3Props.performance.limits.max).toBe(
  //     120
  //   );
  //
  //   pbEditTimeV3.state.convertedSecondsToHMS = {
  //     hours: 0,
  //     minutes: 1,
  //     seconds: 31,
  //     hundredths: 0,
  //   };
  //   pbEditTimeV3.convertUserInputToSeconds();
  //   res = pbEditTimeV3.validate();
  //
  //   // expect(pbEditTimeV3.state.errors).toBe("PB must be greater than 0.");
  //   expect(res).toBe(true);
  //   expect(pbEditTimeV3.state.outputSeconds).toBe(91);
  // });

  // test("test split below", () => {
  //   let pbEditTimeV3 = factoryPbEditTimeV3();
  //   let res;
  //   pbEditTimeV3 = factoryPbEditTimeV3();
  //   pbEditTimeV3.init(simpleClone(editTimePbV3Props));
  //
  //   pbEditTimeV3.state.editTimePbV3Props.athleteCompSchedMini.split = 90;
  //
  //   expect(pbEditTimeV3.state.editTimePbV3Props.performance.limits.min).toBe(
  //     40
  //   );
  //   expect(pbEditTimeV3.state.editTimePbV3Props.performance.limits.max).toBe(
  //     120
  //   );
  //
  //   pbEditTimeV3.state.convertedSecondsToHMS = {
  //     hours: 0,
  //     minutes: 1,
  //     seconds: 19,
  //     hundredths: 0,
  //   };
  //   pbEditTimeV3.convertUserInputToSeconds();
  //   expect(pbEditTimeV3.state.outputSeconds).toBe(79);
  //
  //   res = pbEditTimeV3.validate();
  //   expect(res).toBe(false);
  //   expect(pbEditTimeV3.state.errors.SPLIT_UNDER).toBe(
  //     "PB 79 cannot be less than split: 90."
  //   );
  // });

  // test("test split over", () => {
  //   let pbEditTimeV3 = factoryPbEditTimeV3();
  //   let res;
  //   pbEditTimeV3 = factoryPbEditTimeV3();
  //   pbEditTimeV3.init(simpleClone(editTimePbV3Props));
  //
  //   pbEditTimeV3.state.editTimePbV3Props.athleteCompSchedMini.split = -90;
  //
  //   expect(pbEditTimeV3.state.editTimePbV3Props.performance.limits.min).toBe(
  //     40
  //   );
  //   expect(pbEditTimeV3.state.editTimePbV3Props.performance.limits.max).toBe(
  //     120
  //   );
  //
  //   pbEditTimeV3.state.convertedSecondsToHMS = {
  //     hours: 0,
  //     minutes: 1,
  //     seconds: 31,
  //     hundredths: 0,
  //   };
  //   pbEditTimeV3.convertUserInputToSeconds();
  //   expect(pbEditTimeV3.state.outputSeconds).toBe(91);
  //
  //   res = pbEditTimeV3.validate();
  //   expect(res).toBe(false);
  //   expect(pbEditTimeV3.state.errors.SPLIT_OVER).toBe(
  //     "PB 91 cannot be greater than split: 90."
  //   );
  // });
});
