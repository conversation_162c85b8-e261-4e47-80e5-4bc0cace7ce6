<template>
  <div class="e4s-flex-column">
    <div class="e4s-flex-row e4s-flex-row--end">
      <div class="edit-pb-v3--cell edit-pb-v3--header">m</div>
      <div class="edit-pb-v3--cell edit-pb-v3--header">cm</div>
    </div>

    <div class="e4s-flex-row e4s-flex-row--end">
      <FieldNumberV2
        :value="pbEditLengthV3.state.convertedLengthToMCM.metres"
        @input="pbEditLengthV3.onUserChangeMetres($event)"
        @keyUp="pbEditLengthV3.onUserChangeMetres($event)"
        class="edit-pb-v3--cell edit-pb-v3--input e4s-square--right"
        :min="0"
      />
      <FieldNumberV2
        :value="pbEditLengthV3.state.convertedLengthToMCM.centimetres"
        @input="pbEditLengthV3.onUserChangeCentimetres($event)"
        @keyUp="pbEditLengthV3.onUserChangeCentimetres($event)"
        class="edit-pb-v3--cell edit-pb-v3--input e4s-square--left"
        :min="0"
        :max="99"
      />
    </div>

    <div class="e4s-flex-column e4s-info-text--warn">
      <div
        v-for="(err, prop) in pbEditLengthV3.state.errors"
        :key="prop"
        v-text="err"
      ></div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  SetupContext,
  watch,
} from "vue";
import FieldNumberV2 from "../../../../common/ui/layoutV2/fields/field-number-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { usePbEditLengthV3 } from "./useEditLengthPbV3";
import { IEditPbV3Props } from "../../pb-models";

export default defineComponent({
  name: "EditLengthPbV3",
  components: { ButtonGenericV2, FieldNumberV2 },
  props: {
    editTimePbV3Props: {
      type: Object as PropType<IEditPbV3Props>,
      required: true,
    },
  },
  setup(props: { editTimePbV3Props: IEditPbV3Props }, context: SetupContext) {
    const pbEditLengthV3 = usePbEditLengthV3(context);
    //  pbEditLengthV3.controller.init(props.editTimePbV3Props);

    watch(
      () => props.editTimePbV3Props,
      (newValue: IEditPbV3Props) => {
        pbEditLengthV3.controller.init(newValue);
      },
      {
        immediate: true,
      }
    );

    function submit() {
      context.emit("input", pbEditLengthV3.controller.getUserOutput());
    }

    return { pbEditLengthV3, submit };
  },
});
</script>
