import * as PbServiceV3 from "../pb-service-v3";
import * as CommonServiceUtils from "../../../../common/common-service-utils";
import { IPbEditBaseV3State } from "../edit-pb-v3-models";
import { getFractionalPartAsWholeNumber } from "../../../../common/common-service-utils";
import { IEditPbV3Props, IPbEditV3Output } from "../../pb-models";
import { PBErrorMessageType } from "../pb-service-v3";

export interface IConvertedLengthToMCM {
  metres: number;
  centimetres: number;
}

export interface IPbEditLengthV3State extends IPbEditBaseV3State {
  convertedLengthToMCM: IConvertedLengthToMCM;
  outputLength: number;
}

export function factoryEditLengthPbV3() {
  const state: IPbEditLengthV3State = {
    ...PbServiceV3.factoryIPbBaseV3State(),
    convertedLengthToMCM: {
      metres: 0,
      centimetres: 0,
    },
    outputLength: 0,
  };

  function init(editTimePbV3Props: IEditPbV3Props) {
    // if (!editTimePbV3Props.athletePb) {
    //   return;
    // }
    // state.athletePb = CommonServiceUtils.simpleClone(
    //   editTimePbV3Props.athletePb
    // );
    state.editTimePbV3Props = CommonServiceUtils.simpleClone(editTimePbV3Props);
    // state.convertedLengthToMCM = convertLengthToMCM(state.athletePb.pb!);
    state.convertedLengthToMCM = convertLengthToMCM(
      PbServiceV3.getDefaultValue(state.editTimePbV3Props.performance)
    );
  }

  function convertLengthToMCM(length: number): IConvertedLengthToMCM {
    return {
      metres: Math.floor(length),
      centimetres: getFractionalPartAsWholeNumber(length),
    };
  }

  function convertUserInputToLength(
    convertedLengthToMCM: IConvertedLengthToMCM
  ): number {
    return Number(
      convertedLengthToMCM.metres +
        "." +
        convertedLengthToMCM.centimetres.toString().padStart(2, "0")
    );
  }

  // function onUserChangeMetres(newValue: number): void {
  //   onUserChange("metres", newValue);
  // }
  //
  // function onUserChangeCentimetres(newValue: number): void {
  //   onUserChange("centimetres", newValue);
  // }

  function onUserChange(
    propName: keyof IConvertedLengthToMCM,
    newValue: number
  ): void {
    state.convertedLengthToMCM[propName] = newValue;
    validate();
    state.outputLength = convertUserInputToLength(state.convertedLengthToMCM);
    state.outputText = getUserOutputText(state.convertedLengthToMCM);
  }

  function getUserOutputText(
    convertedLengthToMCM: IConvertedLengthToMCM
  ): string {
    return (
      convertedLengthToMCM.metres +
      "." +
      convertedLengthToMCM.centimetres.toString().padStart(2, "0")
    );
  }

  function getUserOutput(): IPbEditV3Output {
    return {
      outputPb: state.outputLength,
      outputText: state.outputText,
      errors: state.errors,
    };
  }

  function validate(): boolean {
    let errors: Partial<Record<PBErrorMessageType, string>> = {};
    const inputResultAsLength = convertUserInputToLength(
      state.convertedLengthToMCM
    );
    errors = PbServiceV3.genericValidationEstimatedPerformance(
      inputResultAsLength,
      state.editTimePbV3Props.athleteCompSchedMini,
      state.editTimePbV3Props.performance
    );

    if (
      state.convertedLengthToMCM.metres < 0 ||
      state.convertedLengthToMCM.centimetres < 0
    ) {
      errors.NEGATIVE = "No negative values allowed.";
    }

    state.errors = errors;
    state.isValid = Object.keys(state.errors).length === 0;
    return state.isValid;
  }

  return {
    state,
    init,
    convertLengthToMCM,
    convertUserInputToLength,
    onUserChange,
    // onUserChangeMetres,
    // onUserChangeCentimetres,
    getUserOutputText,
    getUserOutput,
  };
}
