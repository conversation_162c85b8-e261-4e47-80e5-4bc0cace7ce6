import { reactive, SetupContext } from "vue";
import { factoryEditLengthPbV3 } from "./factory-edit-length-pb-v3";

export function usePbEditLengthV3(context: SetupContext) {
  const controller = factoryEditLengthPbV3();
  const state = reactive(controller.state);

  function onUserChangeMetres(newValue: number): void {
    controller.onUserChange("metres", newValue);
    emitInput();
  }

  function onUserChangeCentimetres(newValue: number): void {
    controller.onUserChange("centimetres", newValue);
    emitInput();
  }

  function emitInput(): void {
    context.emit("input", controller.getUserOutput());
  }

  return { state, controller, onUserChangeMetres, onUserChangeCentimetres };
}
