<template>
  <div class="e4s-flex-column e4s-gap--large" id="editPbV3">
    <!--    <div style="display: flex; align-items: center; justify-content: center">-->
    <!--      Sizes: {{ sizes }}-->
    <!--    </div>-->

    <div class="e4s-flex-row e4s-justify-flex-space-between e4s-header--400">
      <div class="e4s-flex-row e4s-gap--standard">
        <span v-text="editPbV3InputProp.athleteMini.firstName"></span>
        <span v-text="editPbV3InputProp.athleteMini.surName"></span>
      </div>

      <span v-text="editPbV3InputProp.performance.eventName"></span>
    </div>

    <hr class="dat-e4s-hr-only" />

    <div class="e4s-flex-row e4s-gap--standard">
      Enter the estimated performance you will be seeded on.
    </div>

    <div
      class="e4s-flex-column e4s-full-width e4s-gap--standard"
      v-if="
        pbControllerV3.state.powerOfTen.seasonsBest.has ||
        pbControllerV3.state.powerOfTen.personalBest.has
      "
    >
      <div
        class="e4s-flex-row e4s-flex-center e4s-justify-flex-space-between"
        v-if="pbControllerV3.state.powerOfTen.seasonsBest.has"
      >
        <div class="e4s-flex-row e4s-gap--standard">
          <span>Seasons Best</span>
          <span
            v-text="pbControllerV3.controller.getPerfForDisplay('sbText')"
          ></span>
          <PowerOfTenLinkV2
            :urn="pbControllerV3.state.editPbV3InputProp.athleteMini.URN"
            :athlete-ao-code="
              pbControllerV3.state.editPbV3InputProp.athleteMini.aocode
            "
            :show-urn-link="false"
          />
        </div>

        <ButtonGenericV2
          text="Use"
          button-type="secondary"
          v-on:click="pbControllerV3.usePowerOfTenSeasonBest()"
        />
      </div>

      <div
        class="e4s-flex-row e4s-flex-center e4s-justify-flex-space-between"
        v-if="pbControllerV3.state.powerOfTen.personalBest.has"
      >
        <div class="e4s-flex-row e4s-gap--standard">
          <span>Personal Best:</span>
          <span
            v-text="pbControllerV3.controller.getPerfForDisplay('pbText')"
          ></span>
          <PowerOfTenLinkV2
            :urn="pbControllerV3.state.editPbV3InputProp.athleteMini.URN"
            :athlete-ao-code="
              pbControllerV3.state.editPbV3InputProp.athleteMini.aocode
            "
            :show-urn-link="false"
          />
        </div>

        <ButtonGenericV2
          text="Use"
          button-type="secondary"
          v-on:click="pbControllerV3.usePowerOfTenPersonalBest()"
        />
      </div>
    </div>

    <div class="e4s-flex-row e4s-flex-row--end">
      <EditTimePbV3
        v-if="pbControllerV3.state.isTypeTime"
        :edit-time-pb-v3-props="pbControllerV3.getEditTimePbV3Props.value"
        v-on:input="pbControllerV3.controller.userInputChanged"
      />

      <EditLengthPbV3
        v-if="!pbControllerV3.state.isTypeTime"
        :edit-time-pb-v3-props="pbControllerV3.getEditTimePbV3Props.value"
        v-on:input="pbControllerV3.controller.userInputChanged"
      />
    </div>

    <hr class="dat-e4s-hr-only" />

    <div class="e4s-flex-row e4s-justify-flex-space-between">
      <slot name="button-cancel">
        <ButtonGenericV2
          text="Cancel"
          button-type="secondary"
          v-on:click="pbControllerV3.cancel"
        />
      </slot>

      <ButtonGenericV2
        text="Save"
        v-on:click="pbControllerV3.submit()"
        :disabled="pbControllerV3.isSubmitDisabled.value"
      />
    </div>
    <LoadingSpinnerV2 v-if="pbControllerV3.state.isLoading" />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  PropType,
  ref,
  SetupContext,
  watch,
} from "vue";
import EditTimePbV3 from "./time/EditTimePbV3.vue";
import { EditPbV3InputProp, EditPbV3Options } from "./edit-pb-v3-models";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { usePbControllerV3 } from "./usePbControllerV3";
import EditLengthPbV3 from "./length/EditLengthPbV3.vue";
import PowerOfTenLinkV2 from "../../../common/ui/layoutV2/PowerOfTenLinkV2.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";

import {
  getElementWidthAndHeight,
  ObjectWidthAndHeight,
} from "../../../common/common-service-utils";

export default defineComponent({
  name: "EditPbV3",
  components: {
    LoadingSpinnerV2,
    PowerOfTenLinkV2,
    EditLengthPbV3,
    ButtonGenericV2,
    EditTimePbV3,
  },
  props: {
    editPbV3InputProp: {
      type: Object as PropType<EditPbV3InputProp>,
      required: true,
    },
    editPbOptions: {
      type: Object as PropType<EditPbV3Options>,
      default: () => {
        const options: EditPbV3Options = {
          doSubmit: true,
        };
        return options;
      },
    },
  },
  setup(
    props: {
      editPbV3InputProp: EditPbV3InputProp;
      editPbOptions: EditPbV3Options;
    },
    context: SetupContext
  ) {
    const pbControllerV3 = usePbControllerV3(context);
    pbControllerV3.controller.init(
      props.editPbV3InputProp,
      props.editPbOptions
    );

    watch(props.editPbV3InputProp, (newValue, oldValue) => {
      pbControllerV3.controller.init(
        props.editPbV3InputProp,
        props.editPbOptions
      );
    });

    const sizes = ref({
      athleteForm: {
        width: 0,
        height: 0,
      },
      body: {
        width: 0,
        height: 0,
      },
      editLengthPbV3: {
        width: 0,
        height: 0,
      },
    });

    onMounted(() => {
      setSizes();

      //  set the focus to the 1st input field found with div id "editPbV3"
      const input = document.getElementById("editPbV3")?.querySelector("input");
      if (input) {
        input.focus();
      }
    });

    function setSizes() {
      //  @ts-ignore
      sizes.value = getElementWH();
    }
    function getElementWH() {
      const bodyWH: ObjectWidthAndHeight = getElementWidthAndHeight("body");
      const athleteWH: ObjectWidthAndHeight =
        getElementWidthAndHeight("#athlete-form");
      const editPbV3: ObjectWidthAndHeight =
        getElementWidthAndHeight("#editPbV3");

      return {
        bodyWH,
        athleteWH,
        editPbV3,
      };
    }

    return { pbControllerV3, sizes };
  },
});
</script>

<style>
.edit-pb-v3--cell {
  width: 60px;
}

.edit-pb-v3--header {
  text-align: center;
  font: var(--e4s-header--400) !important;
}

.edit-pb-v3--input {
  text-align: right;
  font: var(--e4s-header--400) !important;
}
</style>
