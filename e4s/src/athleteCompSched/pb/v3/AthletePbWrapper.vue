<template>
  <div class="athlete-pb-route--body">
    <LoadingSpinnerV2 v-if="athleteState.isLoading.value" />
    <EditPbV3
      v-if="editPbV3InputProp.athleteMini.id > 0"
      :edit-pb-v3-input-prop="editPbV3InputProp"
      @cancel="pbCancel"
      @submitRoute="pbSaved"
    />
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  SetupContext,
} from "vue";
import {
  useConfigController,
  useConfigStore,
} from "../../../config/useConfigStore";
import { useAthleteState } from "../../../athlete/v2/form/useAthleteState";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import { EditPbV3InputProp, IPbEditV3RouteOutput } from "./edit-pb-v3-models";
import EditPbV3 from "./EditPbV3.vue";
import { factoryEditPbV3InputProp } from "./pb-service-v3";
import { IPbEditV3Output } from "../pb-models";

export default defineComponent({
  name: "AthletePbWrapper",
  props: {
    entryId: {
      type: Number,
      required: true,
    },
  },
  components: { EditPbV3, LoadingSpinnerV2 },
  setup(props: { entryId: number }, context: SetupContext) {
    const configStore = useConfigStore();
    const configController = useConfigController();

    const athleteState = useAthleteState();

    const editPbV3InputProp = ref<EditPbV3InputProp>(
      factoryEditPbV3InputProp()
    );

    const defaultAo = computed(() => {
      return configStore.configApp.defaultao;
    });

    const aos = computed(() => {
      return configStore.configApp.aos;
    });

    const entryId = props.entryId;

    if (entryId > 0) {
      loadData();
    }

    function loadData() {
      athleteState.getEntryPerf(entryId).then((response) => {
        if (response) {
          const editPbV3InputPropInit: EditPbV3InputProp =
            factoryEditPbV3InputProp();

          editPbV3InputPropInit.athleteMini = response.athleteMini;
          editPbV3InputPropInit.athleteCompSchedMini = {
            entryId: entryId,
            split: response.compEventMini.split,
          };
          editPbV3InputPropInit.performance = response.performance;

          editPbV3InputProp.value = editPbV3InputPropInit;
        }
      });
    }

    function pbSaved(pbEditV3RouteOutput: IPbEditV3RouteOutput) {
      loadData();
      context.emit("submitted", pbEditV3RouteOutput);
    }

    function pbCancel(pbEditV3Output: IPbEditV3Output) {
      context.emit("cancel", {
        type: "pbCancel",
        entryId: entryId,
        pb: pbEditV3Output,
      });
    }

    return {
      configController,
      defaultAo,
      aos,
      athleteState,
      editPbV3InputProp,
      pbSaved,
      pbCancel,
    };
  },
});
</script>

<style scoped>
.athlete-pb-route--body {
  background-color: white;
  opacity: 1;
}
</style>
