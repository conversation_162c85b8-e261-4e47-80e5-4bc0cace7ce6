import {
  EditPbV3InputProp,
  EditPbV3Options,
  IPbEditV3RouteOutput,
  IPerfInfo,
} from "./edit-pb-v3-models";
import * as PbServiceV3 from "./pb-service-v3";
import { simpleClone } from "../../../common/common-service-utils";
import { IPbEditV3Output } from "../pb-models";
import { messageDispatchHelper } from "../../../user-message/user-message-store";
import { AthleteData } from "../../../athlete/athlete-data";
import { handleResponseMessages } from "../../../common/handle-http-reponse";
import {
  factoryEditPbV3InputProp,
  getPerfTextForDisplay,
} from "./pb-service-v3";

export interface IFactoryPbControllerState {
  isLoading: boolean;
  isDirty: boolean;
  doSubmit: boolean; //  If false, just emits the result, something else will submit.
  editPbV3InputProp: EditPbV3InputProp;
  pbEditV3Output: IPbEditV3Output;
  isTypeTime: boolean;
  isSplitOk: boolean;
  powerOfTen: {
    seasonsBest: {
      has: boolean;
      displayText: string;
    };
    personalBest: {
      has: boolean;
      displayText: string;
    };
  };
}

export function factoryPbController() {
  const state: IFactoryPbControllerState = {
    isLoading: false,
    isDirty: false,
    doSubmit: true,
    isSplitOk: true,
    editPbV3InputProp: factoryEditPbV3InputProp(),
    pbEditV3Output: {
      outputPb: 0,
      outputText: "",
      errors: {},
    },
    powerOfTen: {
      seasonsBest: {
        has: false,
        displayText: "",
      },
      personalBest: {
        has: false,
        displayText: "",
      },
    },
    get isTypeTime(): boolean {
      return PbServiceV3.isTypeTime(this.editPbV3InputProp.performance);
    },
  };

  function init(
    editPbV3InputProp: EditPbV3InputProp,
    editPbV3Options: EditPbV3Options
  ) {
    state.editPbV3InputProp = simpleClone(editPbV3InputProp);
    state.doSubmit = editPbV3Options.doSubmit;

    state.powerOfTen.seasonsBest.has =
      hasPowerOfTenSeasonsBest(editPbV3InputProp);

    if (state.powerOfTen.seasonsBest.has) {
      state.powerOfTen.seasonsBest.displayText =
        editPbV3InputProp.performance.sbText;
    }

    state.powerOfTen.personalBest.has =
      hasPowerOfTenPersonalBest(editPbV3InputProp);
    if (state.powerOfTen.personalBest.has) {
      state.powerOfTen.personalBest.displayText =
        editPbV3InputProp.performance.pbText;
    }
  }

  function hasPowerOfTenSeasonsBest(
    editPbV3InputProp: EditPbV3InputProp
  ): boolean {
    return editPbV3InputProp.performance.sb > 0;
  }

  function hasPowerOfTenPersonalBest(
    editPbV3InputProp: EditPbV3InputProp
  ): boolean {
    return editPbV3InputProp.performance.pb > 0;
  }

  function userInputChanged(pbEditV3Output: IPbEditV3Output) {
    state.pbEditV3Output = simpleClone(pbEditV3Output);
    state.isDirty = true;
    state.isSplitOk = isSplitOk();
  }

  function isSplitOk() {
    if (!state.editPbV3InputProp.athleteCompSchedMini.split) {
      return true;
    }
    if (state.editPbV3InputProp.athleteCompSchedMini.split === 0) {
      return true;
    }

    // if split is negative, then make sure PB is below split else above split.
    return state.editPbV3InputProp.athleteCompSchedMini.split < 0
      ? state.pbEditV3Output.outputPb <
          Math.abs(state.editPbV3InputProp.athleteCompSchedMini.split)
      : state.pbEditV3Output.outputPb >
          state.editPbV3InputProp.athleteCompSchedMini.split;
  }

  function submit(): Promise<IPbEditV3RouteOutput | null> {
    const errors = PbServiceV3.genericValidationEstimatedPerformance(
      state.pbEditV3Output.outputPb,
      state.editPbV3InputProp.athleteCompSchedMini,
      state.editPbV3InputProp.performance
    );

    if (Object.keys(errors).length > 0) {
      messageDispatchHelper(Object.values(errors).join(" "));
      // messageDispatchHelper("Invalid estimated performance can not be saved.");
      return Promise.reject();
    }

    const performance: IPerfInfo = simpleClone(
      state.editPbV3InputProp.performance
    );
    performance.perf = state.pbEditV3Output.outputPb;
    performance.perfText = state.pbEditV3Output.outputText;

    const pbEditV3RouteOutput: IPbEditV3RouteOutput = {
      entryId: state.editPbV3InputProp.athleteCompSchedMini.entryId,
      perfInfo: performance,
    };

    //  Let the caller "consuming component" submit the data.
    if (!state.doSubmit) {
      return Promise.resolve(pbEditV3RouteOutput);
    }

    state.isLoading = true;
    const prom = new AthleteData().submitAthletePbV2(
      state.editPbV3InputProp.athleteCompSchedMini.entryId,
      performance
    );
    handleResponseMessages(prom);
    return prom
      .then((resp) => {
        if (resp.errNo === 0) {
          messageDispatchHelper("Estimated performance saved.");
          return pbEditV3RouteOutput;
        }
        return null;
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  function getPerfForDisplay(
    propName: keyof Pick<IPerfInfo, "sbText" | "pbText">
  ): string {
    // const perfText = state.editPbV3InputProp.performance[propName];
    // return perfText.replace(ATHLETE_PERF_TEXT_PREFIX, "");

    const textDisplay = getPerfTextForDisplay(
      propName,
      state.editPbV3InputProp.performance
    );

    console.log(
      "getPerfForDisplay: " + propName + "=" + textDisplay,
      state.editPbV3InputProp.performance
    );
    return textDisplay;
  }

  return {
    state,
    init,
    userInputChanged,
    submit,
    getPerfForDisplay,
  };
}
