import { factoryPbEditTimeV3 } from "./factory-pb-edit-time-v3";
import {
  convertSecondsToUserDisplayString,
  simpleClone,
} from "../../../../common/common-service-utils";
import { IEditPbV3Props } from "../../pb-models";
import { getFieldPatternMap } from "../pb-service-v3";

const editTimePbV3Props: IEditPbV3Props = {
  athleteCompSchedMini: {
    split: 0,
  },
  performance: {
    perf: 65,
    perfText: "!#!1.05.00",
    pb: 65,
    pbText: "!#!1.05.00",
    pbAchieved: "2021-07-17",
    sb: 68,
    sbText: "!#!1.08.00",
    sbAchieved: null,
    ageGroup: "",
    curAgeGroup: "U20",
    eventName: "400m",
    uom: [
      { pattern: "m.ss.SS", text: "minutes", short: "mins" },
      { pattern: "m:ss.SS", text: "minutes", short: "mins" },
      { pattern: "s.SS", text: "seconds", short: "s" },
    ],
    limits: {
      min: 40,
      max: 120,
    },
  },
} as any as IEditPbV3Props;

describe("factoryPbEditTimeV3", () => {
  test("validate", () => {
    let pbEditTimeV3 = factoryPbEditTimeV3();

    pbEditTimeV3.init(simpleClone(editTimePbV3Props));

    let res;

    pbEditTimeV3.convertUserInputToSeconds();
    res = pbEditTimeV3.validate();

    expect(pbEditTimeV3.state.outputSeconds).toBe(65);
    expect(res).toBe(true);
    expect(Object.keys(pbEditTimeV3.state.errors).length).toBe(0);

    pbEditTimeV3.state.convertedSecondsToHMS = {
      hours: 0,
      minutes: 0,
      seconds: 12,
      hundredths: 0,
    };
    pbEditTimeV3.convertUserInputToSeconds();
    res = pbEditTimeV3.validate();
    expect(res).toBe(false);
    expect(Object.keys(pbEditTimeV3.state.errors).length).toBe(1);
    expect(pbEditTimeV3.state.outputSeconds).toBe(12);
    expect(pbEditTimeV3.state.errors.MIN_MAX).toBe(
      "EP must be between 40.00 and 02:00.00 (40secs and 120secs)."
    );

    pbEditTimeV3.state.convertedSecondsToHMS = {
      hours: 0,
      minutes: 3,
      seconds: 12,
      hundredths: 0,
    };
    pbEditTimeV3.convertUserInputToSeconds();
    res = pbEditTimeV3.validate();
    expect(res).toBe(false);
    expect(Object.keys(pbEditTimeV3.state.errors).length).toBe(1);
    expect(pbEditTimeV3.state.outputSeconds).toBe(192);
    expect(pbEditTimeV3.state.errors.MIN_MAX).toBe(
      "EP must be between 40.00 and 02:00.00 (40secs and 120secs)."
    );

    pbEditTimeV3 = factoryPbEditTimeV3();
    pbEditTimeV3.init(simpleClone(editTimePbV3Props));

    pbEditTimeV3.state.convertedSecondsToHMS = {
      hours: 0,
      minutes: 3,
      seconds: 99,
      hundredths: 0,
    };
    pbEditTimeV3.convertUserInputToSeconds();
    res = pbEditTimeV3.validate();
    expect(res).toBe(false);
    expect(Object.keys(pbEditTimeV3.state.errors).length).toBe(2);
    expect(pbEditTimeV3.state.outputSeconds).toBe(279);
    expect(pbEditTimeV3.state.errors.RANGE_SECS).toBe(
      "Seconds must be between 0 and 59."
    );
    expect(pbEditTimeV3.state.errors.MIN_MAX).toBe(
      "EP must be between 40.00 and 02:00.00 (40secs and 120secs)."
    );

    pbEditTimeV3 = factoryPbEditTimeV3();
    pbEditTimeV3.init(simpleClone(editTimePbV3Props));

    pbEditTimeV3.state.convertedSecondsToHMS = {
      hours: 0,
      minutes: -3,
      seconds: -1,
      hundredths: 0,
    };
    pbEditTimeV3.convertUserInputToSeconds();
    res = pbEditTimeV3.validate();
    expect(res).toBe(false);
    expect(pbEditTimeV3.state.outputSeconds).toBe(-181);
    expect(pbEditTimeV3.state.errors.ZERO).toBe("PB must be greater than 0.");
  });

  test("test split OK", () => {
    let pbEditTimeV3 = factoryPbEditTimeV3();
    let res;
    pbEditTimeV3 = factoryPbEditTimeV3();
    pbEditTimeV3.init(simpleClone(editTimePbV3Props));

    pbEditTimeV3.state.editTimePbV3Props.athleteCompSchedMini.split = 90;

    expect(pbEditTimeV3.state.editTimePbV3Props.performance.limits.min).toBe(
      40
    );
    expect(pbEditTimeV3.state.editTimePbV3Props.performance.limits.max).toBe(
      120
    );

    pbEditTimeV3.state.convertedSecondsToHMS = {
      hours: 0,
      minutes: 1,
      seconds: 31,
      hundredths: 0,
    };
    pbEditTimeV3.convertUserInputToSeconds();
    res = pbEditTimeV3.validate();

    // expect(pbEditTimeV3.state.errors).toBe("PB must be greater than 0.");
    expect(res).toBe(true);
    expect(pbEditTimeV3.state.outputSeconds).toBe(91);
  });

  test("test split below", () => {
    let pbEditTimeV3 = factoryPbEditTimeV3();
    let res;
    pbEditTimeV3 = factoryPbEditTimeV3();
    pbEditTimeV3.init(simpleClone(editTimePbV3Props));

    pbEditTimeV3.state.editTimePbV3Props.athleteCompSchedMini.split = 90;

    expect(pbEditTimeV3.state.editTimePbV3Props.performance.limits.min).toBe(
      40
    );
    expect(pbEditTimeV3.state.editTimePbV3Props.performance.limits.max).toBe(
      120
    );

    pbEditTimeV3.state.convertedSecondsToHMS = {
      hours: 0,
      minutes: 1,
      seconds: 19,
      hundredths: 0,
    };
    pbEditTimeV3.convertUserInputToSeconds();
    expect(pbEditTimeV3.state.outputSeconds).toBe(79);

    res = pbEditTimeV3.validate();
    expect(res).toBe(false);
    expect(pbEditTimeV3.state.errors.SPLIT_UNDER).toBe(
      "EP 79 cannot be less than: 90."
    );
  });

  test("test split over", () => {
    let pbEditTimeV3 = factoryPbEditTimeV3();
    let res;
    pbEditTimeV3 = factoryPbEditTimeV3();
    pbEditTimeV3.init(simpleClone(editTimePbV3Props));

    pbEditTimeV3.state.editTimePbV3Props.athleteCompSchedMini.split = -90;

    expect(pbEditTimeV3.state.editTimePbV3Props.performance.limits.min).toBe(
      40
    );
    expect(pbEditTimeV3.state.editTimePbV3Props.performance.limits.max).toBe(
      120
    );

    pbEditTimeV3.state.convertedSecondsToHMS = {
      hours: 0,
      minutes: 1,
      seconds: 31,
      hundredths: 0,
    };
    pbEditTimeV3.convertUserInputToSeconds();
    expect(pbEditTimeV3.state.outputSeconds).toBe(91);

    res = pbEditTimeV3.validate();
    expect(res).toBe(false);
    expect(pbEditTimeV3.state.errors.SPLIT_OVER).toBe(
      "EP 91 cannot be greater than: 90."
    );
  });

  test("No Min or Max", () => {
    let pbEditTimeV3 = factoryPbEditTimeV3();
    let res;
    pbEditTimeV3 = factoryPbEditTimeV3();
    pbEditTimeV3.init(simpleClone(editTimePbV3Props));

    pbEditTimeV3.state.editTimePbV3Props.athleteCompSchedMini.split = 0;
    pbEditTimeV3.state.editTimePbV3Props.performance.limits.min = 0;
    pbEditTimeV3.state.editTimePbV3Props.performance.limits.max = 0;

    expect(pbEditTimeV3.state.editTimePbV3Props.performance.limits.min).toBe(0);
    expect(pbEditTimeV3.state.editTimePbV3Props.performance.limits.max).toBe(0);

    pbEditTimeV3.state.convertedSecondsToHMS = {
      hours: 0,
      minutes: 1,
      seconds: 31,
      hundredths: 0,
    };
    pbEditTimeV3.convertUserInputToSeconds();
    expect(pbEditTimeV3.state.outputSeconds).toBe(91);

    res = pbEditTimeV3.validate();
    expect(res).toBe(true);
  });

  test("howManyFieldsToshow", () => {
    let pbEditTimeV3 = factoryPbEditTimeV3();

    pbEditTimeV3 = factoryPbEditTimeV3();
    let data;

    data = simpleClone(editTimePbV3Props);
    pbEditTimeV3.init(data);
    expect(pbEditTimeV3.state.howManyFieldsToShow).toBe(3);
    expect(pbEditTimeV3.state.fieldPatternToUse).toBe("m.ss.SS");
    expect(pbEditTimeV3.state.fieldPatternToShowMap.m).toBe("m");
    expect(pbEditTimeV3.state.fieldPatternToShowMap.s).toBe("s");
    expect(pbEditTimeV3.state.fieldPatternToShowMap.S).toBe("S");

    data = simpleClone(editTimePbV3Props);
    data.performance.uom = [
      {
        pattern: "s.SS",
        text: "seconds",
        short: "s",
        uomType: "T",
      },
    ];
    pbEditTimeV3.init(data);
    expect(pbEditTimeV3.state.howManyFieldsToShow).toBe(2);
    expect(pbEditTimeV3.state.fieldPatternToUse).toBe("s.SS");
    expect(pbEditTimeV3.state.fieldPatternToShowMap.s).toBe("s");
    expect(pbEditTimeV3.state.fieldPatternToShowMap.S).toBe("S");

    data = simpleClone(editTimePbV3Props);
    data.performance.uom = [
      { pattern: "s.SS", text: "seconds", short: "s", uomType: "T" },
      { pattern: "s", text: "seconds", short: "s", uomType: "T" },
    ];
    pbEditTimeV3.init(data);
    expect(pbEditTimeV3.state.howManyFieldsToShow).toBe(2);
    expect(pbEditTimeV3.state.fieldPatternToUse).toBe("s.SS");
    expect(pbEditTimeV3.state.fieldPatternToShowMap.s).toBe("s");
    expect(pbEditTimeV3.state.fieldPatternToShowMap.S).toBe("S");

    data = simpleClone(editTimePbV3Props);
    data.performance.uom = [
      { pattern: "s", text: "seconds", short: "s", uomType: "T" },
    ];
    pbEditTimeV3.init(data);
    //  Minimum of 2 fields to show
    expect(pbEditTimeV3.state.howManyFieldsToShow).toBe(2);
    expect(pbEditTimeV3.state.fieldPatternToUse).toBe("s");
    expect(pbEditTimeV3.state.fieldPatternToShowMap.s).toBe("s");
  });

  test("getUserOutputText", () => {
    const pbEditTimeV3 = factoryPbEditTimeV3();

    expect(
      pbEditTimeV3.getUserOutputText({
        hours: 0,
        minutes: 0,
        seconds: 12,
        hundredths: 1,
      })
    ).toBe("12.01");

    expect(
      pbEditTimeV3.getUserOutputText({
        hours: 0,
        minutes: 1,
        seconds: 4,
        hundredths: 1,
      })
    ).toBe("01:04.01");

    expect(
      pbEditTimeV3.getUserOutputText({
        hours: 0,
        minutes: 1,
        seconds: 4,
        hundredths: 15,
      })
    ).toBe("01:04.15");

    expect(
      pbEditTimeV3.getUserOutputText({
        hours: 0,
        minutes: 2,
        seconds: 12,
        hundredths: 1,
      })
    ).toBe("02:12.01");

    expect(
      pbEditTimeV3.getUserOutputText({
        hours: 0,
        minutes: 2,
        seconds: 12,
        hundredths: 0,
      })
    ).toBe("02:12.00");

    expect(
      pbEditTimeV3.getUserOutputText({
        hours: 1,
        minutes: 23,
        seconds: 2,
        hundredths: 0,
      })
    ).toBe("01:23:02.00");
  });

  test("convertSecondsToUserDisplayString", () => {
    expect(convertSecondsToUserDisplayString(0.01)).toBe("00.01");
    expect(convertSecondsToUserDisplayString(0.11)).toBe("00.11");
    expect(convertSecondsToUserDisplayString(12.01)).toBe("12.01");
    expect(convertSecondsToUserDisplayString(180)).toBe("03:00.00");
  });

  test("getFieldPatternMap", () => {
    expect(getFieldPatternMap("s.SS")).toStrictEqual({
      s: "s",
      S: "S",
    });

    expect(getFieldPatternMap("m.ss")).toStrictEqual({
      m: "m",
      s: "s",
    });
  });
});
