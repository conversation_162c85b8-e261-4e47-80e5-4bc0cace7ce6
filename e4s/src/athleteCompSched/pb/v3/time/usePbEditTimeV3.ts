import { reactive, SetupContext } from "vue";
import { factoryPbEditTimeV3 } from "./factory-pb-edit-time-v3";

export interface IPbEditTimeV3Output {
  outputSeconds: number;
  outputText: string;
}

export function usePbEditTimeV3(context: SetupContext) {
  const controller = factoryPbEditTimeV3();
  const state = reactive(controller.state);

  // watch(
  //   () => state.outputSeconds,
  //   (newValue: number, oldValue: number) => {
  //     if (newValue !== oldValue) {
  //       if (state.isValid) {
  //         context.emit("input", controller.getUserOutput());
  //       }
  //     }
  //   }
  // );

  function onUserChangeHours(newValue: number): void {
    controller.onUserChange("hours", newValue);
    emitInput();
  }

  function onUserChangeMinutes(newValue: number): void {
    controller.onUserChange("minutes", newValue);
    emitInput();
  }

  function onUserChangeSeconds(newValue: number): void {
    controller.onUserChange("seconds", newValue);
    emitInput();
  }

  function onUserChangeHundredths(newValue: number): void {
    controller.onUserChange("hundredths", newValue);
    emitInput();
  }

  function emitInput(): void {
    context.emit("input", controller.getUserOutput());
  }

  return {
    state,
    controller,
    onUserChangeHours,
    onUserChangeMinutes,
    onUserChangeSeconds,
    onUserChangeHundredths,
  };
}
