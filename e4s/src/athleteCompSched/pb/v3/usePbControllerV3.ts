import { computed, reactive, SetupContext } from "vue";
import { IEditPbV3Props } from "../pb-models";
import { factoryPbController } from "./pb-controller";

export function usePbControllerV3(context: SetupContext) {
  const controller = factoryPbController();
  const state = reactive(controller.state);

  const getEditTimePbV3Props = computed<IEditPbV3Props>(() => {
    return {
      athleteCompSchedMini: state.editPbV3InputProp.athleteCompSchedMini,
      performance: state.editPbV3InputProp.performance,
    };
  });

  function cancel() {
    context.emit("cancel");
  }

  function usePowerOfTenSeasonBest() {
    state.pbEditV3Output.outputPb = state.editPbV3InputProp.performance.sb;
    state.pbEditV3Output.outputText =
      state.editPbV3InputProp.performance.sbText;

    controller.submit();

    submit();
  }

  function usePowerOfTenPersonalBest() {
    state.pbEditV3Output.outputPb = state.editPbV3InputProp.performance.pb;
    state.pbEditV3Output.outputText =
      state.editPbV3InputProp.performance.pbText;
    submit();
  }

  function submit() {
    controller.submit().then((pbEditV3RouteOutput) => {
      if (pbEditV3RouteOutput) {
        context.emit("submit", state.pbEditV3Output);
        context.emit("submitRoute", pbEditV3RouteOutput);
      }
    });
  }

  const isSplitOk = computed<boolean>(() => {
    if (!state.editPbV3InputProp.athleteCompSchedMini.split) {
      return true;
    }
    if (state.editPbV3InputProp.athleteCompSchedMini.split === 0) {
      return true;
    }

    // if split is negative, ten make sure PB is below split else above split.
    return state.editPbV3InputProp.athleteCompSchedMini.split < 0
      ? state.pbEditV3Output.outputPb <
          Math.abs(state.editPbV3InputProp.athleteCompSchedMini.split)
      : state.pbEditV3Output.outputPb >
          state.editPbV3InputProp.athleteCompSchedMini.split;
  });

  const isSubmitDisabled = computed<boolean>(() => {
    const hasAnyError = Object.keys(state.pbEditV3Output.errors).length > 0;
    return state.isLoading || !state.isDirty || hasAnyError;
  });

  return {
    state,
    controller,
    getEditTimePbV3Props,
    cancel,
    submit,
    usePowerOfTenSeasonBest,
    usePowerOfTenPersonalBest,
    isSubmitDisabled,
    isSplitOk,
  };
}
