<template functional>
    <span v-text="$options.methods.formatUserPb(props.compEvent, props.pbService)"></span>
</template>

<script lang="ts">
    import {IAthleteCompSchedRuleEvent} from "../athletecompsched-models"
    import {PBService} from "../pb-service"

    export default {
        props: ["compEvent"],
        methods: {
            formatUserPb: (compEvent: IAthleteCompSchedRuleEvent, pbService: PBService) => {
                return pbService.getFormattedUserPb(compEvent);
            }
        }
    }
</script>
