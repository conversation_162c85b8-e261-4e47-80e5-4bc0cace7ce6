import { IAthlete } from "../../../athlete/athlete-models";
import { IAthleteCompSched } from "../../athletecompsched-models";
import { IProcessPb } from "../../pb-service";

export interface EditTimePbInputV2 {
  athlete: IAthlete;
  athleteCompSched: IAthleteCompSched;
}

export interface EditTimePbModelStateV2 {
  editTimePbInputV2: EditTimePbInputV2;
  pbUserEntry: string;
  showTimeFields: boolean;
  pbUserEntryMins: string;
  pbUserEntrySecs: string;
  pbUserEntryMille: string;
  processPb: IProcessPb;
  isLoading: boolean;
  pbEditMessage: string;
  isDirty: boolean;
}
