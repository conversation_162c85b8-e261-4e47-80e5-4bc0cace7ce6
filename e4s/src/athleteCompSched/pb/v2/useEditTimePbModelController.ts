import { reactive } from "vue";
import {
  EditTimePbInputV2,
  EditTimePbModelStateV2,
} from "./edit-time-pb-models-v2";
import { AthleteService } from "../../../athlete/athlete-service";
import { simpleClone } from "../../../common/common-service-utils";
import { IProcessPb, PBService } from "../../pb-service";
import { AthleteCompSchedService } from "../../athletecompsched-service";
import { IEventUom } from "../../athletecompsched-models";
import { AthleteData } from "../../../athlete/athlete-data";
import { handleResponseMessages } from "../../../common/handle-http-reponse";
import { messageDispatchHelper } from "../../../user-message/user-message-store";

const athleteService: AthleteService = new AthleteService();
const pbService: PBService = new PBService();
const athleteCompSchedService: AthleteCompSchedService =
  new AthleteCompSchedService();

export function useEditTimePbModelController() {
  const state = reactive<EditTimePbModelStateV2>({
    editTimePbInputV2: {
      athlete: athleteService.factoryGetAthlete(),
      athleteCompSched: athleteCompSchedService.factory(),
    },
    pbUserEntry: "",
    showTimeFields: false,
    pbUserEntryMins: "",
    pbUserEntrySecs: "",
    pbUserEntryMille: "",
    processPb: {
      userText: "",
      isValid: false,
      pb: 0,
      eventUomUsed: {} as IEventUom,
      validationResults: [],
      patternResults: [],
    },
    isLoading: false,
    pbEditMessage: "",
    isDirty: false,
  });

  function init(editTimePbInputV2: EditTimePbInputV2) {
    state.editTimePbInputV2 = simpleClone(editTimePbInputV2);
    state.showTimeFields =
      state.editTimePbInputV2.athleteCompSched.uomType === "T";
    loadUserPb();
  }

  function loadUserPb(): void {
    const perfInfo = state.editTimePbInputV2.athleteCompSched.perfInfo;
    const pbUserEntry = pbService.getAthleteFormattedUserPb(perfInfo);
    if (
      state.showTimeFields &&
      pbUserEntry.length > 0 &&
      perfInfo.uom.length > 0
    ) {
      const pbPlaceholders = pbService.getAthletePbPlaceholders(
        pbUserEntry,
        perfInfo
      );
      state.pbUserEntryMins = pbPlaceholders.m;
      state.pbUserEntrySecs = pbPlaceholders.s;
      state.pbUserEntryMille = pbPlaceholders.S;
    }
    state.pbUserEntry = pbUserEntry;
  }

  function onUserEditedPb(pbUserInput: string) {
    state.pbUserEntry = pbUserInput;
    let processPb: IProcessPb;
    const hasCompEvent = state.editTimePbInputV2.athleteCompSched.ceid > 0;
    const disciplineType = state.editTimePbInputV2.athleteCompSched.uomType;
    // if (hasCompEvent) {
    //   disciplineType = state.editTimePbInputV2.athleteCompSched
    //     .uomType;
    //
    //   athletePb.uomInfo.type = disciplineType;
    //   athletePb.uomInfo.options =
    //     state.editTimePbInputV2.athleteCompSched.perfInfo.uom;
    // }
    const perfInfo = state.editTimePbInputV2.athleteCompSched.perfInfo;
    if (disciplineType === "T") {
      processPb = pbService.processAthletePbFromUserInput(
        state.pbUserEntry,
        perfInfo
      );
    } else {
      const split = hasCompEvent
        ? state.editTimePbInputV2.athleteCompSched.split
        : 0;
      processPb = pbService.convertAthleteUserFieldFormat(
        state.pbUserEntry,
        perfInfo,
        split
      );
    }

    state.processPb = processPb;
    if (
      state.editTimePbInputV2.athleteCompSched.uomType === "T" &&
      state.pbUserEntry.length > 0 &&
      perfInfo.uom.length > 0
    ) {
      const pbPlaceholders = hasCompEvent
        ? pbService.getPbPlaceholders(
            state.pbUserEntry,
            state.editTimePbInputV2.athleteCompSched
          )
        : pbService.getAthletePbPlaceholders(state.pbUserEntry, perfInfo);

      state.pbUserEntryMins = pbPlaceholders.m;
      state.pbUserEntrySecs = pbPlaceholders.s;
      state.pbUserEntryMille = pbPlaceholders.S;
    }

    // if (state.processPb.pb)
    setIsDirty(state.processPb.pb !== perfInfo.perf);

    validate();
  }

  function validate(): boolean {
    state.pbEditMessage = "";

    const athleteCompSched = state.editTimePbInputV2.athleteCompSched;
    const hasCompEvent = athleteCompSched.ceid > 0;
    const perfInfo = state.editTimePbInputV2.athleteCompSched.perfInfo;
    // if (hasCompEvent) {
    //  WTF is this here...something to do with when editing from a comp event?
    // athletePbToProcess.uomInfo.options =
    //   state.editTimePbInputV2.athleteCompSched.perfInfo.uom;
    // }

    if (perfInfo.uom.length === 0) {
      state.pbEditMessage =
        "No unit of measure has been entered for this event, PB can not be entered.";
      console.error("No unit of measure has been entered for this event.");
      return false;
    }

    let processPb: IProcessPb;
    // const hasCompEvent = athleteCompSched.ceid > 0;
    // if (athletePbToProcess.uomInfo.type === "T") {
    if (state.processPb.eventUomUsed.uomType === "T") {
      if (hasCompEvent) {
        processPb = pbService.processPbFromUserInput(
          state.pbUserEntry,
          athleteCompSched
        );
      } else {
        processPb = pbService.processAthletePbFromUserInput(
          state.pbUserEntry,
          perfInfo
        );
      }
    } else {
      if (hasCompEvent) {
        processPb = pbService.convertUserFieldFormat(
          state.pbUserEntry,
          athleteCompSched
        );
      } else {
        processPb = pbService.convertAthleteUserFieldFormat(
          state.pbUserEntry,
          perfInfo,
          0
        );
      }
    }

    state.processPb = processPb;

    if (!processPb.isValid) {
      state.pbEditMessage = pbService.getUserValidationMessage(
        processPb,
        perfInfo
      );
      return false;
    }
    return true;
  }

  function save() {
    if (!validate()) {
      return;
    }

    // const athleteCompSched = state.editTimePbInputV2.athleteCompSched;
    const processPb = state.processPb;

    state.isLoading = true;
    const prom = new AthleteData().submitAthletePb(
      state.editTimePbInputV2.athlete.id,
      state.editTimePbInputV2.athleteCompSched.eventid,
      processPb.pb,
      state.pbUserEntry,
      true,
      state.editTimePbInputV2.athleteCompSched.ceid
    );
    handleResponseMessages(prom);
    prom
      .then((resp) => {
        if (resp.errNo === 0) {
          messageDispatchHelper("Estimated performance saved.");
        }
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  function setIsDirty(isDirty: boolean) {
    state.isDirty = isDirty;
  }

  return {
    state,
    init,
    onUserEditedPb,
    save,
    setIsDirty,
  };
}
