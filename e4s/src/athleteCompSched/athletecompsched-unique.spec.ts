import { AthleteCompSchedService } from "./athletecompsched-service";
import { simpleClone } from "../common/common-service-utils";
import {
  athleteUniqueEvent,
  compUniqueEvents,
  uniqueEvents,
  userInfoUniqueEvents,
  userUniqueEvent,
} from "./athletecompsched-spec-mock";
import {
  IAthleteCompSchedRuleEvent,
  ICompShedRuleConfig,
} from "./athletecompsched-models";
import { events722 } from "./mock/comp722-mock";
import { mockPerfInfo } from "./test/mock-perf-info";

const athleteCompSchedService: AthleteCompSchedService =
  new AthleteCompSchedService();

describe("AthleteCompSchedService", () => {
  test("applyEventGroupUnique", () => {
    const events = simpleClone(uniqueEvents).map((evt) => {
      evt.perfInfo = simpleClone(mockPerfInfo);
      return evt;
    });

    let uniqueEventsInternal = simpleClone(uniqueEvents).map((evt) => {
      evt.perfInfo = simpleClone(mockPerfInfo);
      return evt;
    });

    //  <one>
    let eventsFromFactory: IAthleteCompSchedRuleEvent[] =
      athleteCompSchedService.factoryEventsToEventRule(
        simpleClone(uniqueEventsInternal)
      );

    // const event800 = eventsFromFactory[0];
    const event1500 = eventsFromFactory[1];
    expect(event1500.paid).toBe(0);
    expect(event1500.userSelected).toBe(false);

    //  simulating user selected
    event1500.order.productId = 999;

    let results = athleteCompSchedService.applyEventGroupUnique(
      event1500,
      eventsFromFactory
    );

    let result800 = results[0];
    let result1500 = results[1];

    //  800 should be disabled
    expect(result800.eventGroup).toBe("800m");
    expect(result800.ruleIsDisabledBy).toBe(true);
    expect(result800.ruleMessage).toBe("Discipline disabled due to: 1500m");

    //  1500 is left alone by this rule...as it was event passed in.
    expect(result1500.ruleIsDisabledBy).toBe(false);
    //  </one>

    //  <two>
    eventsFromFactory = athleteCompSchedService.factoryEventsToEventRule(
      simpleClone(uniqueEvents)
    );

    results = athleteCompSchedService.ruleUnique(event1500, eventsFromFactory);
    result800 = results[0];
    result1500 = results[1];
    //  800 should be disabled
    expect(result800.eventGroup).toBe("800m");
    expect(result800.ruleIsDisabledBy).toBe(false);
    expect(result800.ruleMessage).toBe("");

    //  1500 is left alone by this rule...as it was event passed in.
    expect(result1500.ruleIsDisabledBy).toBe(false);
    //  <two>

    //  <three>
    eventsFromFactory = athleteCompSchedService.factoryEventsToEventRule(
      simpleClone(uniqueEvents)
    );

    results = athleteCompSchedService.ruleCompUnique(
      event1500,
      eventsFromFactory
    );
    result800 = results[0];
    result1500 = results[1];
    //  800 should be disabled
    expect(result800.eventGroup).toBe("800m");
    expect(result800.ruleIsDisabledBy).toBe(false);
    expect(result800.ruleMessage).toBe("");

    //  1500 is left alone by this rule...as it was event passed in.
    expect(result1500.ruleIsDisabledBy).toBe(false);
    //  <three>

    // events = this.applyEventGroupUnique(event, events);
    // events = this.ruleUnique(event, events);
    // events = this.ruleCompUnique(event, events);

    //  Let's pass in thr whole process.
    const resultsAllRules = athleteCompSchedService.applyRulesToEvents(
      athleteCompSchedService.factoryEventsToEventRule(events),
      compUniqueEvents
    );

    const resultAll800 = resultsAllRules[0];
    // const resultAll1500 = resultsAllRules[1];

    expect(resultAll800.eventGroup).toBe("800m");
    expect(resultAll800.order.productId > 0).toBe(true);
    expect(resultAll800.paid).toBe(1);

    //  applyRulesToEvents doesn't set it...so above waste of time!!!
    expect(resultAll800.ruleIsDisabledBy).toBe(false);
    expect(resultAll800.ruleMessage).toBe("");

    ///////////////
    ///////////////
    ///////////////

    uniqueEventsInternal = simpleClone(uniqueEvents).map((evt) => {
      evt.perfInfo = simpleClone(mockPerfInfo);
      return evt;
    });

    const eventsFromFactoryComplete: IAthleteCompSchedRuleEvent[] =
      athleteCompSchedService
        .factoryEventsToEventRule(uniqueEventsInternal)
        .map((evt) => {
          evt.perfInfo = simpleClone(mockPerfInfo);
          return evt;
        });

    const eventComplete800 = eventsFromFactoryComplete[0];
    const eventComplete1500 = eventsFromFactoryComplete[1];

    //  Set selected
    eventComplete1500.order.productId = 999;

    const resultsComplete = athleteCompSchedService.processEvents(
      athleteUniqueEvent,
      {},
      {
        id: 0,
        options: {},
      } as any as ICompShedRuleConfig,
      eventComplete1500,
      [eventComplete800],
      eventsFromFactoryComplete,
      userUniqueEvent,
      {
        currentAge: 23,
        ageGroupId: 13,
        ageGroup: "Senior",
        vetAgeGroupId: 0,
        vetAgeGroup: "",
      },
      compUniqueEvents,
      userInfoUniqueEvents
    );

    const resultsComplete800 = resultsComplete[0];
    const resultsComplete1500 = resultsComplete[1];

    expect(resultsComplete1500.eventGroup).toBe("1500m");

    expect(resultsComplete800.eventGroup).toBe("800m");
    expect(resultsComplete800.order.productId > 0).toBe(true);
    expect(resultsComplete800.paid).toBe(1);
    expect(resultsComplete800.ruleIsDisabledBy).toBe(true);
  });

  /*
  test("applyEventGroupUnique applyRulesToEventsFromSelected", () => {
    let eventsFromFactoryComplete: IAthleteCompSchedRuleEvent[] =
        athleteCompSchedService.factoryEventsToEventRule(
            simpleClone(uniqueEvents)
        );

    const eventComplete1500 = eventsFromFactoryComplete[0];
    //  Set selected
    eventComplete1500.userSelected = true;

    let results = athleteCompSchedService.applyRulesToEventsFromSelected(
        eventComplete1500,
        eventsFromFactoryComplete
    );

    let resultsComplete800 = results[0];
    expect(resultsComplete800.eventGroup).toBe("800m");
    expect(resultsComplete800.order.productId > 0).toBe(true);
    expect(resultsComplete800.paid).toBe(1);

    //    TODO!!!!!!!!!   should be true
    expect(resultsComplete800.ruleIsDisabledBy).toBe(false);

    eventsFromFactoryComplete =
        athleteCompSchedService.factoryEventsToEventRule(
            simpleClone(uniqueEvents)
        );

    results = athleteCompSchedService.applyEventGroupUnique(
        eventComplete1500,
        eventsFromFactoryComplete
    );

    const result800 = results[0];
    // result1500 = results[1];

    //  800 should be disabled
    expect(result800.eventGroup).toBe("800m");
    //  TODO
    expect(result800.ruleIsDisabledBy).toBe(false);
    expect(result800.ruleMessage).toBe("Discipline disabled due to: 1500m");

    results = athleteCompSchedService.applyEventGroupUnique(
        eventComplete1500,
        eventsFromFactoryComplete
    );
    resultsComplete800 = results[0];
    expect(resultsComplete800.eventGroup).toBe("800m");
    //  TODO
    expect(resultsComplete800.ruleIsDisabledBy).toBe(false);

    // results = athleteCompSchedService.ruleUnique(
    //   eventComplete800,
    //   eventsFromFactoryComplete
    // );
    // results = athleteCompSchedService.ruleCompUnique(
    //   eventComplete800,
    //   eventsFromFactoryComplete
    // );
  });
  */
  test("applyRulesToEventsFromSelected", () => {
    //  <one>
    const eventsFromFactory: IAthleteCompSchedRuleEvent[] =
      athleteCompSchedService.factoryEventsToEventRule(
        simpleClone(uniqueEvents)
      );

    const event1500 = eventsFromFactory[1];
    event1500.order.productId = 999;

    const results = athleteCompSchedService.applyRulesToEventsFromSelected(
      event1500,
      eventsFromFactory
    );

    const result800 = results[0];
    const result1500 = results[1];

    //  800 should be disabled
    expect(result800.eventGroup).toBe("800m");
    expect(result800.ruleIsDisabledBy).toBe(true);
    expect(result800.ruleMessage).toBe("Discipline disabled due to: 1500m");

    //  1500 is left alone by this rule...as it was event passed in.
    expect(result1500.ruleIsDisabledBy).toBe(false);
  });

  test("applyRulesToEventsFromSelected do not pass in event", () => {
    //  <one>
    const eventsFromFactory: IAthleteCompSchedRuleEvent[] =
      athleteCompSchedService.factoryEventsToEventRule(
        simpleClone(uniqueEvents)
      );

    const event1500 = eventsFromFactory[1];
    event1500.order.productId = 1;

    const results = athleteCompSchedService.applyRulesToEventsFromSelected(
      event1500,
      eventsFromFactory
    );

    const result800 = results[0];
    const result1500 = results[1];

    //  800 should be disabled
    expect(result800.eventGroup).toBe("800m");
    expect(result800.ruleIsDisabledBy).toBe(true);
    expect(result800.ruleMessage).toBe("Discipline disabled due to: 1500m");

    //  1500 is left alone by this rule...as it was event passed in.
    expect(result1500.ruleIsDisabledBy).toBe(false);
  });

  test("applyRulesToEventsFromSelected 722", () => {
    //  <one>
    const eventsFromFactory: IAthleteCompSchedRuleEvent[] =
      athleteCompSchedService.factoryEventsToEventRule(simpleClone(events722));

    const event4MileMas = eventsFromFactory[0];
    expect(event4MileMas.eventGroup).toBe("4 Mile Mas");
    expect(event4MileMas.maxgroup).toBe(11904);
    expect(event4MileMas.paid).toBe(0);
    expect(event4MileMas.userSelected).toBe(false);
    expect(event4MileMas.ceoptions.uniqueEventGroups.length).toBe(0);

    const event4MileNov = eventsFromFactory[1];
    expect(event4MileNov.eventGroup).toBe("4 Mile Mas Nov");
    expect(event4MileNov.maxgroup).toBe(11905);
    expect(event4MileNov.paid).toBe(0);
    expect(event4MileNov.userSelected).toBe(false);
    expect(event4MileNov.ceoptions.uniqueEventGroups.length).toBe(2);

    const event4MileSen = eventsFromFactory[2];
    expect(event4MileSen.eventGroup).toBe("4 Mile Mas Sen");
    expect(event4MileSen.maxgroup).toBe(11906);
    expect(event4MileSen.paid).toBe(0);
    expect(event4MileSen.userSelected).toBe(false);
    expect(event4MileSen.ceoptions.uniqueEventGroups.length).toBe(2);

    //  "Enter" the event4MileSen
    event4MileSen.userSelected = true;
    event4MileSen.order.productId = 999;

    // const results = athleteCompSchedService.applyRulesToEventsFromSelected(
    //     event4MileSen,
    //     eventsFromFactory
    // );

    const results = athleteCompSchedService.applyEventGroupUnique(
      event4MileSen,
      [event4MileMas, event4MileNov, event4MileSen]
    );

    // expect(results).toBe("4 Mile Mas");

    const result4MileMas = results[0];
    expect(result4MileMas.eventGroup).toBe("4 Mile Mas");
    expect(result4MileMas.ruleIsDisabledBy).toBe(false);

    const result4MileNov = results[1];
    expect(result4MileNov.eventGroup).toBe("4 Mile Mas Nov");
    expect(result4MileNov.ruleIsDisabledBy).toBe(true);

    const result4MileSen = results[2];
    expect(result4MileSen.eventGroup).toBe("4 Mile Mas Sen");
    expect(result4MileSen.ruleIsDisabledBy).toBe(false);

    /*
    const results = athleteCompSchedService.applyRulesToEventsFromSelected(
        event1500,
        eventsFromFactory
    );

    const result800 = results[0];
    const result1500 = results[1];

    //  800 should be disabled
    expect(result800.eventGroup).toBe("800m");
    expect(result800.ruleIsDisabledBy).toBe(true);
    expect(result800.ruleMessage).toBe("Discipline disabled due to: 1500m");

    //  1500 is left alone by this rule...as it was event passed in.
    expect(result1500.ruleIsDisabledBy).toBe(false);
    */
  });
});
