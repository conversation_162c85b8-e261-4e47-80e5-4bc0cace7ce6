<template>
  <div>
    <div v-if="!showEligibility">
      <event-team-name
        class="row"
        :name-prop="compEventTeam.teamName"
        :edit-mode-prop="editMode"
        :can-edit="getCanEditTeamName"
        v-on:changed="onTeamNameChanged"
      >
      </event-team-name>

      <div class="e4s-section-padding-separator"></div>

      <div class="row" v-if="canShowEligibility">
        <div class="col s12 m12 l12">
          <div class="right">
            Can't find athlete? Click
            <a
              class="anchor-buttons"
              href="#"
              v-on:click.prevent="showEligibility = true"
            >
              here
            </a>
            to check why.
          </div>
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>

      <div class="row">
        <div class="col s12 m12 l12">
          <div
            v-if="!isNew"
            class="row"
            v-for="(athlete, index) in compEventTeam.athletes"
          >
            <div class="row" v-if="athlete.id > 0">
              <div class="col s12 m12 l12 div-sep-border-bottom">
                <div class="e4s-card-small-row">
                  <div class="col s6 m6 l6">
                    <span v-text="getTeamPositionNameReadMode(index)"></span>
                    <span> </span>
                    <span v-text="athlete.firstName"></span>
                    <span> </span>
                    <span v-text="athlete.surName"></span>
                  </div>

                  <div class="col s6 m6 l6">
                    <div class="right">
                      <div v-text="getAthleteDescriptorRight(athlete)"></div>
                    </div>
                  </div>
                </div>
              </div>
              <!--                            <div class="comp-event-team-name-sep"></div>-->
            </div>
          </div>

          <fieldset class="e4s-fieldset" v-if="isNew">
            <div class="row" v-for="index in getAthleteSlots">
              <div
                :class="
                  allowAddAthletes ? 'col s10 m10 l10' : 'col s12 m12 l12'
                "
              >
                <comp-event-name-picker
                  :position="index - 1"
                  :competition="competition"
                  :event-team-header="eventTeamHeader"
                  :team-position-name="getTeamPositionNameEditMode(index - 1)"
                  :athlete-default="compEventTeam.athletes[index - 1]"
                  :show-all-athletes="false"
                  :user-entity="userEntity"
                  v-on:onUserFocus="athleteRowSelected"
                  v-on:athleteSelected="athleteSelected"
                  v-on:athleteRemoved="athleteRemoved"
                >
                </comp-event-name-picker>
              </div>

              <div
                v-show="athleteForAddButtonToShow === index - 1"
                v-if="allowAddAthletes"
                class="col s2 m2 l2"
              >
                <!--                                <a href="#" v-on:click.prevent="addAthlete(index)">Add</a>-->
                <button
                  class="btn waves-effect waves green xxx-btn-small right"
                  v-on:click.prevent="addAthlete(index)"
                >
                  New
                </button>
              </div>
            </div>
          </fieldset>
        </div>
      </div>

      <user-validation-messages
        :validation-messages="userMessages"
      ></user-validation-messages>

      <div v-if="showUserOwner" class="row">
        <div class="col s12 m12 l12">
          Team entered by <span v-text="compEventTeam.userName"></span>
        </div>
      </div>

      <event-team-buttons
        class="row e4s-section-padding-separator"
        :comp-event-team="compEventTeam"
        :competition-summary="selectedCompetition"
        :isLoading="isLoading"
        :edit-mode="editMode"
        :is-user-owner="isUserOwner"
        :isSubmitDisabled="!editMode || isLoading"
        v-on:cancel="cancelCompEventTeam"
        v-on:edit="editCompEventTeam"
        v-on:delete="deleteCompEventTeam"
        v-on:submit="submitCompEventTeam"
        @addUserCart="addUserCart"
      >
      </event-team-buttons>
    </div>

    <!--        <div class="row">-->
    <!--            <div class="col s12 m12 l12">-->
    <!--                compEventTeam.athletes{{compEventTeam.athletes}}-->
    <!--            </div>-->
    <!--        </div>-->

    <div v-if="showEligibility" class="row">
      <div class="col s12 m12 l12">
        <div class="e4s-section-padding-separator"></div>
        <EventAthleteEligible
          class="eligibility"
          :ceid="eventTeamHeader.id"
          v-on:cancel="showEligibility = false"
        >
        </EventAthleteEligible>
      </div>
    </div>

    <e4s-modal
      v-if="showAddAthleteDialog"
      :css-class="'e4s-modal-container--full-size'"
    >
      <div slot="header"></div>
      <div slot="body">
        <athlete-form
          :is-admin="isAdmin"
          :athlete-prop="athleteForAdd"
          @onSubmit="onAddAthleteSubmit"
          @onCancel="onAddAthleteCancel"
          @onError="onAddAthleteCancel"
        >
        </athlete-form>
      </div>
      <div slot="footer">
        <!--                <div class="row">-->
        <!--                    <div class="col s12 m12 l12">-->
        <!--                        qwertyqwerty qwertyqwertyqwerty qwertyqwertyqwertyqwertyqwertyqwertyqwerty qwerty qwerty qwerty-->
        <!--                    </div>-->
        <!--                </div>-->
      </div>
    </e4s-modal>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {
  IAthlete,
  IAthleteSearch,
  IAthleteSummary,
} from "../../athlete/athlete-models";
import LoadingSpinner from "../../common/ui/loading-spinner.vue";
import { IConfigApp, IEntity, IUserInfo } from "../../config/config-app-models";
import {
  IUserMessage,
  USER_MESSAGE_LEVEL,
} from "../../user-message/user-message-models";
import { IEventTeam } from "../athletecompsched-models";
import CompEventNamePicker from "./comp-event-name-picker.vue";
import {
  ICompEventTeam,
  IEventTeamHeader,
  IEventTeamFormField,
} from "./event-teams-models";
import EventTeamForm from "./event-team-form.vue";
import { IAutoCompleteValue } from "../../common/ui/autocomplete/auto-complete-mat-models";
import SchoolTeamForm from "./school/school-team-form.vue";
import EventTeamBase from "./event-team-base";
import { EventTeamService } from "./event-team-service";
import UserValidationMessages from "../../user-message/user-validation-messages.vue";
import EventTeamButtons from "./event-team-buttons.vue";
import EventAthleteEligible from "./event-athlete-eligible.vue";
import { mapGetters, mapState } from "vuex";
import { ENTRY_STORE_CONST, IEntryStoreState } from "../../entry/entry-store";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import { ICompetitionInfo } from "../../competition/competition-models";
import { CompetitionService } from "../../competition/competiton-service";
import E4sModal from "../../common/ui/e4s-modal.vue";
import AthleteForm from "../../athlete/maint/athlete-form.vue";
import { AthleteService } from "../../athlete/athlete-service";
import { AthleteData } from "../../athlete/athlete-data";
import {
  IServerGenericResponse,
  IServerPagingResponseList,
} from "../../common/common-models";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { ConfigService } from "../../config/config-service";

const eventTeamService: EventTeamService = new EventTeamService();
const competitionService: CompetitionService = new CompetitionService();
const athleteService: AthleteService = new AthleteService();
const athleteData: AthleteData = new AthleteData();
const configService: ConfigService = new ConfigService();

@Component({
  name: "event-team-default",
  components: {
    "comp-event-name-picker": CompEventNamePicker,
    "loading-spinner": LoadingSpinner,
    "event-team-form": EventTeamForm,
    "school-team-form": SchoolTeamForm,
    "user-validation-messages": UserValidationMessages,
    "event-team-buttons": EventTeamButtons,
    EventAthleteEligible,
    E4sModal,
    AthleteForm,
  },
  computed: {
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      selectedCompetition: (state: any) =>
        state.entryForm.selectedCompetition,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
      userInfo: (state: any) => state.configApp.userInfo,
      userEntityStore: (state: any) => state.userEntity,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class EventTeamDefault extends EventTeamBase {
  public readonly selectedCompetition: ICompetitionInfo;
  public readonly userInfo: IUserInfo;
  public readonly configApp: IConfigApp;

  public userEntityStore: IEntity;
  public eventTeamHeaders: IEventTeamHeader[];

  @Prop()
  public readonly eventTeamHeader: IEventTeamHeader;

  @Prop({
    required: true,
  })
  public readonly compEventTeamProp: ICompEventTeam;

  @Prop({ default: false })
  public readonly isLoading: boolean;

  @Prop({
    default: () => {
      return configService.factoryEntity();
    },
  })
  public readonly userEntity: IEntity;

  public compEventTeam: ICompEventTeam;
  public showEligibility: boolean = false;
  public showAddAthleteDialog: boolean = false;

  public athleteForAddButtonToShow = 0;
  public athleteForAdd = athleteService.factoryGetAthlete();
  public athleteForAddIndex: number = -1;

  public created() {
    console.log("EventTeamDefault.created() userEntity: ", this.userEntity);
    this.compEventTeam = R.clone(this.compEventTeamProp);
  }

  public mounted() {
    this.initForm();
  }

  public initForm() {
    this.compEventTeam = R.clone(this.compEventTeamProp);
    this.compEventTeam.ceid = this.eventTeamHeader.id;
    this.compEventTeam.athletes = eventTeamService.setUpAthleteArray(
      this.eventTeamHeader,
      this.compEventTeam
    );
    this.setClubSchoolForAthleteAdd(this.userEntity);
  }

  @Watch("compEventTeamProp")
  public onCompEventTeamProp(newValue: ICompEventTeam) {
    this.compEventTeam = R.clone(newValue);
    this.reset();
  }

  @Watch("userEntity")
  public onUserEntityPropChanged(newValue: IEntity) {
    console.log("zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz", newValue);
    this.setClubSchoolForAthleteAdd(newValue);
  }

  public setClubSchoolForAthleteAdd(entity: IEntity) {
    const athlete = athleteService.factoryGetAthlete();
    if (this.isSchool) {
      athlete.schoolid = entity.id;
      athlete.school = entity.name;
    } else {
      athlete.clubid = entity.id;
      athlete.club = entity.name;
    }
    athlete.aocode = this.configApp.defaultao.code;
    this.athleteForAdd = R.clone(athlete);
  }

  public reset() {
    this.editingLine = -1;
    this.hasBeenEdited = false;
    this.editTeamName = false;
    this.showDeleteConfirmation = false;
    this.initForm();
  }

  public get getAthleteSlots(): number {
    const eventTeam: IEventTeam = eventTeamService.getEventTeamOptions(
      this.eventTeamHeader
    );
    return eventTeam ? eventTeam.max : 0;
  }

  public get isSchool() {
    return competitionService.isSchool(this.competition);
  }

  public get allowAddAthletes(): boolean {
    // return this.isSchool;
    return this.configApp.theme !== "irl";
  }

  public addAthlete(index: number) {
    this.showAddAthleteDialog = true;
    this.athleteForAddIndex = index;
  }

  public onAddAthleteSubmit(athlete: IAthlete) {
    console.log("EventTeamDefault.onAddAthleteSubmit()", athlete);
    this.showAddAthleteDialog = false;

    const athleteSearch: IAthleteSearch =
      athleteService.factoryGetAthleteSearch();
    athleteSearch.athleteid = athlete.id;
    messageDispatchHelper(
      "Validating athlete.",
      USER_MESSAGE_LEVEL.INFO.toString()
    );
    athleteData
      .findAthletes(this.competition.id, 0, 1, 10, "surname", athleteSearch)
      .then((response: IServerPagingResponseList<IAthleteSummary>) => {
        console.log(
          "onAddAthleteSubmit() athleteData.findAthletes()",
          response
        );
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return null;
        }
        if (response.data.length !== 1) {
          messageDispatchHelper(
            "A unique record with same demographics could not be found, please use search box to add name.",
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return null;
        }
        return response.data[0];
      })
      .then((athleteSummary: IAthleteSummary | null) => {
        if (!athleteSummary) {
          return;
        }
        return athleteData
          .checkAthleteEventEligible(athleteSummary.id, this.eventTeamHeader.id)
          .then((response: IServerGenericResponse) => {
            if (response.errNo > 0) {
              messageDispatchHelper(
                response.error,
                USER_MESSAGE_LEVEL.ERROR.toString()
              );
              return;
            }
            const message = response.data;
            messageDispatchHelper(message, USER_MESSAGE_LEVEL.INFO.toString());
            if (message.indexOf("is eligible for this event") > -1) {
              const position = this.athleteForAddIndex - 1;
              this.setAthlete(athleteSummary, position);
            }
            return;
          })
          .catch((error) => {
            messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
            return;
          });
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return;
      });
  }

  public onAddAthleteCancel() {
    this.showAddAthleteDialog = false;
  }

  public athleteSelected(payload: {
    autoCompleteValue: IAutoCompleteValue;
    position: number;
  }) {
    this.userMessages = [];
    console.log("CompEventTeam.athleteSelected", payload);
    if (payload.autoCompleteValue.value) {
      const athlete: IAthleteSummary = payload.autoCompleteValue.value;
      this.setAthlete(athlete, payload.position);
    }
  }

  public athleteRowSelected(index: number) {
    console.log("athleteRowSelected() index: " + index);
    this.athleteForAddButtonToShow = index;
  }

  public setAthlete(athlete: IAthleteSummary, position: number) {
    const result: IUserMessage[] = eventTeamService.canAddAthlete(
      this.eventTeamHeader,
      this.compEventTeam,
      athlete,
      position
    );
    this.userMessages = result;
    const athletes = R.clone(this.compEventTeam.athletes);
    athletes[position] = R.clone(athlete);
    this.compEventTeam.athletes = athletes;
    this.onFieldChanged();
    this.editLine(-1);
  }

  public athleteRemoved(position: number) {
    this.userMessages = [];
    console.log("CompEventTeam.athleteRemoved: " + position);
    const athletes = R.clone(this.compEventTeam.athletes);
    athletes[position] = this.athleteService.factoryGetAthlete();
    this.compEventTeam.athletes = athletes;
    this.onFieldChanged();
  }

  public get getTeamPositionName() {
    return eventTeamService.getOptionsTeamPositionName(this.eventTeamHeader);
  }

  public getTeamPositionNameReadMode(index: number) {
    const position: number = index + 1;
    return this.getTeamPositionName.length > 0
      ? this.getTeamPositionName + " " + position
      : position;
  }

  public getTeamPositionNameEditMode(index: number) {
    const position: number = index + 1;
    const isOptional: boolean =
      position > eventTeamService.getOptionsMin(this.eventTeamHeader);
    if (isOptional) {
      const substituteName = eventTeamService.getOptionsTeamSubstituteName(
        this.eventTeamHeader
      );
      return (
        (substituteName === "" ? "Substitute" : substituteName) + " (Optional)"
      );
    } else {
      return this.getTeamPositionNameReadMode(index);
    }
  }

  public getAthleteDescriptorRight(athlete: IAthleteSummary): string {
    return eventTeamService.getAthleteDescriptorRight(athlete);
  }

  public editLine(index: number) {
    this.editingLine = index;
    this.hasBeenEdited = true;
  }

  public get canShowEligibility() {
    return eventTeamService.getOptionsMin(this.eventTeamHeader) > 0;
  }

  public submitCompEventTeam() {
    console.log("CompEventTeam.submitCompEventTeam()");
    this.userMessages = [];
    const userMessages: IUserMessage[] = eventTeamService.validateCompEventTeam(
      this.eventTeamHeader,
      this.compEventTeam,
      this.competition,
      this.eventTeamHeaders
    );
    if (userMessages.length > 0) {
      this.userMessages = userMessages;
      return;
    }

    this.$emit("submit", this.compEventTeam);
  }

  public eventFormChange(formInfo: IEventTeamFormField[]) {
    this.compEventTeam.formInfo = formInfo;
  }

  public addUserCart() {
    console.log(
      "EventTeamDefault.addUserCart()",
      this.compEventTeam,
      this.eventTeamHeader
    );
    this.$emit("addUserCart", this.compEventTeam);
  }
}
</script>

<style scoped>
.e4s-name-edit {
  margin-left: 1rem !important;
}
</style>
