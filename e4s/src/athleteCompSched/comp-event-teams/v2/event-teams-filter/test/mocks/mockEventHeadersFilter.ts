import { IEventTeamHeader } from "../../../../event-teams-models";

export const mockEventTeamHeadersFilter: IEventTeamHeader[] = [
  {
    id: 45953,
    eventid: 282,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "F",
    ageGroup: { id: 214, name: "Under 19" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 214, name: "Under 19" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45954,
    eventid: 281,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "M",
    ageGroup: { id: 214, name: "Under 19" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 214, name: "Under 19" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45931,
    eventid: 282,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "F",
    ageGroup: { id: 15, name: "VET 35" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 15, name: "VET 35" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45932,
    eventid: 281,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "M",
    ageGroup: { id: 15, name: "VET 35" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 15, name: "VET 35" },
    compEventTeams: [
      {
        id: 1406,
        teamName: "Nuneaton Harriers 4 x 100 Male VET 35 {A}",
        paid: 0,
        prodId: 138371,
        userId: 1,
        userName: "e4sadmin",
        entity: {
          id: 1198,
          entityLevel: 1,
          name: "Nuneaton Harriers",
          entity: "Club",
        },
        athletes: [
          {
            id: 108867,
            clubid: 1198,
            aocode: "EA",
            club: "Nuneaton Harriers",
            pos: 1,
            firstName: "Thomas",
            surName: "BURTON",
            URN: null,
            dob: "1988-10-06",
            ageInfo: {
              ageGroups: [
                {
                  compDate: "2025-05-13",
                  agid: 15,
                  minAge: 35,
                  toDate: "1990-05-13",
                  fromDate: "1985-05-14",
                  id: 15,
                  Name: "VET 35",
                  MaxAge: 39,
                  AtDay: 0,
                  AtMonth: 0,
                  year: 0,
                  minAtDay: 0,
                  minAtMonth: 0,
                  minYear: 0,
                  keyName: "VET 35",
                  options: [
                    { aocode: "EA", default: false, base: 1 },
                    { aocode: "IRL", default: false, base: 1 },
                    { aocode: "ANI", default: false, base: 1 },
                  ],
                  name: "VET 35",
                  shortName: "VET 35",
                },
              ],
              vetAgeGroup: null,
              currentAge: 36,
              competitionAge: 36,
              ageGroup: {
                compDate: "2025-05-13",
                agid: 15,
                minAge: 35,
                toDate: "1990-05-13",
                fromDate: "1985-05-14",
                id: 15,
                Name: "VET 35",
                MaxAge: 39,
                AtDay: 0,
                AtMonth: 0,
                year: 0,
                minAtDay: 0,
                minAtMonth: 0,
                minYear: 0,
                keyName: "VET 35",
                options: [
                  { aocode: "EA", default: false, base: 1 },
                  { aocode: "IRL", default: false, base: 1 },
                  { aocode: "ANI", default: false, base: 1 },
                ],
                name: "VET 35",
                shortName: "VET 35",
              },
            },
          },
          {
            id: 132323,
            clubid: 1198,
            aocode: "",
            club: "Nuneaton Harriers",
            pos: 2,
            firstName: "Warren",
            surName: "CLAMP",
            URN: null,
            dob: "1989-03-11",
            ageInfo: {
              ageGroups: [
                {
                  compDate: "2025-05-13",
                  agid: 15,
                  minAge: 35,
                  toDate: "1990-05-13",
                  fromDate: "1985-05-14",
                  id: 15,
                  Name: "VET 35",
                  MaxAge: 39,
                  AtDay: 0,
                  AtMonth: 0,
                  year: 0,
                  minAtDay: 0,
                  minAtMonth: 0,
                  minYear: 0,
                  keyName: "VET 35",
                  options: [
                    { aocode: "EA", default: false, base: 1 },
                    { aocode: "IRL", default: false, base: 1 },
                    { aocode: "ANI", default: false, base: 1 },
                  ],
                  name: "VET 35",
                  shortName: "VET 35",
                },
              ],
              vetAgeGroup: null,
              currentAge: 36,
              competitionAge: 36,
              ageGroup: {
                compDate: "2025-05-13",
                agid: 15,
                minAge: 35,
                toDate: "1990-05-13",
                fromDate: "1985-05-14",
                id: 15,
                Name: "VET 35",
                MaxAge: 39,
                AtDay: 0,
                AtMonth: 0,
                year: 0,
                minAtDay: 0,
                minAtMonth: 0,
                minYear: 0,
                keyName: "VET 35",
                options: [
                  { aocode: "EA", default: false, base: 1 },
                  { aocode: "IRL", default: false, base: 1 },
                  { aocode: "ANI", default: false, base: 1 },
                ],
                name: "VET 35",
                shortName: "VET 35",
              },
            },
          },
        ],
      },
    ],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45933,
    eventid: 282,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "F",
    ageGroup: { id: 16, name: "VET 40" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 16, name: "VET 40" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45934,
    eventid: 281,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "M",
    ageGroup: { id: 16, name: "VET 40" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 16, name: "VET 40" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45935,
    eventid: 282,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "F",
    ageGroup: { id: 18, name: "VET 50" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 18, name: "VET 50" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45936,
    eventid: 281,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "M",
    ageGroup: { id: 18, name: "VET 50" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 18, name: "VET 50" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45937,
    eventid: 282,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "F",
    ageGroup: { id: 19, name: "VET 55" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 19, name: "VET 55" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45938,
    eventid: 281,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "M",
    ageGroup: { id: 19, name: "VET 55" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 19, name: "VET 55" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45939,
    eventid: 282,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "F",
    ageGroup: { id: 20, name: "VET 60" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 20, name: "VET 60" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45940,
    eventid: 281,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "M",
    ageGroup: { id: 20, name: "VET 60" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 20, name: "VET 60" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45943,
    eventid: 282,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "F",
    ageGroup: { id: 22, name: "VET 65" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 22, name: "VET 65" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45944,
    eventid: 281,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "M",
    ageGroup: { id: 22, name: "VET 65" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 22, name: "VET 65" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45941,
    eventid: 282,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "F",
    ageGroup: { id: 21, name: "VET 70" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 21, name: "VET 70" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45942,
    eventid: 281,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "M",
    ageGroup: { id: 21, name: "VET 70" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 21, name: "VET 70" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45945,
    eventid: 282,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "F",
    ageGroup: { id: 23, name: "VET 75" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 23, name: "VET 75" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45946,
    eventid: 281,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "M",
    ageGroup: { id: 23, name: "VET 75" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 23, name: "VET 75" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45947,
    eventid: 282,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "F",
    ageGroup: { id: 24, name: "VET 80" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 24, name: "VET 80" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45948,
    eventid: 281,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "M",
    ageGroup: { id: 24, name: "VET 80" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 24, name: "VET 80" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45949,
    eventid: 282,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "F",
    ageGroup: { id: 25, name: "VET 85" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 25, name: "VET 85" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45950,
    eventid: 281,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "M",
    ageGroup: { id: 25, name: "VET 85" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 25, name: "VET 85" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45951,
    eventid: 282,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "F",
    ageGroup: { id: 26, name: "VET 90" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 26, name: "VET 90" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45952,
    eventid: 281,
    eventName: "4 x 100",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 35,
      max: 99,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 1,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {
      maxInHeat: 8,
      min: 35,
      max: 99,
      helpText: "",
      isTeamEvent: true,
      eventTeam: { min: 4, max: 4 },
    },
    gender: "M",
    ageGroup: { id: 26, name: "VET 90" },
    isDisabled: false,
    eventGroup: {
      id: 7389,
      name: "4 x 100 std",
      eventNo: 3,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 26, name: "VET 90" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45929,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 70,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 46,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 47,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 214, name: "Under 19" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 214, name: "Under 19" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45977,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 214, name: "Under 19" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 214, name: "Under 19" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45930,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 71,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 48,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 49,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 214, name: "Under 19" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 214, name: "Under 19" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45978,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 214, name: "Under 19" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 214, name: "Under 19" },
    compEventTeams: [
      {
        id: 1409,
        teamName: "Nuneaton Harriers Relay Male Under 19 {A}",
        paid: 0,
        prodId: 138372,
        userId: 1,
        userName: "e4sadmin",
        entity: { id: 0, entityLevel: 0, name: "Unknown", entity: "Unknown" },
        athletes: [
          {
            id: null,
            clubid: null,
            aocode: null,
            club: null,
            pos: null,
            name: " ",
            consent: false,
          },
        ],
      },
    ],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45907,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 48,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 2,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 3,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 15, name: "VET 35" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 15, name: "VET 35" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45955,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 15, name: "VET 35" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 15, name: "VET 35" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45908,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 49,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 4,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 5,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 15, name: "VET 35" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 15, name: "VET 35" },
    compEventTeams: [
      {
        athletes: [],
        entityLevel: 1,
        entityId: 1198,
        ceid: 45908,
        formRows: [
          {
            id: 4,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
            athlete: {
              id: 103426,
              clubid: 176744,
              aocode: "EA",
              club: "Badgers",
              pos: 1,
              firstName: "Warren",
              surName: "CLAMP",
              URN: 4013141,
              dob: "1989-03-11",
              ageInfo: {
                ageGroups: [
                  {
                    compDate: "2025-05-13",
                    agid: 15,
                    minAge: 35,
                    toDate: "1990-05-13",
                    fromDate: "1985-05-14",
                    id: 15,
                    Name: "VET 35",
                    MaxAge: 39,
                    AtDay: 0,
                    AtMonth: 0,
                    year: 0,
                    minAtDay: 0,
                    minAtMonth: 0,
                    minYear: 0,
                    keyName: "VET 35",
                    options: [
                      { aocode: "EA", default: false, base: 1 },
                      { aocode: "IRL", default: false, base: 1 },
                      { aocode: "ANI", default: false, base: 1 },
                    ],
                    name: "VET 35",
                    shortName: "VET 35",
                  },
                ],
                vetAgeGroup: null,
                currentAge: 36,
                competitionAge: 36,
                ageGroup: {
                  compDate: "2025-05-13",
                  agid: 15,
                  minAge: 35,
                  toDate: "1990-05-13",
                  fromDate: "1985-05-14",
                  id: 15,
                  Name: "VET 35",
                  MaxAge: 39,
                  AtDay: 0,
                  AtMonth: 0,
                  year: 0,
                  minAtDay: 0,
                  minAtMonth: 0,
                  minYear: 0,
                  keyName: "VET 35",
                  options: [
                    { aocode: "EA", default: false, base: 1 },
                    { aocode: "IRL", default: false, base: 1 },
                    { aocode: "ANI", default: false, base: 1 },
                  ],
                  name: "VET 35",
                  shortName: "VET 35",
                },
              },
            },
          },
          {
            id: 5,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
            athlete: {
              id: 162963,
              clubid: 790,
              aocode: "EA",
              club: "Herne Hill Harriers",
              pos: 1,
              firstName: "Samuel",
              surName: "ADEYEMI",
              URN: 2809576,
              dob: "1990-03-15",
              ageInfo: {
                ageGroups: [
                  {
                    compDate: "2025-05-13",
                    agid: 15,
                    minAge: 35,
                    toDate: "1990-05-13",
                    fromDate: "1985-05-14",
                    id: 15,
                    Name: "VET 35",
                    MaxAge: 39,
                    AtDay: 0,
                    AtMonth: 0,
                    year: 0,
                    minAtDay: 0,
                    minAtMonth: 0,
                    minYear: 0,
                    keyName: "VET 35",
                    options: [
                      { aocode: "EA", default: false, base: 1 },
                      { aocode: "IRL", default: false, base: 1 },
                      { aocode: "ANI", default: false, base: 1 },
                    ],
                    name: "VET 35",
                    shortName: "VET 35",
                  },
                ],
                vetAgeGroup: null,
                currentAge: 35,
                competitionAge: 35,
                ageGroup: {
                  compDate: "2025-05-13",
                  agid: 15,
                  minAge: 35,
                  toDate: "1990-05-13",
                  fromDate: "1985-05-14",
                  id: 15,
                  Name: "VET 35",
                  MaxAge: 39,
                  AtDay: 0,
                  AtMonth: 0,
                  year: 0,
                  minAtDay: 0,
                  minAtMonth: 0,
                  minYear: 0,
                  keyName: "VET 35",
                  options: [
                    { aocode: "EA", default: false, base: 1 },
                    { aocode: "IRL", default: false, base: 1 },
                    { aocode: "ANI", default: false, base: 1 },
                  ],
                  name: "VET 35",
                  shortName: "VET 35",
                },
              },
            },
          },
        ],
        id: 1405,
        teamName: "Nuneaton Harriers Relay Male VET 35 {A}",
        paid: 0,
        prodId: 138370,
        userId: 1,
        userName: "e4sadmin",
      },
    ],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45956,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 15, name: "VET 35" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 15, name: "VET 35" },
    compEventTeams: [
      {
        id: 1410,
        teamName: "Nuneaton Harriers Relay Male VET 35 {B}",
        paid: 0,
        prodId: 138373,
        userId: 1,
        userName: "e4sadmin",
        entity: {
          id: 1198,
          entityLevel: 1,
          name: "Nuneaton Harriers",
          entity: "Club",
        },
        athletes: [
          {
            id: null,
            clubid: null,
            aocode: null,
            club: null,
            pos: null,
            name: " ",
            consent: false,
          },
        ],
      },
    ],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45909,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 50,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 6,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 7,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 16, name: "VET 40" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 16, name: "VET 40" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45957,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 16, name: "VET 40" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 16, name: "VET 40" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45910,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 51,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 8,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 9,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 16, name: "VET 40" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 16, name: "VET 40" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45958,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 16, name: "VET 40" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 16, name: "VET 40" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45911,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 52,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 10,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 11,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 18, name: "VET 50" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 18, name: "VET 50" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45959,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 18, name: "VET 50" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 18, name: "VET 50" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45912,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 53,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 12,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 13,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 18, name: "VET 50" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 18, name: "VET 50" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45960,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 18, name: "VET 50" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 18, name: "VET 50" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45913,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 54,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 14,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 15,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 19, name: "VET 55" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 19, name: "VET 55" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45961,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 19, name: "VET 55" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 19, name: "VET 55" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45914,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 55,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 16,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 17,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 19, name: "VET 55" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 19, name: "VET 55" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45962,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 19, name: "VET 55" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 19, name: "VET 55" },
    compEventTeams: [
      {
        id: 1413,
        teamName: "Nuneaton Harriers Relay Male VET 55 {A}",
        paid: 0,
        prodId: 138375,
        userId: 1,
        userName: "e4sadmin",
        entity: {
          id: 1198,
          entityLevel: 1,
          name: "Nuneaton Harriers",
          entity: "Club",
        },
        athletes: [
          {
            id: 186203,
            clubid: 1198,
            aocode: "",
            club: "Nuneaton Harriers",
            pos: 1,
            name: "Dds DDS",
            consent: true,
          },
          {
            id: 186204,
            clubid: 1198,
            aocode: "",
            club: "Nuneaton Harriers",
            pos: 2,
            name: "Wfwefw WFWEFW",
            consent: true,
          },
        ],
      },
      {
        id: 1414,
        teamName: "Nuneaton Harriers Relay Male VET 55 {B}",
        paid: 0,
        prodId: 138376,
        userId: 1,
        userName: "e4sadmin",
        entity: {
          id: 1198,
          entityLevel: 1,
          name: "Nuneaton Harriers",
          entity: "Club",
        },
        athletes: [
          {
            id: 186205,
            clubid: 1198,
            aocode: "",
            club: "Nuneaton Harriers",
            pos: 1,
            name: "Eeweef EEWEEF",
            consent: true,
          },
          {
            id: 186206,
            clubid: 1198,
            aocode: "",
            club: "Nuneaton Harriers",
            pos: 2,
            name: "Werfefewf WERFEFEWF",
            consent: true,
          },
        ],
      },
    ],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45915,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 56,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 18,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 19,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 20, name: "VET 60" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 20, name: "VET 60" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45963,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 20, name: "VET 60" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 20, name: "VET 60" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45916,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 57,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 20,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 21,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 20, name: "VET 60" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 20, name: "VET 60" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45964,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 20, name: "VET 60" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 20, name: "VET 60" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45919,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 60,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 26,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 27,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 22, name: "VET 65" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 22, name: "VET 65" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45967,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 22, name: "VET 65" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 22, name: "VET 65" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45920,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 61,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 28,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 29,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 22, name: "VET 65" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 22, name: "VET 65" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45968,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 22, name: "VET 65" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 22, name: "VET 65" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45917,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 58,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 22,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 23,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 21, name: "VET 70" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 21, name: "VET 70" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45965,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 21, name: "VET 70" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 21, name: "VET 70" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45918,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 59,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 24,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 25,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 21, name: "VET 70" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 21, name: "VET 70" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45966,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 21, name: "VET 70" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 21, name: "VET 70" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45921,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 62,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 30,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 31,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 23, name: "VET 75" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 23, name: "VET 75" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45969,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 23, name: "VET 75" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 23, name: "VET 75" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45922,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 63,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 32,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 33,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 23, name: "VET 75" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 23, name: "VET 75" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45970,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 23, name: "VET 75" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 23, name: "VET 75" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45923,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 64,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 34,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 35,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 24, name: "VET 80" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 24, name: "VET 80" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45971,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 24, name: "VET 80" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 24, name: "VET 80" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45924,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 65,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 36,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 37,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 24, name: "VET 80" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 24, name: "VET 80" },
    compEventTeams: [
      {
        athletes: [],
        entityLevel: 1,
        entityId: 1198,
        ceid: 45924,
        formRows: [
          {
            id: 36,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
            athlete: {
              id: 157296,
              clubid: 1198,
              aocode: "EA",
              club: "Nuneaton Harriers",
              pos: 1,
              firstName: "Terry",
              surName: "MORRIS",
              URN: 2739585,
              dob: "1944-10-12",
              ageInfo: {
                ageGroups: [
                  {
                    compDate: "2025-05-13",
                    agid: 24,
                    minAge: 80,
                    toDate: "1945-05-13",
                    fromDate: "1940-05-14",
                    id: 24,
                    Name: "VET 80",
                    MaxAge: 84,
                    AtDay: 0,
                    AtMonth: 0,
                    year: 0,
                    minAtDay: null,
                    minAtMonth: null,
                    minYear: null,
                    keyName: "VET 80",
                    options: [
                      { aocode: "EA", default: false, base: 1 },
                      { aocode: "IRL", default: false, base: 1 },
                      { aocode: "ANI", default: false, base: 1 },
                    ],
                    name: "VET 80",
                    shortName: "VET 80",
                  },
                ],
                vetAgeGroup: null,
                currentAge: 80,
                competitionAge: 80,
                ageGroup: {
                  compDate: "2025-05-13",
                  agid: 24,
                  minAge: 80,
                  toDate: "1945-05-13",
                  fromDate: "1940-05-14",
                  id: 24,
                  Name: "VET 80",
                  MaxAge: 84,
                  AtDay: 0,
                  AtMonth: 0,
                  year: 0,
                  minAtDay: null,
                  minAtMonth: null,
                  minYear: null,
                  keyName: "VET 80",
                  options: [
                    { aocode: "EA", default: false, base: 1 },
                    { aocode: "IRL", default: false, base: 1 },
                    { aocode: "ANI", default: false, base: 1 },
                  ],
                  name: "VET 80",
                  shortName: "VET 80",
                },
              },
            },
          },
          {
            id: 37,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
            athlete: {},
          },
        ],
        id: 1415,
        teamName: "League some weird team name {A}",
        paid: 0,
        prodId: 138377,
        userId: 1,
        userName: "e4sadmin",
      },
    ],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45972,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 24, name: "VET 80" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 24, name: "VET 80" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45925,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 66,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 38,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 39,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 25, name: "VET 85" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 25, name: "VET 85" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45973,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 25, name: "VET 85" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 25, name: "VET 85" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45926,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 67,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 40,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 41,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 25, name: "VET 85" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 25, name: "VET 85" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45974,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 25, name: "VET 85" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 25, name: "VET 85" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45927,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 68,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 42,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 43,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 26, name: "VET 90" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 26, name: "VET 90" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45975,
    eventid: 837,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: { id: 26, name: "VET 90" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "F",
    ageInfo: { id: 26, name: "VET 90" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45928,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "LEAGUE",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: 69,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
        formRows: [
          {
            id: 44,
            position: 1,
            dateTime: "2025-05-13",
            eventDef: { id: 27, name: "100m" },
          },
          {
            id: 45,
            position: 2,
            dateTime: "2025-05-13",
            eventDef: { id: 219, name: "Javelin" },
          },
        ],
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, isMultiEvent: false },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 26, name: "VET 90" },
    isDisabled: false,
    eventGroup: {
      id: 7388,
      name: "Relay League",
      eventNo: 2,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 26, name: "VET 90" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  {
    id: 45976,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 26, name: "VET 90" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 26, name: "VET 90" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
] as any as IEventTeamHeader[];
