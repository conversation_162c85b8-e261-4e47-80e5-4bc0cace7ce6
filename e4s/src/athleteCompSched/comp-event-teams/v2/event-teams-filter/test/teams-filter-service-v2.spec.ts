import { filterEventTeamHeadersV2 } from "../models/teams-filter-service-v2";
// import { IEventTeamHeader } from "../../../event-teams-models";
import { IEventTeamHeaderFilterV2 } from "../../../event-teams-filter/event-teams-filter-models";
// import { GENDER } from "../../../../../common/common-models";
import { mockEventTeamHeadersFilter } from "./mocks/mockEventHeadersFilter";

describe("Teams Filter Service V2", () => {
  test("should filter event headers by event name", () => {
    const filter: IEventTeamHeaderFilterV2 = {
      eventName: "Relay",
      gender: "ALL",
      ageGroup: { id: 0, name: "ALL" },
      hasTeams: "ALL",
      freeText: "",
    };

    const result = filterEventTeamHeadersV2(filter, mockEventTeamHeadersFilter);

    expect(result.length).toBe(48);
    expect(result[0].eventGroup.name).toBe("Relay League");
    expect(result[1].eventGroup.name).toBe("Relay School");
  });

  test("should filter teams by athlete name 'clamp'", () => {
    const filter: IEventTeamHeaderFilterV2 = {
      eventName: "",
      gender: "ALL",
      ageGroup: { id: 0, name: "ALL" },
      hasTeams: "ALL",
      freeText: "clamp",
    };

    const result = filterEventTeamHeadersV2(filter, mockEventTeamHeadersFilter);

    expect(result.length).toBeGreaterThan(0);
    // Verify that at least one team in the results contains an athlete with "clamp" in their name
    const hasClampAthlete = result.some((header) =>
      header.compEventTeams.some((team) =>
        team.athletes.some((athlete) =>
          (athlete.firstName + " " + athlete.surName)
            .toLowerCase()
            .includes("clamp")
        )
      )
    );
    expect(hasClampAthlete).toBe(true);
  });

  test("should filter teams by athlete name 'Dds' and return header with only one team", () => {
    const filter: IEventTeamHeaderFilterV2 = {
      eventName: "",
      gender: "ALL",
      ageGroup: { id: 0, name: "ALL" },
      hasTeams: "ALL",
      freeText: "Dds",
    };

    const result = filterEventTeamHeadersV2(filter, mockEventTeamHeadersFilter);

    expect(result.length).toBeGreaterThan(0);
    // Check that the first header in results has exactly 1 team
    expect(result[0].compEventTeams.length).toBe(1);

    const firstTeam = result[0].compEventTeams[0];

    expect(firstTeam.athletes.length).toBe(2);

    // Verify that the team contains an athlete with "Dds" in their name
    const hasDdsAthlete = firstTeam.athletes.some((athlete) =>
      athlete.name!.toLowerCase().includes("dds")
    );
    expect(hasDdsAthlete).toBe(true);
  });
});
