import {
  IEventTeamHeaderFilter,
  IEventTeamHeaderFilterV2,
} from "../../../event-teams-filter/event-teams-filter-models";
import { IEventTeamHeader } from "../../../event-teams-models";
import { GENDER } from "../../../../../common/common-models";
import {
  ICompEventTeamSchool,
  ISchoolTeamAthlete,
} from "../../../school/school-team-models";

export function filterEventTeamHeadersV2(
  eventTeamHeaderFilter: IEventTeamHeaderFilter | IEventTeamHeaderFilterV2,
  eventTeamHeaders: IEventTeamHeader[],
  applyFilterHasTeams: boolean = true
): IEventTeamHeader[] {
  const eventGroupName = (eventTeamHeader: IEventTeamHeader) => {
    if (eventTeamHeaderFilter.eventName.length === 0) {
      return true;
    }
    return (
      eventTeamHeader.eventGroup.name
        .toLowerCase()
        .indexOf(eventTeamHeaderFilter.eventName.toLowerCase()) > -1
    );
  };

  const gender = (eventTeamHeader: IEventTeamHeader) => {
    if ([GENDER.UNKNOWN, "ALL"].indexOf(eventTeamHeaderFilter.gender) > -1) {
      return true;
    }
    return eventTeamHeader.gender === eventTeamHeaderFilter.gender;
  };

  const ageGroup = (eventTeamHeader: IEventTeamHeader) => {
    if (eventTeamHeaderFilter.ageGroup.id === 0) {
      return true;
    }
    return eventTeamHeader.ageGroup.id === eventTeamHeaderFilter.ageGroup.id;
  };

  const hasTeams = (eventTeamHeader: IEventTeamHeader) => {
    if (!applyFilterHasTeams) {
      return true;
    }
    //  If not applying "applyFilterHasTeams", basically just apply stuff so
    //  can tell if there is a team to "add" for that combo.
    if (eventTeamHeaderFilter.hasTeams === "ALL") {
      return true;
    }
    if (eventTeamHeaderFilter.hasTeams === "WITH") {
      return eventTeamHeader.compEventTeams.length > 0;
    }
    if (eventTeamHeaderFilter.hasTeams === "WITHOUT") {
      return eventTeamHeader.compEventTeams.length === 0;
    }
    return true;
  };

  const freeText = (eventTeamHeader: IEventTeamHeader) => {
    const filterOptions = eventTeamHeaderFilter as IEventTeamHeaderFilterV2;
    if (!filterOptions.freeText) {
      return true;
    }
    if (filterOptions.freeText.length === 0) {
      return true;
    }

    // check for team name match
    const teamNameMatch = eventTeamHeader.compEventTeams.some((team) => {
      return (
        team.teamName
          .toLowerCase()
          .indexOf(filterOptions.freeText.toLowerCase()) > -1
      );
    });

    // get team type from event header
    const teamType = eventTeamHeader.ceoptions.eventTeam.formType;

    // check for athlete name match.  Be aware there are 3 types of team:
    //  1.  Default
    //  2.  School
    //  3.  League
    //  THe athletes are stored differently for each type.
    const athleteNameMatch = eventTeamHeader.compEventTeams.some((team) => {
      // bet athletes from team by teamType
      if (teamType === "LEAGUE") {
        try {
          return team.formRows.some((formRow) => {
            if (!formRow.athlete.firstName) {
              return false;
            }
            try {
              return (
                formRow.athlete.firstName
                  .toLowerCase()
                  .indexOf(filterOptions.freeText.toLowerCase()) > -1 ||
                formRow.athlete.surName
                  .toLowerCase()
                  .indexOf(filterOptions.freeText.toLowerCase()) > -1
              );
            } catch (error) {
              console.error("EventTeamService.filterEventTeamHeaders LEAGUE", {
                error,
                formRow,
              });
              return false;
            }
          });
        } catch (error) {
          console.error("EventTeamService.filterEventTeamHeaders LEAGUE", {
            error,
            team,
          });
          return false;
        }
      }

      if (teamType === "SCHOOL") {
        try {
          return (team as unknown as ICompEventTeamSchool).athletes.some(
            (schoolTeamAthlete: ISchoolTeamAthlete) => {
              return (
                schoolTeamAthlete.name
                  .toLowerCase()
                  .indexOf(filterOptions.freeText.toLowerCase()) > -1
              );
            }
          );
        } catch (error) {
          console.error("EventTeamService.filterEventTeamHeaders SCHOOL", {
            error,
            team,
          });
          return false;
        }
      }

      if (teamType === "DEFAULT") {
        return team.athletes.some((athlete) => {
          return (
            athlete.firstName
              .toLowerCase()
              .indexOf(filterOptions.freeText.toLowerCase()) > -1 ||
            athlete.surName
              .toLowerCase()
              .indexOf(filterOptions.freeText.toLowerCase()) > -1
          );
        });
      }

      return false;
    });

    return teamNameMatch || athleteNameMatch;
  };

  const result = eventTeamHeaders
    .filter((eventTeamHeader: IEventTeamHeader) => {
      return (
        eventGroupName(eventTeamHeader) &&
        gender(eventTeamHeader) &&
        ageGroup(eventTeamHeader) &&
        hasTeams(eventTeamHeader) &&
        freeText(eventTeamHeader)
      );
    })
    .map((eventTeamHeader) => {
      // Create a copy of the event header
      const filteredHeader = { ...eventTeamHeader };

      // If we have freeText filter, filter the teams within the header
      const filterOptions = eventTeamHeaderFilter as IEventTeamHeaderFilterV2;
      if (filterOptions.freeText && filterOptions.freeText.length > 0) {
        const teamType = eventTeamHeader.ceoptions.eventTeam.formType;

        // Filter teams that match the criteria
        filteredHeader.compEventTeams = eventTeamHeader.compEventTeams.filter(
          (team) => {
            // Team name match
            const teamNameMatch =
              team.teamName
                .toLowerCase()
                .indexOf(filterOptions.freeText.toLowerCase()) > -1;

            // Athlete name match based on team type
            let athleteNameMatch = false;

            if (teamType === "LEAGUE" && team.formRows) {
              athleteNameMatch = team.formRows.some((formRow) => {
                if (!formRow.athlete.firstName) return false;
                return (
                  formRow.athlete.firstName
                    .toLowerCase()
                    .indexOf(filterOptions.freeText.toLowerCase()) > -1 ||
                  formRow.athlete.surName
                    .toLowerCase()
                    .indexOf(filterOptions.freeText.toLowerCase()) > -1
                );
              });
            } else if (teamType === "SCHOOL") {
              athleteNameMatch = (
                team as unknown as ICompEventTeamSchool
              ).athletes.some((schoolTeamAthlete: ISchoolTeamAthlete) => {
                return (
                  schoolTeamAthlete.name
                    .toLowerCase()
                    .indexOf(filterOptions.freeText.toLowerCase()) > -1
                );
              });
            } else if (teamType === "DEFAULT") {
              athleteNameMatch = team.athletes.some((athlete) => {
                return (
                  athlete.firstName
                    .toLowerCase()
                    .indexOf(filterOptions.freeText.toLowerCase()) > -1 ||
                  athlete.surName
                    .toLowerCase()
                    .indexOf(filterOptions.freeText.toLowerCase()) > -1
                );
              });
            }

            return teamNameMatch || athleteNameMatch;
          }
        );
      }

      return filteredHeader;
    });

  // console.log("EventTeamService.filterEventTeamHeaders", result);
  return result;
}
