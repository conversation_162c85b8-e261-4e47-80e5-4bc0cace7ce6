<template>
  <!--  :default-object="athlete"-->
  <CommonTypeAhead
    slot="default"
    :default-object="athlete"
    :data-function="doSearch"
    :get-object-description="getLabelForAthleteAutoComplete"
    :show-cancel-button="false"
    :is-modal="true"
    :is-disabled="isDisabled"
    :place-holder="placeHolder"
    v-on:selected="onAthleteSelected"
    v-on:reset="reset"
  >
  </CommonTypeAhead>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { AthleteService } from "../../../athlete/athlete-service";
import { AthleteData } from "../../../athlete/athlete-data";
import { EventTeamService } from "../event-team-service";
import { ConfigService } from "../../../config/config-service";
import {
  IAthleteSearch,
  IAthlete<PERSON>ummary,
} from "../../../athlete/athlete-models";
import { GenderType } from "../../../common/common-models";
import { IEntity } from "../../../config/config-app-models";
import CommonTypeAhead from "../../../common/ui/type-ahead/common-type-ahead.vue";
import { mapGetters } from "vuex";
import { CONFIG_STORE_CONST } from "../../../config/config-store";

const athleteService: AthleteService = new AthleteService();
const athleteData: AthleteData = new AthleteData();
const eventTeamService: EventTeamService = new EventTeamService();
const configService: ConfigService = new ConfigService();

@Component({
  name: "athlete-type-ahead-v2",
  components: {
    CommonTypeAhead,
  },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class AthleteTypeAhead extends Vue {
  @Prop({
    default: () => {
      return {
        id: 0,
        URN: "",
        aocode: "",
        firstName: "",
        surName: "",
      } as IAthleteSummary;
    },
  })
  public readonly athleteDefault: IAthleteSummary;

  @Prop({
    default: 1,
  })
  public readonly position: number;

  @Prop({
    default: 0,
  })
  public readonly competitionId: number;

  @Prop({
    default: 0,
  })
  public readonly ceid: number;

  @Prop({
    default: 0,
  })
  public readonly teamId: number;

  @Prop({
    default: "",
  })
  public readonly gender: GenderType;

  @Prop({
    default: "",
  })
  public readonly fieldLabel: string;
  @Prop({
    default: () => [],
  })
  public readonly ageGroupIds: number[];

  @Prop({
    default: true,
  })
  public readonly showAllAthletes: boolean;

  @Prop({
    default: () => {
      return configService.factoryEntity();
    },
  })
  public readonly userEntity: IEntity;

  @Prop({ default: false })
  public readonly isDisabled: boolean;

  @Prop({ default: "" })
  public readonly placeHolder: string;

  public athlete: IAthleteSummary = athleteService.factoryGetAthlete();
  public athletes: IAthleteSummary[] = [] as IAthleteSummary[];
  public isLoadingAthletes: boolean = false;
  public selectedAthlete: IAthleteSummary = athleteService.factoryGetAthlete();
  public searchTextEntered: string = "";

  public mounted() {
    this.athlete = R.clone(this.athleteDefault);
  }

  @Watch("athleteDefault")
  public onAthleteDefaultChanged(newValue: IAthleteSummary) {
    this.athlete = R.clone(newValue);
  }

  public getLabelForAthleteAutoComplete(athlete: IAthleteSummary): string {
    return eventTeamService.getLabelForAthleteAutoComplete(athlete);
  }

  public doSearch(searchKey: string) {
    this.searchTextEntered = searchKey;

    const athleteSearch: IAthleteSearch =
      athleteService.factoryGetAthleteSearch();
    athleteSearch.search = searchKey;
    athleteSearch.gender = this.gender;
    athleteSearch.ageGroupId = this.ageGroupIds.join(",");
    athleteSearch.ceid = this.ceid;
    athleteSearch.showAllAthletes = this.showAllAthletes ? "1" : "0";

    if (this.userEntity.id > 0) {
      if (this.userEntity.entityName === "Region") {
        athleteSearch.region = this.userEntity.name;
      }
      if (this.userEntity.entityName === "County") {
        athleteSearch.county = this.userEntity.name;
      }
      if (this.userEntity.entityName === "Club") {
        athleteSearch.club = this.userEntity.name;
      }
    }

    this.isLoadingAthletes = true;
    const teamId = 0;
    const pageNumber = 1;
    const pageSize = 5;
    const orderByProperty = "surname";

    return athleteData.findAthletesV2(
      this.competitionId,
      teamId,
      pageNumber,
      pageSize,
      orderByProperty,
      athleteSearch
    );
  }

  public get getAthleteDefaultName() {
    if (this.athlete.firstName.length > 0 || this.athlete.surName.length > 0) {
      return this.athlete.firstName + " " + this.athlete.surName;
    }
    return "";
  }

  public onAthleteSelected(ath: IAthleteSummary) {
    this.athlete = R.clone(ath);
    this.$emit("athleteSelected", {
      athlete: R.clone(ath),
      position: this.position,
    });
  }

  public reset() {
    this.athlete = athleteService.factoryGetAthlete();
    this.$emit("reset", this.position);
  }
}
</script>
