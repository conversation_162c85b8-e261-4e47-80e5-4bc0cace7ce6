<template>
  <div class="e4s-flex-row">
    <div class="e4s-card e4s-card--generic e4s-full-width">
      <div
        class="e4s-flex-column e4s-gap--standard"
        v-if="showSection === 'HEADER'"
      >
        <!--<header>-->
        <div class="e4s-flex-row e4s-justify-flex-space-between e4s-flex-start">
          <EventTeamHeaderNameV2
            class="e4s-input--label"
            :event-team-header="eventTeamHeader"
          />

          <EventTeamHeaderStatusPillV2 :event-team-header="eventTeamHeader" />
        </div>
        <!--</header>-->

        <div class="e4s-flex-row">
          <div class="e4s-gap--standard e4s-subheader--200">
            <div
              v-if="eventTeamHeader.ceoptions.helpText.length > 0"
              v-text="eventTeamHeader.ceoptions.helpText"
            ></div>

            <!--<warning text>-->
            <div
              class="e4s-info-text--error"
              v-if="getWarningMessage.length > 0"
              v-text="getWarningMessage"
            ></div>
            <!--</warning text>-->
          </div>
        </div>

        <div class="e4s-flex-column e4s-gap--standard">
          <div
            class="e4s-flex-row e4s-gap--standard"
            v-for="teamSimple in getTeamNamesSimple"
            :key="teamSimple.name"
          >
            <div v-text="teamSimple.name + ' - '"></div>
            <div v-text="teamSimple.athletes"></div>
          </div>
        </div>

        <div class="e4s-flex-row e4s-justify-flex-space-between">
          <div class="e4s-flex-row">
            <div
              v-if="
                eventTeamHeader.compEventTeams.length > 0 &&
                !isClubCompInfoCompetition
              "
              class="e4s-flex-row e4s-gap--large"
            >
              <CounterV2 text="Cart" :counter="getStatus.cart" />
              <CounterV2 text="Paid" :counter="getStatus.paid" />
            </div>
          </div>

          <button-generic-v2
            text="Event Team Details"
            @click="setSection('TEAMS')"
            class="e4s-button--auto"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { format, parse } from "date-fns";
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import { ConfigService } from "../../../config/config-service";
import EntitySelect from "../../../config/entity/entity-select.vue";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import { USER_MESSAGE_LEVEL } from "../../../user-message/user-message-models";
import { messageDispatchHelper } from "../../../user-message/user-message-store";
import { ATH_COMP_SCHED_STORE_CONST } from "../../store/athleteCompSched-store";
import { COMP_EVENT_TEAMS_STORE_CONST } from "../comp-event-store";
import {
  ICompEventTeam,
  ICompEventTeamBase,
  IEventTeamHeader,
} from "../event-teams-models";
import { ICompEventTeamSchool } from "../school/school-team-models";
import { SchoolTeamData } from "../school/school-team-data";
import { CompEventTeamData } from "../comp-event-team-data";
import { IConfigApp, IEntity } from "../../../config/config-app-models";
import { GENDER, IServerResponse } from "../../../common/common-models";
import { ResourceData } from "../../../common/resource/resource-service";
import { IEventTeam, ShowFormType } from "../../athletecompsched-models";
import { EventTeamService, TeamLimits } from "../event-team-service";
import { SchoolTeamService } from "../school/school-team-service";
import {
  ICompetitionInfo,
  ICompetitionSummaryPublic,
} from "../../../competition/competition-models";
import CardGenericV2 from "../../../common/ui/layoutV2/card-generic-v2.vue";
import ModalV2 from "../../../common/ui/layoutV2/modal/modal-v2.vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import LaunchHeaderBlankV2 from "../../../launch/v2/launch-header-blank-v2.vue";
import ButtonGenericBackV2 from "../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import EventTeamHeaderNameV2 from "./event-team-header-name-v2.vue";
import EventTeamHeaderStatusPillV2 from "./event-team-header-status-pill-v2.vue";
import CounterV2 from "../../../common/ui/layoutV2/counter/counter-v2.vue";
import ButtonProcess from "../../../common/ui/layoutV2/buttons/button-process.vue";
import * as ClubCompInfoService from "../../../entry/v2/schools/clubCompInfoService";
import { IClubCompInfo } from "../../../entry/v2/schools/clubCompInfo-models";

const configService: ConfigService = new ConfigService();

type SectionType = "HEADER" | "TEAMS" | "ADD" | "EDIT" | "ELIGIBLE";

@Component({
  name: "event-team-header-v2",
  components: {
    ButtonProcess,
    CounterV2,
    EventTeamHeaderStatusPillV2,
    EventTeamHeaderNameV2,
    LoadingSpinnerV2,
    ButtonGenericBackV2,
    LaunchHeaderBlankV2,
    ButtonGenericV2,
    ModalV2,
    CardGenericV2,
    EntitySelect,
  },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
      userEntityStore: (state: any) => state.userEntity,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class EventTeamHeader extends Vue {
  public readonly configApp: IConfigApp;
  // public userEntity: IEntity;

  @Prop({ default: -11 }) public index: number;
  @Prop({
    required: true,
  })
  public readonly competition: ICompetitionInfo;
  @Prop({
    required: true,
  })
  public readonly eventTeamHeader: IEventTeamHeader;

  @Prop({
    default: () => {
      return configService.factoryEntity();
    },
  })
  public readonly userEntity: IEntity;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly userEntities: IEntity[];

  public gender = GENDER;
  public eventTeamService: EventTeamService = new EventTeamService();
  public schoolTeamService: SchoolTeamService = new SchoolTeamService();
  public displayMoreInfoComponent: boolean = false;
  public compEventTeamNew: ICompEventTeam;
  public showNewTeam: boolean = false;
  public isLoading: boolean = false;
  public formType: ShowFormType = "DEFAULT";
  public userEntitySelected: IEntity = configService.factoryEntity();
  public showSection: SectionType = "HEADER";
  public editCompEventTeam: ICompEventTeamBase | null = null;

  public teamLimits: TeamLimits = {
    maxTeamsReached: false,
    getTeamEventHasSecurity: false,
    isEntitySelected: false,
    canAddTeam: true,
    doesUserHaveAccessToThisEvent: true,
  };

  public showExistingTeams = false;

  public created() {
    this.init();
  }

  public setSection(sectionName: SectionType) {
    // this.showSection = sectionName;
    this.$emit("selected", this.eventTeamHeader);
  }

  public get getEntityForUserLabel(): string {
    const entity = this.userEntity;
    return entity.id === 0
      ? "Public Access"
      : entity.entityName + " - " + entity.name;
  }

  @Watch("userEntity", { immediate: true })
  public onUserEntityChanged(newValue: IEntity) {
    this.init();
  }

  @Watch("userEntities", { immediate: true })
  public onUserEntitiesChanged(newValue: IEntity[]) {
    this.init();
  }

  public init() {
    // const userEntities = this.userEntities.filter( (entity) => {
    //   return entity.id >  0;
    // })
    // if (userEntities.length === 0) {
    //   this.userEntity = simpleClone(userEntities[0]);
    // }

    this.formType = this.eventTeamService.displayWhichFormType(
      this.eventTeamHeader
    );

    this.compEventTeamNew = this.eventTeamService.factoryGetCompEventTeam(
      this.eventTeamHeader,
      this.userEntity
    );
    this.setUpEditObject();

    this.teamLimits = this.eventTeamService.getTeamLimits(
      this.eventTeamHeader,
      this.userEntity,
      this.configApp.userInfo
    );
  }

  public setUpEditObject() {
    let editCompEventTeam: ICompEventTeamBase =
      this.eventTeamService.factoryGetCompEventTeamBase(
        this.eventTeamHeader,
        this.userEntity
      );
    if (this.formType === "DEFAULT") {
      editCompEventTeam = this.eventTeamService.factoryGetCompEventTeam(
        this.eventTeamHeader,
        this.userEntity
      );
    }
    if (this.formType === "SCHOOL") {
      editCompEventTeam = this.schoolTeamService.factoryCompEventTeamSchool(
        this.eventTeamHeader,
        this.userEntity
      );
    }
    editCompEventTeam.ceid = this.eventTeamHeader.id;
    this.editCompEventTeam = editCompEventTeam;
  }

  public get getEventDate() {
    return format(parse(this.eventTeamHeader.startdate), "Do MMM YY");
  }

  public get getEventTime() {
    let startTime = format(parse(this.eventTeamHeader.startdate), "HH:mm");
    startTime = startTime === "00:00" ? "TBC" : startTime;
    return startTime;
  }

  // public get getGender() {
  //   return commonService.getGenderLabel(this.eventTeamHeader.gender);
  // }

  public get getEventTeamOptions(): IEventTeam {
    return this.eventTeamService.getEventTeamOptions(this.eventTeamHeader);
  }

  public get getTeamAthleteMin() {
    const eventTeam: IEventTeam = this.getEventTeamOptions;
    return eventTeam ? eventTeam.min : 0;
  }

  public get getTeamAthleteMax() {
    const eventTeam: IEventTeam = this.getEventTeamOptions;
    return eventTeam ? eventTeam.max : 0;
  }

  public get getShowAthleteMinMax() {
    return this.getTeamAthleteMin > 0 || this.getTeamAthleteMax > 0;
  }

  public showMoreInfo(showIt: boolean) {
    this.displayMoreInfoComponent = showIt;
  }

  public get showCloseTeams() {
    return this.displayMoreInfoComponent && !this.showNewTeam;
  }

  public get getShowPrice(): boolean {
    if (this.eventTeamHeader.ceoptions.rowOptions) {
      return this.eventTeamHeader.ceoptions.rowOptions.showPrice;
    }
    return false;
  }

  public get getPrice() {
    //  "€"
    return this.eventTeamHeader.price && this.eventTeamHeader.price.curPrice
      ? this.configApp.currency + this.eventTeamHeader.price.curPrice.toFixed(2)
      : "";
  }

  public get getOptionsMaxEventTeams(): number {
    return this.eventTeamService.getOptionsMaxEventTeams(this.eventTeamHeader);
  }

  public get getShowMaxEventTeams(): boolean {
    return this.getOptionsMaxEventTeams > 0;
  }

  public get canAddTeam(): boolean {
    return this.eventTeamService.canAddTeamFromTeamLimits(
      this.eventTeamHeader,
      this.teamLimits
    );
  }

  public get getWarningMessage(): string {
    if (this.eventTeamHeader.isDisabled) {
      if (this.doesUserHaveAccessToThisEvent) {
        // return "Please select from drop down above.";
        return "";
      }
      return this.eventTeamHeader.ceoptions.warningMessage;
    }
    return this.eventTeamService.maxTeamsReached(this.eventTeamHeader)
      ? "Max Teams Entered"
      : "";
  }

  public get doesUserHaveAccessToThisEvent(): boolean {
    return this.eventTeamService.doesUserHaveAccessToThisEvent(
      this.configApp.userInfo,
      this.eventTeamHeader
    );
  }

  public get getShowAddTeamButton(): boolean {
    return this.canAddTeam && !this.showNewTeam;
  }

  public get getShowCancelTeamButton(): boolean {
    return this.canAddTeam && this.showNewTeam;
  }

  public addCompEventTeam() {
    console.log("add team");
    const compEventTeam: ICompEventTeam =
      this.eventTeamService.factoryGetCompEventTeam(
        this.eventTeamHeader,
        this.userEntity
      );
    this.compEventTeamNew = compEventTeam;

    this.setUpEditObject();

    // this.showMoreInfo(true);
    // this.showNewTeam = true;
    this.showSection = "ADD";
  }

  public editTeam(compEventTeam: ICompEventTeamBase) {
    this.editCompEventTeam = R.clone(compEventTeam);
    // this.showMoreInfo(true)

    // this.showNewTeam = true;
    this.showSection = compEventTeam.id > 0 ? "EDIT" : "ADD";
  }

  public cancelTeam() {
    this.setUpEditObject();
    // this.showMoreInfo(false);
    this.showNewTeam = false;
  }

  public closeAddCompEventTeam() {
    this.showNewTeam = false;
    if (this.eventTeamHeader.compEventTeams.length === 0) {
      this.showMoreInfo(false);
    }
  }

  public submitDefault(compEventTeam: ICompEventTeam) {
    const compEventTeamData: CompEventTeamData = new CompEventTeamData();
    const compEventTeamStripped: ICompEventTeam =
      this.eventTeamService.stripEventCompEventTeam(compEventTeam);
    console.log(compEventTeam);
    this.submitTeam(compEventTeamStripped, compEventTeamData);
  }

  public submitSchool(compEventTeam: ICompEventTeamSchool) {
    const schoolTeamData: SchoolTeamData = new SchoolTeamData();
    this.submitTeam(compEventTeam, schoolTeamData);
  }

  public submitTeam(
    compEventTeam: ICompEventTeamBase,
    resourceData: ResourceData<ICompEventTeamBase>
  ) {
    let prom;
    this.isLoading = true;
    if (compEventTeam.id > 0) {
      prom = resourceData.update(compEventTeam);
    } else {
      compEventTeam.entityLevel = this.userEntity.entityLevel;
      compEventTeam.entityId = this.userEntity.id;
      prom = resourceData.create(compEventTeam);
    }
    prom
      .then((response: IServerResponse<ICompEventTeamBase>) => {
        if (response.errNo > 0) {
          messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR);
          return;
        }
        messageDispatchHelper(
          response.message && response.message.length > 0
            ? response.message
            : "Team " + compEventTeam.teamName + " saved to Order Summary",
          USER_MESSAGE_LEVEL.INFO
        );
        this.standardSuccess();
        this.closeAddCompEventTeam();
      })
      .catch((error: any) => {
        this.standardCatch(error);
        return;
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public deleteCompEventTeam(compEventTeam: ICompEventTeam) {
    const compEventTeamData: CompEventTeamData = new CompEventTeamData();

    if (this.configApp.userId !== compEventTeam.userId) {
      messageDispatchHelper(
        "Team " +
          compEventTeam.teamName +
          " not deleted, entered by: " +
          compEventTeam.userName,
        USER_MESSAGE_LEVEL.WARN
      );
      return false;
    }

    this.isLoading = true;
    return compEventTeamData
      .delete(compEventTeam.id)
      .then((response: IServerResponse<ICompEventTeam>) => {
        this.isLoading = false;
        if (response.errNo > 0) {
          messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR);
          return;
        }
        messageDispatchHelper(
          "Team " + compEventTeam.teamName + " deleted.",
          USER_MESSAGE_LEVEL.INFO
        );
        this.standardSuccess();
        return;
      })
      .catch((error) => {
        this.standardCatch(error);
        return;
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public standardSuccess() {
    this.$store.dispatch(
      COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME +
        "/" +
        COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_ACTIONS_GET_TEAM_HEADERS,
      { compId: this.competition.id }
    );

    this.$store.dispatch(
      ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_GET_CART
    );
  }

  public get displayWhichFormType() {
    return this.eventTeamService.displayWhichFormType(this.eventTeamHeader);
  }

  public get getXiText() {
    return this.eventTeamHeader.ceoptions &&
      this.eventTeamHeader.ceoptions.xiText
      ? this.eventTeamHeader.ceoptions.xiText
      : "";
  }

  // public get hasCompEventHelpText() {
  //   return this.eventTeamHeader.ceoptions &&
  //     this.eventTeamHeader.ceoptions.helpText.length > 0
  //     ? true
  //     : false;
  // }

  public onUserEntitySelected(entity: IEntity) {
    this.userEntitySelected = { ...entity };
  }

  public standardCatch(error: any) {
    this.isLoading = false;
    messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR);
    return;
  }

  public get getShowEntitySelect(): boolean {
    return this.userEntities.length > 1 && !this.showNewTeam;
  }

  // public get getEventSecurityLabel(): string | "Public" {
  //   const label = this.eventTeamService.getEventSecurityLabel(
  //     this.eventTeamHeader
  //   );
  //   return "Security: " + (label.length === 0 ? "Public" : label);
  // }

  public get getTeamEventHasSecurity(): boolean {
    return this.eventTeamService.getTeamEventHasSecurity(this.eventTeamHeader);
  }

  public get getUserEntityLabel() {
    return this.userEntities
      .map((ent) => {
        return "[" + ent.id + "] " + ent.entityName + " - " + ent.name;
      })
      .join(", ");
  }

  public get getStatus() {
    const paidStatus = this.eventTeamService.getPaidStatus(
      this.eventTeamHeader
    );
    return paidStatus;
  }

  public get isClubCompInfoCompetition() {
    const clubCompInfo: IClubCompInfo = (
      this.competition as any as ICompetitionSummaryPublic
    ).clubCompInfo;
    return ClubCompInfoService.isClubCompInfoCompetition(clubCompInfo);
  }

  public get getTeamNamesSimple(): { name: string; athletes: string }[] {
    return this.eventTeamService.getTeamNamesSimple(this.eventTeamHeader);
  }

  //
  // public get getMaxTeamReached() {
  //     return this.eventTeamService.maxTeamsReached(this.eventTeamHeader);
  // }
}
</script>
