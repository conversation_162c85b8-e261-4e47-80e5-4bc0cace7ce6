import { IEventTeamHeader } from "../../event-teams-models";
// import { ShowFormType } from "../../../athletecompsched-models";

/**
 * Formats the event team header display text for dropdown options
 * @param eventHeader The event team header to format
 * @returns Formatted display text for the event team header
 */
export function formatEventTeamHeaderDisplay(
  eventHeader: IEventTeamHeader
): string {
  // const formType = eventHeader.ceoptions.eventTeam.formType;
  // let formTypeDisplay = formType as any as string;
  // if (typeof formType === "undefined") {
  //   formTypeDisplay = "";
  // } else {
  //   formTypeDisplay =
  //     formTypeDisplay.length > 0 ? " [" + formTypeDisplay + "]" : "";
  // }

  return eventHeader.eventGroup.name + (eventHeader.IsOpen ? "" : " (Closed)");
}
