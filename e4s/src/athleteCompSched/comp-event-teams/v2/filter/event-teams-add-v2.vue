<template>
  <div>
    <!--    dropDownSteps{{ dropDownSteps }}-->

    <FormGenericFieldGridV2>
      <template slot="content">
        <FormGenericInputTemplateV2 form-label="Event">
          <select
            slot="field"
            class="browser-default e4s-input-field e4s-input-field--primary"
            v-on:change="onFilterChangeEventName"
            v-model="dropDownSteps.eventGroupName"
          >
            <option
              v-for="eventHeader in optionsTeam"
              :key="eventHeader.eventGroup.id"
              :value="eventHeader.eventGroup.name"
              :disabled="!eventHeader.IsOpen"
              v-text="formatEventTeamHeaderDisplay(eventHeader)"
            ></option>
          </select>
        </FormGenericInputTemplateV2>

        <FormGenericInputTemplateV2 form-label="Gender">
          <template slot="field">
            <div class="e4s-flex-column">
              <div
                class="
                  e4s-flex-row e4s-flex-nowrap
                  e4s-input--container
                  e4s-gap--standard
                "
              >
                <label v-for="gen in optionsGender" :key="gen">
                  <input
                    type="radio"
                    class="
                      browser-default
                      e4s-input-field e4s-input-field--primary
                    "
                    :value="gen"
                    v-model="dropDownSteps.gender"
                    v-on:change="onFilterChangeGender"
                  />
                  <span v-text="getGenderLabel(gen)"></span>
                </label>
              </div>
            </div>
          </template>
        </FormGenericInputTemplateV2>
      </template>
    </FormGenericFieldGridV2>

    <FormGenericFieldGridV2>
      <template slot="content">
        <FormGenericInputTemplateV2 form-label="Age Group">
          <template slot="field">
            <!--            optionsAgeGroup.length {{ optionsAgeGroup.length }}-->
            <FieldTextV2
              :value="getAgeGroupDropDownLabel(optionsAgeGroup[0])"
              :is-disabled="true"
              v-if="optionsAgeGroup.length === 1"
            />
            <select
              v-if="optionsAgeGroup.length > 1"
              class="browser-default e4s-input-field e4s-input-field--primary"
              @change="onFilterChangeAgeGroup"
              name="gender"
              v-model="dropDownSteps.ageGroupId"
            >
              <option
                v-for="ageGroup in optionsAgeGroup"
                :value="ageGroup.id"
                v-text="getAgeGroupDropDownLabel(ageGroup)"
              ></option>
            </select>
          </template>
        </FormGenericInputTemplateV2>
      </template>
    </FormGenericFieldGridV2>

    <InfoSectionV2
      info-type="error"
      v-if="getCanAddTeamResult.message.length > 0"
      style="margin-bottom: var(--e4s-gap--standard)"
    >
      <span
        v-text="getCanAddTeamResult.message"
        class="e4s-info-text--error"
      ></span>
    </InfoSectionV2>

    <!--    {{ getCanAddTeamResult }}-->

    <div class="e4s-flex-row">
      <InputCheckboxV2
        v-if="isAdmin && !getCanAddTeamResult.canAdd"
        class="e4s-admin--section"
        v-model="adminOverride"
        value-label="Admin Override"
      />

      <div
        class="
          e4s-flex-row
          e4s-gap--standard
          e4s-flex-row--end
          e4s-justify-flex-row-vert-center
        "
      >
        <EventTeamHeaderStatusPillV2
          :event-team-header="eventTeamHeadersSelected"
          style="height: fit-content"
          v-if="eventTeamHeadersSelected.id > 0"
        />
        <ButtonGenericV2
          text="Add Team"
          v-on:click="addTeam"
          :disabled="!getCanAddTeamResult.canAdd && !adminOverride"
          :class="adminOverride ? 'e4s-button--admin' : ''"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { EventTeamService } from "../../event-team-service";
import { IEventTeamHeader } from "../../event-teams-models";
import { GENDER, GenderType } from "../../../../common/common-models";
import { IAgeGroup } from "../../../../agegroup/agegroup-models";
import {
  convertArrayToObject,
  pluckUnique,
} from "../../../../common/common-service-utils";
import FormGenericInputTemplateV2 from "../../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import FormGenericFieldGridV2 from "../../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import EventTeamHeaderStatusPillV2 from "../event-team-header-status-pill-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../../config/config-store";
import {
  IConfigApp,
  IEntity,
  IUserInfo,
} from "../../../../config/config-app-models";
import { ConfigService } from "../../../../config/config-service";
import { formatEventTeamHeaderDisplay } from "./event-team-header-formatter";
import InfoSectionV2 from "../../../../common/ui/layoutV2/info-section-v2.vue";
import { ICompetitionSummaryPublic } from "../../../../competition/competition-models";
import * as ClubCompInfoService from "../../../../entry/v2/schools/clubCompInfoService";
import InputCheckboxV2 from "../../../../common/ui/layoutV2/fields/input-checkbox-v2.vue";
import FieldTextV2 from "../../../../common/ui/layoutV2/fields/field-text-v2.vue";

const eventTeamService: EventTeamService = new EventTeamService();
const configService: ConfigService = new ConfigService();

type TeamAddDropDownSteps = {
  eventGroupName: string;
  gender: GenderType | "";
  ageGroupId: number;
};

@Component({
  name: "event-teams-add-v2",
  components: {
    FieldTextV2,
    InputCheckboxV2,
    InfoSectionV2,
    ButtonGenericV2,
    EventTeamHeaderStatusPillV2,
    FormGenericFieldGridV2,
    FormGenericInputTemplateV2,
  },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
      userInfo: (state: any) => state.configApp.userInfo,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class EventTeamsFilter extends Vue {
  public readonly isAdmin: boolean;
  public readonly configApp: IConfigApp;
  public readonly userInfo: IUserInfo;

  public readonly gender = GENDER;

  @Prop({
    required: true,
  })
  public readonly competition: ICompetitionSummaryPublic;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly eventTeamHeaders: IEventTeamHeader[];

  @Prop({
    default: () => {
      return configService.factoryEntity();
    },
  })
  public readonly userEntity: IEntity;

  public optionsTeam: IEventTeamHeader[] = [];
  public optionsTeamName: string[] = [];
  public eventTeamHeadersEventName: IEventTeamHeader[] = [];

  public optionsGender: GenderType[] = [];
  public eventTeamHeadersGender: IEventTeamHeader[] = [];

  public optionsAgeGroup: IAgeGroup[] = [];
  public eventTeamHeadersAgeGroup: IEventTeamHeader[] = [];

  public dropDownSteps: TeamAddDropDownSteps = {
    eventGroupName: "",
    gender: "",
    ageGroupId: 0,
  };

  public eventTeamHeadersSelected: IEventTeamHeader =
    eventTeamService.factoryEventTeamHeader();

  public adminOverride: boolean = false;

  public get isClubCompInfoCompetition() {
    return ClubCompInfoService.isClubCompInfoCompetition(
      this.competition.clubCompInfo
    );
  }

  @Watch("eventTeamHeaders", { immediate: true })
  public onEventTeamHeadersChanged() {
    this.optionsTeamName = pluckUnique<IEventTeamHeader, string>(
      (evtHeader) => {
        return evtHeader.eventGroup.name;
      },
      this.eventTeamHeaders
    );

    const uniqueEvents = convertArrayToObject(
      (evtTeamHeader) => evtTeamHeader.eventGroup.name,
      this.eventTeamHeaders
    );

    let optionsTeam = Object.values(uniqueEvents);

    // If this is a club comp info competition, filter out team events that the user cannot enter
    if (this.isClubCompInfoCompetition) {
      const canUserEnterRelayTeams =
        this.competition.clubCompInfo.configData.maxRelays > 0;
      const canUserEnterMixedTeams =
        this.competition.clubCompInfo.configData.maxMixedRelays > 0;

      // If user cannot enter mixed teams, filter them out, but leave any that are excluded from count rule
      if (!canUserEnterMixedTeams) {
        optionsTeam = optionsTeam.filter((evtHeader) => {
          const isMixed = evtHeader.gender === "O";
          const isExcluded = evtHeader.ceoptions.excludeFromCntRule;

          return !isMixed || isExcluded;
        });
      }

      // If user cannot enter relay teams, filter them out, but leave any that are excluded from count rule
      if (!canUserEnterRelayTeams) {
        optionsTeam = optionsTeam.filter((evtHeader) => {
          const isExcluded = evtHeader.ceoptions.excludeFromCntRule;
          return (
            (evtHeader.gender !== "M" && evtHeader.gender !== "F") || isExcluded
          );
        });
      }
    }

    this.optionsTeam = optionsTeam;

    // this.optionsTeam = pluckUnique<IEventTeamHeader, string>((evtHeader) => {
    //   return evtHeader.eventGroup.name;
    //   // (evtHeader.isDisabled ? " (Disabled)" : "") +
    // }, this.eventTeamHeaders);
    this.onFilterChangeEventName();
  }

  /**
   * Filters the event team headers based on the selected event name.
   *
   * @return {void} Does not return any value.
   */
  public onFilterChangeEventName() {
    const eventNameFilter = (eventTeamHeader: IEventTeamHeader) => {
      return (
        eventTeamHeader.eventGroup.name
          .toLowerCase()
          .indexOf(this.dropDownSteps.eventGroupName.toLowerCase()) > -1
      );
    };

    this.dropDownSteps.gender = "";
    this.dropDownSteps.ageGroupId = 0;
    this.optionsAgeGroup = [];
    this.eventTeamHeadersSelected = eventTeamService.factoryEventTeamHeader();

    this.eventTeamHeadersEventName =
      this.eventTeamHeaders.filter(eventNameFilter);

    this.optionsGender = pluckUnique<IEventTeamHeader, GenderType>(
      "gender",
      this.eventTeamHeadersEventName
    );
    if (this.optionsGender.length === 1) {
      this.dropDownSteps.gender = this.optionsGender[0];
      this.onFilterChangeGender();
    }

    // this.eventTeamHeadersGender = this.eventTeamHeadersEventName.filter(genderFilter);

    // this.optionsAgeGroup = pluckUnique<IEventTeamHeader, IAgeGroup>("ageGroup", this.eventTeamHeadersGender);
    // this.eventTeamHeadersAgeGroup = this.eventTeamHeadersGender.filter(ageGroupFilter);
    //
    // if (this.eventTeamHeadersAgeGroup.length === 1) {
    //   this.eventTeamHeadersSelected = this.eventTeamHeadersAgeGroup[0];
    // }
  }

  /**
   * Filters the event team headers based on the selected gender.
   *
   * @return {void} Does not return any value.
   */
  public onFilterChangeGender() {
    const genderFilter = (eventTeamHeader: IEventTeamHeader) => {
      return (
        eventTeamHeader.gender === this.dropDownSteps.gender &&
        eventTeamHeader.eventGroup.name === this.dropDownSteps.eventGroupName
      );
    };

    this.dropDownSteps.ageGroupId = 0;
    this.eventTeamHeadersSelected = eventTeamService.factoryEventTeamHeader();

    this.eventTeamHeadersGender =
      this.eventTeamHeadersEventName.filter(genderFilter);
    this.optionsAgeGroup = pluckUnique<IEventTeamHeader, IAgeGroup>(
      "ageGroup",
      this.eventTeamHeadersGender
    );
    if (this.optionsAgeGroup.length === 1) {
      this.dropDownSteps.ageGroupId = this.optionsAgeGroup[0].id;
      this.onFilterChangeAgeGroup();
    }
  }

  /**
   * Filters the event team headers based on the selected age group.
   *
   * @return {void} Does not return any value.
   */
  public onFilterChangeAgeGroup() {
    this.eventTeamHeadersSelected = eventTeamService.factoryEventTeamHeader();

    // evt.gender === this.dropDownSteps.gender &&
    // evt.eventGroup.name === this.dropDownSteps.eventGroupName

    this.eventTeamHeadersAgeGroup = this.eventTeamHeadersGender.filter(
      (evt) => {
        return (
          evt.ageGroup.id === this.dropDownSteps.ageGroupId &&
          evt.gender === this.dropDownSteps.gender &&
          evt.eventGroup.name === this.dropDownSteps.eventGroupName
        );
      }
    );

    if (this.eventTeamHeadersAgeGroup.length === 1) {
      this.eventTeamHeadersSelected = this.eventTeamHeadersAgeGroup[0];
    }
  }

  /**
   * Emits an "addTeam" event with the selected team headers data.
   *
   * @return {void} Does not return any value.
   */
  public addTeam() {
    console.log("addTeam()", this.eventTeamHeadersSelected);
    this.$emit("addTeam", this.eventTeamHeadersSelected);
  }

  public getGenderLabel(genderCode: GenderType) {
    switch (genderCode) {
      case "O":
        return "Mixed";
      case "M":
        return "Male";
      case "F":
        return "Female";
      default:
        return "Unknown";
    }
  }

  /**
   * Determines the message regarding the ability to add a team based on event team header,
   * user entity, and competition configuration. It checks various conditions such as team limits,
   * competition type, and event status, and returns an appropriate message.
   *
   * @return {string} A message indicating the status or requirements for adding a team.
   * Possible messages include "Please select a team.", "Entries Closed", "Max Teams Reached",
   * "Please select an entity.", "Event Disabled", or an empty string if conditions are met for adding a team.
   */
  public get getCanAddTeamMessage(): string {
    if (this.eventTeamHeadersSelected.id === 0) {
      return "Please select a team.";
    }
    const eventTeamHeader = this.eventTeamHeadersSelected;

    // continue with normal checks
    const teamLimits = eventTeamService.getTeamLimits(
      eventTeamHeader,
      this.userEntity,
      this.configApp.userInfo
    );

    console.log("getTeamLimits", teamLimits);

    if (eventTeamHeader.ceoptions.eventTeam.maxEventTeams === -1) {
      return "Entries Closed";
    }

    if (teamLimits.maxTeamsReached) {
      return "Max Teams Reached";
    }

    if (teamLimits.getTeamEventHasSecurity) {
      return teamLimits.isEntitySelected ? "" : "Please select an entity.";
    }

    if (eventTeamHeader.isDisabled) {
      return "Event Disabled";
    }

    // Is clubCompInfo comp, have different checks
    if (this.isClubCompInfoCompetition) {
      // it's a mixed gender event.  We need to run specific checks
      const result = ClubCompInfoService.hasMaxClubInfoTeamsBeenReachedResult(
        this.competition.clubCompInfo,
        eventTeamHeader,
        this.eventTeamHeaders
      );
      console.log("hasMaxClubInfoTeamsBeenReachedResult", result);
      return result.message;
      // Don't need to run any further checks.
    }

    return "";

    // return eventTeamService.canAddTeamFromTeamLimits(
    //   eventTeamHeader,
    //   teamLimits
    // );
  }

  /**
   * Determines the message regarding the ability to add a team based on event team header,
   * user entity, and competition configuration. It checks various conditions such as team limits,
   * competition type, and event status, and returns an appropriate message.
   *
   * @return {string} A message indicating the status or requirements for adding a team.
   * Possible messages include "Please select a team.", "Entries Closed", "Max Teams Reached",
   * "Please select an entity.", "Event Disabled", or an empty string if conditions are met for adding a team.
   */
  public get getCanAddTeamResult(): {
    message: string;
    canAdd: boolean;
  } {
    const result = {
      message: "",
      canAdd: true,
    };

    if (this.eventTeamHeadersSelected.id === 0) {
      // return "Please select a team.";
      result.canAdd = false;
      result.message = "Please select a team.";
      return result;
    }
    const eventTeamHeader = this.eventTeamHeadersSelected;

    //
    if (eventTeamHeader.ceoptions.excludeFromCntRule) {
      // return "Entries Closed (s)";
      result.canAdd = true;
      result.message = "";
      return result;
    }

    // continue with normal checks
    const teamLimits = eventTeamService.getTeamLimits(
      eventTeamHeader,
      this.userEntity,
      this.configApp.userInfo
    );

    console.log("getTeamLimits", teamLimits);

    if (eventTeamHeader.ceoptions.eventTeam.maxEventTeams === -1) {
      result.canAdd = false;
      result.message = "Entries Closed";
      return result;
    }

    if (teamLimits.maxTeamsReached) {
      result.canAdd = false;
      result.message = "Max Teams Reached";
      return result;
    }

    if (teamLimits.getTeamEventHasSecurity) {
      result.canAdd = false;
      result.message = "Please select an entity.";
      return result;
    }

    if (eventTeamHeader.isDisabled) {
      result.canAdd = false;
      result.message = "Event Disabled";
      return result;
    }

    // Is clubCompInfo comp, have different checks
    if (this.isClubCompInfoCompetition) {
      // it's a mixed gender event.  We need to run specific checks
      const resultClubInfo =
        ClubCompInfoService.hasMaxClubInfoTeamsBeenReachedResult(
          this.competition.clubCompInfo,
          eventTeamHeader,
          this.eventTeamHeaders
        );
      console.log("hasMaxClubInfoTeamsBeenReachedResult", resultClubInfo);
      // return result.message;
      if (resultClubInfo.reached) {
        result.canAdd = !resultClubInfo.reached;
        result.message = resultClubInfo.message;
        return result;
      }
      // Don't need to run any further checks.
    }

    return result;
  }

  public getAgeGroupDropDownLabel(ageGroup: IAgeGroup | null | undefined) {
    if (!ageGroup || typeof ageGroup === "undefined" || ageGroup.id === 0) {
      return "";
    }

    return ageGroup.name + (this.isAdmin ? " (" + ageGroup.id + ")" : "");
  }

  // Add the formatter function to the component
  public formatEventTeamHeaderDisplay = formatEventTeamHeaderDisplay;
}
</script>
