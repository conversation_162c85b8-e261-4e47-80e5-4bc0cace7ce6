import { computed, reactive } from "vue";
import { ICompEventTeamBase } from "../event-teams-models";
import { IEventTeamHeader } from "../event-teams-models";
import { useConfigController } from "../../../config/useConfigStore";
import { IEntity } from "../../../config/config-app-models";
import { ConfigService } from "../../../config/config-service";

const configService = new ConfigService();

export interface ITeamControllerState {
  isUserOwner: boolean;
  canEditTeamName: boolean;
  canDelete: boolean;
}

export interface ITeamControllerInput {
  competition: {
    compId: number;
    compOrgId: number;
  };
  eventTeamHeader: IEventTeamHeader;
  compEventTeam: ICompEventTeamBase;
  userEntity: IEntity;
}

export function useTeamController() {
  let input: ITeamControllerInput | null = null;

  const state = reactive<ITeamControllerState>({
    isUserOwner: false,
    canEditTeamName: false,
    canDelete: false,
  });

  const configController = useConfigController();

  function init(teamControllerInput: ITeamControllerInput) {
    input = teamControllerInput;

    const isUserOwner =
      teamControllerInput.compEventTeam.entityId === input.userEntity.id;
    const nameEditable =
      !teamControllerInput.eventTeamHeader.ceoptions.eventTeam
        .disableTeamNameEdit;

    state.isUserOwner = isUserOwner;
    state.canEditTeamName =
      (nameEditable && isUserOwner) || configController.isAdmin.value;
    state.canDelete = isUserOwner && teamControllerInput.compEventTeam.id > 0;
  }

  const hasBuilderPermissionForComp = computed((): boolean => {
    // const configApp =
    //   storeInternal.state[CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME]
    //     .configApp;

    const userInfo = configController.getStore.value.configApp.userInfo;

    const hasAppAdminPermissionForNational =
      configService.hasAppAdminPermissionForNational(userInfo);

    const hasAppAdminPermissionForNationalOrg =
      configService.hasAppAdminPermissionForNational(userInfo);

    const hasBuilderPermissionForComp =
      configService.hasBuilderPermissionForComp(
        userInfo,
        input!.competition.compOrgId,
        input!.competition.compId
      );

    return (
      hasAppAdminPermissionForNational ||
      hasAppAdminPermissionForNationalOrg ||
      hasBuilderPermissionForComp
    );
  });

  return {
    state,

    hasBuilderPermissionForComp,

    init,
  };
}
