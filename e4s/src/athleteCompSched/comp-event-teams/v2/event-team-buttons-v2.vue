<template>
  <div class="e4s-flex-row e4s-justify-flex-row-vert-center okmijn">
    <slot name="left-buttons">
      <div class="e4s-flex-row e4s-gap--small e4s-justify-flex-row-vert-center">
        <slot name="left-buttons-other"></slot>
        <div
          class="e4s-flex-row e4s-gap--small e4s-justify-flex-row-vert-center"
        >
          <ButtonGenericV2
            v-if="showCancel || showDeleteConfirmation"
            class="e4s-button--75"
            text="Cancel"
            button-type="tertiary"
            v-on:click="cancelCompEventTeam"
          />
        </div>
      </div>
    </slot>

    <slot name="right-buttons">
      <div
        class="
          e4s-flex-row
          e4s-gap--small
          e4s-justify-flex-row-vert-center
          e4s-flex-row--end
        "
      >
        <ButtonGenericV2
          v-if="isAdmin"
          class="e4s-button--auto e4s-button--admin"
          text="Assign to User"
          button-type="secondary"
          v-on:click="addUserCart"
        />
        <ButtonGenericV2
          v-if="!editMode"
          :disabled="!getCanEdit"
          class="e4s-button--75"
          text="Edit"
          button-type="secondary"
          v-on:click="setEditMode(true)"
        />

        <slot name="confirm-delete">
          <ButtonGenericV2
            v-if="showDeleteConfirmation"
            class="e4s-button--75"
            :disabled="isDeleteDisabled"
            button-type="destructive"
            @click="deleteCompEventTeam"
            text="Confirm Delete"
          />
        </slot>
        <slot name="submit">
          <ButtonGenericV2
            class="e4s-button--75"
            v-if="showSubmit && !showDeleteConfirmation"
            :disabled="isSubmitDisabled"
            @click="submitCompEventTeam"
            text="Submit"
          />
        </slot>
      </div>
    </slot>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref, PropType } from "vue";
import { ICompEventTeamBase, IEventTeamHeader } from "../event-teams-models";
import { EventTeamService } from "../event-team-service";
import { ICompetitionInfo } from "../../../competition/competition-models";
import { CompetitionService } from "../../../competition/competiton-service";
import { ConfigService } from "../../../config/config-service";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import PrimaryLink from "../../../common/ui/layoutV2/href/PrimaryLink.vue";
import { useConfigController } from "../../../config/useConfigStore";

const eventTeamService: EventTeamService = new EventTeamService();
const competitionService: CompetitionService = new CompetitionService();
const configService: ConfigService = new ConfigService();

export default defineComponent({
  name: "event-team-buttons-v2",
  components: {
    PrimaryLink,
    ButtonGenericV2,
  },
  props: {
    compEventTeam: {
      type: Object as PropType<ICompEventTeamBase>,
      default: () => {
        return eventTeamService.factoryGetCompEventTeamBase(
          {
            id: 0,
          } as IEventTeamHeader,
          configService.factoryEntity()
        );
      },
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    isUserOwner: {
      type: Boolean,
      default: false,
    },
    editMode: {
      type: Boolean,
      default: false,
    },
    isSubmitDisabled: {
      type: Boolean,
      default: true,
    },
    competitionSummary: {
      type: Object as PropType<ICompetitionInfo>,
      default: () => {
        return competitionService.factoryCompetitionInfo();
      },
    },
  },
  setup(props, { emit }) {
    const showDeleteConfirmation = ref(false);

    // Get state from store
    // const configApp = computed<IConfigApp>(
    //   () => store.state[CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME].configApp
    // );

    // const userInfo = computed<IUserInfo>(() => configApp.value.userInfo);

    const configController = useConfigController();
    const userInfo = configController.getStore.value.configApp.userInfo;

    // const userEntity = computed<IEntity>(
    //   () => store.state[CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME].userEntity
    // );

    const isAdmin = configController.isAdmin.value;

    // Computed properties
    const showCancel = computed(() => {
      return props.editMode;
    });

    const showSubmit = computed(() => {
      return props.editMode;
    });

    const isDeleteDisabled = computed(() => {
      return props.isLoading;
    });

    const canDelete = computed(() => {
      if (isAdmin) {
        return true;
      }
      if (competitionService.isClosed(props.competitionSummary.entriesClose)) {
        return false;
      }

      if (!props.isUserOwner) {
        return false;
      }
      return props.compEventTeam.id > 0 && !showDeleteConfirmation.value;
    });

    const getCanEdit = computed((): boolean => {
      if (isAdmin) {
        return true;
      }
      if (competitionService.isClosed(props.competitionSummary.entriesClose)) {
        return false;
      }
      return !props.editMode;
    });

    const getHasBuilderPermissionForComp = computed((): boolean => {
      const hasAppAdminPermissionForNational =
        configService.hasAppAdminPermissionForNational(userInfo);

      const hasAppAdminPermissionForNationalOrg =
        configService.hasAppAdminPermissionForNational(userInfo);

      const hasBuilderPermissionForComp =
        configService.hasBuilderPermissionForComp(
          userInfo,
          props.competitionSummary.compOrgId,
          props.competitionSummary.compId
        );

      return (
        hasAppAdminPermissionForNational ||
        hasAppAdminPermissionForNationalOrg ||
        hasBuilderPermissionForComp
      );
    });

    const getCanAddToUserCart = computed((): boolean => {
      if (props.compEventTeam.paid !== 0) {
        return false;
      }
      return getHasBuilderPermissionForComp.value;
    });

    // Methods
    function deleteCompEventTeamAsk() {
      showDeleteConfirmation.value = true;
    }

    function setEditMode(editMode: boolean) {
      emit("edit", editMode);
    }

    function cancelCompEventTeam() {
      showDeleteConfirmation.value = false;
      emit("cancel");
    }

    function deleteCompEventTeam() {
      emit("delete", props.compEventTeam);
    }

    function submitCompEventTeam() {
      emit("submit");
    }

    function addUserCart() {
      console.log("EventTeamButtonsV2.addUserCart", props.compEventTeam);
      emit("addUserCart", props.compEventTeam);
    }

    return {
      showDeleteConfirmation,
      showCancel,
      showSubmit,
      isDeleteDisabled,
      canDelete,
      getCanEdit,
      getHasBuilderPermissionForComp,
      getCanAddToUserCart,
      isAdmin,

      deleteCompEventTeamAsk,
      setEditMode,
      cancelCompEventTeam,
      deleteCompEventTeam,
      submitCompEventTeam,
      addUserCart,
    };
  },
});
</script>

<style scoped>
.e4s-button--auto {
  width: auto;
}
</style>
