<template>
  <div class="e4s-flex-column e4s-gap--standard">

<!--    <div class="e4s-flex-row e4s-full-width e4s-justify-flex-end">-->
<!--      <ButtonGenericBackV2 v-on:click="cancel"/>-->
<!--    </div>-->

    <CardGenericV2>
      <template slot="all">

        <div class="e4s-flex-column e4s-gap--standard">

          <div class="e4s-flex-row e4s-justify-flex-space-between">
            <div class="e4s-flex-row e4s-gap--standard e4s-input--label">
              Eligibility:
              <div v-text="compEventTeam.teamName"></div>
            </div>
            <ButtonGenericBackV2 text="Close" v-on:click="cancel"/>
          </div>

          <p>
            Search for athlete to see if eligible.   Enter first few characters of name or
            athlete's athletic registration identifier (URN).
          </p>

          <p>
            If an athlete can't be added, it is usually an age group issue.
          </p>

          <AthleteTypeAheadV2
            :disabled="isLoading"
            :user-entity="userEntity"
            :field-label="'Enter name or URN'"
            :is-disabled="isLoading"
            v-on:athleteSelected="athleteSelected"
            v-on:reset="reset"
          />

          <div class="e4s-flex-row e4s-info-text--error" v-text="message"></div>

        </div>

      </template>
    </CardGenericV2>

  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { mapState } from "vuex";
import { AthleteData } from "../../../../athlete/athlete-data";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../../config/config-store";
import { IAthleteSummary } from "../../../../athlete/athlete-models";
import { USER_MESSAGE_LEVEL } from "../../../../user-message/user-message-models";
import { IEntity } from "../../../../config/config-app-models";
import AthleteTypeAheadV2 from "../athlete-type-ahead-v2.vue"
import {IServerGenericResponse} from "../../../../common/common-models"
import {messageDispatchHelper} from "../../../../user-message/user-message-store"
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue"
import ButtonGenericBackV2 from "../../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue"
import CardGenericV2 from "../../../../common/ui/layoutV2/card-generic-v2.vue"
import {ICompEventTeam} from "../../event-teams-models"

const athleteData: AthleteData = new AthleteData();

@Component({
  name: "event-athlete-eligible-v2",
  components: {
    CardGenericV2,
    ButtonGenericBackV2,
    ButtonGenericV2,
    AthleteTypeAheadV2,
  },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
      userEntity: (state: any) => state.userEntity,
    }),
  },
})
export default class EventAthleteEligible extends Vue {
  public readonly userEntity: IEntity;

  @Prop({
    default: 0,
  })
  public readonly ceid: number;

  @Prop({
    required: true,
  })
  public compEventTeam: ICompEventTeam;

  public isLoading: boolean = false;
  public message: string = "";

  public athleteSelected(payload: {
    athlete: IAthleteSummary;
    position: number;
  }) {
    const athlete: IAthleteSummary = payload.athlete;
    this.message = "";
    if (this.ceid > 0 && athlete && athlete.id) {
      this.message = "Checking...";
      this.isLoading = true;
      athleteData
        .checkAthleteEventEligible(athlete.id, this.ceid)
        .then((response: IServerGenericResponse) => {
          if (response.errNo > 0) {
            messageDispatchHelper(
              response.error,
              USER_MESSAGE_LEVEL.ERROR.toString()
            );
            return;
          }
          this.message = response.data;
          return;
        })
        .catch((error) => {
          messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
          return;
        })
        .finally(() => {
          this.isLoading = false;
        });
    }
  }

  public reset() {
    this.message = "";
  }

  public cancel() {
    this.$emit("cancel");
  }
}
</script>
