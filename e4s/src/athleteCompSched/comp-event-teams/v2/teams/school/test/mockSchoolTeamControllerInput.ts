import { ISchoolTeamControllerInput } from "../models/school-team-models";

export const mockSchoolTeamControllerInput: ISchoolTeamControllerInput = {
  competition: { compId: 472, compOrgId: 54 },
  eventTeamHeader: {
    id: 45964,
    eventid: 838,
    eventName: "Relay",
    startdate: "2025-05-13T00:00:00",
    tf: "T",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      international: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 2,
        max: 4,
        teamPositionLabel: "",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "SCHOOL",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        currCount: 0,
        showForm: false,
        price: "",
        teamNameFormat: "{{entity}} {{eventname}} {{gender}} {{agegroup}}",
      },
      rowOptions: {
        autoExpandHelpText: true,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: true,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: { clubs: [0] },
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      internationalAthletes: false,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: { id: 0, name: "", eventNo: 0, typeNo: "" },
      ageGroupOverlap: false,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: { id: 20, name: "VET 60" },
    isDisabled: false,
    eventGroup: {
      id: 7390,
      name: "Relay School",
      eventNo: 4,
      startDate: "2025-05-13T00:00:00",
    },
    Gender: "M",
    ageInfo: { id: 20, name: "VET 60" },
    compEventTeams: [],
    price: {
      id: 630,
      stdPrice: 5,
      curPrice: 5,
      salePrice: 5,
      saleDate: null,
      actualPrice: 5,
    },
  },
  compEventTeam: {
    id: 0,
    ceid: 45964,
    teamName: "Nuneaton Harriers Relay Male VET 60",
    userId: 0,
    userName: "",
    paid: 0,
    prodId: 0,
    athletes: [
      { id: 1, name: "", consent: true },
      { id: 2, name: "", consent: true },
      { id: 3, name: "", consent: true },
      { id: 4, name: "", consent: true },
    ],
    formInfo: [],
    formRows: [],
    entityId: 0,
    entityLevel: 0,
  },
  userEntity: {
    name: "Nuneaton Harriers",
    entityLevel: 1,
    id: 1198,
    entityName: "Club",
    clubType: "C",
  },
} as any as ISchoolTeamControllerInput;
