<template>
  <AccordionV4
    :is-expanded="true"
    id="qazwsx"
    style="padding: 0"
    header-style="background: #f8fafc;padding:0;"
  >
    <div
      slot="summary"
      class="
        e4s-flex-row
        e4s-full-width
        e4s-justify-flex-space-between-xxx
        e4s-flex-start
      "
    >
      <div class="e4s-flex-column">
        <EventTeamHeaderNameV2
          class="e4s-input--label"
          :event-team-header="eventTeamHeader"
        >
        </EventTeamHeaderNameV2>
      </div>

      <div class="e4s-flex-row e4s-flex-row--end e4s-gap--standard">
        <!--        <span-->
        <!--          v-text="eventTeamHeader.ceoptions.eventTeam.formType"-->
        <!--          class="e4s-subheader&#45;&#45;general"-->
        <!--        ></span>-->
        <EventTeamHeaderStatusPillV2
          :event-team-header="eventTeamHeader"
          :title="'Team Type: ' + eventTeamHeader.ceoptions.eventTeam.formType"
        />
      </div>
    </div>

    <div
      slot="content"
      class="e4s-flex-column e4s-gap--large-xyz"
      v-if="showSection === 'TEAMS'"
    >
      <div class="e4s-flex-column">
        <div
          class="e4s-flex-column e4s-flex-end e4s-info-text--error"
          v-text="getWarningMessage"
          v-if="getWarningMessage.length > 0"
        ></div>
      </div>

      <!--      eventTeamHeader.ceoptions.eventTeam.formType AA:-->
      <!--      {{ eventTeamHeader.ceoptions.eventTeam.formType }}-->

      <div
        class="e4s-flex-column e4s-gap--standard"
        v-for="(compEventTeam, index) in eventTeamHeader.compEventTeams"
        :key="compEventTeam.id"
      >
        <!--This component can render all team types-->
        <EventTeamDefaultV2
          :isLoading="isLoading"
          :competition="competition"
          :comp-event-team-prop="compEventTeam"
          :event-team-header="eventTeamHeader"
          :user-entity="userEntity"
          :is-new="false"
          v-on:add="addTeam"
          v-on:edit="editTeam"
          v-on:delete="deleteTeam"
        />

        <SectionThickLine
          style="height: 2px"
          v-if="index !== eventTeamHeader.compEventTeams.length - 1"
        />
      </div>
    </div>
  </AccordionV4>
</template>

<script lang="ts">
import { format, parse } from "date-fns";
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import EventTeamHeaderNameV2 from "../event-team-header-name-v2.vue";
import CardGenericV2 from "../../../../common/ui/layoutV2/card-generic-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import ButtonGenericBackV2 from "../../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import EventTeamDefaultV2 from "./event-team-default-v2.vue";
import LoadingSpinnerV2 from "../../../../common/ui/loading-spinner-v2.vue";
import { ConfigService } from "../../../../config/config-service";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../../config/config-store";
import { IConfigApp, IEntity } from "../../../../config/config-app-models";
import {
  ICompEventTeam,
  ICompEventTeamBase,
  IEventTeamHeader,
} from "../../event-teams-models";
import { GENDER, IServerResponse } from "../../../../common/common-models";
import { EventTeamService } from "../../event-team-service";
import { SchoolTeamService } from "../../school/school-team-service";
import { IEventTeam, ShowFormType } from "../../../athletecompsched-models";
import { ICompetitionInfo } from "../../../../competition/competition-models";
import { CompEventTeamData } from "../../comp-event-team-data";
import { USER_MESSAGE_LEVEL } from "../../../../user-message/user-message-models";
import { messageDispatchHelper } from "../../../../user-message/user-message-store";
import { ATH_COMP_SCHED_STORE_CONST } from "../../../store/athleteCompSched-store";
import { COMP_EVENT_TEAMS_STORE_CONST } from "../../comp-event-store";
import { ResourceData } from "../../../../common/resource/resource-service";
import EventTeamHeaderStatusPillV2 from "../event-team-header-status-pill-v2.vue";
import InfoSectionV2 from "../../../../common/ui/layoutV2/info-section-v2.vue";
import EntitySelectV2 from "../../../../config/entity/entity-select-v2.vue";
import FormGenericInputTemplateV2 from "../../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import {
  ENTRY_STORE_CONST,
  IEntryStoreState,
} from "../../../../entry/entry-store";
import { IClubCompInfo } from "../../../../entry/v2/schools/clubCompInfo-models";
import * as ClubCompInfoService from "../../../../entry/v2/schools/clubCompInfoService";
import { simpleClone } from "../../../../common/common-service-utils";
import AccordionV4 from "../../../../common/ui/layoutV2/accordion/AccordionV4.vue";
import SectionThickLine from "../../../../common/ui/layoutV2/form/SectionThickLine.vue";

const configService: ConfigService = new ConfigService();

export type EventTeamHeaderShowTeamsV2EmitEdit = {
  eventTeamHeader: IEventTeamHeader;
  compEventTeam: ICompEventTeamBase;
};

type SectionType = "TEAMS" | "ELIGIBLE";

type TeamLimits = {
  maxTeamsReached: boolean;
  getTeamEventHasSecurity: boolean;
  isEntitySelected: boolean;
  canAddTeam: boolean;
  doesUserHaveAccessToThisEvent: boolean;
};

@Component({
  name: "event-team-header-show-teams-v2",
  components: {
    SectionThickLine,
    AccordionV4,
    FormGenericInputTemplateV2,
    EntitySelectV2,
    InfoSectionV2,
    EventTeamHeaderStatusPillV2,
    LoadingSpinnerV2,
    EventTeamDefaultV2,
    ButtonGenericBackV2,
    ButtonGenericV2,
    CardGenericV2,
    EventTeamHeaderNameV2,
  },
  computed: {
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      clubCompInfo: (state: any) => state.entryForm.clubCompInfo,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
      userEntityStore: (state: any) => state.userEntity,
      // userEntity: (state: any) => state.userEntity,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class EventTeamHeaderShowTeamsV2 extends Vue {
  public readonly configApp: IConfigApp;
  public readonly clubCompInfo: IClubCompInfo;

  @Prop({ default: -11 }) public index: number;
  @Prop() public readonly competition: ICompetitionInfo;
  @Prop() public readonly eventTeamHeader: IEventTeamHeader;

  @Prop({
    default: () => {
      return configService.factoryEntity();
    },
  })
  public readonly userEntity: IEntity;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly userEntities: IEntity[];

  public gender = GENDER;
  public eventTeamService: EventTeamService = new EventTeamService();
  public schoolTeamService: SchoolTeamService = new SchoolTeamService();
  public displayMoreInfoComponent: boolean = false;
  public compEventTeamNew: ICompEventTeam;
  public showNewTeam: boolean = false;
  public isLoading: boolean = false;
  public formType: ShowFormType = "DEFAULT";
  public userEntitySelected: IEntity = configService.factoryEntity();
  public showSection: SectionType = "TEAMS";
  public editCompEventTeam: ICompEventTeamBase | null = null;

  public teamLimits: TeamLimits = {
    maxTeamsReached: false,
    getTeamEventHasSecurity: false,
    isEntitySelected: false,
    canAddTeam: true,
    doesUserHaveAccessToThisEvent: true,
  };

  public showExistingTeams = false;

  public created() {
    this.init();
  }

  public setSection(sectionName: SectionType) {
    this.showSection = sectionName;
  }

  public get getEntityForUserLabel(): string {
    const entity = this.userEntity;
    return entity.id === 0
      ? "Public Access"
      : entity.entityName + " - " + entity.name;
  }

  @Watch("userEntity", { immediate: true })
  public onUserEntityChanged(newValue: IEntity) {
    this.init();
  }

  @Watch("userEntities", { immediate: true })
  public onUserEntitiesChanged(newValue: IEntity[]) {
    this.init();
  }

  public init() {
    // const userEntities = this.userEntities.filter( (entity) => {
    //   return entity.id >  0;
    // })
    // if (userEntities.length === 0) {
    //   this.userEntity = simpleClone(userEntities[0]);
    // }

    this.formType = this.eventTeamService.displayWhichFormType(
      this.eventTeamHeader
    );

    this.compEventTeamNew = this.eventTeamService.factoryGetCompEventTeam(
      this.eventTeamHeader,
      this.userEntity,
      this.clubCompInfo
    );
    this.setUpEditObject();

    this.teamLimits = {
      maxTeamsReached: this.eventTeamService.maxTeamsReached(
        this.eventTeamHeader
      ),
      getTeamEventHasSecurity: this.eventTeamService.getTeamEventHasSecurity(
        this.eventTeamHeader
      ),
      isEntitySelected: this.userEntity.id > 0,
      canAddTeam: this.eventTeamService.canAddTeam(this.eventTeamHeader),
      doesUserHaveAccessToThisEvent:
        this.eventTeamService.doesUserHaveAccessToThisEvent(
          this.configApp.userInfo,
          this.eventTeamHeader
        ),
    };
  }

  public setUpEditObject() {
    let editCompEventTeam: ICompEventTeamBase =
      this.eventTeamService.factoryGetCompEventTeamBase(
        this.eventTeamHeader,
        this.userEntity,
        this.clubCompInfo
      );
    if (this.formType === "DEFAULT") {
      editCompEventTeam = this.eventTeamService.factoryGetCompEventTeam(
        this.eventTeamHeader,
        this.userEntity,
        this.clubCompInfo
      );
    }
    if (this.formType === "SCHOOL") {
      editCompEventTeam = this.schoolTeamService.factoryCompEventTeamSchool(
        this.eventTeamHeader,
        this.userEntity,
        this.clubCompInfo
      );
    }
    editCompEventTeam.ceid = this.eventTeamHeader.id;
    this.editCompEventTeam = editCompEventTeam;
  }

  public get getEventDate() {
    return format(parse(this.eventTeamHeader.startdate), "Do MMM YY");
  }

  public get getEventTime() {
    let startTime = format(parse(this.eventTeamHeader.startdate), "HH:mm");
    startTime = startTime === "00:00" ? "TBC" : startTime;
    return startTime;
  }

  // public get getGender() {
  //   return commonService.getGenderLabel(this.eventTeamHeader.gender);
  // }

  public get getEventTeamOptions(): IEventTeam {
    return this.eventTeamService.getEventTeamOptions(this.eventTeamHeader);
  }

  public get getTeamAthleteMin() {
    const eventTeam: IEventTeam = this.getEventTeamOptions;
    return eventTeam ? eventTeam.min : 0;
  }

  public get getTeamAthleteMax() {
    const eventTeam: IEventTeam = this.getEventTeamOptions;
    return eventTeam ? eventTeam.max : 0;
  }

  public get getShowAthleteMinMax() {
    return this.getTeamAthleteMin > 0 || this.getTeamAthleteMax > 0;
  }

  public showMoreInfo(showIt: boolean) {
    this.displayMoreInfoComponent = showIt;
  }

  public get showCloseTeams() {
    return this.displayMoreInfoComponent && !this.showNewTeam;
  }

  public get getShowPrice(): boolean {
    if (this.eventTeamHeader.ceoptions.rowOptions) {
      return this.eventTeamHeader.ceoptions.rowOptions.showPrice;
    }
    return false;
  }

  public get getPrice() {
    //  "€"
    return this.eventTeamHeader.price && this.eventTeamHeader.price.curPrice
      ? this.configApp.currency + this.eventTeamHeader.price.curPrice.toFixed(2)
      : "";
  }

  public get getOptionsMaxEventTeams(): number {
    return this.eventTeamService.getOptionsMaxEventTeams(this.eventTeamHeader);
  }

  public get getShowMaxEventTeams(): boolean {
    return this.getOptionsMaxEventTeams > 0;
  }

  public get canAddTeam(): boolean {
    if (this.teamLimits.maxTeamsReached) {
      return false;
    }

    if (this.teamLimits.getTeamEventHasSecurity) {
      return this.teamLimits.isEntitySelected;
    }

    if (this.eventTeamHeader.isDisabled) {
      return false;
    }

    return true;
  }

  public get getWarningMessage(): string {
    if (this.eventTeamHeader.isDisabled) {
      if (this.doesUserHaveAccessToThisEvent) {
        // return "Please select from drop down above.";
        return "";
      }
      return this.eventTeamHeader.ceoptions.warningMessage;
    }
    return this.eventTeamService.maxTeamsReached(this.eventTeamHeader)
      ? "Max Teams Entered"
      : "";
  }

  public get doesUserHaveAccessToThisEvent(): boolean {
    return this.eventTeamService.doesUserHaveAccessToThisEvent(
      this.configApp.userInfo,
      this.eventTeamHeader
    );
  }

  public get getShowAddTeamButton(): boolean {
    return this.canAddTeam && !this.showNewTeam;
  }

  public get getShowCancelTeamButton(): boolean {
    return this.canAddTeam && this.showNewTeam;
  }

  public addCompEventTeam() {
    console.log("add team");
    const compEventTeam: ICompEventTeam =
      this.eventTeamService.factoryGetCompEventTeam(
        this.eventTeamHeader,
        this.userEntity,
        this.clubCompInfo
      );
    this.compEventTeamNew = compEventTeam;

    // this.setUpEditObject();

    // this.showMoreInfo(true);
    // this.showNewTeam = true;
    // this.showSection = "ADD";
    const eventTeamHeaderShowTeamsV2EmitEdit: EventTeamHeaderShowTeamsV2EmitEdit =
      {
        eventTeamHeader: simpleClone(this.eventTeamHeader),
        compEventTeam: simpleClone(
          this.editCompEventTeam
        ) as ICompEventTeamBase,
      };
    this.$emit("addTeam", this.editCompEventTeam);
    this.$emit("addTeamNew", eventTeamHeaderShowTeamsV2EmitEdit);
  }

  public addTeam(compEventTeamToClone: ICompEventTeam) {
    console.log("add team");
    const compEventTeam: ICompEventTeam =
      this.eventTeamService.factoryGetCompEventTeam(
        this.eventTeamHeader,
        this.userEntity,
        this.clubCompInfo
      );
    if (compEventTeamToClone.entity) {
      compEventTeam.entityId = compEventTeamToClone.entity.entityId;
      compEventTeam.entityLevel = compEventTeamToClone.entity.entityLevel;
    }

    this.compEventTeamNew = compEventTeam;

    //  Legacy BS.
    // this.setUpEditObject();
    // this.editCompEventTeam = compEventTeam;

    // this.showMoreInfo(true);
    // this.showNewTeam = true;
    // this.showSection = "ADD";
    this.$emit("addTeam", this.editCompEventTeam);
  }

  public editTeam(compEventTeam: ICompEventTeamBase) {
    this.editCompEventTeam = R.clone(compEventTeam);
    // this.showMoreInfo(true)

    // this.showNewTeam = true;
    // this.showSection = compEventTeam.id > 0 ? "EDIT" : "ADD";
    this.$emit("editTeam", this.editCompEventTeam);

    const eventTeamHeaderShowTeamsV2EmitEdit: EventTeamHeaderShowTeamsV2EmitEdit =
      {
        eventTeamHeader: simpleClone(this.eventTeamHeader),
        compEventTeam: simpleClone(this.editCompEventTeam),
      };
    this.$emit("editTeamNew", eventTeamHeaderShowTeamsV2EmitEdit);
  }

  public deleteTeam(compEventTeam: ICompEventTeamBase) {
    this.$emit("deleteTeam", compEventTeam);
  }

  public cancel() {
    this.$emit("cancel");
  }

  public cancelTeam() {
    this.setUpEditObject();
    // this.showMoreInfo(false);
    this.showNewTeam = false;
  }

  public closeAddCompEventTeam() {
    this.showNewTeam = false;
    if (this.eventTeamHeader.compEventTeams.length === 0) {
      this.showMoreInfo(false);
    }
  }

  public submitDefault(compEventTeam: ICompEventTeam) {
    const compEventTeamData: CompEventTeamData = new CompEventTeamData();
    const compEventTeamStripped: ICompEventTeam =
      this.eventTeamService.stripEventCompEventTeam(compEventTeam);
    console.log(compEventTeam);
    this.submitTeam(compEventTeamStripped, compEventTeamData);
  }

  public submitTeam(
    compEventTeam: ICompEventTeamBase,
    resourceData: ResourceData<ICompEventTeamBase>
  ) {
    let prom;
    this.isLoading = true;
    if (compEventTeam.id > 0) {
      prom = resourceData.update(compEventTeam);
    } else {
      compEventTeam.entityLevel = this.userEntity.entityLevel;
      compEventTeam.entityId = this.userEntity.id;
      prom = resourceData.create(compEventTeam);
    }
    prom
      .then((response: IServerResponse<ICompEventTeamBase>) => {
        if (response.errNo > 0) {
          messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR);
          return;
        }
        messageDispatchHelper(
          response.message && response.message.length > 0
            ? response.message
            : "Team " + compEventTeam.teamName + " saved to Order Summary",
          USER_MESSAGE_LEVEL.INFO
        );
        this.standardSuccess();
        this.closeAddCompEventTeam();
      })
      .catch((error: any) => {
        this.standardCatch(error);
        return;
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public deleteCompEventTeam(compEventTeam: ICompEventTeam) {
    /*
    const compEventTeamData: CompEventTeamData = new CompEventTeamData();

    if (this.configApp.userId !== compEventTeam.userId) {
      messageDispatchHelper(
        "Team " +
          compEventTeam.teamName +
          " not deleted, entered by: " +
          compEventTeam.userName,
        USER_MESSAGE_LEVEL.WARN
      );
      return false;
    }

    this.isLoading = true;
    return compEventTeamData
      .delete(compEventTeam.id)
      .then((response: IServerResponse<ICompEventTeam>) => {
        this.isLoading = false;
        if (response.errNo > 0) {
          messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR);
          return;
        }
        messageDispatchHelper(
          "Team " + compEventTeam.teamName + " deleted.",
          USER_MESSAGE_LEVEL.INFO
        );
        this.standardSuccess();
        return;
      })
      .catch((error) => {
        this.standardCatch(error);
        return;
      })
      .finally(() => {
        this.isLoading = false;
      });
     */
  }

  public standardSuccess() {
    this.$store.dispatch(
      COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME +
        "/" +
        COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_ACTIONS_GET_TEAM_HEADERS,
      { compId: this.competition.id }
    );

    this.$store.dispatch(
      ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_GET_CART
    );

    if (
      ClubCompInfoService.hasClubCompInfoCompetition(
        this.clubCompInfo as IClubCompInfo
      )
    ) {
      this.$store.dispatch(
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
          "/" +
          ENTRY_STORE_CONST.ENTRY_STORE_ACTIONS_GET_CLUB_COMP_INFO,
        { compId: this.competition.id }
      );
    }
  }

  public get displayWhichFormType() {
    return this.eventTeamService.displayWhichFormType(this.eventTeamHeader);
  }

  public get getXiText() {
    return this.eventTeamHeader.ceoptions &&
      this.eventTeamHeader.ceoptions.xiText
      ? this.eventTeamHeader.ceoptions.xiText
      : "";
  }

  // public get hasCompEventHelpText() {
  //   return this.eventTeamHeader.ceoptions &&
  //     this.eventTeamHeader.ceoptions.helpText.length > 0
  //     ? true
  //     : false;
  // }

  public onUserEntitySelected(entity: IEntity) {
    this.userEntitySelected = { ...entity };
    this.$emit("onUserEntitySelected", entity);
  }

  public standardCatch(error: any) {
    this.isLoading = false;
    messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR);
    return;
  }

  public get getShowEntitySelect(): boolean {
    return this.userEntities.length > 1 && !this.showNewTeam;
  }

  // public get getEventSecurityLabel(): string | "Public" {
  //   const label = this.eventTeamService.getEventSecurityLabel(
  //     this.eventTeamHeader
  //   );
  //   return "Security: " + (label.length === 0 ? "Public" : label);
  // }

  public get getTeamEventHasSecurity(): boolean {
    return this.eventTeamService.getTeamEventHasSecurity(this.eventTeamHeader);
  }

  public get getUserEntityLabel() {
    return this.userEntities
      .map((ent) => {
        return "[" + ent.id + "] " + ent.entityName + " - " + ent.name;
      })
      .join(", ");
  }

  //
  // public get getMaxTeamReached() {
  //     return this.eventTeamService.maxTeamsReached(this.eventTeamHeader);
  // }
}
</script>
