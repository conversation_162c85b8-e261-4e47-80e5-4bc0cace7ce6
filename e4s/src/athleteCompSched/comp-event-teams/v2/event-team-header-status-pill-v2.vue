<template>
  <PillV2 :text="getEventSecurityLabel" :pill-type="getTeamEventHasSecurity ? 'closed' : 'open'" />
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "vue";
import { IEventTeamHeader } from "../event-teams-models";
import PillV2 from "../../../common/ui/layoutV2/pills/pill-v2.vue";
import { EventTeamService } from "../event-team-service";

const eventTeamService = new EventTeamService();

export default defineComponent({
  name: "event-team-header-status-pill-v2",
  components: { PillV2 },
  props: {
    eventTeamHeader: {
      type: Object as PropType<IEventTeamHeader>,
      default: () => {
        return eventTeamService.factoryEventTeamHeader();
      },
    },
  },
  setup(props: { eventTeamHeader: IEventTeamHeader }, context: SetupContext) {
    const getEventSecurityLabel = computed(() => {
      const label = eventTeamService.getEventSecurityLabel(
        props.eventTeamHeader
      );
      return (label.length === 0 ? "Public Access" : "Security: " + label);
    });

    const getTeamEventHasSecurity = computed(() => {
      return eventTeamService.getTeamEventHasSecurity(props.eventTeamHeader);
    });

    return { getEventSecurityLabel, getTeamEventHasSecurity };
  },
});
</script>
