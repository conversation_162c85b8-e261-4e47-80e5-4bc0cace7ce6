import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import {
  IConfigStoreState,
  CONFIG_STORE_CONST,
} from "../../config/config-store";
import { mapState } from "vuex";
import { IConfigApp } from "../../config/config-app-models";
import type { ICompEventTeamBase, IEventTeamHeader } from "./event-teams-models";
import { Prop } from "vue-property-decorator";
import type { IUserMessage } from "../../user-message/user-message-models";
import { AthleteService } from "../../athlete/athlete-service";
import type { ICompetitionInfo } from "../../competition/competition-models";
import {
  COMP_EVENT_TEAMS_STORE_CONST,
  ICompEventTeamsStoreState,
} from "./comp-event-store";
import EventTeamName from "./event-team-name.vue";

@Component({
  name: "event-team-default",
  components: {
    "event-team-name": EventTeamName,
  },
  computed: {
    configApp(): IConfigApp {
      const configState = this.$store.state[CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME] as IConfigStoreState;
      return configState.configApp;
    },
    eventTeamHeaders(): IEventTeamHeader[] {
      const teamsState = this.$store.state[COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME] as ICompEventTeamsStoreState;
      return teamsState.eventTeamHeaders;
    }
  },
})
export default class EventTeamBase extends Vue {
  // TypeScript declarations for computed properties
  public configApp!: IConfigApp;
  public eventTeamHeaders!: IEventTeamHeader[];

  @Prop({ default: false }) public isLoading: boolean;
  @Prop({ default: true }) public isNew: boolean;
  @Prop() public eventTeamHeader: IEventTeamHeader;
  @Prop() public competition: ICompetitionInfo;

  public editMode: boolean = false;
  public athleteService: AthleteService = new AthleteService();
  public compEventTeam: ICompEventTeamBase;
  public showDeleteConfirmation: boolean = false;
  public hasBeenEdited: boolean = false;
  public editTeamName: boolean = false;
  public editingLine: number = -1;
  public prefix: string = Math.random().toString(36).substring(2);
  public userMessages: IUserMessage[] = [];

  public created() {
    this.editMode = this.isNew;
  }

  public get isUserOwner() {
    if (this.compEventTeam.userId === 0) {
      //  New team
      return true;
    }
    return this.configApp.userId === this.compEventTeam.userId;
  }

  public onTeamNameChanged(teamName: string) {
    this.compEventTeam.teamName = teamName;
    this.onFieldChanged();
  }

  public get canDelete() {
    if (!this.isUserOwner) {
      return false;
    }
    return this.compEventTeam.id > 0 && !this.showDeleteConfirmation;
  }

  public onFieldChanged() {
    this.hasBeenEdited = true;
    this.editMode = true;
  }

  public get isInEditMode() {
    return this.isNew || this.editMode;
  }

  public get getCanEditTeamName(): boolean {
    // this.editMode && && this.isUserOwner &&
    return (
      this.editMode &&
      !this.eventTeamHeader.ceoptions.eventTeam.disableTeamNameEdit
    );
  }

  public setEditMode(editMode: boolean) {
    this.editMode = editMode;
    this.$emit("edit", R.clone(this.compEventTeam));
  }

  public get showSubmit() {
    return (
      this.isNew || this.editMode || this.editTeamName || this.hasBeenEdited
    );
  }

  public get showCancel() {
    return (
      this.compEventTeam.id > 0 &&
      (this.editMode || this.hasBeenEdited || this.showDeleteConfirmation)
    );
  }

  public get isSubmitDisabled() {
    return this.isLoading;
  }

  public get isDeleteDisabled() {
    return this.isLoading;
  }

  public deleteCompEventTeamAsk() {
    this.showDeleteConfirmation = true;
  }

  public get showUserOwner() {
    return !this.isNew || this.compEventTeam.userId > 0;
  }

  public deleteCompEventTeam() {
    this.$emit("delete", R.clone(this.compEventTeam));
  }

  public cancelCompEventTeam() {
    this.$emit("cancel", R.clone(this.compEventTeam));
  }

  public editCompEventTeam(_editMode: boolean) {
    this.$emit("edit", R.clone(this.compEventTeam));
  }
}
