<template functional>
    <div>
        <div class="col s1 m1 l1">
            <div v-text="props.positionLabel"></div>
        </div>

        <div class="col s11 m11 l11">
            <span v-text="$options.methods.getName(props.schoolTeamAthlete)"></span>
        </div>

        <div class="col s3 m3 l3" v-if="false">
            <div class="right">
                <span v-text="$options.methods.getConsent(props.schoolTeamAthlete)"></span>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import {ISchoolTeamAthlete} from "./school-team-models";

    export default {
        props: ["positionLabel", "schoolTeamAthlete"],
        methods: {
            getName: (schoolTeamAthlete: ISchoolTeamAthlete) => {
                return schoolTeamAthlete ?  schoolTeamAthlete.name : "";
            },
            getConsent: (schoolTeamAthlete: ISchoolTeamAthlete) => {
                return schoolTeamAthlete && schoolTeamAthlete.consent ? "Yes" : "No";
            }
        }
    };
</script>
