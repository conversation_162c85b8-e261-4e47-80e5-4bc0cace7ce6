import { IBase } from "../../../common/common-models";
import { ICompEventTeamBase } from "../event-teams-models";

export interface ISchoolAthlete {
  name: string;
}

export interface ISchoolTeamAthlete extends IBase {
  name: string;
  consent: boolean;
}

export interface ISchoolTeamAthleteRead extends IBase {
  firstName: string;
  surName: string;
  consent: boolean;
}

export interface ISchoolTeamAthleteOutput {
  positionIndex: number;
  positionLabel: string;
  schoolTeamAthlete: ISchoolTeamAthlete;
}

export interface ICompEventTeamSchool extends ICompEventTeamBase {
  athletes: ISchoolTeamAthlete[];
}
