import { SchoolTeamService } from "./school-team-service";
import { ICompEventTeamSchool } from "./school-team-models";
import { IAthlete<PERSON>ummary } from "../../../athlete/athlete-models";

const schoolTeamService = new SchoolTeamService();

describe("SchoolTeamService", () => {
  test("cleanSchoolTeam", () => {
    const sourceTeam = {
      id: 1389,
      teamName: "Avon 4 x 400 Female VET 80 {A}",
      paid: 0,
      prodId: 138357,
      userId: 1,
      userName: "e4sadmin",
      entity: {
        id: 10600,
        entityLevel: 1,
        name: "<PERSON>",
        entity: "Club",
      },
      athletes: [
        {
          id: 186186,
          clubid: 1198,
          aocode: "",
          club: "Nuneaton Harriers",
          pos: 1,
          firstName: "Erg",
          surName: "ERG",
          URN: null,
          dob: "2000-01-01",
          ageInfo: {
            ageGroups: [
              {
                id: 0,
                shortName: "N/A",
                Name: "No events for athletes age group",
              },
            ],
            vetAgeGroup: null,
            currentAge: 25,
            competitionAge: 25,
            ageGroup: {
              id: 0,
              shortName: "N/A",
              Name: "No events for athletes age group",
            },
          },
        },
        {
          id: 186187,
          clubid: 1198,
          aocode: "",
          club: "Nuneaton Harriers",
          pos: 2,
          firstName: "Erg",
          surName: "ERG",
          URN: null,
          dob: "2000-01-01",
          ageInfo: {
            ageGroups: [
              {
                id: 0,
                shortName: "N/A",
                Name: "No events for athletes age group",
              },
            ],
            vetAgeGroup: null,
            currentAge: 25,
            competitionAge: 25,
            ageGroup: {
              id: 0,
              shortName: "N/A",
              Name: "No events for athletes age group",
            },
          },
        },
        {
          id: 0,
          firstName: "",
          surName: "",
          URN: "",
          dob: "",
          club: "",
          clubname: "",
          clubid: 0,
          club2: "",
          club2id: 0,
          club2Id: 0,
          gender: "",
          classification: 0,
          schoolid: 0,
          school: "",
          inTeam: false,
          ageInfo: {
            ageGroup: {
              id: 0,
              Name: "",
              name: "",
              shortName: "",
            },
            ageGroups: [],
            vetAgeGroup: {
              Name: "",
              shortName: "",
            },
            currentAge: 0,
            competitionAge: 0,
          },
          aocode: "",
          activeEndDate: "",
          userAthletes: [],
          events: [],
          image: "",
          options: {
            noEntryReason: "",
            emergency: {
              name: "",
              tel: "",
              relationship: "",
            },
            socials: {
              tiktok: "",
              instagram: "",
              facebook: "",
            },
            coach: "",
            trainingGroup: "",
            genericCompAthleteEntities: {},
          },
          pbInfo: [],
          email: "",
          infoText: "",
        },
        {
          id: 0,
          firstName: "",
          surName: "",
          URN: "",
          dob: "",
          club: "",
          clubname: "",
          clubid: 0,
          club2: "",
          club2id: 0,
          club2Id: 0,
          gender: "",
          classification: 0,
          schoolid: 0,
          school: "",
          inTeam: false,
          ageInfo: {
            ageGroup: {
              id: 0,
              Name: "",
              name: "",
              shortName: "",
            },
            ageGroups: [],
            vetAgeGroup: {
              Name: "",
              shortName: "",
            },
            currentAge: 0,
            competitionAge: 0,
          },
          aocode: "",
          activeEndDate: "",
          userAthletes: [],
          events: [],
          image: "",
          options: {
            noEntryReason: "",
            emergency: {
              name: "",
              tel: "",
              relationship: "",
            },
            socials: {
              tiktok: "",
              instagram: "",
              facebook: "",
            },
            coach: "",
            trainingGroup: "",
            genericCompAthleteEntities: {},
          },
          pbInfo: [],
          email: "",
          infoText: "",
        },
      ],
      ceid: 45821,
    } as any as ICompEventTeamSchool;

    const result = schoolTeamService.cleanSchoolTeam(sourceTeam);

    expect(result.athletes.length).toBe(4);
    expect(result.athletes[0].name).toBe("Erg");
    expect(result.athletes[1].name).toBe("Erg");

    const athlete3Source = sourceTeam.athletes[2];
    expect((athlete3Source as any as IAthleteSummary).firstName).toBe("");

    const athlete3School =
      schoolTeamService.convertAthleteSummaryToSchoolTeamAthlete(
        athlete3Source as any as IAthleteSummary
      );

    expect(athlete3School.name).toBe("");

    expect(result.athletes[2].name).toBe("");
    // expect(result.athletes[3].name).toBe("");
  });
});
