<template>
  <FormGenericInputTextV2
    :form-label="getPositionLabel"
    :value="schoolTeamAthlete.name"
    @input="handleInputChange"
    @keyUpEnter="handleInputChange"
  />
  <!--    @input="handleInputChange"-->
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {
  ISchoolTeamAthlete,
  ISchoolTeamAthleteOutput,
} from "./school-team-models";
import { SchoolTeamService } from "./school-team-service";
import { IAthleteSummary } from "../../../athlete/athlete-models";
import { simpleClone } from "../../../common/common-service-utils";
import FormGenericInputTextV2 from "../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";

const schoolTeamService: SchoolTeamService = new SchoolTeamService();

@Component({
  name: "school-team-athlete",
  components: { FormGenericInputTextV2 },
})
export default class SchoolTeamAthlete extends Vue {
  @Prop({ required: true })
  public readonly positionIndex: number;

  @Prop({
    default: () => {
      return schoolTeamService.factorySchoolTeamAthlete();
    },
  })
  public readonly schoolTeamAthleteProp: ISchoolTeamAthlete | IAthleteSummary;

  @Prop({ default: "" })
  public readonly positionLabel: string;

  public PREFIX = Math.random().toString(36).substring(2);

  public schoolTeamAthlete: ISchoolTeamAthlete =
    schoolTeamService.factorySchoolTeamAthlete();

  public created() {
    console.log(
      "SchoolTeamAthlete.created() schoolTeamAthleteProp: ",
      this.schoolTeamAthleteProp
    );
    this.setAthlete(this.schoolTeamAthleteProp);
  }

  @Watch("schoolTeamAthleteProp")
  public onSchoolTeamAthletePropChanged(newValue: ISchoolTeamAthlete) {
    this.setAthlete(newValue);
  }

  public get getPositionLabel() {
    return this.positionLabel.toString();
  }

  public setAthlete(athlete: ISchoolTeamAthlete | IAthleteSummary) {
    //this.schoolTeamAthlete = R.clone(athlete as ISchoolTeamAthlete);

    const athleteSummary: IAthleteSummary = athlete as IAthleteSummary;
    if (athleteSummary.firstName) {
      const isFirstNameSameAsSurName =
        athleteSummary.firstName.toUpperCase() ===
        athleteSummary.surName.toUpperCase();

      athleteSummary.name = isFirstNameSameAsSurName
        ? athleteSummary.firstName
        : athleteSummary.firstName + " " + athleteSummary.surName;
    }
    this.schoolTeamAthlete = simpleClone(
      athleteSummary as any as ISchoolTeamAthlete
    );
  }

  public handleChange() {
    this.$emit("onChange", {
      positionLabel: this.positionLabel,
      schoolTeamAthlete: this.schoolTeamAthlete,
    } as ISchoolTeamAthleteOutput);
  }

  public handleInputChange(athleteName: string) {
    console.log("SchoolTeamAthlete.handleInputChange", athleteName);
    this.$emit("onChange", {
      positionIndex: this.positionIndex,
      positionLabel: this.positionLabel,
      schoolTeamAthlete: {
        ...this.schoolTeamAthlete,
        name: athleteName,
      },
    } as ISchoolTeamAthleteOutput);
  }
}
</script>

<style scoped>
[type="checkbox"].reset-checkbox,
[type="checkbox"].reset-checkbox:checked,
[type="checkbox"].reset-checkbox:not(checked) {
  opacity: 1;
  position: relative;
}

[type="checkbox"].reset-checkbox + span::before,
[type="checkbox"].reset-checkbox + span::after,
[type="checkbox"].reset-checkbox:checked + span::before,
[type="checkbox"].reset-checkbox:checked + span::after {
  display: none;
}

[type="checkbox"].reset-checkbox + span:not(.lever) {
  padding-left: 10px;
}
</style>
