import * as R from "ramda";
import { IEventTeam } from "../../athletecompsched-models";
import { IEventTeamHeader } from "../event-teams-models";
import { IUserMessage } from "../../../user-message/user-message-models";
import { SchoolTeamService } from "./school-team-service";
import { ICompetitionInfo } from "../../../competition/competition-models";
import { ICompEventTeamSchool, ISchoolTeamAthlete } from "./school-team-models";
import { ConfigService } from "../../../config/config-service";

// const eventTeamService: EventTeamService = new EventTeamService();
const schoolTeamService: SchoolTeamService = new SchoolTeamService();
const configService: ConfigService = new ConfigService();

describe("Comp Event Teams", () => {
  test("validateCompEventTeam", () => {
    const eventTeamHeader: IEventTeamHeader = {
      eoptions: {
        eventTeam: {
          min: 1,
          max: 6,
        } as IEventTeam,
      },
      id: 12134,
    } as IEventTeamHeader;

    const userEntity = configService.factoryEntity();
    const compEventTeam: ICompEventTeamSchool =
      schoolTeamService.factoryCompEventTeamSchool(eventTeamHeader, userEntity);

    let userMessages: IUserMessage[] = schoolTeamService.validateCompEventTeam(
      eventTeamHeader,
      compEventTeam,
      {} as ICompetitionInfo,
      []
    );
    let res = userMessages.filter((userMessage) => {
      return userMessage.message === "Enter team name";
    });
    expect(res.length).toBe(0);

    compEventTeam.teamName = "test team";
    userMessages = schoolTeamService.validateCompEventTeam(
      eventTeamHeader,
      compEventTeam,
      {} as ICompetitionInfo,
      []
    );
    res = userMessages.filter((userMessage) => {
      return userMessage.message === "Enter team name";
    });
    expect(res.length).toBe(0);

    res = userMessages.filter((userMessage) => {
      return userMessage.message === "Enter between 1 and 6 athletes.";
    });
    expect(res.length).toBe(1);

    //  Add athlete, but no name
    let schoolTeamAthlete: ISchoolTeamAthlete = {
      id: 1,
      name: "",
      consent: true,
    };
    compEventTeam.athletes.push(schoolTeamAthlete);

    userMessages = schoolTeamService.validateCompEventTeam(
      eventTeamHeader,
      compEventTeam,
      {} as ICompetitionInfo,
      []
    );
    // console.log(" ******schoolTeamService.validateCompEventTeam", userMessages);
    res = userMessages.filter((userMessage) => {
      return userMessage.message === "Enter between 1 and 6 athletes.";
    });
    expect(res.length).toBe(1);

    //  Add athlete, but has a name
    schoolTeamAthlete = {
      id: 1,
      name: "Bob Smith",
      consent: true,
    };
    compEventTeam.athletes.push(schoolTeamAthlete);

    userMessages = schoolTeamService.validateCompEventTeam(
      eventTeamHeader,
      compEventTeam,
      {} as ICompetitionInfo,
      []
    );
    // console.log(" ******schoolTeamService.validateCompEventTeam", userMessages);
    res = userMessages.filter((userMessage) => {
      return userMessage.message === "Enter between 1 and 6 athletes.";
    });
    expect(res.length).toBe(0);
  });

  test("canAddAthlete", () => {
    const eventTeamHeader: IEventTeamHeader = {
      ceoptions: {
        eventTeam: {
          min: 1,
          max: 3,
        } as IEventTeam,
      },
      id: 12134,
    } as IEventTeamHeader;
    const userEntity = configService.factoryEntity();
    let compEventTeam: ICompEventTeamSchool =
      schoolTeamService.factoryCompEventTeamSchool(eventTeamHeader, userEntity);
    let result = schoolTeamService.canAddAthlete(
      compEventTeam,
      eventTeamHeader
    );
    expect(result).toBe(true);

    //  Add empty athlete
    compEventTeam.athletes = schoolTeamService.addEmptyAthlete(
      compEventTeam.athletes,
      compEventTeam.athletes.length + 1
    );
    result = schoolTeamService.canAddAthlete(compEventTeam, eventTeamHeader);
    expect(result).toBe(false);

    //  Add name to last athlete
    compEventTeam = R.clone(compEventTeam);
    compEventTeam.athletes[compEventTeam.athletes.length - 1].name = "Joe";
    result = schoolTeamService.canAddAthlete(compEventTeam, eventTeamHeader);
    expect(result).toBe(true);

    //  Add name up to max atheletes
    compEventTeam = R.clone(compEventTeam);
    compEventTeam.athletes.push({ id: 4, name: "sss", consent: true });
    compEventTeam.athletes.push({ id: 5, name: "sss", consent: true });
    compEventTeam.athletes.push({ id: 6, name: "sss", consent: true });
    result = schoolTeamService.canAddAthlete(compEventTeam, eventTeamHeader);
    expect(result).toBe(false);
  });

  test("canAddAthlete zero max", () => {
    const eventTeamHeader: IEventTeamHeader = {
      ceoptions: {
        eventTeam: {
          min: 1,
          max: 0,
        } as IEventTeam,
      },
      id: 12134,
    } as IEventTeamHeader;
    const userEntity = configService.factoryEntity();
    let compEventTeam: ICompEventTeamSchool =
      schoolTeamService.factoryCompEventTeamSchool(eventTeamHeader, userEntity);
    let result = schoolTeamService.canAddAthlete(
      compEventTeam,
      eventTeamHeader
    );
    expect(result).toBe(true);

    //  Add empty athlete
    compEventTeam.athletes = schoolTeamService.addEmptyAthlete(
      compEventTeam.athletes,
      compEventTeam.athletes.length + 1
    );
    // console.log("compEventTeam.athletes..........", R.clone(compEventTeam.athletes));
    result = schoolTeamService.canAddAthlete(compEventTeam, eventTeamHeader);
    // console.log("..........", result);
    //  THe last athlete in list has empty string, so no point in adding another.
    expect(result).toBe(false);

    //  Add name up to max atheletes
    compEventTeam = R.clone(compEventTeam);
    compEventTeam.athletes.push({ id: 4, name: "sss", consent: true });
    compEventTeam.athletes.push({ id: 5, name: "sss", consent: true });
    compEventTeam.athletes.push({ id: 6, name: "sss", consent: true });
    result = schoolTeamService.canAddAthlete(compEventTeam, eventTeamHeader);
    expect(result).toBe(true);
  });
});
