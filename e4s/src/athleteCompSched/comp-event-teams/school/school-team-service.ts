import * as R from "ramda";
import { ICompEventTeamSchool, ISchoolTeamAthlete } from "./school-team-models";
import { IEventTeamHeader } from "../event-teams-models";
import { ICompetitionInfo } from "../../../competition/competition-models";
import {
  IUserMessage,
  USER_MESSAGE_LEVEL,
} from "../../../user-message/user-message-models";
import { EventTeamService } from "../event-team-service";
import { IEntity } from "../../../config/config-app-models";
import { IClubCompInfo } from "../../../entry/v2/schools/clubCompInfo-models";
import { simpleClone } from "../../../common/common-service-utils";
import { IAthleteSummary } from "../../../athlete/athlete-models";

const eventTeamService: EventTeamService = new EventTeamService();

export class SchoolTeamService {
  public factoryCompEventTeamSchool(
    eventTeamHeader: IEventTeamHeader,
    entity: IEntity,
    _clubCompInfo?: IClubCompInfo | null
  ): ICompEventTeamSchool {
    const base = eventTeamService.factoryGetCompEventTeamBase(
      eventTeamHeader,
      entity
    );
    const compEventTeamSchool: ICompEventTeamSchool = {
      ...base,
      athletes: [],
    };
    return compEventTeamSchool;
  }

  public factorySchoolTeamAthlete(id?: number): ISchoolTeamAthlete {
    return {
      id: id ? id : 0,
      name: "",
      consent: true,
    } as ISchoolTeamAthlete;
  }

  public canAddAthlete(
    compEventTeam: ICompEventTeamSchool,
    eventTeamHeader: IEventTeamHeader
  ): boolean {
    let schoolTeamAthletes = compEventTeam.athletes;
    if (schoolTeamAthletes.length === 0) {
      return true;
    }
    const lastAthlete: ISchoolTeamAthlete =
      schoolTeamAthletes[schoolTeamAthletes.length - 1];
    if (lastAthlete.name.length === 0) {
      //  Last athlete is already an empty slot.
      return false;
    }
    if (eventTeamHeader.ceoptions.eventTeam.max === 0) {
      //  No limit
      return true;
    }
    compEventTeam = this.stripEmptyAthletes(compEventTeam);
    schoolTeamAthletes = compEventTeam.athletes;
    if (schoolTeamAthletes.length >= eventTeamHeader.ceoptions.eventTeam.max) {
      return false;
    }
    return true;
  }

  public addEmptyAthlete(
    schoolTeamAthletes: ISchoolTeamAthlete[],
    id: number
  ): ISchoolTeamAthlete[] {
    schoolTeamAthletes = R.clone(schoolTeamAthletes);
    schoolTeamAthletes.push(this.factorySchoolTeamAthlete(id));
    return schoolTeamAthletes;
  }

  public stripEmptyAthletes(
    compEventTeam: ICompEventTeamSchool
  ): ICompEventTeamSchool {
    compEventTeam = R.clone(compEventTeam);
    compEventTeam.athletes = compEventTeam.athletes.filter((ath) => {
      if (!ath.name) {
        return false;
      }
      const athleteName: string = ath.name.replace(/\s/g, "");
      return athleteName.length > 0;
    });
    return compEventTeam;
  }

  public convertAthleteSummaryToSchoolTeamAthlete(
    athleteSummary: IAthleteSummary
  ): ISchoolTeamAthlete {
    const isFirstNameSameAsSurName =
      athleteSummary.firstName.toUpperCase() ===
      athleteSummary.surName.toUpperCase();

    return {
      id: athleteSummary.id,
      name: isFirstNameSameAsSurName
        ? athleteSummary.firstName
        : athleteSummary.firstName + " " + athleteSummary.surName,
      consent: true,
    } as ISchoolTeamAthlete;
  }

  public cleanSchoolTeam(team: ICompEventTeamSchool): ICompEventTeamSchool {
    const teamInternal = simpleClone(team);

    teamInternal.athletes = teamInternal.athletes.map((athlete) => {
      const athleteSummary: IAthleteSummary = athlete as any as IAthleteSummary;

      // if athleteSummary has firstName property then convert to school team athlete
      if ("firstName" in athleteSummary) {
        // Turn an athlete summary into a school team athlete
        return this.convertAthleteSummaryToSchoolTeamAthlete(athleteSummary);
      }
      return athleteSummary as any as ISchoolTeamAthlete;
    });
    return teamInternal;
  }

  public validateCompEventTeam(
    eventTeamHeader: IEventTeamHeader,
    compEventTeam: ICompEventTeamSchool,
    _competition: ICompetitionInfo,
    _eventTeamHeaders: IEventTeamHeader[]
  ): IUserMessage[] {
    let userMessages: IUserMessage[] = [];

    compEventTeam = this.stripEmptyAthletes(compEventTeam);
    userMessages = userMessages.concat(
      ...eventTeamService.validateBase(eventTeamHeader, compEventTeam)
    );

    // const athletes = compEventTeam.athletes.event-teams-filter((ath) => {
    //     const athleteName: string = ath.name.replace(/\s/g, "");
    //     return athleteName.length > 0;
    // });

    const options = eventTeamService.getEventTeamOptions(eventTeamHeader);
    const hasMinBeenReached = compEventTeam.athletes.length >= options.min;
    const hasMaxBeenReached =
      options.max === 0 ? true : compEventTeam.athletes.length <= options.max;
    if (!(hasMinBeenReached && hasMaxBeenReached)) {
      userMessages.push({
        message:
          "Enter between " + options.min + " and " + options.max + " athletes.",
        level: USER_MESSAGE_LEVEL.WARN,
      });
    }

    return userMessages;
  }
}
