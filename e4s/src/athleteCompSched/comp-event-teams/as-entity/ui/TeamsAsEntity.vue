<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <EntitySelector
      :is-loading="state.isLoading"
      @area-selected="onAreaSelected"
      @club-selected="onClubSelected"
    />

    <div v-if="state.isLoading" class="e4s-loading">Loading team data...</div>

    <div v-if="isEnteringTeam"></div>

    <div v-if="hasTeamHeaders" class="e4s-flex-column e4s-gap--standard">
      <div
        v-for="header in state.teamHeaders"
        :key="header.id"
        class="e4s-card e4s-card--generic"
      >
        <div class="e4s-flex-column e4s-gap--standard e4s-padding--standard">
          <div
            class="e4s-flex-row e4s-justify-flex-space-between e4s-flex-start"
          >
            <EventTeamHeaderNameV2
              class="e4s-input--label"
              :event-team-header="header"
            />
            <EventTeamHeaderStatusPillV2 :event-team-header="header" />
          </div>

          <div class="e4s-flex-column e4s-gap--standard">
            <div
              v-if="header.ceoptions.helpText.length > 0"
              class="e4s-subheader--200"
              v-text="header.ceoptions.helpText"
            ></div>

            <div class="e4s-flex-column e4s-gap--standard">
              <div
                class="e4s-flex-row e4s-gap--standard"
                v-for="team in header.compEventTeams"
                :key="team.id"
              >
                <div v-text="team.teamName"></div>
                <div v-if="team.athletes" class="e4s-text--muted">
                  ({{ team.athletes.length }} athletes)
                </div>
              </div>
            </div>

            <div class="e4s-flex-row e4s-justify-flex-end">
              <ButtonGenericV2
                text="Add Team"
                :disabled="!canAddTeam(header)"
                @click="addTeam(header)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  computed,
  SetupContext,
} from "vue";
import EntitySelector from "../../../../config/entity/EntitySelector.vue";
import ClubTypeAheadByType from "../../../../club/v2/clubtypeahead/ClubTypeAheadByType.vue";
import FormGenericInputTemplateV2 from "../../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import FormGenericInputErrorMessageV2 from "../../../../common/ui/layoutV2/form/form-generic-input-error-message-v2.vue";
import { IArea } from "../../../../area/area-models";
import { IClub } from "../../../../club/club-models";
import { CompEventTeamHeaderData } from "../../comp-event-team-header-data";
import { IEventTeamHeader } from "../../event-teams-models";
import { messageDispatchHelper } from "../../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../../user-message/user-message-models";
import { IServerGenericResponse } from "../../../../common/common-models";
import FieldRadioV2 from "../../../../common/ui/layoutV2/fields/field-radio-v2.vue";
import FieldLabelGenderRadioV2 from "../../../../common/ui/layoutV2/fields/gender/field-label-gender-radio-v2.vue";
import EventTeamHeaderNameV2 from "../../v2/event-team-header-name-v2.vue";
import EventTeamHeaderStatusPillV2 from "../../v2/event-team-header-status-pill-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";

export default defineComponent({
  name: "TeamsAsEntity",

  components: {
    FieldLabelGenderRadioV2,
    FieldRadioV2,
    ClubTypeAheadByType,
    EntitySelector,
    FormGenericInputTemplateV2,
    FormGenericInputErrorMessageV2,
    EventTeamHeaderNameV2,
    EventTeamHeaderStatusPillV2,
    ButtonGenericV2,
  },

  props: {
    competitionId: {
      type: Number,
      required: true,
    },
  },

  setup(props, context: SetupContext) {
    const compEventTeamHeaderData = new CompEventTeamHeaderData();

    const state = reactive({
      selectedArea: null as IArea | null,
      selectedClub: null as IClub | null,
      isLoading: false,
      clubType: "C" as "C" | "S",
      errors: {
        area: "",
        club: "",
      },
      teamHeaders: [] as IEventTeamHeader[],
      isEnteringTeam: false,
    });

    const fetchTeamHeaders = async (entityLevel: number, entityId: number) => {
      state.isLoading = true;
      try {
        const response: IServerGenericResponse =
          await compEventTeamHeaderData.getEventHeaders(
            props.competitionId,
            entityLevel,
            entityId
          );

        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error || "Failed to fetch team headers",
            USER_MESSAGE_LEVEL.ERROR
          );
          return;
        }

        state.teamHeaders = response.data;
        context.emit("team-headers-updated", response.data);
      } catch (error) {
        messageDispatchHelper(
          "An error occurred while fetching team data",
          USER_MESSAGE_LEVEL.ERROR
        );
      } finally {
        state.isLoading = false;
      }
    };

    const onAreaSelected = async (area: IArea) => {
      state.selectedArea = area;
      state.errors.area = "";

      // Reset club and team headers when area changes
      state.selectedClub = null;
      state.teamHeaders = [];

      context.emit("area-selected", area);

      if (area?.id) {
        await fetchTeamHeaders(1, area.id);
      }
    };

    const onClubSelected = async (club: IClub) => {
      state.selectedClub = club;
      state.errors.club = "";

      // Reset team headers when club changes
      state.teamHeaders = [];

      context.emit("club-selected", club);

      if (club?.id) {
        await fetchTeamHeaders(2, club.id);
      }
    };

    const validate = (): boolean => {
      let isValid = true;

      if (!state.selectedArea) {
        state.errors.area = "Please select an area";
        isValid = false;
      }

      if (!state.selectedClub) {
        state.errors.club = "Please select a club";
        isValid = false;
      }

      return isValid;
    };

    const reset = () => {
      state.selectedArea = null;
      state.selectedClub = null;
      state.teamHeaders = [];
      state.errors.area = "";
      state.errors.club = "";
    };

    // Computed properties
    const hasSelections = computed(() => {
      return state.selectedArea !== null && state.selectedClub !== null;
    });

    const hasTeamHeaders = computed(() => {
      return state.teamHeaders.length > 0;
    });

    const canAddTeam = (header: IEventTeamHeader): boolean => {
      if (!header.ceoptions.eventTeam) return false;
      const maxTeams = header.ceoptions.eventTeam.maxEventTeams || 0;
      return maxTeams === 0 || header.compEventTeams.length < maxTeams;
    };

    const addTeam = (header: IEventTeamHeader) => {
      // Implement team addition logic
      console.log("Adding team for header:", header.id);
    };

    return {
      state,
      props,
      onAreaSelected,
      onClubSelected,
      validate,
      reset,
      hasSelections,
      hasTeamHeaders,
      canAddTeam,
      addTeam,
    };
  },
});
</script>

<style scoped>
.e4s-loading {
  text-align: center;
  padding: 1rem;
}

.e4s-team-headers {
  margin-top: 1rem;
}
</style>
