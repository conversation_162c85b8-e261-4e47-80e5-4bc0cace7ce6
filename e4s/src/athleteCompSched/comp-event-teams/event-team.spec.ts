import {
  IAgeGroup,
  IAgeInfo,
  IAthleteSummary,
} from "../../athlete/athlete-models";
import { GENDER, IObjectKeyType } from "../../common/common-models";
import { IUserMessage } from "../../user-message/user-message-models";
import {
  IAgeGroupUpScale,
  ICeoptions,
  ICompShedRuleConfig,
  ICompShedRuleOptions,
  IEventTeam,
  ISecurity,
} from "../athletecompsched-models";
// import {IAgeGroupUpScale, ICeoptions, ICompShedRuleConfig, ICompShedRuleOptions, IEventTeam, ISecurity} from "../athletecompsched-models";
import { EventTeamService } from "./event-team-service";
import { ICompEventTeam, IEventTeamHeader } from "./event-teams-models";
import { ICompetitionInfo } from "../../competition/competition-models";
import { ConfigService } from "../../config/config-service";
import {
  IAreaUserInfo,
  IClubUserInfo,
  IUserInfo,
} from "../../config/config-app-models";
import { IOrg } from "../../org/org-models";
import { EventTeamsFilterService } from "./event-teams-filter/event-teams-service";
import { IEventTeamHeaderFilter } from "./event-teams-filter/event-teams-filter-models";
import { simpleClone } from "../../common/common-service-utils";
import { mockEventTeamHeaders } from "./comp-event-teams-mock--teams";

const eventTeamService: EventTeamService = new EventTeamService();
const configService: ConfigService = new ConfigService();

describe("Comp Event Teams", () => {
  test("canAddAthlete", () => {
    const eventTeamHeader: IEventTeamHeader = {
      eoptions: {
        eventTeam: {
          max: 0,
        } as IEventTeam,
      },
    } as IEventTeamHeader;
    const compEventTeam: ICompEventTeam = {
      athletes: [] as IAthleteSummary[],
    } as ICompEventTeam;

    let athlete: IAthleteSummary = { id: 1 } as IAthleteSummary;

    let result: IUserMessage[] = eventTeamService.canAddAthlete(
      eventTeamHeader,
      compEventTeam,
      athlete,
      0
    );
    // console.log("", result);
    expect(result.length).toBe(1);
    result = result.filter((mess) => mess.message === "Hit max of 0");
    expect(result.length).toBe(1);

    eventTeamHeader.eoptions.eventTeam.max = 6;
    //  can add the athlete
    result = eventTeamService.canAddAthlete(
      eventTeamHeader,
      compEventTeam,
      athlete,
      0
    );
    expect(result.length).toBe(0);
    compEventTeam.athletes.push(athlete);

    athlete = { id: 3 } as IAthleteSummary;
    result = eventTeamService.canAddAthlete(
      eventTeamHeader,
      compEventTeam,
      athlete,
      1
    );
    // console.log("................", result);
    expect(result.length).toBe(0);
    compEventTeam.athletes.push(athlete);

    athlete = { id: 1 } as IAthleteSummary;
    result = eventTeamService.canAddAthlete(
      eventTeamHeader,
      compEventTeam,
      athlete,
      2
    );
    // console.log("", result);
    expect(result.length).toBe(1);
    result = result.filter((mess) => mess.message === "Name already in list.");
    expect(result.length).toBe(1);

    //  add to position zero, where originally added
    athlete = { id: 1 } as IAthleteSummary;
    result = eventTeamService.canAddAthlete(
      eventTeamHeader,
      compEventTeam,
      athlete,
      0
    );
    // console.log("................", result);
    expect(result.length).toBe(0);
  });

  test("validateCompEventTeam", () => {
    const eventTeamHeader: IEventTeamHeader = {
      eventName: "Egg and spoon",
      eoptions: {
        eventTeam: {
          min: 4,
          max: 6,
        } as IEventTeam,
      },
      ageGroup: {
        name: "",
      },
      compEventTeams: [] as ICompEventTeam[],
    } as IEventTeamHeader;

    const compEventTeam: ICompEventTeam = {
      teamName: "",
      athletes: [] as IAthleteSummary[],
    } as ICompEventTeam;

    let result: IUserMessage[] = eventTeamService.validateCompEventTeam(
      eventTeamHeader,
      compEventTeam,
      {} as ICompetitionInfo,
      []
    );
    // console.log("zzzzzzzzzzzzzz", result);
    // expect(result.length).toBe(2);

    let res;
    res = result.filter((userMessage) => {
      return userMessage.message === "Enter team name";
    });
    expect(res.length).toBe(1);

    res = result.filter((userMessage) => {
      return userMessage.message === "Enter between 4 and 6 athletes.";
    });
    expect(res.length).toBe(1);

    // res = result.event-teams-filter((userMessage) => {
    //     return userMessage.message === "Need at least 2 U11";
    // });
    // expect(res.length).toBe(1 );

    compEventTeam.teamName = "qwerty";
    compEventTeam.athletes.push({ id: 1, clubid: 3 } as IAthleteSummary);
    compEventTeam.athletes.push({
      id: 2,
      clubid: 3,
      firstName: "joe",
      surName: "Qaz",
    } as IAthleteSummary);
    compEventTeam.athletes.push({ id: 3, clubid: 3 } as IAthleteSummary);
    compEventTeam.athletes.push({ id: 4, clubid: 3 } as IAthleteSummary);
    result = eventTeamService.validateCompEventTeam(
      eventTeamHeader,
      compEventTeam,
      {} as ICompetitionInfo,
      []
    );
    // console.log("qwerty", result);
    expect(result.length).toBe(0);

    compEventTeam.athletes.push({
      id: 2,
      firstName: "joe",
      surName: "Qaz",
      clubid: 3,
    } as IAthleteSummary);
    result = eventTeamService.validateCompEventTeam(
      eventTeamHeader,
      compEventTeam,
      {} as ICompetitionInfo,
      []
    );
    // console.log("qwerty", result);
    expect(result.length).toBe(1);

    compEventTeam.athletes.push({
      id: 77,
      firstName: "joex",
      surName: "Qazx",
      clubid: 4,
    } as IAthleteSummary);
    result = eventTeamService.validateCompEventTeam(
      eventTeamHeader,
      compEventTeam,
      {} as ICompetitionInfo,
      []
    );
    // console.log("qwerty", result);
    expect(result.length).toBe(1);

    eventTeamHeader.eoptions.eventTeam = {
      ...eventTeamHeader.eoptions.eventTeam,
      singleClub: true,
    };
    result = eventTeamService.validateCompEventTeam(
      eventTeamHeader,
      compEventTeam,
      {} as ICompetitionInfo,
      []
    );
    // console.log("qwerty", result);
    expect(result.length).toBe(2);
  });

  test("stripeventCompEventTeam", () => {
    const compEventTeam: any = {
      id: 0,
      ceid: 0,
      teamName: "qwdqw",
      athletes: [
        {
          id: 50645,
          firstName: "Regina",
          surName: "Rawle",
          issueid: 0,
          aocode: "IRL",
          URN: 308827,
          dob: "2002-03-10",
          gender: "F",
          classification: 0,
          schoolid: null,
          clubid: 983,
          activeEndDate: "2019-12-31",
          clubname: "Longford AC",
          region: "Longford",
          club: "Longford AC, Longford",
          ageInfo: {
            ageGroups: [
              {
                compDate: "2019-03-31",
                agid: 42,
                minAge: 16,
                todate: "2003-12-31",
                fromdate: "1984-01-01",
                id: 42,
                Name: "Senior",
                MaxAge: 35,
                AtDay: 31,
                AtMonth: 12,
                year: 0,
                minAtDay: null,
                minAtMonth: null,
                minYear: null,
                keyName: "Senior 16-35",
                shortName: "Senior",
              },
            ],
            vetAgeGroup: null,
            currentAge: 16,
            competitionAge: 17,
            ageGroup: {
              compDate: "2019-03-31",
              agid: 42,
              minAge: 16,
              todate: "2003-12-31",
              fromdate: "1984-01-01",
              id: 42,
              Name: "Senior",
              MaxAge: 35,
              AtDay: 31,
              AtMonth: 12,
              year: 0,
              minAtDay: null,
              minAtMonth: null,
              minYear: null,
              keyName: "Senior 16-35",
              shortName: "Senior",
            },
          },
          inTeam: false,
        },
        {
          id: 50310,
          firstName: "Ella",
          surName: "Quinn",
          issueid: 0,
          aocode: "IRL",
          URN: 294431,
          dob: "2011-08-16",
          gender: "F",
          classification: 0,
          schoolid: null,
          clubid: 691,
          activeEndDate: "2019-12-31",
          clubname: "Glenmore AC",
          region: "Louth",
          club: "Glenmore AC, Louth",
          ageInfo: {
            ageGroups: [
              {
                compDate: "2019-12-31",
                agid: 45,
                minAge: 8,
                todate: "2011-12-31",
                fromdate: "2011-01-01",
                id: 45,
                Name: "Under 9",
                MaxAge: 8,
                AtDay: 31,
                AtMonth: 12,
                year: 0,
                minAtDay: null,
                minAtMonth: null,
                minYear: null,
                keyName: "Under 9 Eire",
                shortName: "U9",
              },
            ],
            vetAgeGroup: null,
            currentAge: 7,
            competitionAge: 8,
            ageGroup: {
              compDate: "2019-12-31",
              agid: 45,
              minAge: 8,
              todate: "2011-12-31",
              fromdate: "2011-01-01",
              id: 45,
              Name: "Under 9",
              MaxAge: 8,
              AtDay: 31,
              AtMonth: 12,
              year: 0,
              minAtDay: null,
              minAtMonth: null,
              minYear: null,
              keyName: "Under 9 Eire",
              shortName: "U9",
            },
          },
          inTeam: false,
        },
        {
          id: 60548,
          firstName: "Pauline",
          surName: "Yankovski",
          issueid: 0,
          aocode: "IRL",
          URN: 313137,
          dob: "2011-08-06",
          gender: "F",
          classification: 0,
          schoolid: null,
          clubid: 3085,
          activeEndDate: "2019-12-31",
          clubname: "St. Benedict's AC",
          region: "Wicklow",
          club: "St. Benedict's AC, Wicklow",
          ageInfo: {
            ageGroups: [
              {
                compDate: "2019-12-31",
                agid: 45,
                minAge: 8,
                todate: "2011-12-31",
                fromdate: "2011-01-01",
                id: 45,
                Name: "Under 9",
                MaxAge: 8,
                AtDay: 31,
                AtMonth: 12,
                year: 0,
                minAtDay: null,
                minAtMonth: null,
                minYear: null,
                keyName: "Under 9 Eire",
                shortName: "U9",
              },
            ],
            vetAgeGroup: null,
            currentAge: 7,
            competitionAge: 8,
            ageGroup: {
              compDate: "2019-12-31",
              agid: 45,
              minAge: 8,
              todate: "2011-12-31",
              fromdate: "2011-01-01",
              id: 45,
              Name: "Under 9",
              MaxAge: 8,
              AtDay: 31,
              AtMonth: 12,
              year: 0,
              minAtDay: null,
              minAtMonth: null,
              minYear: null,
              keyName: "Under 9 Eire",
              shortName: "U9",
            },
          },
          inTeam: false,
        },
        {
          id: 27735,
          firstName: "Elaine",
          surName: "Irhue",
          issueid: 0,
          aocode: "IRL",
          URN: 306639,
          dob: "2011-02-28",
          gender: "F",
          classification: 0,
          schoolid: null,
          clubid: 1130,
          activeEndDate: "2019-12-31",
          clubname: "Newbridge AC",
          region: "Kildare",
          club: "Newbridge AC, Kildare",
          ageInfo: {
            ageGroups: [
              {
                compDate: "2019-12-31",
                agid: 45,
                minAge: 8,
                todate: "2011-12-31",
                fromdate: "2011-01-01",
                id: 45,
                Name: "Under 9",
                MaxAge: 8,
                AtDay: 31,
                AtMonth: 12,
                year: 0,
                minAtDay: null,
                minAtMonth: null,
                minYear: null,
                keyName: "Under 9 Eire",
                shortName: "U9",
              },
            ],
            vetAgeGroup: null,
            currentAge: 8,
            competitionAge: 8,
            ageGroup: {
              compDate: "2019-12-31",
              agid: 45,
              minAge: 8,
              todate: "2011-12-31",
              fromdate: "2011-01-01",
              id: 45,
              Name: "Under 9",
              MaxAge: 8,
              AtDay: 31,
              AtMonth: 12,
              year: 0,
              minAtDay: null,
              minAtMonth: null,
              minYear: null,
              keyName: "Under 9 Eire",
              shortName: "U9",
            },
          },
          inTeam: false,
        },
        {
          id: 0,
          firstName: "",
          surName: "",
          URN: "",
          dob: "",
          club: "",
          clubid: 0,
          gender: "",
          classification: 0,
          school: "",
          schoolid: 0,
          pbInfo: [],
          inTeam: false,
          ageInfo: {
            ageGroup: {},
            ageGroups: [],
            vetAgeGroup: {},
          },
          aocode: "",
        },
        {
          id: 0,
          firstName: "",
          surName: "",
          URN: "",
          dob: "",
          club: "",
          clubid: 0,
          gender: "",
          classification: 0,
          school: "",
          schoolid: 0,
          pbInfo: [],
          inTeam: false,
          ageInfo: {
            ageGroup: {},
            ageGroups: [],
            vetAgeGroup: {},
          },
          aocode: "",
        },
      ],
      user: {},
      paid: 0,
      prodId: 0,
    } as any as ICompEventTeam;

    const result = eventTeamService.stripEventCompEventTeam(compEventTeam);
    expect(compEventTeam.athletes.length).toBe(6);
    // console.log("stripeventCompEventTeam", result);
    expect(result.athletes.length).toBe(4);
  });

  test("upscale", () => {
    const eventTeamHeader: IEventTeamHeader = {
      id: 0,
      eventName: "Under 9 4 x 200",
      ageGroup: {
        id: 9,
        name: "Under 9",
      },
      eoptions: {
        min: 4,
        max: 6,
      },
      ceoptions: {
        ageGroups: [
          {
            id: 8,
            name: "under 8",
          },
          {
            id: 7,
            name: "under 7",
          },
        ],
        eventTeam: {
          ageGroupUpScale: [
            {
              id: 9,
              minCount: 2,
              maxCount: -1,
            },
            {
              id: 8,
              minCount: 2,
              maxCount: 4,
            },
            {
              id: 7,
              minCount: -1,
              maxCount: 2,
            },
            {
              id: 6,
              minCount: -1,
              maxCount: 1,
            },
          ],
        },
      },
    } as IEventTeamHeader;

    let result = eventTeamService.upscaleAvailable(eventTeamHeader);
    expect(result).toBe(true);

    result = eventTeamService.upscaleAvailable({
      ceoptions: {},
    } as IEventTeamHeader);
    expect(result).toBe(false);

    // result = eventTeamService.upscaleConfigMeetsMinRequirement(eventTeamHeader);
    const resultUpscaleObj: IObjectKeyType<IAgeGroupUpScale> =
      eventTeamService.upscaleGetConfigAsObject(eventTeamHeader);
    // console.log("result", resultUpscaleObj);

    expect(resultUpscaleObj["8"].id).toBe(8);

    let compEventTeam: ICompEventTeam = {
      athletes: [
        {
          firstName: "joe",
          ageInfo: {
            ageGroup: {
              id: 8,
            },
            ageGroups: [{ id: 7 }, { id: 8 }],
          },
        } as IAthleteSummary,
      ],
    } as any as ICompEventTeam;

    let upscaleValid: IUserMessage[] =
      eventTeamService.upscaleValidateCompEventTeam(
        eventTeamHeader,
        compEventTeam
      );
    // console.log("upscaleValid", upscaleValid);
    expect(upscaleValid.length).toBe(2);

    compEventTeam = {
      athletes: [
        {
          firstName: "sue",
          ageInfo: {
            ageGroup: {
              id: 6,
            },
          },
        } as IAthleteSummary,
        {
          firstName: "jill",
          ageInfo: {
            ageGroup: {
              id: 6,
            },
          },
        } as IAthleteSummary,
      ],
    } as any as ICompEventTeam;

    upscaleValid = eventTeamService.upscaleValidateCompEventTeam(
      eventTeamHeader,
      compEventTeam
    );
    // console.log("upscaleValid", upscaleValid);
    expect(upscaleValid.length).toBe(3);
  });

  test("isTeamNameUnique A", () => {
    let compTemp: ICompEventTeam;
    const eventTeamHeader: IEventTeamHeader = {
      id: 2,
      eoptions: {
        eventTeam: {
          min: 4,
          max: 6,
        } as IEventTeam,
      },
      compEventTeams: [] as ICompEventTeam[],
    } as IEventTeamHeader;

    const compEventTeam: ICompEventTeam = {
      teamName: "",
    } as ICompEventTeam;

    //  Team name empty
    let result: boolean = eventTeamService.isTeamNameUnique(
      eventTeamHeader,
      compEventTeam
    );
    expect(result).toBe(false);

    const userEntity = configService.factoryEntity();

    compEventTeam.teamName = "foo";
    result = eventTeamService.isTeamNameUnique(eventTeamHeader, compEventTeam);
    expect(result).toBe(true);

    compTemp = eventTeamService.factoryGetCompEventTeam(
      eventTeamHeader,
      userEntity
    );
    compTemp.teamName = "foo";
    eventTeamHeader.compEventTeams.push(compTemp);
    result = eventTeamService.isTeamNameUnique(eventTeamHeader, compEventTeam);
    expect(result).toBe(false);

    compEventTeam.teamName = "bar";
    // console.log("......compEventTeam", compEventTeam);
    // console.log("......", eventTeamHeader.compEventTeams);
    result = eventTeamService.isTeamNameUnique(eventTeamHeader, compEventTeam);
    expect(result).toBe(true);

    compTemp = eventTeamService.factoryGetCompEventTeam(
      eventTeamHeader,
      userEntity
    );
    compTemp.teamName = "one";
    eventTeamHeader.compEventTeams.push(compTemp);

    compTemp = eventTeamService.factoryGetCompEventTeam(
      eventTeamHeader,
      userEntity
    );
    compTemp.teamName = "two";
    eventTeamHeader.compEventTeams.push(compTemp);

    result = eventTeamService.isTeamNameUnique(eventTeamHeader, compEventTeam);
    expect(result).toBe(true);

    compTemp = eventTeamService.factoryGetCompEventTeam(
      eventTeamHeader,
      userEntity
    );
    compTemp.teamName = "bar";
    eventTeamHeader.compEventTeams.push(compTemp);

    result = eventTeamService.isTeamNameUnique(eventTeamHeader, compEventTeam);
    expect(result).toBe(false);
  });

  test("isTeamNameUnique B", () => {
    let compTemp: ICompEventTeam;
    const eventTeamHeader = eventTeamService.factoryEventTeamHeader();
    eventTeamHeader.id = 2;
    eventTeamHeader.eventName = "4 x 100m";
    eventTeamHeader.gender = GENDER.FEMALE;
    eventTeamHeader.ceoptions.eventTeam.min = 4;
    eventTeamHeader.ceoptions.eventTeam.max = 6;
    /*
        const eventTeamHeader: IEventTeamHeader = {
            id: 2,
            eventName: "4 x 100m",
            gender: "F",
            ceoptions: {
                eventTeam: {
                    min: 4,
                    max: 6,
                    teamNameFormat: ""
                } as IEventTeam
            },
            ageGroup: {
                name: ""
            },
            compEventTeams: [] as ICompEventTeam[]
        } as IEventTeamHeader;
        */

    const userEntity = configService.factoryEntity();
    compTemp = eventTeamService.factoryGetCompEventTeam(
      eventTeamHeader,
      userEntity
    );
    expect(compTemp.teamName).toBe("4 x 100m Female");

    // compTemp = eventTeamService.factoryGetCompEventTeam(eventTeamHeader, userEntity);
    // expect(compTemp.teamName).toBe("");
    //
    // compTemp = eventTeamService.factoryGetCompEventTeam(eventTeamHeader, userEntity);
    // expect(compTemp.teamName).toBe("");

    // compTemp = eventTeamService.factoryGetCompEventTeam(eventTeamHeader, userEntity);
    // expect(compTemp.teamName).toBe("");

    // userEntity.name = "Seaton Runners";
    // userEntity.entityName = "Club";
    // eventTeamHeader.ceoptions.eventTeam.teamNameFormat = TEAM_NAME_FORMAT_TYPES.ENTITY + " " + TEAM_NAME_FORMAT_TYPES.UNIQUE;
    // compTemp = eventTeamService.factoryGetCompEventTeam(eventTeamHeader, userEntity);
    // expect(compTemp.teamName).toBe("Seaton Runners A");

    // eventTeamHeader.ceoptions.eventTeam.teamNameFormat = TEAM_NAME_FORMAT_TYPES.ENTITY + " " + TEAM_NAME_FORMAT_TYPES.EVENT ;
    // compTemp = eventTeamService.factoryGetCompEventTeam(eventTeamHeader, userEntity);
    // expect(compTemp.teamName).toBe("Seaton Runners 4 x 100m");

    // eventTeamHeader.ceoptions.eventTeam.teamNameFormat = TEAM_NAME_FORMAT_TYPES.ENTITY + " " +
    //     TEAM_NAME_FORMAT_TYPES.EVENT  +
    //     " (" + TEAM_NAME_FORMAT_TYPES.GENDER + ")";
    // compTemp = eventTeamService.factoryGetCompEventTeam(eventTeamHeader, userEntity);
    // expect(compTemp.teamName).toBe("Seaton Runners 4 x 100m (Female)");
    //
    //
    // eventTeamHeader.ceoptions.eventTeam.teamNameFormat = TEAM_NAME_FORMAT_TYPES.ENTITY + " " +
    //     TEAM_NAME_FORMAT_TYPES.EVENT  +
    //     " (" + TEAM_NAME_FORMAT_TYPES.GENDER + ")";
    // compTemp = eventTeamService.factoryGetCompEventTeam(eventTeamHeader, userEntity);
    // expect(compTemp.teamName).toBe("Seaton Runners 4 x 100m (Female)");
    //
    // eventTeamHeader.ceoptions.eventTeam.teamNameFormat = TEAM_NAME_FORMAT_TYPES.ENTITY + " " +
    //     TEAM_NAME_FORMAT_TYPES.EVENT  +
    //     " (" + TEAM_NAME_FORMAT_TYPES.GENDER + ")" + " " + TEAM_NAME_FORMAT_TYPES.AGE_GROUP;
    //
    // eventTeamHeader.ageGroup.name = "Year 3";
    // compTemp = eventTeamService.factoryGetCompEventTeam(eventTeamHeader, userEntity);
    // expect(compTemp.teamName).toBe("Seaton Runners 4 x 100m (Female) Year 3");
  });

  test("minTargetAgeGroup", () => {
    const eventTeamHeader: IEventTeamHeader = {
      ageGroup: {
        id: 12,
      },
      ceoptions: {
        eventTeam: {
          max: 0,
          minTargetAgeGroupCount: 2,
        } as IEventTeam,
      },
    } as IEventTeamHeader;

    const compEventTeam: ICompEventTeam = {
      athletes: [
        {
          id: 1,
          firstName: "bob",
          ageInfo: {
            ageGroups: [
              {
                id: 11,
              } as IAgeGroup,
              {
                id: 12,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
        {
          id: 2,
          firstName: "john",
          ageInfo: {
            ageGroups: [
              {
                id: 9,
              } as IAgeGroup,
              {
                id: 10,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
        {
          id: 3,
          firstName: "sam",
          ageInfo: {
            ageGroups: [
              {
                id: 7,
              } as IAgeGroup,
              {
                id: 6,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
      ],
    } as ICompEventTeam;

    let result: IAthleteSummary[] =
      eventTeamService.athletesInTargetAgeGroupCount(
        eventTeamHeader,
        compEventTeam,
        true
      );
    expect(result.length).toBe(1);

    result = eventTeamService.athletesInTargetAgeGroupCount(
      eventTeamHeader,
      compEventTeam,
      false
    );
    expect(result.length).toBe(2);
  });

  test("userInHowManyTeams", () => {
    const eventTeamHeader: IEventTeamHeader = {
      ageGroup: {
        id: 12,
      },
      ceoptions: {
        eventTeam: {
          max: 0,
          minTargetAgeGroupCount: 2,
        } as IEventTeam,
      },
    } as IEventTeamHeader;

    const compEventTeam: ICompEventTeam = {
      teamName: "Team 1",
      athletes: [
        {
          id: 1,
          firstName: "bob",
          ageInfo: {
            ageGroups: [
              {
                id: 11,
              } as IAgeGroup,
              {
                id: 12,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
        {
          id: 2,
          firstName: "john",
          ageInfo: {
            ageGroups: [
              {
                id: 9,
              } as IAgeGroup,
              {
                id: 10,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
        {
          id: 3,
          firstName: "sam",
          ageInfo: {
            ageGroups: [
              {
                id: 7,
              } as IAgeGroup,
              {
                id: 6,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
      ],
    } as ICompEventTeam;

    const compEventTeam2: ICompEventTeam = {
      teamName: "team 2",
      athletes: [
        {
          id: 2,
          firstName: "john",
          ageInfo: {
            ageGroups: [
              {
                id: 9,
              } as IAgeGroup,
              {
                id: 10,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
      ],
    } as ICompEventTeam;

    eventTeamHeader.compEventTeams = [compEventTeam, compEventTeam2];

    const result = eventTeamService.userInHowManyTeams(eventTeamHeader, {
      id: 2,
    } as IAthleteSummary);
    expect(result.length).toBe(2);

    expect(result[1].teamName).toBe("team 2");
  });

  test("athletesInHowManyTeamsObj", () => {
    const eventTeamHeader: IEventTeamHeader = {
      eventName: "4 x 200m",
      ageGroup: {
        id: 12,
      },
      ceoptions: {
        eventTeam: {
          max: 0,
          minTargetAgeGroupCount: 2,
        } as IEventTeam,
      },
    } as IEventTeamHeader;

    const compEventTeam: ICompEventTeam = {
      teamName: "Team 1",
      athletes: [
        {
          id: 1,
          firstName: "bob",
          ageInfo: {
            ageGroups: [
              {
                id: 11,
              } as IAgeGroup,
              {
                id: 12,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
        {
          id: 2,
          firstName: "john",
          ageInfo: {
            ageGroups: [
              {
                id: 9,
              } as IAgeGroup,
              {
                id: 10,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
        {
          id: 3,
          firstName: "sam",
          ageInfo: {
            ageGroups: [
              {
                id: 7,
              } as IAgeGroup,
              {
                id: 6,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
      ],
    } as ICompEventTeam;

    const compEventTeam2: ICompEventTeam = {
      teamName: "team 2",
      athletes: [
        {
          id: 2,
          firstName: "john",
          ageInfo: {
            ageGroups: [
              {
                id: 9,
              } as IAgeGroup,
              {
                id: 10,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
      ],
    } as ICompEventTeam;

    eventTeamHeader.compEventTeams = [compEventTeam, compEventTeam2];

    const result = eventTeamService.athletesInHowManyTeamsObj(eventTeamHeader);
    expect(result["2"].length).toBe(2);
  });

  // test("validateCompEventTeam EventTeamForm", () => {
  //     const eventTeamHeader: IEventTeamHeader = {
  //         eoptions: {
  //             eventTeam: {
  //                 min: 4,
  //                 max: 6,
  //                 showForm: true
  //             } as IEventTeam
  //         }
  //     } as IEventTeamHeader;
  //
  //     const compEventTeam: ICompEventTeam = {
  //         teamName: "Test Team",
  //         athletes: [] as IAthleteSummary[]
  //     } as ICompEventTeam;
  //
  //     const formInfoProp: IEventTeamFormField[]  = [
  //         {
  //             label: "Club Contact",
  //             value: "",
  //             req: true
  //         },
  //         {
  //             label: "Phone",
  //             value: ""
  //         },
  //         {
  //             label: "Email",
  //             value: ""
  //         }
  //     ];
  //
  //     compEventTeam.formInfo = formInfoProp;
  //
  //     let result: IUserMessage[] = eventTeamService.validateCompEventTeamForm(eventTeamHeader, compEventTeam);
  //     expect(result.length).toBe(1);
  //
  //     result = eventTeamService.validateCompEventTeam(eventTeamHeader, compEventTeam, {} as ICompetitionSummary, []);
  //     expect(result.length).toBe(1);
  // });

  test("athletesInHowManyEventTeamsObj", () => {
    const eventTeamHeader1: IEventTeamHeader = {
      eventName: "4 x 200m",
      ageGroup: {
        id: 12,
      },
      ceoptions: {
        eventTeam: {
          max: 0,
          minTargetAgeGroupCount: 2,
        } as IEventTeam,
      },
    } as IEventTeamHeader;

    const compEventTeam1: ICompEventTeam = {
      teamName: "Team 1",
      athletes: [
        {
          id: 1,
          firstName: "bob",
          ageInfo: {
            ageGroups: [
              {
                id: 11,
              } as IAgeGroup,
              {
                id: 12,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
        {
          id: 2,
          firstName: "john",
          ageInfo: {
            ageGroups: [
              {
                id: 9,
              } as IAgeGroup,
              {
                id: 10,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
        {
          id: 3,
          firstName: "sam",
          ageInfo: {
            ageGroups: [
              {
                id: 7,
              } as IAgeGroup,
              {
                id: 6,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
      ],
    } as ICompEventTeam;

    const compEventTeam2: ICompEventTeam = {
      teamName: "team 2",
      athletes: [
        {
          id: 2,
          firstName: "john",
          ageInfo: {
            ageGroups: [
              {
                id: 9,
              } as IAgeGroup,
              {
                id: 10,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
      ],
    } as ICompEventTeam;

    eventTeamHeader1.compEventTeams = [compEventTeam1, compEventTeam2];

    const eventTeamHeader2: IEventTeamHeader = {
      eventName: "4 x 200m",
      ageGroup: {
        id: 12,
      },
      ceoptions: {
        eventTeam: {
          max: 0,
          minTargetAgeGroupCount: 2,
        } as IEventTeam,
      },
    } as IEventTeamHeader;

    const compEventTeam3: ICompEventTeam = {
      teamName: "Team 1",
      athletes: [
        {
          id: 1,
          firstName: "bob",
          ageInfo: {
            ageGroups: [
              {
                id: 11,
              } as IAgeGroup,
              {
                id: 12,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
        {
          id: 2,
          firstName: "john",
          ageInfo: {
            ageGroups: [
              {
                id: 9,
              } as IAgeGroup,
              {
                id: 10,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
        {
          id: 3,
          firstName: "sam",
          ageInfo: {
            ageGroups: [
              {
                id: 7,
              } as IAgeGroup,
              {
                id: 6,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
      ],
    } as ICompEventTeam;

    const compEventTeam4: ICompEventTeam = {
      teamName: "team 2",
      athletes: [
        {
          id: 2,
          firstName: "john",
          ageInfo: {
            ageGroups: [
              {
                id: 9,
              } as IAgeGroup,
              {
                id: 10,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
      ],
    } as ICompEventTeam;

    eventTeamHeader2.compEventTeams = [compEventTeam3, compEventTeam4];

    const result = eventTeamService.athletesInHowManyEventTeamsObj([
      eventTeamHeader1,
      eventTeamHeader2,
    ]);
    expect(result["2"].length).toBe(4);
    expect(result["2"].length).toBe(4);
  });

  test("maxTeamsForAthlete", () => {
    const eventTeamHeader1: IEventTeamHeader = {
      eventName: "4 x 200m",
      ageGroup: {
        id: 12,
      },
      ceoptions: {
        eventTeam: {
          min: 0,
          max: 0,
          minTargetAgeGroupCount: 2,
          maxTeamsForAthlete: 1,
        } as IEventTeam,
      },
    } as IEventTeamHeader;

    const compEventTeam1: ICompEventTeam = {
      teamName: "Team 1a",
      athletes: [
        {
          id: 1,
          firstName: "bob",
          ageInfo: {
            ageGroups: [
              {
                id: 11,
              } as IAgeGroup,
              {
                id: 12,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
        {
          id: 2,
          firstName: "john",
          ageInfo: {
            ageGroups: [
              {
                id: 9,
              } as IAgeGroup,
              {
                id: 10,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
        {
          id: 3,
          firstName: "sam",
          ageInfo: {
            ageGroups: [
              {
                id: 7,
              } as IAgeGroup,
              {
                id: 6,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
      ],
    } as ICompEventTeam;

    const compEventTeam2: ICompEventTeam = {
      teamName: "team 2",
      athletes: [
        {
          id: 2,
          firstName: "john",
          ageInfo: {
            ageGroups: [
              {
                id: 9,
              } as IAgeGroup,
              {
                id: 10,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
      ],
    } as ICompEventTeam;

    eventTeamHeader1.compEventTeams = [compEventTeam1, compEventTeam2];

    const eventTeamHeader2: IEventTeamHeader = {
      eventName: "4 x 200m",
      ageGroup: {
        id: 12,
      },
      ceoptions: {
        eventTeam: {
          max: 0,
          minTargetAgeGroupCount: 2,
        } as IEventTeam,
      },
    } as IEventTeamHeader;

    const compEventTeam3: ICompEventTeam = {
      teamName: "Team 1",
      athletes: [
        {
          id: 1,
          firstName: "bob",
          ageInfo: {
            ageGroups: [
              {
                id: 11,
              } as IAgeGroup,
              {
                id: 12,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
        {
          id: 2,
          firstName: "john",
          ageInfo: {
            ageGroups: [
              {
                id: 9,
              } as IAgeGroup,
              {
                id: 10,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
        {
          id: 3,
          firstName: "sam",
          ageInfo: {
            ageGroups: [
              {
                id: 7,
              } as IAgeGroup,
              {
                id: 6,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
      ],
    } as ICompEventTeam;

    const compEventTeam4: ICompEventTeam = {
      teamName: "team 2b",
      athletes: [
        {
          id: 2,
          firstName: "john",
          ageInfo: {
            ageGroups: [
              {
                id: 9,
              } as IAgeGroup,
              {
                id: 10,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
      ],
    } as ICompEventTeam;

    eventTeamHeader2.compEventTeams = [compEventTeam3, compEventTeam4];

    const result = eventTeamService.athletesInHowManyEventTeamsObj([
      eventTeamHeader1,
      eventTeamHeader2,
    ]);
    // console.log(".............", result);
    expect(result["2"].length).toBe(4);
    expect(result["2"].length).toBe(4);

    const obj = {
      options: {
        min: 1,
      },
    };
    const opt = (obj.options && obj.options.min) || 0;
    expect(opt).toBe(1);
    const optMax = (obj.options && (obj.options as any).max) || 0;
    expect(optMax).toBe(0);

    // =========================

    const compRule: ICompShedRuleConfig = {
      agid: 11,
      options: {
        maxTeamEvents: 2,
      } as ICompShedRuleOptions,
    } as ICompShedRuleConfig;

    const competition: ICompetitionInfo = {
      compRules: [compRule],
    } as any as ICompetitionInfo;
    const compEventTeamNew: ICompEventTeam = {
      teamName: "Team New",
      athletes: [
        {
          id: 1,
          firstName: "bob",
          ageInfo: {
            ageGroups: [
              {
                id: 11,
              } as IAgeGroup,
              {
                id: 12,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
        {
          id: 2,
          firstName: "john",
          ageInfo: {
            ageGroups: [
              {
                id: 9,
              } as IAgeGroup,
              {
                id: 10,
              } as IAgeGroup,
            ],
          } as IAgeInfo,
        } as IAthleteSummary,
      ],
    } as ICompEventTeam;

    const eventTeamHeaderNew = {
      eventName: "4 x 200m",
      ageGroup: {
        id: 45464,
        name: "U11",
      },
      ceoptions: {
        eventTeam: {
          min: 2,
          max: 6,
          minTargetAgeGroupCount: 2,
          maxTeamsForAthlete: 1,
          showForm: false,
        } as IEventTeam,
      },
      compEventTeams: [] as ICompEventTeam[],
    } as IEventTeamHeader;

    eventTeamHeaderNew.ceoptions.eventTeam.min = 2;
    eventTeamHeaderNew.ceoptions.eventTeam.max = 6;
    const resultV = eventTeamService.validateCompEventTeam(
      eventTeamHeaderNew,
      compEventTeamNew,
      competition,
      [eventTeamHeaderNew, eventTeamHeader1, eventTeamHeader2]
    );
    //  console.log("......SSccS.......", resultV);
    expect(resultV.length).toBe(1);
    const res = resultV.filter((userMessage) => {
      return userMessage.message === "Need at least 2 U11";
    });
    expect(res.length).toBe(1);
  });

  test("getTeamName", () => {
    // let res;
    const data = simpleClone(mockEventTeamHeaders);
    expect(data.length).toBe(8);

    const eventTeamHeader: IEventTeamHeader = data[1];
    expect(eventTeamHeader.eventName).toBe("Mixed Relay");
    expect(eventTeamHeader.ageGroup.name).toBe("Under 14");
    expect(eventTeamHeader.compEventTeams.length).toBe(1);
    expect(eventTeamHeader.compEventTeams[0].teamName).toBe(
      "Mixed Relay  Under 14 A"
    );

    // const userEntity: IEntity = {
    //   name: "Avon",
    //   entityLevel: 1,
    //   id: 10600,
    //   entityName: "Club",
    //   clubType: "A", //  <<<--- "A" is not valid.
    // } as any as IEntity;

    const allTeamNames = eventTeamService.getAllTeamNames(eventTeamHeader);
    expect(allTeamNames[0]).toBe("Mixed Relay  Under 14 A");

    // const pickTeamNameWithSuffix2 = eventTeamService.pickTeamNameWithSuffix2(
    //   "Mixed Relay  Under 14 {{unique}}",
    //   ["Mixed Relay  Under 14 A", "Mixed Relay  Under 14 B"],
    //   ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"]
    // );
    //
    // expect(pickTeamNameWithSuffix2).toBe("Mixed Relay  Under 14 C");

    const nextTeamName = eventTeamService.getTeamName(
      eventTeamHeader,
      {
        name: "",
        entityLevel: 1,
        id: 0,
        entityName: "",
        clubType: "",
      },
      null
    );

    expect(nextTeamName).toBe("Mixed Relay  Under 14");

    // const eventTeamHeader: IEventTeamHeader = {
    //     eventName: "4 x 200m",
    //     gender: GENDER.FEMALE,
    //     ageGroup: {
    //         id: 12,
    //         name: "Under 12"
    //     },
    //     ceoptions: {
    //         eventTeam: {
    //             min: 0,
    //             max: 0,
    //             minTargetAgeGroupCount: 2,
    //             maxTeamsForAthlete: 1,
    //             teamNameFormat: "{{eventname}} {{gender}}"
    //         } as IEventTeam
    //     }
    // } as IEventTeamHeader;

    // const userInfo: IUserInfo = {
    //     areas: [],
    //     clubs: [],
    //     orgs: [],
    //     security: {
    //         permLevels: {
    //             builder: [],
    //         },
    //         permissions: []
    //     },
    //     user: {
    //         id: 1234,
    //         display_name: "",
    //         impersonating: false,
    //         role: "",
    //         user_email: "string"
    //     }
    // };

    // const userEntity = configService.factoryEntity();
    //
    // let result = eventTeamService.getTeamName(eventTeamHeader, userEntity);
    // expect(result).toBe("4 x 200m Female");
    //
    // userEntity.entityName = "Club";
    // userEntity.name = "Seaton Runners";
    // eventTeamHeader.ceoptions.eventTeam.teamNameFormat = "{{entity;<Please enter entity here>}}";
    // result = eventTeamService.getTeamName(eventTeamHeader, userEntity);
    // expect(result).toBe("Seaton Runners");
    //
    // //  Forgotten to set up the format like: {{entity;<Please enter entity here>}}
    // eventTeamHeader.ceoptions.eventTeam.teamNameFormat = "{{<Please enter area here>}}";
    // result = eventTeamService.getTeamName(eventTeamHeader, userEntity);
    // expect(result).toBe("{{<Please enter area here>}}");
    //
    // eventTeamHeader.ceoptions.eventTeam.teamNameFormat = "{{entity}}";
    // result = eventTeamService.getTeamName(eventTeamHeader, userEntity);
    // expect(result).toBe("Seaton Runners");
    //
    // eventTeamHeader.ceoptions.eventTeam.teamNameFormat = "{{entity}} {{eventname}} {{gender}} {{agegroup}}";
    // result = eventTeamService.getTeamName(eventTeamHeader, userEntity);
    // expect(result).toBe("Seaton Runners 4 x 200m Female Under 12");        // const userEntity = configService.factoryEntity();
    //
    // let result = eventTeamService.getTeamName(eventTeamHeader, userEntity);
    // expect(result).toBe("4 x 200m Female");
    //
    // userEntity.entityName = "Club";
    // userEntity.name = "Seaton Runners";
    // eventTeamHeader.ceoptions.eventTeam.teamNameFormat = "{{entity;<Please enter entity here>}}";
    // result = eventTeamService.getTeamName(eventTeamHeader, userEntity);
    // expect(result).toBe("Seaton Runners");
    //
    // //  Forgotten to set up the format like: {{entity;<Please enter entity here>}}
    // eventTeamHeader.ceoptions.eventTeam.teamNameFormat = "{{<Please enter area here>}}";
    // result = eventTeamService.getTeamName(eventTeamHeader, userEntity);
    // expect(result).toBe("{{<Please enter area here>}}");
    //
    // eventTeamHeader.ceoptions.eventTeam.teamNameFormat = "{{entity}}";
    // result = eventTeamService.getTeamName(eventTeamHeader, userEntity);
    // expect(result).toBe("Seaton Runners");
    //
    // eventTeamHeader.ceoptions.eventTeam.teamNameFormat = "{{entity}} {{eventname}} {{gender}} {{agegroup}}";
    // result = eventTeamService.getTeamName(eventTeamHeader, userEntity);
    // expect(result).toBe("Seaton Runners 4 x 200m Female Under 12");
  });

  test("userAccessMessage", () => {
    const userInfo: IUserInfo = {
      areas: [
        {
          areaid: 51,
          areaname: "Aberdeenshire",
          areashortname: "Abe",
          areaparentid: 4,
          entityLevel: 2,
          entityName: "County",
        },
        {
          areaid: 76,
          areaname: "Derbyshire",
          areashortname: "Der",
          areaparentid: 4,
          entityLevel: 2,
          entityName: "County",
        },
        {
          areaid: 77,
          areaname: "Devon",
          areashortname: "Dev",
          areaparentid: 4,
          entityLevel: 2,
          entityName: "County",
        },
      ],
      clubs: [
        {
          id: 222,
          Clubname: "Brandon Fern Hoppers",
          areaid: 4,
          clubtype: "C",
          active: 1,
          entityLevel: 1,
          entityName: "Club",
        },
        {
          id: 2233,
          Clubname: "Dublin Ba'y Running Club",
          areaid: 30,
          clubtype: "C",
          active: 1,
          entityLevel: 1,
          entityName: "Club",
        },
      ] as IClubUserInfo[],
    } as IUserInfo;

    const result: string = eventTeamService.userAccessMessage(userInfo);
    // console.log("", result);
    expect(result.length).toBe(104);
  });

  test("getTeamEventHasSecurity", () => {
    const eventTeamHeader: IEventTeamHeader = {
      eventName: "4 x 200m",
      gender: GENDER.FEMALE,
      ageGroup: {
        id: 12,
        name: "Under 12",
      },
      ceoptions: {
        eventTeam: {
          min: 0,
          max: 0,
          minTargetAgeGroupCount: 2,
          maxTeamsForAthlete: 1,
          teamNameFormat: "{{eventname}} {{gender}}",
        } as IEventTeam,
      },
    } as IEventTeamHeader;
    let result = eventTeamService.getTeamEventHasSecurity(eventTeamHeader);
    expect(result).toBe(false);

    eventTeamHeader.ceoptions.security = {} as ISecurity;
    result = eventTeamService.getTeamEventHasSecurity(eventTeamHeader);
    expect(result).toBe(false);

    eventTeamHeader.ceoptions.security = {
      clubs: [],
    } as unknown as ISecurity;
    result = eventTeamService.getTeamEventHasSecurity(eventTeamHeader);
    expect(result).toBe(false);

    eventTeamHeader.ceoptions.security = {
      clubs: [
        {
          id: 55,
        },
      ],
    } as unknown as ISecurity;
    result = eventTeamService.getTeamEventHasSecurity(eventTeamHeader);
    expect(result).toBe(true);
  });

  test("getTeamEventHasSecurity", () => {
    const eventTeamHeader: IEventTeamHeader = {
      eventName: "4 x 200m",
      gender: GENDER.FEMALE,
      ageGroup: {
        id: 12,
        name: "Under 12",
      },
      ceoptions: {
        eventTeam: {
          min: 0,
          max: 0,
          minTargetAgeGroupCount: 2,
          maxTeamsForAthlete: 1,
          teamNameFormat: "{{eventname}} {{gender}}",
        } as IEventTeam,
      },
    } as IEventTeamHeader;

    const eventTeamHeaders: IEventTeamHeader[] = [];

    eventTeamHeaders.push(eventTeamHeader);
    let result = eventTeamService.getTeamEventsHaveSecurity(eventTeamHeaders);
    expect(result).toBe(false);

    eventTeamHeaders.push({
      ...eventTeamHeader,
      ceoptions: {
        security: {
          clubs: [{ id: 88 }],
        },
      } as ICeoptions,
    });
    result = eventTeamService.getTeamEventsHaveSecurity(eventTeamHeaders);
    expect(result).toBe(true);
  });

  test("doesUserHaveAccessToThisEvent", () => {
    const userInfo: IUserInfo = new ConfigService().factoryUserInfo();
    const eventTeamHeader: IEventTeamHeader =
      eventTeamService.factoryEventTeamHeader();

    userInfo.clubs.push({
      id: 12345,
    } as IClubUserInfo);

    //  event has no security
    let result = eventTeamService.doesUserHaveAccessToThisEvent(
      userInfo,
      eventTeamHeader
    );
    expect(result).toBe(true);

    //  add club security to event
    eventTeamHeader.ceoptions.security.clubs = [
      {
        id: 0,
      },
    ];
    result = eventTeamService.doesUserHaveAccessToThisEvent(
      userInfo,
      eventTeamHeader
    );
    expect(result).toBe(true);

    //  take security off user info
    userInfo.clubs = [];
    result = eventTeamService.doesUserHaveAccessToThisEvent(
      userInfo,
      eventTeamHeader
    );
    expect(result).toBe(false);

    // add county security to userInfo
    userInfo.areas.push({
      areaid: 12345,
    } as IAreaUserInfo);
    result = eventTeamService.doesUserHaveAccessToThisEvent(
      userInfo,
      eventTeamHeader
    );
    expect(result).toBe(false);

    //  Add county to event
    eventTeamHeader.ceoptions.security.clubs = [];
    eventTeamHeader.ceoptions.security.counties = [
      {
        id: 0,
      },
    ];
    result = eventTeamService.doesUserHaveAccessToThisEvent(
      userInfo,
      eventTeamHeader
    );
    expect(result).toBe(true);

    //  Take club\ counties off, add region, but user does not have region security
    eventTeamHeader.ceoptions.security.clubs = [];
    eventTeamHeader.ceoptions.security.counties = [];
    //  add region
    eventTeamHeader.ceoptions.security.regions = [
      {
        id: 0,
      },
    ];
    result = eventTeamService.doesUserHaveAccessToThisEvent(
      userInfo,
      eventTeamHeader
    );
    expect(result).toBe(false);

    userInfo.orgs.push({
      id: 8888,
    } as IOrg);
    result = eventTeamService.doesUserHaveAccessToThisEvent(
      userInfo,
      eventTeamHeader
    );
    expect(result).toBe(true);
  });

  test("getEventSecurityLabel", () => {
    const eventTeamHeader: IEventTeamHeader =
      eventTeamService.factoryEventTeamHeader();
    eventTeamHeader.ceoptions.security.regions = [
      {
        id: 0,
      },
    ];
    let result = eventTeamService.getEventSecurityLabel(eventTeamHeader);
    expect(result).toBe("region");

    eventTeamHeader.ceoptions.security.clubs = [
      {
        id: 0,
      },
    ];
    result = eventTeamService.getEventSecurityLabel(eventTeamHeader);
    expect(result).toBe("club, region");

    eventTeamHeader.ceoptions.security.counties = [
      {
        id: 0,
      },
    ];
    result = eventTeamService.getEventSecurityLabel(eventTeamHeader);
    expect(result).toBe("club, county, region");
  });

  test("getEntitiesForEvent", () => {
    let userInfo: IUserInfo = new ConfigService().factoryUserInfo();
    let isSchool: boolean = false;
    let eventTeamHeader: IEventTeamHeader =
      eventTeamService.factoryEventTeamHeader();
    let result;

    //  User has a club security, event is for clubs
    userInfo.clubs.push({
      id: 12345,
      entityName: "Club",
    } as IClubUserInfo);

    eventTeamHeader.ceoptions.security.clubs = [
      {
        id: 0,
      },
    ];

    result = eventTeamService.getEntitiesForEvent(
      userInfo,
      isSchool,
      eventTeamHeader
    );
    expect(result.length).toBe(1);
    expect(result[0].id).toBe(12345);

    //  User has a club security, event is for clubs, counties
    userInfo.areas.push({
      areaname: "Cork",
    } as IAreaUserInfo);

    eventTeamHeader.ceoptions.security.clubs = [
      {
        id: 0,
      },
    ];

    result = eventTeamService.getEntitiesForEvent(
      userInfo,
      isSchool,
      eventTeamHeader
    );
    expect(result.length).toBe(1);
    expect(result[0].id).toBe(12345);

    //  Add another club to userInfo
    userInfo.clubs.push({
      id: 22222,
      entityName: "Club",
    } as IClubUserInfo);

    result = eventTeamService.getEntitiesForEvent(
      userInfo,
      isSchool,
      eventTeamHeader
    );
    expect(result.length).toBe(2);
    expect(result[0].id).toBe(12345);

    //  add county to event
    eventTeamHeader = eventTeamService.factoryEventTeamHeader();
    userInfo = new ConfigService().factoryUserInfo();
    userInfo.areas.push({
      areaname: "Cork",
      entityName: "County",
    } as IAreaUserInfo);
    eventTeamHeader.ceoptions.security.counties = [
      {
        id: 0,
      },
    ];

    isSchool = false;
    result = eventTeamService.getEntitiesForEvent(
      userInfo,
      isSchool,
      eventTeamHeader
    );
    expect(result.length).toBe(1);

    //  add county to event
    eventTeamHeader = eventTeamService.factoryEventTeamHeader();
    userInfo = new ConfigService().factoryUserInfo();
    userInfo.areas.push({
      areaname: "Cork",
      entityName: "County",
    } as IAreaUserInfo);

    userInfo.clubs.push({
      id: 12345,
      entityName: "Club",
    } as IClubUserInfo);

    userInfo.clubs.push({
      id: 22222,
      entityName: "Club",
    } as IClubUserInfo);

    eventTeamHeader.ceoptions.security.clubs = [
      {
        id: 0,
      },
    ];
    eventTeamHeader.ceoptions.security.counties = [
      {
        id: 0,
      },
    ];

    result = eventTeamService.getEntitiesForEvent(
      userInfo,
      isSchool,
      eventTeamHeader
    );
    expect(result.length).toBe(3);

    //  add club, comp is SCHOOL.
    eventTeamHeader = eventTeamService.factoryEventTeamHeader();
    userInfo = new ConfigService().factoryUserInfo();
    userInfo.clubs.push({
      id: 12345,
      entityName: "Club",
    } as IClubUserInfo);
    eventTeamHeader.ceoptions.security.clubs = [
      {
        id: 0,
      },
    ];

    isSchool = true;
    result = eventTeamService.getEntitiesForEvent(
      userInfo,
      isSchool,
      eventTeamHeader
    );
    expect(result.length).toBe(0);

    //  add school, comp is SCHOOL.
    eventTeamHeader = eventTeamService.factoryEventTeamHeader();
    userInfo = new ConfigService().factoryUserInfo();
    userInfo.clubs.push({
      id: 12345,
      entityName: "School",
    } as IClubUserInfo);
    eventTeamHeader.ceoptions.security.clubs = [
      {
        id: 0,
      },
    ];

    isSchool = true;
    result = eventTeamService.getEntitiesForEvent(
      userInfo,
      isSchool,
      eventTeamHeader
    );
    expect(result.length).toBe(1);
  });

  test("filterEventTeamHeaders", () => {
    const eventTeamsFilterService: EventTeamsFilterService =
      new EventTeamsFilterService();
    const eventTeamHeaders: IEventTeamHeader[] = [
      {
        eventName: "abc",
        gender: "",
        ageGroup: {
          id: 0,
        },
        compEventTeams: [],
        eventGroup: {
          name: "abc",
        },
      } as any as IEventTeamHeader,
    ];

    const eventTeamHeaderFilter: IEventTeamHeaderFilter =
      eventTeamsFilterService.factoryEventTeamHeaderFilter();
    let result = eventTeamService.filterEventTeamHeaders(
      eventTeamHeaderFilter,
      eventTeamHeaders
    );
    expect(result.length).toBe(1);

    eventTeamHeaderFilter.eventName = "wfswf";
    result = eventTeamService.filterEventTeamHeaders(
      eventTeamHeaderFilter,
      eventTeamHeaders
    );
    expect(result.length).toBe(0);

    eventTeamHeaderFilter.eventName = "ab";
    result = eventTeamService.filterEventTeamHeaders(
      eventTeamHeaderFilter,
      eventTeamHeaders
    );
    expect(result.length).toBe(1);
  });

  // test("TS decorator", () => {
  //
  //
  //     // const doA = () => {
  //     //     console.log("doA......");
  //     // };
  //
  //     // @doA()
  //     // const doX = () => {
  //     //     console.log("doX......");
  //     // };
  //
  //     function logMethod(
  //         target: any,
  //         propertyName: string,
  //         propertyDesciptor: PropertyDescriptor): PropertyDescriptor {
  //         // target === Employee.prototype
  //         // propertyName === "greet"
  //         // propertyDesciptor === Object.getOwnPropertyDescriptor(Employee.prototype, "greet")
  //         const method = propertyDesciptor.value;
  //
  //         propertyDesciptor.value = function (...args: any[]) {
  //
  //             // convert list of greet arguments to string
  //             const params = args.map(a => JSON.stringify(a)).join();
  //
  //             // invoke greet() and get its return value
  //             const result = method.apply(this, args);
  //
  //             // convert result to string
  //             const r = JSON.stringify(result);
  //
  //             // display in console the function call details
  //             console.log(`Call: ${propertyName}(${params}) => ${r}`);
  //
  //             // return the result of invoking the method
  //             return result;
  //         };
  //         return propertyDesciptor;
  //     };
  //
  //
  //     class Test {
  //         @logMethod
  //         public doSomething() {
  //             console.log("doSomething......");
  //         }
  //     }
  //
  //     const test = new Test();
  //
  //     test.doSomething();
  //
  //     expect(true).toBe(false);
  // });
});
