<template>
  <div class="user-team-access--wrapper">
    <div v-if="false" class="section-border-top-XXX">
      <a
        href="#"
        v-if="!showAdminPicker"
        v-on:click.prevent="showAdminPicker = true"
        >Show Admin</a
      >

      <EntityPicker
        v-if="showAdminPicker"
        :competition="competition"
        :only-self-service="false"
        v-on:onSelected="onEntitySelectedAdmin"
      >
        <div slot="title">
          <div class="row">
            <div class="col s12 m12 l12">
              <span class="event-card-title">Admin:</span>
              <a
                href="#"
                v-if="showAdminPicker"
                v-on:click.prevent="showAdminPicker = false"
                >Hide Admin</a
              >
            </div>
          </div>
        </div>
      </EntityPicker>
    </div>

    <div v-if="getShowSelfService" class="section-border-top-XXX">
      <EntityPicker
        title-text="Self Service"
        :competition="competition"
        v-on:onSelected="onSelfServiceEntitySelected"
      >
        <div slot="title">
          <div class="row">
            <div class="col s12 m12 l12">
              <span class="event-card-title"
                >Self Service
                <FieldHelp
                  title="Self Service"
                  help-key="self-service"
                  :get-from-server="true"
                />:
              </span>
              <span v-text="getSelfServiceMessage"></span>
            </div>
          </div>
        </div>
        <div class="e4s-force-inline-block" slot="link-label">
          Click Here to Add:
        </div>
      </EntityPicker>
    </div>

    <!--      <div class="row" v-if="getTeamEventsHaveSecurity">-->
    <div class="row">
      <div class="col s12 m12 l12">
        <!--                <div v-if="userEntities.length < 2" class="section-border-top-XXX">-->
        <div>
          <span class="event-card-title"
            >Current Access:
            <FieldHelp
              title="Self Service"
              help-key="self-service"
              :get-from-server="true"
          /></span>
          <span v-text="getEntityDisplayName"></span>
        </div>
        <span v-if="!getShowSelfService && !getDoesUserHaveAnEntity">
          This competition has restricted events. Your current profile
          permission does not allow you to enter restricted
          <span v-text="inTeamSection ? ' Teams' : ' Individual Events'"></span>
          into this competition. Contact the organiser.
        </span>
        <div class="e4s-flex-row">
          <ButtonGenericV2
            style="margin-bottom: var(--e4s-gap--small)"
            class="e4s-button--auto e4s-flex-row--end"
            text="Contact Organiser"
            @click="showContactOrganiser = true"
          />
        </div>
      </div>
    </div>

    <e4s-modal
      v-if="showSelfServiceConfirmation"
      :header-message="'Self Service'"
      :body-message="getConfirmSelfServiceMessage"
      :button-primary-text="'Continue'"
      :isLoading="confirmSelfServiceIsLoading"
      v-on:closeSecondary="showSelfServiceConfirmation = false"
      v-on:closePrimary="selfServiceEntitySubmit"
    >
    </e4s-modal>

    <AskOrganiserModal
      v-if="showContactOrganiser"
      :show-contact-organiser="true"
      :selected-competition="competition"
      v-on:onClose="showContactOrganiser = false"
    >
    </AskOrganiserModal>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Component from "vue-class-component";
import Vue from "vue";
import { Prop, Watch } from "vue-property-decorator";
import type { IEntity, IUserInfo } from "../../../config/config-app-models";
import { ConfigService } from "../../../config/config-service";
import { CONFIG_STORE_CONST } from "../../../config/config-store";
import { COMP_EVENT_TEAMS_STORE_CONST } from "../comp-event-store";
import { mapGetters } from "vuex";
import type { ICompetitionInfo } from "../../../competition/competition-models";
import { CompetitionService } from "../../../competition/competiton-service";
import { BuilderService } from "../../../builder/builder-service";
import { messageDispatchHelper } from "../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../user-message/user-message-models";
import type { ISelfService } from "../../../builder/builder-models";
import E4sModal from "../../../common/ui/e4s-modal.vue";
import { EventTeamService } from "../event-team-service";
import type { IEventTeamHeader } from "../event-teams-models";
import type { IEntityDropDown } from "./user-team-access-models";
import type { IEntityPickerOutput } from "../../../builder/form/entity/enity-picker/enity-picker-models";
import AskOrganiserModal from "../../../competition/askorganiser/ask-organiser-modal.vue";
import FieldHelp from "../../../common/ui/field/field-help/field-help.vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";

const configService: ConfigService = new ConfigService();
const competitionService: CompetitionService = new CompetitionService();
const builderService: BuilderService = new BuilderService();
const eventTeamService: EventTeamService = new EventTeamService();

@Component({
  name: "user-team-access",
  components: {
    ButtonGenericV2,
    FieldHelp,
    AskOrganiserModal,
    EntityPicker: () => {
      return import("../../../builder/form/entity/enity-picker/entity-picker.vue");
    },
    E4sModal,
  },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class UserTeamAccess extends Vue {
  @Prop({
    default: () => {
      return configService.factoryUserInfo();
    },
  })
  public readonly userInfo: IUserInfo;

  @Prop({
    default: () => {
      return configService.factoryEntity();
    },
  })
  public readonly userEntity: IEntity;

  @Prop({
    default: () => {
      return competitionService.factoryCompetitionInfo();
    },
  })
  public readonly competition: ICompetitionInfo;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly eventTeamHeaders: IEventTeamHeader[];

  @Prop({
    default: () => {
      return true;
    },
  })
  public readonly inTeamSection: boolean;

  public entityDropDown: IEntity = configService.factoryEntity();
  public userEntities: IEntity[] | IEntityDropDown[] = [];

  public confirmSelfServiceIsLoading: boolean = false;
  public showSelfServiceConfirmation: boolean = false;
  public confirmSelfServiceEntity: IEntity = configService.factoryEntity();
  public confirmSelfService: ISelfService = builderService.factorySelfService();

  public showAdminPicker: boolean = false;
  public showContactOrganiser = false;

  public created() {
    // this.setUserEntities();
  }

  public mounted() {
    this.setUserEntities();
    this.onEntitySelected(this.entityDropDown);
  }

  @Watch("userInfo")
  public onUserInfoChanged() {
    this.setUserEntities();
    this.onEntitySelected(this.entityDropDown);
  }

  @Watch("competition")
  public onCompetitionChanged() {
    this.setUserEntities();
    this.onEntitySelected(this.entityDropDown);
  }

  public setUserEntities() {
    const userEntities = configService
      .getEntitiesFromUserInfo(this.userInfo)
      .map((userEntity) => {
        const entityDropDown: IEntityDropDown = {
          ...userEntity,
          isDisabled: false,
        };
        if (this.competition && this.competition.options) {
          const isSchoolEntity =
            userEntity.entityName === "Club" && userEntity.clubType === "S";
          if (this.competition.options.school) {
            entityDropDown.isDisabled = !isSchoolEntity;
          }
          if (!this.competition.options.school) {
            entityDropDown.isDisabled = isSchoolEntity;
          }
        }
        return entityDropDown;
      });

    // const hasMoreThanOneEntity: boolean = userEntities.length > 1;
    if (userEntities.length > 1) {
      userEntities.unshift({
        ...configService.factoryEntity(),
        name: "Please Select",
        entityName: "",
        isDisabled: false,
      });
    }

    this.userEntities = userEntities;
    if (userEntities.length > 0) {
      // if (hasMoreThanOneEntity) {
      //     this.entityDropDown = userEntities[1];
      // } else {
      this.entityDropDown = userEntities[0];
      // }

      if (this.userEntity.id > 0) {
        this.userEntities.forEach((userEnt) => {
          if (userEnt.id === this.userEntity.id) {
            this.entityDropDown = userEnt;
          }
        });
      }
    }
  }

  public get getShowDropDown(): boolean {
    return this.userEntities.length > 1;
  }

  public getEntityDropDownLabel(entity: IEntity) {
    return (
      (R.isEmpty(entity.entityName)
        ? ""
        : (entity.entityName === "Club" && entity.clubType === "S"
            ? "School"
            : entity.entityName) + " - ") + entity.name
    );
  }

  public onSelectedDropDown() {
    this.onEntitySelected(this.entityDropDown);
  }

  public get getShowSelfService(): boolean {
    return builderService.getSelfServices(this.competition.options).length > 0;
  }

  public get getEntityDisplayName() {
    if (this.userEntities.length === 1) {
      return this.getEntityDropDownLabel(this.userEntities[0]);
    }
    return "Public";
  }

  public get getDoesUserHaveAnEntity(): boolean {
    return this.userEntities.length > 0;
  }

  public get getSelfServiceMessage(): string {
    const selfServiceEntities = competitionService.getServServiceEntities(
      this.competition
    );
    return (
      "If you are a representative of a " +
      selfServiceEntities
        .map((ent) => {
          const entityName = ent.entityName;
          return entityName === "Club" && this.competition.options.school
            ? "School"
            : ent.entityName;
        })
        .join(", ") +
      ", this competition allows you to add to your profile."
    );
  }

  public onEntitySelected(entity: IEntity) {
    console.log("onEntitySelected", {
      competition: { ...this.competition },
    });

    if (this.competition.id === 0 || this.userInfo.user.id === 0) {
      console.log("onEntitySelected...exiting");
      return;
    }

    this.$store.commit(
      CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_MUTATIONS_SET_USER_ENTITY,
      entity
    );

    this.$store.dispatch(
      COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME +
        "/" +
        COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_ACTIONS_GET_TEAM_HEADERS
    );
  }

  public onEntitySelectedAdmin(entityPickerOutput: IEntityPickerOutput) {
    this.onEntitySelected(entityPickerOutput.entity);
  }

  public get getEmailMessage(): string {
    return this.confirmSelfService.approvalUsers.length > 0
      ? " An approval email will be sent to: " +
          this.confirmSelfService.approvalUsers
            .map((user) => user.email)
            .join(", ") +
          " who then update your profile."
      : "";
  }

  public get getConfirmSelfServiceMessage(): string {
    return (
      "Do you want to add " +
      this.confirmSelfServiceEntity.entityName +
      ": " +
      this.confirmSelfServiceEntity.name +
      " to your profile?" +
      this.getEmailMessage
    );
  }

  public onSelfServiceEntitySelected(entityPickerOutput: IEntityPickerOutput) {
    this.showSelfServiceConfirmation = true;
    this.confirmSelfServiceEntity = R.clone(entityPickerOutput.entity);
    this.confirmSelfService = entityPickerOutput.selfService as ISelfService;
  }

  public get getTeamEventsHaveSecurity(): boolean {
    return eventTeamService.getTeamEventsHaveSecurity(this.eventTeamHeaders);
  }

  public selfServiceEntitySubmit() {
    const payload = {
      entity: this.confirmSelfServiceEntity,
      selfService: this.confirmSelfService,
    };
    messageDispatchHelper(
      "Submitting self service request...",
      USER_MESSAGE_LEVEL.INFO.toString()
    );
    this.confirmSelfServiceIsLoading = true;
    this.$store
      .dispatch(
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
          "/" +
          CONFIG_STORE_CONST.CONFIG_ACTIONS_SELF_SERVICE,
        payload
      )
      .then(() => {
        this.showSelfServiceConfirmation = false;
        this.confirmSelfServiceIsLoading = false;

        this.$store.dispatch(
          COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME +
            "/" +
            COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_ACTIONS_GET_TEAM_HEADERS
        );

        this.$emit("submitted");
      });
  }

  public get dropDownLabel() {
    return this.entityDropDown.id === 0
      ? "Please Select your Entity/Authority here."
      : "Entity/Authority";
  }
}
</script>

<style>
.user-team-access--wrapper {
  border-top: 2px solid #ff8888;
  border-bottom: 2px solid #ff8888;
  background-color: #f5dde2;
}
</style>
