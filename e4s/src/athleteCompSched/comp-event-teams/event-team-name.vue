<template>
  <div class="e4s-flex-column">
    <div v-if="!getCanEdit" class="e4s-flex-row e4s-gap--standard">
      <i class="material-icons small e4s-force-img-vert-align-middle"
        >people_outline</i
      >
      <span v-text="name"></span>

      <!--      <div class="col s12 m12 l12">-->
      <!--        <div class="comp-event-team-name-wrapper">-->
      <!--          <i class="material-icons small e4s-force-img-vert-align-middle"-->
      <!--          >people_outline</i-->
      <!--          >-->
      <!--          <span v-text="name"></span>-->
      <!--                    <a v-if="canEdit" href="#" v-on:click.prevent="editMode = true">-->
      <!--                        <i class="material-icons tiny">edit</i>-->
      <!--                    </a>-->
      <!--        </div>-->
      <!--      </div>-->
    </div>

    <div v-if="getCanEdit" class="comp-event-team-name-wrapper">
      <div class="e4s-flex-row e4s-gap--standard">
        <i class="material-icons prefix small e4s-force-img-vert-align-middle"
          >people_outline</i
        >
        <label class="active" :for="prefix + 'team-name'">Team Name</label>
        <input
          :id="prefix + 'team-name'"
          type="text"
          placeholder="Enter team name"
          v-model="name"
          v-on:keyup="onFieldChanged"
          class="e4s-input"
          style="margin-top: 5px"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";

@Component({
  name: "event-team-name",
})
export default class EventTeamName extends Vue {
  @Prop({ default: "" }) public nameProp: string;
  @Prop({ default: false }) public editModeProp: boolean;
  @Prop({ default: false }) public canEdit: boolean;

  public name: string = "";
  public editMode: boolean = false;
  public prefix: string = Math.random().toString(36).substring(2);

  public created() {
    this.name = this.nameProp;
    this.editMode = this.editModeProp;
  }

  @Watch("nameProp")
  public onNamePropChanged(newValue: string, oldValue: string) {
    if (newValue !== oldValue) {
      this.name = newValue;
    }
  }

  @Watch("editModeProp")
  public onEditModePropChanged(newValue: boolean) {
    this.editMode = newValue;
    this.$emit("changed", this.name);
  }

  public get getCanEdit() {
    return this.editMode && this.canEdit;
  }

  public onFieldChanged() {
    this.$emit("changed", this.name);
  }
}
</script>
