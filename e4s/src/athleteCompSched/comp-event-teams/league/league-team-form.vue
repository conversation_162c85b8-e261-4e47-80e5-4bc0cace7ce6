<template>
  <div>
    <div v-if="!showEligibility">
      <event-team-name
        class="row"
        :name-prop="compEventTeam.teamName"
        :edit-mode-prop="editMode"
        :can-edit="getCanEditTeamName"
        v-on:changed="onTeamNameChanged"
      >
      </event-team-name>

      <div class="e4s-section-padding-separator"></div>

      <div class="row">
        <div class="col s12 m12 l12">
          <div class="right">
            Can't find athlete? Click
            <a
              class="anchor-buttons"
              href="#"
              v-on:click.prevent="showEligibility = true"
            >
              here
            </a>
            to check why.
          </div>
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>

      <div v-for="(formRow, index) in getFormRows">
        <div class="row" v-if="editMode">
          <div class="col s12 m12 l12">
            <CompEventNamePicker
              :position="index"
              :competition="competition"
              :event-team-header="eventTeamHeader"
              :team-position-name="getTeamPositionNameEditMode(formRow)"
              :athlete-default="getDefaultAthlete(formRow)"
              :show-all-athletes="false"
              :user-entity="userEntity"
              :event-id="formRow.eventDef.id"
              v-on:onUserFocus="athleteRowSelected"
              v-on:athleteSelected="athleteSelected"
              v-on:athleteRemoved="athleteRemoved"
            >
            </CompEventNamePicker>
          </div>
        </div>

        <div class="row" v-if="!editMode">
          <div class="col s12 m12 l12">
            <!--                        <label class="active">-->
            <!--                            <span v-text="getTeamPositionNameEditMode(formRow)"/>-->
            <!--                        </label>-->
            <!--                        <div v-text="getAthleteName(formRow)"/>-->
            <div class="league-team--read-line">
              <span v-text="getTeamPositionNameEditMode(formRow)" />
              <div class="right" v-text="getAthleteName(formRow)" />
            </div>
          </div>
        </div>
      </div>

      <!--            <div v-for="(formRow, index) in getFormRows">-->
      <!--                <div class="row">-->
      <!--                    <div class="col s12 m12 l12">-->
      <!--                        <div class="league-team&#45;&#45;read-line">-->
      <!--                            <span v-text="getTeamPositionNameEditMode(formRow)"/>-->
      <!--                            <div class="right" v-text="getAthleteName(formRow)"/>-->
      <!--                        </div>-->
      <!--                    </div>-->
      <!--                </div>-->
      <!--            </div>-->

      <user-validation-messages
        :validation-messages="userMessages"
      ></user-validation-messages>

      <div v-if="showUserOwner" class="row">
        <div class="col s12 m12 l12">
          Team entered by <span v-text="compEventTeam.userName"></span>
        </div>
      </div>

      <event-team-buttons
        class="row e4s-section-padding-separator"
        :comp-event-team="compEventTeam"
        :competition-summary="selectedCompetition"
        :isLoading="isLoading"
        :edit-mode="editMode"
        :is-user-owner="isUserOwner"
        :isSubmitDisabled="!editMode || isLoading"
        v-on:cancel="cancelCompEventTeam"
        v-on:edit="editCompEventTeam"
        v-on:delete="deleteCompEventTeam"
        v-on:submit="submitCompEventTeam"
      >
        <template slot="left-buttons-other">
          <a
            class="comp-event-team-anchor-buttons"
            href="#"
            v-on:click.prevent="addUserCart"
          >
            Add To Cart
          </a>
        </template>
      </event-team-buttons>
    </div>

    <div v-if="showEligibility" class="row">
      <div class="col s12 m12 l12">
        <div class="e4s-section-padding-separator"></div>
        <EventAthleteEligible
          class="eligibility"
          :ceid="eventTeamHeader.id"
          v-on:cancel="showEligibility = false"
        >
        </EventAthleteEligible>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import EventTeamBase from "../event-team-base";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import type { ICompEventTeam, IEventTeamHeader } from "../event-teams-models";
import EventAthleteEligible from "../event-athlete-eligible.vue";
import E4sModal from "../../../common/ui/e4s-modal.vue";
import AthleteForm from "../../../athlete/maint/athlete-form.vue";
import { mapGetters, mapState } from "vuex";
import {
  ENTRY_STORE_CONST,
  IEntryStoreState,
} from "../../../entry/entry-store";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import * as R from "ramda";
import { EventTeamService } from "../event-team-service";
import { IFormRow } from "../../athletecompsched-models";
import CompEventNamePicker from "../comp-event-name-picker.vue";
import UserValidationMessages from "../../../user-message/user-validation-messages.vue";
import EventTeamButtons from "../event-team-buttons.vue";
import { IAutoCompleteValue } from "../../../common/ui/autocomplete/auto-complete-mat-models";
import { IAthleteSummary } from "../../../athlete/athlete-models";
import { CommonService } from "../../../common/common-service";
import { IUserMessage } from "../../../user-message/user-message-models";
import { format, parse } from "date-fns";
import type { IEntity } from "../../../config/config-app-models";
import { AthleteService } from "../../../athlete/athlete-service";
import { LeagueService } from "./league-service";
import { ConfigService } from "../../../config/config-service";

const eventTeamService: EventTeamService = new EventTeamService();
const commonService: CommonService = new CommonService();
const athleteService: AthleteService = new AthleteService();
const leagueService: LeagueService = new LeagueService();
const configService: ConfigService = new ConfigService();

@Component({
  name: "league-team-form",
  components: {
    EventAthleteEligible,
    E4sModal,
    AthleteForm,
    CompEventNamePicker,
    UserValidationMessages,
    EventTeamButtons,
  },
  computed: {
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      selectedCompetition: (state: any) =>
        state.entryForm.selectedCompetition,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
      userInfo: (state: any) => state.configApp.userInfo,
      userEntityStore: (state: any) => state.userEntity,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class LeagueTeamForm extends EventTeamBase {
  public userEntityStore: IEntity;

  @Prop({
    default: () => {
      return eventTeamService.factoryEventTeamHeader();
    },
  })
  public readonly eventTeamHeader: IEventTeamHeader;
  @Prop({
    required: true,
  })
  public readonly compEventTeamProp: ICompEventTeam;
  @Prop({ default: false }) public readonly isLoading: boolean;
  @Prop({
    default: () => {
      return configService.factoryEntity();
    },
  })
  public readonly userEntity: IEntity;

  public compEventTeam: ICompEventTeam = {} as ICompEventTeam;
  public showEligibility: boolean = false;

  public eventTeamHeaderInternal: IEventTeamHeader =
    eventTeamService.factoryEventTeamHeader();
  public athleteForAddButtonToShow = 0;
  public editMode: boolean;

  public created() {
    this.initForm();
  }

  public initForm() {
    this.eventTeamHeaderInternal = R.clone(this.eventTeamHeader);
    this.compEventTeam = R.clone(this.compEventTeamProp);
    
    // Initialize compEventTeam if it's empty (for new teams)
    if (!this.compEventTeam || Object.keys(this.compEventTeam).length === 0) {
      this.compEventTeam = eventTeamService.factoryGetCompEventTeam(
        this.eventTeamHeaderInternal,
        this.userEntity
      );
    }
    
    this.compEventTeam.ceid = this.eventTeamHeader.id;
    this.compEventTeam.formRows = leagueService.getFormRows(
      this.eventTeamHeaderInternal,
      this.compEventTeam
    );
    this.compEventTeam.formRows = leagueService.checkAthletesOk(
      this.compEventTeam.formRows
    );
  }

  @Watch("compEventTeamProp")
  public onCompEventTeamProp(newValue: ICompEventTeam) {
    this.compEventTeam = R.clone(newValue);
    this.reset();
  }

  @Watch("eventTeamHeader")
  public onEventTeamHeaderChanged(newValue: IEventTeamHeader) {
    this.eventTeamHeaderInternal = R.clone(newValue);
    this.reset();
  }

  public reset() {
    this.editingLine = -1;
    this.hasBeenEdited = false;
    this.editTeamName = false;
    this.showDeleteConfirmation = false;
    this.initForm();
  }

  public get getFormRows(): IFormRow[] {
    return leagueService.getFormRows(this.eventTeamHeader, this.compEventTeam);
  }

  public getTeamPositionNameEditMode(formRow: IFormRow) {
    if (!formRow) {
      return "";
    }
    let evtTime = "";
    let evtDate = "";
    if (formRow.dateTime.length > 0) {
      evtDate = format(parse(formRow.dateTime), "Do MMM");

      evtTime = commonService.getE4sStandardTimeOutPut(formRow.dateTime);
      evtTime = evtTime === "00:00" ? "TBC" : evtTime;
    }
    // return formRow.position + " " + formRow.eventDef.name + " " + evtDate + " @" + evtTime;
    return formRow.eventDef.name + " " + evtDate + " @" + evtTime;
  }

  public getDefaultAthlete(formRow: IFormRow): IAthleteSummary {
    if (formRow.athlete && formRow.athlete.firstName) {
      return formRow.athlete;
    }
    return athleteService.factoryAthleteSummary();
  }

  public getAthleteName(formRow: IFormRow) {
    if (!formRow || !formRow.athlete) {
      return "";
    }
    let athleteName = "";
    if (formRow.athlete.id > 0) {
      athleteName = formRow.athlete.firstName + " " + formRow.athlete.surName;
    }
    return athleteName;
  }

  public athleteSelected(payload: {
    autoCompleteValue: IAutoCompleteValue;
    position: number;
  }) {
    this.userMessages = [];
    console.log("CompEventTeam.athleteSelected", payload);
    if (payload.autoCompleteValue.value) {
      const athlete: IAthleteSummary = payload.autoCompleteValue.value;
      this.setAthlete(athlete, payload.position);
    }
  }

  public athleteRowSelected(index: number) {
    console.log("LeagueTeamForm.athleteRowSelected() index: " + index);
    this.athleteForAddButtonToShow = index;
  }

  public setAthlete(athlete: IAthleteSummary, position: number) {
    console.log("LeagueTeamForm.setAthlete: " + position, athlete);

    // const formRow = this.eventTeamHeaderInternal.ceoptions.eventTeam.formRows[position];
    const formRow = this.compEventTeam.formRows[position];
    formRow.athlete = R.clone(athlete);
    this.eventTeamHeaderInternal = R.clone(this.eventTeamHeaderInternal);
    this.onFieldChanged();
  }

  public athleteRemoved(position: number) {
    this.userMessages = [];
    console.log("LeagueTeamForm.athleteRemoved: " + position);
    // const formRow = this.eventTeamHeaderInternal.ceoptions.eventTeam.formRows[position];
    const formRow = this.compEventTeam.formRows[position];
    formRow.athlete = {
      id: 0,
    } as IAthleteSummary;
    //  this.eventTeamHeaderInternal = R.clone(this.eventTeamHeaderInternal);
    this.onFieldChanged();
  }

  public submitCompEventTeam() {
    console.log("LeagueTeamForm.submitCompEventTeam()");
    this.userMessages = [];
    const userMessages: IUserMessage[] = eventTeamService.validateLeagueTeam(
      this.eventTeamHeader,
      this.compEventTeam
    );
    if (userMessages.length > 0) {
      this.userMessages = userMessages;
      return;
    }
    // const compEventTeam = R.clone(this.compEventTeamProp);
    const compEventTeam = R.clone(this.compEventTeam);
    // compEventTeam.teamName =
    // compEventTeam.formRows = this.getFormRows;
    // compEventTeam.athletes = [];

    this.$emit("submit", compEventTeam);
  }

  public setEditModeX() {
    this.editMode = false;
    this.isNew = false;
  }
}
</script>
