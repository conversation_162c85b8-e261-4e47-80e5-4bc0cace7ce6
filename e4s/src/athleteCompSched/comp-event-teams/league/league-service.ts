import { IFormRow } from "../../athletecompsched-models";
import { ICompEventTeam, IEventTeamHeader } from "../event-teams-models";
import { AthleteService } from "../../../athlete/athlete-service";
import { simpleClone } from "../../../common/common-service-utils";

const athleteService: AthleteService = new AthleteService();

export class LeagueService {
  /**
   *
   * @param compEventTeam
   * @param eventTeamHeader
   */
  public cleanUpTeam(
    compEventTeam: ICompEventTeam,
    eventTeamHeader: IEventTeamHeader
  ): ICompEventTeam {
    const compEventTeamInternal = simpleClone(compEventTeam);

    if (compEventTeamInternal.ceid === 0) {
      compEventTeamInternal.ceid = eventTeamHeader.id;
    }

    let formRows = this.getFormRows(eventTeamHeader, compEventTeamInternal);
    formRows = this.checkAthletesOk(formRows);

    compEventTeamInternal.formRows = formRows;

    if (compEventTeamInternal.options && compEventTeamInternal.options.others) {
      const othersEventTeamOptions =
        eventTeamHeader.ceoptions?.eventTeam?.options?.others;
      if (othersEventTeamOptions) {
        // merge othersEventTeamOptions into compEventTeamInternal.options.others
        compEventTeamInternal.options.others = {
          ...othersEventTeamOptions,
          ...compEventTeamInternal.options.others,
        };
      }
    } else {
      if (eventTeamHeader.ceoptions?.eventTeam?.options) {
        compEventTeamInternal.options = simpleClone(
          eventTeamHeader.ceoptions.eventTeam.options
        );
      }
    }

    return compEventTeamInternal;
  }

  /**
   *
   * @param eventTeamHeader
   * @param compEventTeam
   */
  public getFormRows(
    eventTeamHeader: IEventTeamHeader,
    compEventTeam: ICompEventTeam
  ): IFormRow[] {
    if (compEventTeam && compEventTeam.formRows && compEventTeam.prodId > 0) {
      const formRows = compEventTeam.formRows;
      formRows.sort((a: IFormRow, b: IFormRow): number => {
        return a.position - b.position;
      });
      return formRows;
    }
    if (
      eventTeamHeader &&
      eventTeamHeader.ceoptions &&
      eventTeamHeader.ceoptions.eventTeam &&
      eventTeamHeader.ceoptions.eventTeam.formRows
    ) {
      const formRows = eventTeamHeader.ceoptions.eventTeam.formRows;
      formRows.sort((a: IFormRow, b: IFormRow): number => {
        return a.position - b.position;
      });

      return formRows;
    }
    return [];
  }

  /**
   *
   * @param formRow
   */
  public hasAthlete(formRow: IFormRow): boolean {
    return formRow.athlete && formRow.athlete.firstName ? true : false;
  }

  /**
   *
   * @param formRows
   */
  public checkAthletesOk(formRows: IFormRow[]): IFormRow[] {
    return formRows.map((formRow) => {
      if (!this.hasAthlete(formRow)) {
        formRow.athlete = athleteService.factoryAthleteSummary();
      }
      return formRow;
    });
  }
}
