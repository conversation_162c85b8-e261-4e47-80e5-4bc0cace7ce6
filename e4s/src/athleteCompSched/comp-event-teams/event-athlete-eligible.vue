<template>
    <div>
        <div class="row">
            <div class="col s12 m12 l12">
                Can't find athlete?  Enter their name here to check why.
            </div>
        </div>
        <AthleteTypeAhead :disabled="isLoading"
                          :user-entity="userEntity"
                          :field-label="'Enter name or URN'"
                          v-on:athleteSelected="athleteSelected">
        </AthleteTypeAhead>
        <div class="row">
            <div class="col s12 m12 l12">
                <div v-text="message"></div>
            </div>
        </div>

        <div class="row">
            <div class="col s12 m12 l12">
                <a class="comp-event-team-anchor-buttons"
                   href="#"
                   v-on:click.prevent="cancel">
                    Cancel
                </a>
            </div>
        </div>

    </div>
</template>

<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import AthleteTypeAhead from "../athlete-type-ahead.vue";
    import { AthleteData } from "../../athlete/athlete-data";
    import {IAthleteSummary} from "../../athlete/athlete-models";
    import { Prop } from "vue-property-decorator";
    import { IServerGenericResponse } from "../../common/common-models";
    import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
    import { messageDispatchHelper } from "../../user-message/user-message-store";
    import {mapState} from "vuex";
    import {CONFIG_STORE_CONST, IConfigStoreState} from "../../config/config-store";

    const athleteData: AthleteData = new AthleteData();

    @Component({
        name: "event-athlete-eligible",
        components: {
            AthleteTypeAhead
        },
        computed: {
            ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
                configApp: (state: any) => state.configApp,
                userEntity: (state: any) => state.userEntity
            })
        }
    })
    export default class EventAthleteEligible extends Vue {
        @Prop({
            default: 0
        }) public readonly ceid: number;

        public isLoading: boolean = false;
        public message: string = "";

        public athleteSelected(payload: {athlete: IAthleteSummary, position: number}) {
            const athlete: IAthleteSummary = payload.athlete;
            this.message = "";
            if (this.ceid > 0 && athlete && athlete.id) {
                this.message = "Checking...";
                this.isLoading = true;
                athleteData.checkAthleteEventEligible(athlete.id, this.ceid)
                    .then( (response: IServerGenericResponse) => {
                        if (response.errNo > 0) {
                            messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
                            return;
                        }
                        this.message = response.data;
                        return;
                    })
                    .catch((error) => {
                        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
                        return;
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            }
        }

        public cancel() {
            this.$emit("cancel");
        }

    }

</script>
