import * as R from "ramda";
import { IAgeGroup, IAthleteSummary } from "../../athlete/athlete-models";
import { AthleteService } from "../../athlete/athlete-service";
import {
  GENDER,
  IBase,
  IObjectKey,
  IObjectKeyType,
  IObjectKeyTypeArray,
} from "../../common/common-models";
import { CommonService } from "../../common/common-service";
import {
  IUserMessage,
  USER_MESSAGE_LEVEL,
} from "../../user-message/user-message-models";
import {
  EVENT_TYPE,
  IAgeGroupUpScale,
  ICompShedRuleConfig,
  IEventTeam,
  IEventTeamCe,
  ISecurity,
  ShowFormType,
  TEAM_NAME_FORMAT_TYPES,
} from "../athletecompsched-models";
import {
  IAthleteEventCompSummary,
  ICompEventTeam,
  ICompEventTeamBase,
  IEventTeamFormField,
  IEventTeamHeader,
  ITeamNamePatternModel,
} from "./event-teams-models";
import { IAgeGroupBase } from "../../agegroup/agegroup-models";
import { ICompetitionInfo } from "../../competition/competition-models";
import {
  IAreaUserInfo,
  IEntity,
  IUserInfo,
} from "../../config/config-app-models";
import { AthleteCompSchedService } from "../athletecompsched-service";
import { AgeGroupService } from "../../agegroup/agegroup-service";
import { IOrg } from "../../org/org-models";
import { ConfigService } from "../../config/config-service";
import {
  IEventTeamHeaderFilter,
  IEventTeamHeaderFilterV2,
} from "./event-teams-filter/event-teams-filter-models";
import { IClubCompInfo } from "../../entry/v2/schools/clubCompInfo-models";
import {
  ICompEventTeamSchool,
  ISchoolTeamAthlete,
} from "./school/school-team-models";

const athleteService: AthleteService = new AthleteService();
const commonService: CommonService = new CommonService();
const athleteCompSchedService: AthleteCompSchedService =
  new AthleteCompSchedService();
const ageGroupService: AgeGroupService = new AgeGroupService();
const configService: ConfigService = new ConfigService();

export type TeamLimits = {
  maxTeamsReached: boolean;
  getTeamEventHasSecurity: boolean;
  isEntitySelected: boolean;
  canAddTeam: boolean;
  doesUserHaveAccessToThisEvent: boolean;
};

export class EventTeamService {
  public factoryEventTeamHeader(): IEventTeamHeader {
    return {
      id: 0,
      eventName: "",
      eventid: 0,
      eventGroup: {
        id: 0,
        name: "",
        eventNo: 0,
        startDate: "",
      },
      startdate: "",
      gender: GENDER.UNKNOWN,
      tf: EVENT_TYPE.TRACK,
      price: athleteCompSchedService.factoryPrice(),
      IsOpen: 1,
      maxAthletes: -1,
      isDisabled: false,
      helpText: "",
      maxgroup: 0,
      ageGroup: ageGroupService.factoryGetAgeGroup(),
      ceoptions: athleteCompSchedService.factoryCeOptions(),
      eoptions: athleteCompSchedService.factoryEOptions(),
      compEventTeams: [],
    };
  }

  public factoryGetCompEventTeamBase(
    eventTeamHeader: IEventTeamHeader,
    userEntity: IEntity,
    clubCompInfo?: IClubCompInfo | null
  ): ICompEventTeamBase {
    return {
      id: 0,
      ceid: eventTeamHeader.id,
      teamName: userEntity
        ? this.getTeamName(eventTeamHeader, userEntity, clubCompInfo)
        : "",
      userId: 0,
      userName: "",
      paid: 0,
      prodId: 0,
    } as ICompEventTeamBase;
  }

  public factoryGetCompEventTeam(
    eventTeamHeader: IEventTeamHeader,
    userEntity: IEntity,
    clubCompInfo?: IClubCompInfo | null
  ): ICompEventTeam {
    if (typeof eventTeamHeader === "undefined") {
      //  FU IE!
      eventTeamHeader = arguments[0];
    }

    const base = this.factoryGetCompEventTeamBase(
      eventTeamHeader,
      userEntity,
      clubCompInfo
    );
    return {
      ...base,
      athletes: [] as IAthleteSummary[],
      formInfo: [],
      formRows: [],
      entityId: 0,
      entityLevel: 0,
      options: {
        others: {},
      },
    };
  }

  public getTeamName(
    eventTeamHeader: IEventTeamHeader,
    userEntity: IEntity,
    clubCompInfo?: IClubCompInfo | null
  ) {
    let teamName = "";
    const eventTeam: IEventTeam = this.getEventTeamOptions(eventTeamHeader);

    let teamNameFormat = "{{entity}} {{eventname}} {{gender}} {{agegroup}}";
    if (
      eventTeam &&
      eventTeam.teamNameFormat &&
      eventTeam.teamNameFormat.length > 0
    ) {
      teamName = teamNameFormat;
    }

    /*
        if (
      eventTeam &&
      eventTeam.teamNameFormat &&
      eventTeam.teamNameFormat.length > 0
    ) {
     */

    if (teamNameFormat.length > 0) {
      // const teamNameFormat = eventTeam.teamNameFormat;
      teamName = teamNameFormat;

      const placeHolders = teamNameFormat.split("{{");
      const placeHoldersObject: IObjectKeyType<ITeamNamePatternModel> =
        placeHolders.reduce((accum, placehold) => {
          // console.log("placeHolders.reduce ...placehold: " + placehold);
          const placeArr = placehold.split(";");
          // console.log("placeHolders.reduce ...placeArr: ", placeArr);
          if (placeArr.length > 1) {
            // console.log("placeHolders.reduce ...placeArr.length found");
            const teamNamePatternModel: ITeamNamePatternModel = {
              default: placeArr[1].split("}}")[0],
              stringToReplace: "{{" + placehold.split("}}")[0] + "}}",
            };
            accum[placeArr[0]] = teamNamePatternModel;
          }
          return accum;
        }, {} as IObjectKey);

      let entity = "";
      if (userEntity && userEntity.entityName.length > 0) {
        entity = userEntity.name;
      }
      if (clubCompInfo) {
        if (clubCompInfo.configData.clubName.length > 0) {
          entity = clubCompInfo.configData.clubName;
        }
      }
      teamName = this.createDefaultPlaceHolder(
        TEAM_NAME_FORMAT_TYPES.ENTITY,
        entity,
        placeHoldersObject,
        teamName
      );

      // let areaName = "";
      // // console.log("placeHolders.reduce ...userInfo.areas: ", userInfo);
      // if (userInfo && userInfo.areas && userInfo.areas.length > 0) {
      //     // console.log("placeHolders.reduce ...userInfo.areas.length found");
      //     const area: IAreaUserInfo = userInfo.areas[0];
      //     areaName = area.areaname;
      // }
      // teamName = this.createDefaultPlaceHolder(TEAM_NAME_FORMAT_TYPES.AREA, areaName, placeHoldersObject, teamName);
      //
      // let clubName = "";
      // // console.log("placeHolders.reduce ...userInfo.clubs: ", userInfo);
      // if (userInfo && userInfo.clubs && userInfo.clubs.length > 0) {
      //     // console.log("placeHolders.reduce ...userInfo.clubs.length found");
      //     const club = userInfo.clubs[0];
      //     // TODO IClub in UserInfo is not IClub, it's a nothing model
      //     clubName = club.Clubname;
      // }
      // teamName = this.createDefaultPlaceHolder(TEAM_NAME_FORMAT_TYPES.CLUB, clubName, placeHoldersObject, teamName);

      teamName = teamName.replace(
        TEAM_NAME_FORMAT_TYPES.EVENT,
        eventTeamHeader.eventName
      );
      if (eventTeamHeader.eventGroup && eventTeamHeader.eventGroup.name) {
        teamName = teamName.replace(
          TEAM_NAME_FORMAT_TYPES.EVENT_GROUP,
          eventTeamHeader.eventGroup.name
        );
      }

      //  @ts-ignore
      if (eventTeamHeader.gender === "O") {
        teamName = teamName.replace(TEAM_NAME_FORMAT_TYPES.GENDER, "");
      } else {
        teamName = teamName.replace(
          TEAM_NAME_FORMAT_TYPES.GENDER,
          eventTeamHeader.gender === "F" ? "Female" : "Male"
        );
      }

      if (eventTeamHeader.ageGroup && eventTeamHeader.ageGroup.name) {
        teamName = teamName.replace(
          TEAM_NAME_FORMAT_TYPES.AGE_GROUP,
          eventTeamHeader.ageGroup.name
        );
      } else {
        teamName = teamName.replace(TEAM_NAME_FORMAT_TYPES.AGE_GROUP, "");
      }
    }

    // console.log("placeHolders.reduce ...near end...");
    // if (teamName.indexOf(TEAM_NAME_FORMAT_TYPES.UNIQUE) > 0) {
    //   teamName = this.pickTeamNameWithSuffix2(
    //     teamName,
    //     this.getAllTeamNames(eventTeamHeader),
    //     this.getTeamNameSuffixes()
    //   );
    // }

    // console.log("placeHolders.reduce ...end.");
    return teamName.trim();
  }

  public getTeamName2(
    eventTeamHeader: IEventTeamHeader,
    userEntity: IEntity,
    clubCompInfo?: IClubCompInfo | null
  ): any {
    let teamName = "";
    const eventTeam: IEventTeam = this.getEventTeamOptions(eventTeamHeader);
    if (
      eventTeam &&
      eventTeam.teamNameFormat &&
      eventTeam.teamNameFormat.length > 0
    ) {
      const teamNameFormat = eventTeam.teamNameFormat;
      teamName = teamNameFormat;

      const placeHolders = teamNameFormat.split("{{");
      const placeHoldersObject: IObjectKeyType<ITeamNamePatternModel> =
        placeHolders.reduce((accum, placehold) => {
          // console.log("placeHolders.reduce ...placehold: " + placehold);
          const placeArr = placehold.split(";");
          // console.log("placeHolders.reduce ...placeArr: ", placeArr);
          if (placeArr.length > 1) {
            // console.log("placeHolders.reduce ...placeArr.length found");
            const teamNamePatternModel: ITeamNamePatternModel = {
              default: placeArr[1].split("}}")[0],
              stringToReplace: "{{" + placehold.split("}}")[0] + "}}",
            };
            accum[placeArr[0]] = teamNamePatternModel;
          }
          return accum;
        }, {} as IObjectKey);

      let entity = "";
      if (userEntity && userEntity.entityName.length > 0) {
        entity = userEntity.name;
      }
      if (clubCompInfo) {
        entity = clubCompInfo.configData.clubName;
      }
      teamName = this.createDefaultPlaceHolder(
        TEAM_NAME_FORMAT_TYPES.ENTITY,
        entity,
        placeHoldersObject,
        teamName
      );

      // let areaName = "";
      // // console.log("placeHolders.reduce ...userInfo.areas: ", userInfo);
      // if (userInfo && userInfo.areas && userInfo.areas.length > 0) {
      //     // console.log("placeHolders.reduce ...userInfo.areas.length found");
      //     const area: IAreaUserInfo = userInfo.areas[0];
      //     areaName = area.areaname;
      // }
      // teamName = this.createDefaultPlaceHolder(TEAM_NAME_FORMAT_TYPES.AREA, areaName, placeHoldersObject, teamName);
      //
      // let clubName = "";
      // // console.log("placeHolders.reduce ...userInfo.clubs: ", userInfo);
      // if (userInfo && userInfo.clubs && userInfo.clubs.length > 0) {
      //     // console.log("placeHolders.reduce ...userInfo.clubs.length found");
      //     const club = userInfo.clubs[0];
      //     // TODO IClub in UserInfo is not IClub, it's a nothing model
      //     clubName = club.Clubname;
      // }
      // teamName = this.createDefaultPlaceHolder(TEAM_NAME_FORMAT_TYPES.CLUB, clubName, placeHoldersObject, teamName);

      teamName = teamName.replace(
        TEAM_NAME_FORMAT_TYPES.EVENT,
        eventTeamHeader.eventName
      );
      teamName = teamName.replace(
        TEAM_NAME_FORMAT_TYPES.EVENT_GROUP,
        eventTeamHeader.eventGroup.name
      );
      //  @ts-ignore
      if (eventTeamHeader.gender === "O") {
        teamName = teamName.replace(TEAM_NAME_FORMAT_TYPES.GENDER, "");
      } else {
        teamName = teamName.replace(
          TEAM_NAME_FORMAT_TYPES.GENDER,
          eventTeamHeader.gender === "F" ? "Female" : "Male"
        );
      }

      if (eventTeamHeader.ageGroup && eventTeamHeader.ageGroup.name) {
        teamName = teamName.replace(
          TEAM_NAME_FORMAT_TYPES.AGE_GROUP,
          eventTeamHeader.ageGroup.name
        );
      } else {
        teamName = teamName.replace(TEAM_NAME_FORMAT_TYPES.AGE_GROUP, "");
      }
    }

    console.log("placeHolders.reduce ...near end...");
    // if (teamName.indexOf(TEAM_NAME_FORMAT_TYPES.UNIQUE) > 0) {
    //   teamName = this.pickTeamNameWithSuffix2(
    //     teamName,
    //     this.getAllTeamNames(eventTeamHeader),
    //     this.getTeamNameSuffixes()
    //   );
    // }

    // console.log("placeHolders.reduce ...end.");
    return teamName.trim();
  }

  public createDefaultPlaceHolder(
    moustache: string,
    moustacheValue: string,
    keyObject: IObjectKeyType<ITeamNamePatternModel>,
    tName: string
  ) {
    const key = moustache.slice(2, moustache.length - 2);
    if (keyObject[key]) {
      const teamNamePatternModel = keyObject[key];
      tName = tName.replace(
        teamNamePatternModel.stringToReplace,
        moustacheValue.length > 0
          ? moustacheValue
          : teamNamePatternModel.default
      );
    } else {
      tName = tName.replace(moustache, moustacheValue);
    }
    return tName;
  }

  public pickTeamNameWithSuffix(
    teamName: string,
    _currentTeamNames: string[],
    _suffixes: string[]
  ): string {
    alert("EventTeamService.pickTeamNameWithSuffix() Deprecated  ");
    // return suffixes.reduce((accum: string, suffix: string) => {
    //   if (accum.length === 0) {
    //     const teamNameWithSuffixWouldLike = teamName.replace(
    //       TEAM_NAME_FORMAT_TYPES.UNIQUE,
    //       suffix
    //     );
    //     if (currentTeamNames.indexOf(teamNameWithSuffixWouldLike) === -1) {
    //       accum = teamNameWithSuffixWouldLike;
    //     }
    //   }
    //   return accum;
    // }, "");
    return "NA";
  }

  /**
   *
   * @param teamName    E.g. "Mixed Relay  Under 14 {{unique}}"
   * @param currentTeamNames E.g. ["Mixed Relay  Under 14 A", "Mixed Relay  Under 14 B"]
   * @param suffixes  ["A", "B", "C"...etc]
   */
  public pickTeamNameWithSuffix2(
    teamName: string,
    _currentTeamNames: string[],
    _suffixes: string[],
    _uniquePlaceholder: string = "{{UNIQUE}}"
  ): string {
    alert("EventTeamService.pickTeamNameWithSuffix2() Deprecated  ");
    // "Mixed Relay  Under 14 {{unique}}"
    // const teamNameToTestForUnique = teamName
    //   .replace(uniquePlaceholder, "")
    //   .trim();
    // const numberOfTimeNameExists: number = currentTeamNames.reduce(
    //   (accum, teamNameLoop) => {
    //     if (teamNameLoop.indexOf(teamNameToTestForUnique) > -1) {
    //       accum++;
    //     }
    //     return accum;
    //   },
    //   0
    // );

    // const teamNameWithSuffixWouldLike = teamName
    //   .replace(TEAM_NAME_FORMAT_TYPES.UNIQUE, suffixes[numberOfTimeNameExists])
    //   .trim();

    // return teamNameWithSuffixWouldLike;
    return "NA";

    // return {
    //   teamName,
    //   teamNameToTestForUnique,
    //   teamNameWithSuffixWouldLike,
    //   numberOfTimeNameExists,
    //   currentTeamNames,
    // };
  }

  public getAllTeamNames(eventTeamHeader: IEventTeamHeader): string[] {
    return eventTeamHeader.compEventTeams.map((compEventTeam) => {
      return compEventTeam.teamName;
    });
  }

  public getTeamNameSuffixes(): string[] {
    return "abcdefghijklmnopqrstuvwxyz".toUpperCase().split("");
  }

  public getEventTeamOptions(eventTeamHeader: IEventTeamHeader): IEventTeam {
    if (eventTeamHeader.ceoptions && eventTeamHeader.ceoptions.eventTeam) {
      return eventTeamHeader.ceoptions.eventTeam;
    }
    return eventTeamHeader.eoptions.eventTeam;
  }

  public showForm(_eventTeamHeader: IEventTeamHeader): boolean {
    // TODO
    // const options = this.getEventTeamOptions(eventTeamHeader);
    // return options.showForm ? true : false;
    return false;
  }

  public displayWhichFormType(eventTeamHeader: IEventTeamHeader): ShowFormType {
    const formType = this.getEventTeamOptions(eventTeamHeader).formType;
    //  TODO switch this to default
    return formType ? formType : "DEFAULT";
  }

  public getOptionsTeamPositionName(eventTeamHeader: IEventTeamHeader): string {
    const options = this.getEventTeamOptions(eventTeamHeader);
    return options.teamPositionLabel ? options.teamPositionLabel : "";
  }

  public getOptionsTeamSubstituteName(
    eventTeamHeader: IEventTeamHeader
  ): string {
    const options = this.getEventTeamOptions(eventTeamHeader);
    return options.teamSubstituteLabel ? options.teamSubstituteLabel : "";
  }

  public getOptionsMin(eventTeamHeader: IEventTeamHeader): number {
    const options = this.getEventTeamOptions(eventTeamHeader);
    return options.min;
  }

  public getOptionsMaxEventTeams(eventTeamHeader: IEventTeamHeader): number {
    const options = this.getEventTeamOptions(eventTeamHeader);
    const maxEventTeams = options.maxEventTeams;
    const maxAthletes = eventTeamHeader.maxAthletes;
    //  This is super confusing.  THe ui in builder maps to maxAthletes
    if (maxEventTeams === 0 && maxAthletes > 0) {
      return maxAthletes;
    }
    return maxEventTeams;
  }

  public getTeamNamesSimple(eventTeamHeader: IEventTeamHeader): {
    name: string;
    athletes: string;
  }[] {
    return eventTeamHeader.compEventTeams.map((compEventTeam) => {
      return this.getTeamNameSimple(compEventTeam);
    });
  }

  public getTeamNameSimple(compEventTeam: ICompEventTeam): {
    name: string;
    athletes: string;
  } {
    const athletes: IAthleteSummary[] = this.getAthletes(compEventTeam).filter(
      (athlete) => {
        //  Pretty sure we have some athletes that are temp and have no id???
        return athlete.id > 0 || athlete.firstName.length > 0;
      }
    );

    return {
      name: compEventTeam.teamName,
      athletes:
        athletes.length > 0
          ? athletes
              .map((athlete) => {
                return athlete.firstName + " " + athlete.surName;
              })
              .join(", ")
          : "No athletes listed",
    };

    // return (
    //   compEventTeam.teamName +
    //   ":" +
    //   athletes
    //     .map((athlete) => {
    //       return athlete.firstName + " " + athlete.surName;
    //     })
    //     .join(", ")
    // );
  }

  public getAthleteNames(compEventTeam: ICompEventTeam): string[] {
    return this.getAthletes(compEventTeam).map((athlete) => {
      return athlete.firstName + " " + athlete.surName;
    });
  }

  public getAthletes(compEventTeam: ICompEventTeam): IAthleteSummary[] {
    return compEventTeam.athletes;
  }

  public getAgeGroupOptions(event: IEventTeamHeader): IAgeGroupBase[] {
    if (event.ceoptions && event.ceoptions.ageGroups) {
      return event.ceoptions.ageGroups;
    }
    return [];
  }

  public getOptionsAgeGroupIdsForAthleteSearch(
    eventTeamHeader: IEventTeamHeader
  ): number[] {
    const eventTargetAgeGroup = eventTeamHeader.ageGroup.id;
    const optionsAgeGroups: number[] = this.getAgeGroupOptions(
      eventTeamHeader
    ).map((ageGroup: IAgeGroupBase) => {
      return ageGroup.id;
    });
    return [eventTargetAgeGroup, ...optionsAgeGroups];
  }

  public canAddTeam(eventTeamHeader: IEventTeamHeader) {
    if (eventTeamHeader.isDisabled) {
      return false;
    }
    return !this.maxTeamsReached(eventTeamHeader);
  }

  public maxTeamsReached(eventTeamHeader: IEventTeamHeader) {
    const max = this.getOptionsMaxEventTeams(eventTeamHeader);

    if (max === 0) {
      return false;
    }
    if (max === -1) {
      return true;
    }
    return eventTeamHeader.compEventTeams.length >= max;
  }

  public canAddAthlete(
    eventTeamHeader: IEventTeamHeader,
    compEventTeam: ICompEventTeam,
    athlete: IAthleteSummary,
    position: number
  ): IUserMessage[] {
    const userMessages: IUserMessage[] = [];
    //  hit the max?
    const athletesEntered = compEventTeam.athletes.filter(
      (ath: IAthleteSummary) => {
        return ath.id > 0;
      }
    );
    const eventTeam: IEventTeam = this.getEventTeamOptions(eventTeamHeader);
    const maxAllowed: number = eventTeam ? eventTeam.max : 0;
    if (athletesEntered.length >= maxAllowed) {
      userMessages.push({
        level: USER_MESSAGE_LEVEL.WARN,
        message: "Hit max of " + maxAllowed,
      });
    }

    const athletesSameName = athletesEntered.filter((ath: IAthleteSummary) => {
      return ath.id === athlete.id;
    });

    if (athletesSameName.length > 0) {
      const athletePosition: number = compEventTeam.athletes.reduce(
        (accum, ath, index) => {
          if (ath.id === athlete.id) {
            accum = index;
          }
          return accum;
        },
        -1
      );

      //  Position will be the same if the user is "editing" the same position.
      if (athletePosition !== position) {
        userMessages.push({
          level: USER_MESSAGE_LEVEL.WARN,
          message: "Name already in list.",
        });
      }
    }
    return userMessages;
  }

  public setUpAthleteArray(
    eventTeamHeader: IEventTeamHeader,
    compEventTeam: ICompEventTeam
  ) {
    const athletes: IAthleteSummary[] = R.clone(compEventTeam.athletes);
    const eventTeam: IEventTeam = this.getEventTeamOptions(eventTeamHeader);
    const slots: number = eventTeam ? eventTeam.max : 0;
    // const temp: IAthleteSummary[] = [...new Array(slots)];

    const temp: IAthleteSummary[] = Array.apply(null, Array(slots)).map(() => {
      return athleteService.factoryGetAthlete();
    }) as IAthleteSummary[];

    for (let i = 0, len = athletes.length; i < len; i++) {
      temp[i] = athletes[i];
    }
    return R.clone(temp);
  }

  public validateCompEventTeamForm(
    eventTeamHeader: IEventTeamHeader,
    compEventTeam: ICompEventTeam
  ): IUserMessage[] {
    const userMessages: IUserMessage[] = [];
    if (compEventTeam.formInfo && compEventTeam.formInfo.length > 0) {
      compEventTeam.formInfo.reduce(
        (accum: IUserMessage[], formInfoField: IEventTeamFormField) => {
          if (
            formInfoField.req &&
            formInfoField.value.toString().length === 0
          ) {
            userMessages.push({
              message: formInfoField.label + " is required.",
              level: USER_MESSAGE_LEVEL.WARN,
            });
          }
          return accum;
        },
        userMessages
      );
    }
    return userMessages;
  }

  public validateBase(
    eventTeamHeader: IEventTeamHeader,
    compEventTeam: ICompEventTeamBase
  ): IUserMessage[] {
    const userMessages: IUserMessage[] = [];
    const teamName: string = compEventTeam.teamName.replace(/\s/g, "");
    if (teamName.length === 0) {
      userMessages.push({
        message: "Enter team name",
        level: USER_MESSAGE_LEVEL.WARN,
      });
    }

    /*
    //  We don't care about dup team names, back end handles this.
    if (!this.isTeamNameUnique(eventTeamHeader, compEventTeam)) {
      userMessages.push({
        message:
          "Team name must be unique to the competition: " +
          compEventTeam.teamName,
        level: USER_MESSAGE_LEVEL.WARN,
      });
    }
    */
    return userMessages;
  }

  public validateLeagueTeam(
    eventTeamHeader: IEventTeamHeader,
    compEventTeam: ICompEventTeam
  ): IUserMessage[] {
    const userMessages: IUserMessage[] = [];
    return userMessages.concat(
      ...this.validateBase(eventTeamHeader, compEventTeam)
    );
  }

  public validateCompEventTeam(
    eventTeamHeader: IEventTeamHeader,
    compEventTeam: ICompEventTeam,
    competition: ICompetitionInfo,
    eventTeamHeaders: IEventTeamHeader[]
  ): IUserMessage[] {
    // console.log("----validateCompEventTeam-------validateCompEventTeam------validateCompEventTeam------");
    let userMessages: IUserMessage[] = [];
    userMessages = userMessages.concat(
      ...this.validateBase(eventTeamHeader, compEventTeam)
    );

    const options = this.getEventTeamOptions(eventTeamHeader);
    if (this.showForm(eventTeamHeader)) {
      //  TODO move to specific form.
      userMessages = userMessages.concat(
        ...this.validateCompEventTeamForm(eventTeamHeader, compEventTeam)
      );
    } else {
      const athletes = compEventTeam.athletes.filter((ath) => {
        return ath.id > 0;
      });

      if (!(athletes.length >= options.min && athletes.length <= options.max)) {
        userMessages.push({
          message:
            "Enter between " +
            options.min +
            " and " +
            options.max +
            " athletes.",
          level: USER_MESSAGE_LEVEL.WARN,
        });
      }

      if (
        athletes.length > 0 &&
        this.areAllAthletesSameClubReqd(eventTeamHeader)
      ) {
        if (!this.areAllAthletesSameClub(athletes)) {
          userMessages.push({
            message: "All athletes must be in the same club",
            level: USER_MESSAGE_LEVEL.WARN,
          });
        }
      }

      athletes.reduce((accum: IObjectKey, ath: IAthleteSummary) => {
        if (accum[ath.id]) {
          userMessages.push({
            message: "User not unique: " + ath.firstName + " " + ath.surName,
            level: USER_MESSAGE_LEVEL.WARN,
          });
        } else {
          accum[ath.id] = ath;
        }
        return accum;
      }, {} as IObjectKey);

      let maxTeamsForAthlete: number = 1;
      if (eventTeamHeader.ceoptions && eventTeamHeader.ceoptions.eventTeam) {
        maxTeamsForAthlete =
          eventTeamHeader.ceoptions.eventTeam &&
          eventTeamHeader.ceoptions.eventTeam.maxTeamsForAthlete
            ? eventTeamHeader.ceoptions.eventTeam.maxTeamsForAthlete
            : 1;

        //  Min users req'd from target age group
        if (eventTeamHeader.ceoptions.eventTeam.minTargetAgeGroupCount) {
          const minTargetAgeGroupCount =
            eventTeamHeader.ceoptions.eventTeam.minTargetAgeGroupCount;
          if (minTargetAgeGroupCount) {
            const athletesInTargetAgeGroup: IAthleteSummary[] =
              this.athletesInTargetAgeGroupCount(
                eventTeamHeader,
                compEventTeam,
                true
              );
            //  N.B. +1 as needs to include comp we are just about to enter. no, taken +1 out.
            if (athletesInTargetAgeGroup.length < minTargetAgeGroupCount) {
              userMessages.push({
                message:
                  "Need at least " +
                  minTargetAgeGroupCount +
                  " " +
                  eventTeamHeader.ageGroup.name,
                level: USER_MESSAGE_LEVEL.WARN,
              });
            }
          }
        }

        //  Max upscale allowed from other age group(s)
        if (eventTeamHeader.ceoptions.eventTeam.maxOtherAgeGroupCount) {
          const maxOtherAgeGroupCount =
            eventTeamHeader.ceoptions.eventTeam.maxOtherAgeGroupCount;
          if (maxOtherAgeGroupCount) {
            const athletesOtherTargetAgeGroup: IAthleteSummary[] =
              this.athletesInTargetAgeGroupCount(
                eventTeamHeader,
                compEventTeam,
                false
              );
            //  N.B. +1 as needs to include comp we are just about to enter. no, taken +1 out.
            if (athletesOtherTargetAgeGroup.length > maxOtherAgeGroupCount) {
              userMessages.push({
                message:
                  "Only allowed " +
                  maxOtherAgeGroupCount +
                  " athletes not target age group " +
                  eventTeamHeader.ageGroup.name,
                level: USER_MESSAGE_LEVEL.WARN,
              });
            }
          }
        }
      }

      //  E.g. U9 4 x 100 relay, but u can enter as many teams as you like, but an indiv athlete can only enter ONCE.
      const athletesInHowManyTeamsObj: IObjectKeyType<
        IAthleteEventCompSummary[]
      > = this.athletesInHowManyTeamsObj(eventTeamHeader, {}, compEventTeam);

      //  ...but needed to check across ALL events, e.g. U10 4 x 100, U9 4 x 200, etc.
      const athletesInHowManyEventTeamsObj: IObjectKeyType<
        IAthleteEventCompSummary[]
      > = this.athletesInHowManyEventTeamsObj(eventTeamHeaders, compEventTeam);

      compEventTeam.athletes
        .filter((ath: IAthleteSummary) => ath.id > 0)
        .forEach((ath: IAthleteSummary) => {
          let currAthCount = 1;

          //  Across this event
          let athleteEventCompSummaries: IAthleteEventCompSummary[] =
            athletesInHowManyTeamsObj[
              ath.id + ""
            ] as IAthleteEventCompSummary[];
          if (athleteEventCompSummaries) {
            currAthCount += athleteEventCompSummaries.length;
          }

          //  The Event rule will decide if user can enter this event x times.
          if (currAthCount > maxTeamsForAthlete) {
            const athleteEventCompSummary: IAthleteEventCompSummary =
              athleteEventCompSummaries[0];
            userMessages.push({
              message:
                athleteEventCompSummary.athName +
                " already entered in this event: " +
                eventTeamHeader.eventName +
                ", " +
                compEventTeam.teamName,
              level: USER_MESSAGE_LEVEL.WARN,
            });
          }

          if (competition.compRules) {
            const ruleConfig = this.getCompRuleForAgeGroup(
              eventTeamHeader.ageGroup.id,
              competition.compRules
            );

            if (!R.isNil(ruleConfig)) {
              //  Across ALL events.
              currAthCount = 1;
              athleteEventCompSummaries = athletesInHowManyEventTeamsObj[
                ath.id + ""
              ] as IAthleteEventCompSummary[];
              if (athleteEventCompSummaries) {
                currAthCount += athleteEventCompSummaries.length;
              }
              const maxExcludedEvents =
                ruleConfig.options &&
                typeof ruleConfig.options.maxTeamEvents !== "undefined" &&
                ruleConfig.options.maxTeamEvents > 0
                  ? ruleConfig.options.maxTeamEvents
                  : 999;

              // console.log("validateCompEventTeam", {
              //   currAthCount,
              //   maxExcludedEvents,
              //   "athleteEventCompSummary.athName":
              //     athleteEventCompSummaries[0].athName,
              // });

              if (currAthCount > maxExcludedEvents) {
                const athleteEventCompSummary: IAthleteEventCompSummary =
                  athleteEventCompSummaries[0];

                const message: string[] = athleteEventCompSummaries.reduce(
                  (
                    accum,
                    compSumy: IAthleteEventCompSummary,
                    index: number
                  ) => {
                    const mess: string =
                      "[" +
                      (index + 1) +
                      "] " +
                      compSumy.eventName +
                      " - " +
                      compSumy.teamName;
                    accum.push(mess);
                    return accum;
                  },
                  [] as string[]
                );

                userMessages.push({
                  message:
                    athleteEventCompSummary.athName +
                    " is already in a maximum of " +
                    athleteEventCompSummaries.length +
                    " teams: " +
                    message.join(", "),
                  level: USER_MESSAGE_LEVEL.WARN,
                });
              }
            }
          }
        });
    }
    return userMessages;
  }

  public areAllAthletesSameClubReqd(eventHeader: IEventTeamHeader): boolean {
    const eventTeam = this.getEventTeamOptions(eventHeader);
    return eventTeam && eventTeam.singleClub;
  }

  public areAllAthletesSameClub(athletes: IAthleteSummary[]): boolean {
    const clubIdObj: IObjectKeyType<string> = athletes.reduce(
      (accum: IObjectKeyType<string>, ath: IAthleteSummary) => {
        const keyClubId: string = ath.clubid + "";
        if (!accum[keyClubId]) {
          accum[keyClubId] = "";
        }
        return accum;
      },
      {} as IObjectKeyType<string>
    );
    const clubIds: string[] = R.keys(clubIdObj) as any as string[];
    return clubIds.length === 1;
  }

  public stripEventCompEventTeam(
    compEventTeam: ICompEventTeam
  ): ICompEventTeam {
    compEventTeam = R.clone(compEventTeam);

    if (compEventTeam.athletes) {
      compEventTeam.athletes = compEventTeam.athletes
        .filter((ath) => {
          return ath.id > 0;
        })
        .map((ath) => {
          return {
            id: ath.id,
          } as IAthleteSummary;
        });
    }
    return compEventTeam;
  }

  public upscaleAvailable(eventTeamHeader: IEventTeamHeader): boolean {
    return eventTeamHeader.ceoptions &&
      eventTeamHeader.ceoptions.eventTeam &&
      eventTeamHeader.ceoptions.eventTeam.ageGroupUpScale &&
      eventTeamHeader.ceoptions.eventTeam.ageGroupUpScale.length > 0
      ? true
      : false;
  }

  public upscaleGetConfigAsObject(
    eventTeamHeader: IEventTeamHeader
  ): IObjectKeyType<IAgeGroupUpScale> {
    return commonService.convertArrayToObject<IAgeGroupUpScale>(
      "id",
      eventTeamHeader.ceoptions.eventTeam.ageGroupUpScale
    );
  }

  public upscaleValidateCompEventTeam(
    eventTeamHeader: IEventTeamHeader,
    compEventTeam: ICompEventTeam
  ): IUserMessage[] {
    // const userMessages: IUserMessage[] = [];
    const upscaleObj: IObjectKeyType<IAgeGroupUpScale> =
      this.upscaleGetConfigAsObject(eventTeamHeader);
    const upscaleAgeKeys: number[] = R.compose(
      R.map((num: string) => Number(num)),
      R.keys
    )(upscaleObj);
    // console.log("upscaleAgeKeys", upscaleAgeKeys);

    const compEventAgeGroupCount: IObjectKeyType<number> =
      this.getAthleteAgeGroupCountObj(compEventTeam, upscaleAgeKeys);
    // console.log("upscaleValidateCompEventTeam compEventAgeGroupCount:", compEventAgeGroupCount);

    const isAgeGroupConfigOk = (ageGroupKey: string): IUserMessage => {
      const upscaleObjByKey = upscaleObj[ageGroupKey];
      const userEntryByKey = compEventAgeGroupCount[ageGroupKey];
      const userMessage: IUserMessage = {
        message: "",
        level: USER_MESSAGE_LEVEL.INFO,
      };
      if (upscaleObjByKey.minCount > -1) {
        //  Have to meet this min req.
        if (
          R.isNil(userEntryByKey) ||
          userEntryByKey < upscaleObjByKey.minCount
        ) {
          const countForKey = R.isNil(userEntryByKey) ? 0 : userEntryByKey;
          userMessage.message =
            "Age group id: " +
            upscaleObjByKey.id +
            ", name: " +
            upscaleObjByKey.name +
            "," +
            " needs min of " +
            upscaleObjByKey.minCount +
            ", current: " +
            countForKey;
          userMessage.level = USER_MESSAGE_LEVEL.WARN;
        }
      }
      if (upscaleObjByKey.maxCount > -1) {
        if (
          !R.isNil(userEntryByKey) &&
          userEntryByKey > upscaleObjByKey.maxCount
        ) {
          userMessage.message =
            "Age group id: " +
            upscaleObjByKey.id +
            ", name: " +
            upscaleObjByKey.name +
            "," +
            " has max of " +
            upscaleObjByKey.maxCount +
            ", current: " +
            userEntryByKey;
          userMessage.level = USER_MESSAGE_LEVEL.WARN;
        }
      }
      return userMessage;
    };

    const userMessagesFromConfig: IUserMessage[] = R.compose(
      R.filter((mess: any) => mess.message.length > 0),
      R.map(isAgeGroupConfigOk),
      R.map((key) => key + "")
    )(R.keys(upscaleObj)) as IUserMessage[];

    return userMessagesFromConfig;
  }

  public getAthleteAgeGroupCountObj(
    compEventTeam: ICompEventTeam,
    upscaleAgeKeys: number[]
  ): IObjectKeyType<number> {
    return compEventTeam.athletes.reduce((accum, ath: IAthleteSummary) => {
      let ageGroupIdForComp: string = "NOT_YET_DEFINED";
      if (ath.ageInfo.ageGroups) {
        const athAgeGroupIds = ath.ageInfo.ageGroups.map(
          (ageGroup: IAgeGroupBase) => {
            return ageGroup.id;
          }
        );
        const ageGroupOverlap = R.intersection(upscaleAgeKeys, athAgeGroupIds);
        // console.log("upscaleValidateCompEventTeam athAgeGroupIds/ageGroupOverlap:", {athAgeGroupIds, ageGroupOverlap});
        //  We are ONLY taking 1st overlap, there should "never" be more than 1.
        ageGroupIdForComp =
          ageGroupOverlap.length > 0
            ? ageGroupOverlap[0] + ""
            : "NO_AGE_GROUP_OVERLAP";
      } else {
        //  For whatever reason, no ageGroups
        ageGroupIdForComp = ath.ageInfo.ageGroup.id + "";
      }

      if (!accum[ageGroupIdForComp]) {
        accum[ageGroupIdForComp] = 0;
      }
      accum[ageGroupIdForComp] = accum[ageGroupIdForComp] + 1;
      return accum;
    }, {} as IObjectKeyType<number>);
  }

  public isTeamNameUnique(
    eventTeamHeader: IEventTeamHeader,
    compEventTeam: ICompEventTeamBase
  ): boolean {
    if (R.isEmpty(compEventTeam.teamName)) {
      return false;
    }
    if (R.isNil(eventTeamHeader.compEventTeams)) {
      return true;
    }
    if (compEventTeam.id > 0) {
      return true;
    }
    return eventTeamHeader.compEventTeams.reduce(
      (accum: boolean, evtTeam: ICompEventTeam) => {
        if (!accum) {
          return accum;
        }
        return !(
          evtTeam.teamName.toLowerCase() ===
          compEventTeam.teamName.toLowerCase()
        );
      },
      true
    );
  }

  public updateTeamHeaderWithCompEventTeam(
    eventTeamHeader: IEventTeamHeader,
    compEventTeam: ICompEventTeam
  ): IEventTeamHeader {
    eventTeamHeader = R.clone(eventTeamHeader);
    const indexEvent = eventTeamHeader.compEventTeams.reduce(
      (accum, evtTeam, index) => {
        if (evtTeam.id === compEventTeam.id) {
          accum = index;
        }
        return accum;
      },
      -1
    );
    if (indexEvent > -1) {
      eventTeamHeader.compEventTeams[indexEvent] = compEventTeam;
    } else {
      eventTeamHeader.compEventTeams.push(compEventTeam);
    }
    return eventTeamHeader;
  }

  public athletesInTargetAgeGroupCount(
    eventTeamHeader: IEventTeamHeader,
    compEventTeam: ICompEventTeam,
    checkForInTarget: boolean
  ): IAthleteSummary[] {
    const athWithTargetAgeGroup = compEventTeam.athletes
      .filter((athlete: IAthleteSummary) => athlete.id > 0)
      .filter((athlete: IAthleteSummary) => {
        const athGroupIds = athlete.ageInfo.ageGroups.map(
          (ageGroup: IAgeGroup) => {
            return ageGroup.id;
          }
        );
        const index: number = athGroupIds.indexOf(eventTeamHeader.ageGroup.id);
        return checkForInTarget ? index > -1 : index === -1;
      });
    return athWithTargetAgeGroup;
  }

  /**
   * If you want to work out for a given user.
   * @param eventTeamHeader
   * @param athlete
   */
  public userInHowManyTeams(
    eventTeamHeader: IEventTeamHeader,
    athlete: IAthleteSummary
  ): ICompEventTeam[] {
    return eventTeamHeader.compEventTeams.reduce(
      (accum: ICompEventTeam[], compEvenTTeam: ICompEventTeam) => {
        const isUserInTeam: boolean = compEvenTTeam.athletes.reduce(
          (acc: boolean, ath: IAthleteSummary) => {
            return ath.id === athlete.id ? true : acc;
          },
          false
        );
        if (isUserInTeam) {
          accum.push(compEvenTTeam);
        }
        return accum;
      },
      [] as ICompEventTeam[]
    );
  }

  /**
   * If you want to work it out for whole EventTeamHeader
   * @param eventTeamHeader
   */
  public athletesInHowManyTeamsObj(
    eventTeamHeader: IEventTeamHeader,
    initialState: IObjectKeyType<IAthleteEventCompSummary[]> = {},
    compEventTeam: ICompEventTeam = { id: -1 } as ICompEventTeam
  ): IObjectKeyType<IAthleteEventCompSummary[]> {
    if (this.getTeamType(compEventTeam) === "LEAGUE") {
      return initialState;
    }

    // compEventTeam = R.isNil(compEventTeam) ? {id: -1} as ICompEventTeam : compEventTeam;
    eventTeamHeader = R.clone(eventTeamHeader);

    return eventTeamHeader.compEventTeams.reduce(
      (accum, compEvtTeam: ICompEventTeam) => {
        if (this.getTeamType(compEventTeam) !== "LEAGUE") {
          compEvtTeam.athletes.reduce((acc, ath: IAthleteSummary) => {
            const athKey = ath.id + "";
            if (!acc[athKey]) {
              acc[athKey] = [] as IAthleteEventCompSummary[];
            }

            //  If compEventTeam passed in, then don't include this in the count
            if (compEventTeam.id !== compEvtTeam.id) {
              acc[athKey].push({
                athId: ath.id,
                athName: ath.firstName + " " + ath.surName,
                eventId: eventTeamHeader.id,
                eventName: eventTeamHeader.eventName,
                teamId: compEvtTeam.id,
                teamName: compEvtTeam.teamName,
              } as IAthleteEventCompSummary);
            }
            return acc;
          }, accum as IObjectKeyType<IAthleteEventCompSummary[]>);
        }

        return accum;
      },
      initialState
    );
  }

  public getTeamType(compEventTeam: ICompEventTeam): ShowFormType {
    if (compEventTeam.formRows && compEventTeam.formRows.length > 0) {
      return "LEAGUE";
    }
    return "DEFAULT";
  }

  public athletesInHowManyEventTeamsObj(
    eventTeamHeaders: IEventTeamHeader[],
    compEventTeam: ICompEventTeam = { id: -1 } as ICompEventTeam
  ): IObjectKeyType<IAthleteEventCompSummary[]> {
    if (this.getTeamType(compEventTeam) === "LEAGUE") {
      return {};
    }

    compEventTeam = R.isNil(compEventTeam)
      ? ({ id: -1 } as ICompEventTeam)
      : compEventTeam;
    eventTeamHeaders = R.clone(eventTeamHeaders);
    return eventTeamHeaders.reduce(
      (accum, eventTeamHeader: IEventTeamHeader) => {
        accum = this.athletesInHowManyTeamsObj(
          eventTeamHeader,
          accum,
          compEventTeam
        );
        return accum;
      },
      {}
    );
  }

  /**
   * Try and get for age group,if none found, check for all.
   * @param ageGroupId
   * @param compShedRuleConfig
   */
  public getCompRuleForAgeGroup(
    ageGroupId: number,
    compShedRuleConfig: ICompShedRuleConfig[]
  ): ICompShedRuleConfig | undefined {
    compShedRuleConfig = R.clone(compShedRuleConfig);
    function filterIdAgeGroup(config: ICompShedRuleConfig) {
      return config.agid === ageGroupId;
    }

    /*
    const ruleForAgeGroup: ICompShedRuleConfig = R.compose(
      R.head() ,
      R.filter(filterIdAgeGroup)
    )(compShedRuleConfig) as any as ICompShedRuleConfig;
*/

    const ruleForAgeGroup = compShedRuleConfig.find((config) => {
      return filterIdAgeGroup(config);
    });

    function filterIdAll(config: ICompShedRuleConfig) {
      return config.agid === 0;
    }
    /*
    const ruleForAall: ICompShedRuleConfig = R.compose(
      R.head(),
      R.filter(filterIdAll)
    )(compShedRuleConfig);
     */

    const ruleForAall = compShedRuleConfig.find((config) => {
      return filterIdAll(config);
    });

    return ruleForAgeGroup ? ruleForAgeGroup : ruleForAall;
  }

  public getLabelForAthleteAutoComplete(athlete: IAthleteSummary): string {
    if (athlete.id === null || athlete.id === 0) {
      return "";
    }
    const label =
      athlete.firstName +
      " " +
      athlete.surName +
      " " +
      this.getAthleteDescriptorRight(athlete);
    return label;
  }

  public getAthleteDescriptorRight(athlete: IAthleteSummary) {
    return (
      "(" +
      (athlete.URN
        ? (athlete.aocode ? athlete.aocode + "-" : "") + athlete.URN
        : "") +
      ") " +
      athlete.ageInfo.ageGroup.shortName +
      ", " +
      athlete.club
    );
  }

  public getAthleteDescriptorRightV2(
    athlete: IAthleteSummary,
    options: Pick<IEventTeamCe, "singleClub">
  ) {
    const hasUrn = athlete.URN && athlete.URN.toString().length > 0;

    // if single club, don't show club, since all from same club!
    const ageAndClub =
      athlete.ageInfo.ageGroup.shortName +
      (athlete.club && !options.singleClub ? ", " + athlete.club : "");
    if (hasUrn) {
      return (
        "(" + (athlete.aocode ? athlete.aocode + "-" : "") + ") " + ageAndClub
      );
    }
    return ageAndClub;
  }

  public userAccessMessage(userInfo: IUserInfo): string {
    const areaMessagesState = {
      regions: [],
      counties: [],
    } as IObjectKeyTypeArray<string>;
    const areaMessages: IObjectKeyTypeArray<string> = userInfo.areas.reduce(
      (accum, area) => {
        area.entityName === "Region"
          ? accum.regions.push(area.areaname)
          : accum.counties.push(area.areaname);
        return accum;
      },
      areaMessagesState
    );

    const regionMessage =
      areaMessages.regions.length > 0
        ? "Region(s): " + areaMessages.regions.sort().join(", ")
        : "";
    const countyMessage =
      areaMessages.counties.length > 0
        ? "County(s): " + areaMessages.counties.sort().join(", ")
        : "";

    const clubMessagesState = {
      clubs: [],
      schools: [],
    } as IObjectKeyTypeArray<string>;
    const clubMessages: IObjectKeyTypeArray<string> = userInfo.clubs.reduce(
      (accum, club) => {
        club.clubtype === "C"
          ? accum.clubs.push(club.Clubname)
          : accum.schools.push(club.Clubname);
        return accum;
      },
      clubMessagesState
    );

    const clubMessage =
      clubMessages.clubs.length > 0
        ? "Club(s): " + clubMessages.clubs.sort().join(", ")
        : "";
    const schoolMessage =
      clubMessages.schools.length > 0
        ? "School(s): " + clubMessages.schools.sort().join(", ")
        : "";

    return (
      (regionMessage.length > 0 ? regionMessage + "." : "") +
      (countyMessage.length > 0 ? "  " + countyMessage + "." : "") +
      (clubMessage.length > 0 ? "  " + clubMessage + "." : "") +
      (schoolMessage.length > 0 ? "  " + schoolMessage + "." : "")
    );
  }

  public getTeamEventsHaveSecurity(
    eventTeamHeader: IEventTeamHeader[]
  ): boolean {
    return eventTeamHeader.reduce((accum: boolean, evtHeader) => {
      if (this.getTeamEventHasSecurity(evtHeader)) {
        accum = true;
      }
      return accum;
    }, false);
  }

  public getTeamEventHasSecurity(eventTeamHeader: IEventTeamHeader): boolean {
    const security: ISecurity = this.getEventSecurity(eventTeamHeader);
    return security.clubs && security.clubs.length > 0
      ? true
      : false || (security.counties && security.counties.length > 0)
      ? true
      : false || (security.regions && security.regions.length > 0)
      ? true
      : false;
  }

  public getEventSecurity(eventTeamHeader: IEventTeamHeader): ISecurity {
    const security: ISecurity = {
      clubs: [],
      counties: [],
      regions: [],
    };
    if (!eventTeamHeader.ceoptions.security) {
      return security;
    }
    const keys = Object.keys(eventTeamHeader.ceoptions.security);
    if (keys.length === 0) {
      return security;
    }
    security.clubs = eventTeamHeader.ceoptions.security.clubs
      ? eventTeamHeader.ceoptions.security.clubs
      : [];
    security.counties = eventTeamHeader.ceoptions.security.counties
      ? eventTeamHeader.ceoptions.security.counties
      : [];
    security.regions = eventTeamHeader.ceoptions.security.regions
      ? eventTeamHeader.ceoptions.security.regions
      : [];
    return security;
  }

  public doesUserHaveAccessToThisEvent(
    userInfo: IUserInfo,
    eventTeamHeader: IEventTeamHeader
  ): any {
    if (this.getTeamEventHasSecurity(eventTeamHeader)) {
      const userInfoBaseObjs: IBase[] = userInfo.clubs ? userInfo.clubs : [];

      if (userInfoBaseObjs.length > 0) {
        const hasClubSecurity =
          eventTeamHeader.ceoptions.security.clubs &&
          eventTeamHeader.ceoptions.security.clubs.length > 0
            ? true
            : false;
        if (hasClubSecurity) {
          return true;
        }
      }

      const userInfoAreaObjs: IAreaUserInfo[] = userInfo.areas
        ? userInfo.areas
        : [];
      if (userInfoAreaObjs.length > 0) {
        const hasCountySecurity =
          eventTeamHeader.ceoptions.security.counties &&
          eventTeamHeader.ceoptions.security.counties.length > 0
            ? true
            : false;
        if (hasCountySecurity) {
          return true;
        }
      }

      const userInfoOrgaObjs: IOrg[] = userInfo.orgs ? userInfo.orgs : [];
      if (userInfoOrgaObjs.length > 0) {
        const hasRegionSecurity =
          eventTeamHeader.ceoptions.security.regions &&
          eventTeamHeader.ceoptions.security.regions.length > 0
            ? true
            : false;
        if (hasRegionSecurity) {
          return true;
        }
      }

      //  UserInfo has no security
      return false;
    }
    //  event has no security
    return true;
  }

  public getEventSecurityLabel(eventTeamHeader: IEventTeamHeader): string {
    const security: ISecurity = this.getEventSecurity(eventTeamHeader);
    const keyMap: Record<string, string> = {
      clubs: "club",
      counties: "county",
      regions: "region",
    };
    const label = Object.keys(security).reduce((accum, key) => {
      // @ts-ignore
      const base = security[key] as IBase[];
      if (base.length > 0) {
        accum +=
          (accum.length > 0 ? ", " : "") + (keyMap[key] ? keyMap[key] : key);
      }
      return accum;
    }, "");
    return label;
  }

  public getEntitiesForEvent(
    userInfo: IUserInfo,
    isSchoolComp: boolean,
    eventTeamHeader: IEventTeamHeader
  ): IEntity[] {
    let entities: IEntity[] = [];
    const userEntities = configService.getEntitiesFromUserInfo(userInfo);
    const security: ISecurity = this.getEventSecurity(eventTeamHeader);
    if (
      security.clubs &&
      security.clubs.length > 0 &&
      userInfo.clubs &&
      userInfo.clubs.length > 0
    ) {
      const entitiesClub = userEntities.filter((ent) => {
        const isClub = ent.entityName === "Club";
        const isSchool = ent.entityName === "School";
        if (isClub && !isSchoolComp) {
          return true;
        }
        if (isSchool && isSchoolComp) {
          return true;
        }
        return false;
      });
      entities = entities.concat(...entitiesClub);
    }

    if (
      security.counties &&
      security.counties.length > 0 &&
      userInfo.areas &&
      userInfo.areas.length > 0
    ) {
      const entitiesArea = userEntities.filter((ent) => {
        return ent.entityName === "County";
      });
      entities = entities.concat(...entitiesArea);
    }

    if (
      security.regions &&
      security.regions.length > 0 &&
      userInfo.orgs &&
      userInfo.orgs.length > 0
    ) {
      const entitiesOrgs = userEntities.filter((ent) => {
        return ent.entityName === "Region";
      });
      entities = entities.concat(...entitiesOrgs);
    }

    return entities;
  }

  // public getEventTeamUserSecuritySummary(userInfo: IUserInfo, eventTeamHeader: IEventTeamHeader): IEventTeamUserSecuritySummary {
  //     // const hasSecurity = this.getTeamEventHasSecurity(eventTeamHeader);
  //     return {
  //         userHasAccess: this.doesUserHaveAccessToThisEvent(userInfo, eventTeamHeader),
  //         label: this.getEventSecurityLabel(eventTeamHeader)
  //     };
  // }

  public filterEventTeamHeaders(
    eventTeamHeaderFilter: IEventTeamHeaderFilter | IEventTeamHeaderFilterV2,
    eventTeamHeaders: IEventTeamHeader[],
    applyFilterHasTeams: boolean = true
  ): IEventTeamHeader[] {
    const eventGroupName = (eventTeamHeader: IEventTeamHeader) => {
      if (eventTeamHeaderFilter.eventName.length === 0) {
        return true;
      }
      return (
        eventTeamHeader.eventGroup.name
          .toLowerCase()
          .indexOf(eventTeamHeaderFilter.eventName.toLowerCase()) > -1
      );
    };

    const gender = (eventTeamHeader: IEventTeamHeader) => {
      if ([GENDER.UNKNOWN, "ALL"].indexOf(eventTeamHeaderFilter.gender) > -1) {
        return true;
      }
      return eventTeamHeader.gender === eventTeamHeaderFilter.gender;
    };

    const ageGroup = (eventTeamHeader: IEventTeamHeader) => {
      if (eventTeamHeaderFilter.ageGroup.id === 0) {
        return true;
      }
      return eventTeamHeader.ageGroup.id === eventTeamHeaderFilter.ageGroup.id;
    };

    const hasTeams = (eventTeamHeader: IEventTeamHeader) => {
      if (!applyFilterHasTeams) {
        return true;
      }
      //  If not applying "applyFilterHasTeams", basically just apply stuff so
      //  can tell if there is a team to "add" for that combo.
      if (eventTeamHeaderFilter.hasTeams === "ALL") {
        return true;
      }
      if (eventTeamHeaderFilter.hasTeams === "WITH") {
        return eventTeamHeader.compEventTeams.length > 0;
      }
      if (eventTeamHeaderFilter.hasTeams === "WITHOUT") {
        return eventTeamHeader.compEventTeams.length === 0;
      }
      return true;
    };

    const freeText = (eventTeamHeader: IEventTeamHeader) => {
      const filterOptions = eventTeamHeaderFilter as IEventTeamHeaderFilterV2;
      if (!filterOptions.freeText) {
        return true;
      }
      if (filterOptions.freeText.length === 0) {
        return true;
      }

      // check for team name match
      const teamNameMatch = eventTeamHeader.compEventTeams.some((team) => {
        return (
          team.teamName
            .toLowerCase()
            .indexOf(filterOptions.freeText.toLowerCase()) > -1
        );
      });

      // get team type from event header
      const teamType = eventTeamHeader.ceoptions.eventTeam.formType;

      // check for athlete name match.  Be aware there are 3 types of team:
      //  1.  Default
      //  2.  School
      //  3.  League
      //  THe athletes are stored differently for each type.
      const athleteNameMatch = eventTeamHeader.compEventTeams.some((team) => {
        // bet athletes from team by teamType
        if (teamType === "LEAGUE") {
          try {
            return team.formRows.some((formRow) => {
              if (!formRow.athlete.firstName) {
                return false;
              }
              try {
                return (
                  formRow.athlete.firstName
                    .toLowerCase()
                    .indexOf(filterOptions.freeText.toLowerCase()) > -1 ||
                  formRow.athlete.surName
                    .toLowerCase()
                    .indexOf(filterOptions.freeText.toLowerCase()) > -1
                );
              } catch (error) {
                console.error(
                  "EventTeamService.filterEventTeamHeaders LEAGUE",
                  {
                    error,
                    formRow,
                  }
                );
                return false;
              }
            });
          } catch (error) {
            console.error("EventTeamService.filterEventTeamHeaders LEAGUE", {
              error,
              team,
            });
            return false;
          }
        }

        if (teamType === "SCHOOL") {
          try {
            return (team as unknown as ICompEventTeamSchool).athletes.some(
              (schoolTeamAthlete: ISchoolTeamAthlete) => {
                return (
                  schoolTeamAthlete.name
                    .toLowerCase()
                    .indexOf(filterOptions.freeText.toLowerCase()) > -1
                );
              }
            );
          } catch (error) {
            console.error("EventTeamService.filterEventTeamHeaders SCHOOL", {
              error,
              team,
            });
            return false;
          }
        }

        if (teamType === "DEFAULT") {
          return team.athletes.some((athlete) => {
            return (
              athlete.firstName
                .toLowerCase()
                .indexOf(filterOptions.freeText.toLowerCase()) > -1 ||
              athlete.surName
                .toLowerCase()
                .indexOf(filterOptions.freeText.toLowerCase()) > -1
            );
          });
        }

        return false;
      });

      return teamNameMatch || athleteNameMatch;
    };

    const result = eventTeamHeaders.filter(
      (eventTeamHeader: IEventTeamHeader) => {
        return (
          eventGroupName(eventTeamHeader) &&
          gender(eventTeamHeader) &&
          ageGroup(eventTeamHeader) &&
          hasTeams(eventTeamHeader) &&
          freeText(eventTeamHeader)
        );
      }
    );
    console.log("EventTeamService.filterEventTeamHeaders", result);
    return result;
  }

  public getTeamLimits(
    eventTeamHeader: IEventTeamHeader,
    userEntity: IEntity,
    userInfo: IUserInfo
  ): TeamLimits {
    const maxTeamsReached = this.maxTeamsReached(eventTeamHeader);

    return {
      maxTeamsReached: maxTeamsReached,
      getTeamEventHasSecurity: this.getTeamEventHasSecurity(eventTeamHeader),
      isEntitySelected: userEntity.id > 0,
      canAddTeam: this.canAddTeam(eventTeamHeader),
      doesUserHaveAccessToThisEvent: this.doesUserHaveAccessToThisEvent(
        userInfo,
        eventTeamHeader
      ),
    };
  }

  public canAddTeamFromTeamLimits(
    eventTeamHeader: IEventTeamHeader,
    teamLimits: TeamLimits
  ): boolean {
    if (teamLimits.maxTeamsReached) {
      return false;
    }

    if (teamLimits.getTeamEventHasSecurity) {
      return teamLimits.isEntitySelected;
    }

    if (eventTeamHeader.isDisabled) {
      return false;
    }
    return true;
  }

  public getPaidStatus(eventTeamHeader: IEventTeamHeader) {
    return eventTeamHeader.compEventTeams.reduce<{
      paid: number;
      cart: number;
    }>(
      (accum, team) => {
        if (team.paid > 0) {
          accum.paid++;
        } else {
          //  As of now, we don't get "order"
          accum.cart++;
        }

        // return (
        //   team. &&
        //   props.athleteCompSchedRuleEvent.paid === 0
        // );
        return accum;
      },
      {
        paid: 0,
        cart: 0,
      }
    );
  }
}
