<template>
  <div class="e4s-card-v1">
    <div class="comp-event-team-header-wrapper">
      <div class="row">
        <div class="col s10 m10 l10">
          <div class="comp-event-team-header-label">
            <!--<span v-text="index"></span>-->
            <i class="material-icons small e4s-force-img-vert-align-middle"
              >event</i
            >
            <!--                        <span v-text="eventTeamHeader.eventName"></span>-->
            <span v-text="eventTeamHeader.eventGroup.name"></span>:
            <span v-text="getGender"></span>
            <span v-text="eventTeamHeader.ageGroup.name"></span>
            <span v-text="getXiText"></span>
            <span
              v-text="eventTeamHeader.IsOpen === 0 ? ' (Closed)' : ''"
            ></span>
          </div>
        </div>

        <div class="col s2 m2 l2">
          <div class="comp-event-team-header-label right" v-if="getShowPrice">
            <span v-text="getPrice"></span>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col s4 m4 l4">
          <span v-text="getEventTime"></span>
          <span> </span>
          <span v-text="getEventDate"></span>
        </div>

        <div class="col s8 m8 l8">
          <div class="right">
            <span v-if="getShowMaxEventTeams">
              Max Teams:
              <span v-text="getOptionsMaxEventTeams"></span>
            </span>
            <div class="e4s-force-inline-block" v-if="getShowAthleteMinMax">
              Athlete min:
              <span v-text="getTeamAthleteMin"></span>
              max:
              <span v-text="getTeamAthleteMax"></span>
            </div>
          </div>
        </div>
      </div>

      <div class="row comp-event-team-header-footer">
        <div class="col s12 m12 l12">
          <div
            v-if="
              eventTeamHeader.compEventTeams &&
              eventTeamHeader.compEventTeams.length === 0
            "
            class="e4s-force-inline-block"
          >
            <div>No teams entered</div>
          </div>
          <div v-else class="e4s-force-inline-block">
            <!--            <a-->
            <!--              v-if="!displayMoreInfoComponent"-->
            <!--              class="comp-event-team-anchor-buttons"-->
            <!--              href="#"-->
            <!--              @click.prevent="showMoreInfo(true)"-->
            <!--            >-->
            <!--              View-->
            <!--              <span v-text="eventTeamHeader.compEventTeams.length"></span>-->
            <!--              Team(s)...-->
            <!--            </a>-->
            <PrimaryLink
              v-if="!displayMoreInfoComponent"
              :link-text="
                'View ' + eventTeamHeader.compEventTeams.length + ' Team(s)...'
              "
              @onClick="showMoreInfo(true)"
            />
          </div>

          <PrimaryLink
            v-if="showCloseTeams"
            link-text="Hide Teams..."
            @onClick="showMoreInfo(false)"
          />
          <!--          <a-->
          <!--            v-if="showCloseTeams"-->
          <!--            class="comp-event-team-anchor-buttons"-->
          <!--            href="#"-->
          <!--            @click.prevent="showMoreInfo(false)"-->
          <!--          >-->
          <!--            Close Teams...-->
          <!--          </a>-->

          <div class="help-text-wrapper">
            <div class="help-text" v-if="hasCompEventHelpText">
              <div
                class="help-text-content"
                v-html="eventTeamHeader.ceoptions.helpText"
              ></div>
            </div>
          </div>

          <div v-show="getShowEntitySelect">
            <EntitySelect
              class="e4s-select--required"
              :please-select="true"
              :user-entities="userEntities"
              v-on:onSelected="onUserEntitySelected"
            >
            </EntitySelect>
          </div>

          <div class="right" v-if="!canAddTeam">
            <div class="comp-event-team-full">
              <span v-text="getWarningMessage"></span>
            </div>
          </div>
          <!--                    Warn: <span v-text="getWarningMessage"></span>-->

          <!--                    getShowCancelTeamButton-->
          <div class="right" v-if="canAddTeam">
            <a
              href="#"
              class="comp-event-team-anchor-buttons"
              v-show="false"
              v-on:click.prevent="closeAddCompEventTeam"
            >
              Cancel Team
            </a>
            <!--{{this.eventTeamHeader.compEventTeams.length}}{{eventTeamHeader.maxTeams}}{{canAddTeam()}}-->
            <span
              v-text="getEntityForUserLabel"
              v-if="!hasClubCompInfoCompetition"
            ></span>
            <ButtonGenericV2
              v-show="getShowAddTeamButton"
              @click="addCompEventTeam"
              text="Add Team"
            />
            <!--            <button-->
            <!--              v-show="getShowAddTeamButton"-->
            <!--              class="btn waves-effect waves green xxx-btn-small"-->
            <!--              v-on:click.stop="addCompEventTeam()"-->
            <!--            >-->
            <!--              Add Team-->
            <!--            </button>-->
          </div>
        </div>
      </div>
    </div>

    <div v-if="isAdmin">
      <div class="row">
        <div class="col s12 m12 l12">ADMIN SECTION</div>
      </div>
      <div class="row">
        <div class="col s12 m12 l12">
          <span v-text="getEventSecurityLabel" />
        </div>
      </div>
      <div class="row">
        <div class="col s12 m12 l12">
          User Entities: <span v-text="getUserEntityLabel" />
        </div>
      </div>
    </div>

    <div class="row" v-if="showNewTeam">
      <div class="col s12 m12 l12">
        <school-team-form
          v-if="displayWhichFormType === 'SCHOOL'"
          :isLoading="isLoading"
          :competition="competition"
          :event-team-header="eventTeamHeader"
          :comp-event-team-school-prop="editCompEventTeam"
          :user-entity="getEntityForUser"
          :is-new="true"
          v-on:cancel="cancelTeam"
          v-on:delete="deleteCompEventTeam"
          v-on:submit="submitSchool"
          @addUserCart="doShowAddUserCart"
        >
        </school-team-form>

        <event-team-default
          v-if="displayWhichFormType === 'DEFAULT'"
          :isLoading="isLoading"
          :competition="competition"
          :event-team-header="eventTeamHeader"
          :comp-event-team-prop="editCompEventTeam"
          :user-entity="getEntityForUser"
          :is-new="true"
          v-on:cancel="cancelTeam"
          v-on:delete="deleteCompEventTeam"
          v-on:submit="submitDefault"
          @addUserCart="doShowAddUserCart"
        >
        </event-team-default>

        <league-team-form
          v-if="displayWhichFormType === 'LEAGUE'"
          :isLoading="isLoading"
          :competition="competition"
          :event-team-header="eventTeamHeader"
          :comp-event-team-prop="editCompEventTeam"
          :user-entity="getEntityForUser"
          :is-new="true"
          v-on:cancel="cancelTeam"
          v-on:delete="deleteCompEventTeam"
          v-on:submit="submitDefault"
        >
        </league-team-form>
      </div>
    </div>

    <div class="row" v-if="addToUserCartOutput">
      <div class="col s12 m12 l12">
        Put in cart for user:
        {{ addToUserCartOutput.userProfile.user.displayName }}
      </div>
    </div>

    <div class="row">
      <div class="e4s-section-padding-separator"></div>
    </div>

    <div v-if="displayMoreInfoComponent">
      <!--            <div class="row">-->
      <!--                <div class="col s12 m12 l12">-->
      <!--                    displayWhichFormType: {{displayWhichFormType}}-->
      <!--                </div>-->
      <!--            </div>-->

      <div class="row" v-if="!showNewTeam">
        <div class="col s12 m12 l12">
          <div
            v-if="
              eventTeamHeader.compEventTeams &&
              eventTeamHeader.compEventTeams.length === 0
            "
          >
            <div>No teams entered</div>
          </div>

          <div v-for="compEventTeam in getCompEventTeamSorted">
            <div
              v-if="compEventTeam.teamName && compEventTeam.teamName.length > 0"
            >
              <school-team-form
                v-if="displayWhichFormType === 'SCHOOL'"
                v-bind:isLoading="isLoading"
                v-bind:competition="competition"
                v-bind:comp-event-team-school-prop="compEventTeam"
                v-bind:event-team-header="eventTeamHeader"
                :user-entity="getEntityForUser"
                v-bind:is-new="false"
                v-on:edit="editTeam"
                v-on:delete="deleteCompEventTeam"
                v-on:submit="submitSchool"
              >
              </school-team-form>

              <event-team-default
                v-if="displayWhichFormType === 'DEFAULT'"
                v-bind:isLoading="isLoading"
                v-bind:competition="competition"
                v-bind:comp-event-team-prop="compEventTeam"
                v-bind:event-team-header="eventTeamHeader"
                :user-entity="getEntityForUser"
                v-bind:is-new="false"
                v-on:edit="editTeam"
                v-on:submit="submitDefault"
                v-on:delete="deleteCompEventTeam"
                @addUserCart="doShowAddUserCart"
              >
              </event-team-default>

              <league-team-form
                v-if="displayWhichFormType === 'LEAGUE'"
                v-bind:isLoading="isLoading"
                v-bind:competition="competition"
                v-bind:event-team-header="eventTeamHeader"
                v-bind:comp-event-team-prop="compEventTeam"
                :user-entity="getEntityForUser"
                v-bind:is-new="false"
                v-on:edit="editTeam"
                v-on:submit="submitDefault"
                v-on:delete="deleteCompEventTeam"
              >
              </league-team-form>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ModalV2
      :is-full-screen="$mq === VUE_MQ_SIZES.MOBILE.name"
      v-if="showAddToUserCart"
      :always-show-header-blank="true"
    >
      <div slot="body" style="margin: 8px; min-width: 300px; min-height: 500px">
        <AddToUserCart
          :product-id="editCompEventTeam.prodId.toString()"
          :only-add-to-user-cart="editCompEventTeam.prodId > 0"
          @cancel="cancelAddUserToTeam"
          @input="addUserToTeam"
          @addedToUserCart="addTeamToUserCart"
        />
      </div>
    </ModalV2>
  </div>
</template>

<script lang="ts">
import { format, parse } from "date-fns";
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import { GENDER, IServerResponse } from "../../common/common-models";
import { ICompetitionInfo } from "../../competition/competition-models";
import { IConfigApp, IEntity } from "../../config/config-app-models";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { IEventTeam, ShowFormType } from "../athletecompsched-models";
import { ATH_COMP_SCHED_STORE_CONST } from "../store/athleteCompSched-store";
import { COMP_EVENT_TEAMS_STORE_CONST } from "./comp-event-store";
import { CompEventTeamData } from "./comp-event-team-data";
import { EventTeamService } from "./event-team-service";
import {
  ICompEventTeam,
  IEventTeamHeader,
  ICompEventTeamBase,
} from "./event-teams-models";
import SchoolTeamForm from "./school/school-team-form.vue";
import EventTeamForm from "./event-team-form.vue";
import EventTeamDefault from "./event-team-default.vue";
import { ICompEventTeamSchool } from "./school/school-team-models";
import { SchoolTeamData } from "./school/school-team-data";
import { ResourceData } from "../../common/resource/resource-service";
import { SchoolTeamService } from "./school/school-team-service";
import { CommonService } from "../../common/common-service";
import LeagueTeamForm from "./league/league-team-form.vue";
import { ConfigService } from "../../config/config-service";
import EntitySelect from "../../config/entity/entity-select.vue";
import { ENTRY_STORE_CONST, IEntryStoreState } from "../../entry/entry-store";
import {
  IClubCompInfo,
  IClubCompInfoEntryIndiv,
  IClubCompInfoEntryTeam,
} from "../../entry/v2/schools/clubCompInfo-models";
import * as ClubCompInfoService from "../../entry/v2/schools/clubCompInfoService";
import { simpleClone, sortArray } from "../../common/common-service-utils";
import AddToUserCart from "../comp-event-actions/add-to-cart/AddToUserCart.vue";
import ModalV2 from "../../common/ui/layoutV2/modal/modal-v2.vue";
import { VUE_MQ_SIZES } from "../../index";
import { AddToUserCartOutput } from "../comp-event-actions/add-to-cart/add-to-user-cart-models";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import PrimaryLink from "../../common/ui/layoutV2/href/PrimaryLink.vue";

const commonService: CommonService = new CommonService();
const configService: ConfigService = new ConfigService();

@Component({
  name: "event-team-header",
  components: {
    PrimaryLink,
    ButtonGenericV2,
    ModalV2,
    AddToUserCart,
    EntitySelect,
    "event-team-default": EventTeamDefault,
    "event-team-form": EventTeamForm,
    "school-team-form": SchoolTeamForm,
    LeagueTeamForm,
  },
  computed: {
    VUE_MQ_SIZES() {
      return VUE_MQ_SIZES;
    },
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      clubCompInfo: (state: any) => state.entryForm.clubCompInfo,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
      userEntityStore: (state: any) => state.userEntity,
      userEntity: (state: any) => state.userEntity,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class EventTeamHeader extends Vue {
  public readonly configApp: IConfigApp;
  public readonly userEntity: IEntity;
  public readonly clubCompInfo: IClubCompInfo;

  @Prop({ default: -11 }) public index: number;
  @Prop() public readonly competition: ICompetitionInfo;
  @Prop() public readonly eventTeamHeader: IEventTeamHeader;
  // @Prop({
  //     default: () => {
  //         return configService.factoryEntity();
  //     }
  // }) public readonly userEntity: IEntity;
  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly userEntities: IEntity[];

  @Prop({
    default: false,
  })
  public readonly suppressAddTeam: boolean;

  public gender = GENDER;
  public eventTeamService: EventTeamService = new EventTeamService();
  public schoolTeamService: SchoolTeamService = new SchoolTeamService();
  public displayMoreInfoComponent: boolean = false;
  public compEventTeamNew: ICompEventTeam;
  public showNewTeam: boolean = false;
  public isLoading: boolean = false;
  public formType: ShowFormType = "DEFAULT";
  public userEntitySelected: IEntity = configService.factoryEntity();

  public editCompEventTeam: ICompEventTeamBase | null = null;
  public showAddToUserCart: boolean = false;

  public addToUserCartOutput: AddToUserCartOutput | null = null;

  public clubCompInfoEntryTeam:
    | IClubCompInfoEntryIndiv
    | IClubCompInfoEntryTeam
    | null = null;

  public created() {
    this.formType = this.eventTeamService.displayWhichFormType(
      this.eventTeamHeader
    );
    this.compEventTeamNew = this.eventTeamService.factoryGetCompEventTeam(
      this.eventTeamHeader,
      this.getEntityForUser
    );

    this.setUpEditObject();

    if (
      ClubCompInfoService.hasClubCompInfoCompetition(
        this.clubCompInfo as IClubCompInfo
      )
    ) {
      this.clubCompInfoEntryTeam =
        ClubCompInfoService.getEntryMatchByEventGroupId(
          this.eventTeamHeader.eventGroup.id.toString(),
          this.clubCompInfo
        );
    }
  }

  public get getEntityForUser(): IEntity {
    // if (ClubCompInfoService.hasClubCompInfoCompetition(this.clubCompInfo)) {
    //   return {
    //     clubType: "C",
    //     entityLevel: 2,
    //     entityName: "Club",
    //     id: this.clubCompInfo.configData.clubId,
    //     name: this.clubCompInfo.configData.clubName,
    //   };
    // }

    if (this.userEntitySelected.id > 0) {
      return { ...this.userEntitySelected };
    }
    if (this.userEntities.length === 1) {
      return { ...this.userEntities[0] };
    }
    return { ...this.userEntity };
  }

  public get getEntityForUserLabel(): string {
    const entity = this.getEntityForUser;
    return entity.id === 0
      ? "Public Access"
      : entity.entityName + " - " + entity.name;
  }

  @Watch("userEntities")
  public onUserEntitiesChanged(newValue: IEntity[]) {
    // if (this.userEntities.length === 1) {
    //     this.userEntity = {...newValue[0]};
    //     return;
    // }
    // this.userEntity = configService.factoryEntity();
  }

  public setUpEditObject() {
    let editCompEventTeam: ICompEventTeamBase =
      this.eventTeamService.factoryGetCompEventTeamBase(
        this.eventTeamHeader,
        this.getEntityForUser,
        this.clubCompInfo
      );
    if (this.formType === "DEFAULT") {
      editCompEventTeam = this.eventTeamService.factoryGetCompEventTeam(
        this.eventTeamHeader,
        this.getEntityForUser,
        this.clubCompInfo
      );
    }
    if (this.formType === "SCHOOL") {
      editCompEventTeam = this.schoolTeamService.factoryCompEventTeamSchool(
        this.eventTeamHeader,
        this.getEntityForUser,
        this.clubCompInfo
      );
    }
    editCompEventTeam.ceid = this.eventTeamHeader.id;
    this.editCompEventTeam = editCompEventTeam;
  }

  public get getEventDate() {
    return format(parse(this.eventTeamHeader.startdate), "Do MMM YY");
  }

  public get getEventTime() {
    let startTime = format(parse(this.eventTeamHeader.startdate), "HH:mm");
    startTime = startTime === "00:00" ? "TBC" : startTime;
    return startTime;
  }

  public get getGender() {
    // @ts-ignore
    if (this.eventTeamHeader.gender.toLocaleUpperCase() === "O") {
      return "";
    }

    // const eoptions: IEoptions = this.eventTeamHeader.eoptions;
    // if (eoptions && eoptions.gender === false) {
    //     return "" ;
    // }
    // @ts-ignore
    // const gender: GENDER = this.eventTeamHeader.gender
    //   ? this.eventTeamHeader.gender
    //   : this.eventTeamHeader.Gender;
    return commonService.getGenderLabel(this.eventTeamHeader.gender);
  }

  public get getEventTeamOptions(): IEventTeam {
    return this.eventTeamService.getEventTeamOptions(this.eventTeamHeader);
  }

  public get getTeamAthleteMin() {
    const eventTeam: IEventTeam = this.getEventTeamOptions;
    return eventTeam ? eventTeam.min : 0;
  }

  public get getTeamAthleteMax() {
    const eventTeam: IEventTeam = this.getEventTeamOptions;
    return eventTeam ? eventTeam.max : 0;
  }

  public get getShowAthleteMinMax() {
    return this.getTeamAthleteMin > 0 || this.getTeamAthleteMax > 0;
  }

  public showMoreInfo(showIt: boolean) {
    this.displayMoreInfoComponent = showIt;
  }

  public get showCloseTeams() {
    return this.displayMoreInfoComponent && !this.showNewTeam;
  }

  public get getShowPrice(): boolean {
    if (this.eventTeamHeader.ceoptions.rowOptions) {
      return this.eventTeamHeader.ceoptions.rowOptions.showPrice;
    }
    return false;
  }

  public get getPrice() {
    //  "€"
    return this.eventTeamHeader.price && this.eventTeamHeader.price.curPrice
      ? this.configApp.currency + this.eventTeamHeader.price.curPrice.toFixed(2)
      : "";
  }

  public get getOptionsMaxEventTeams(): number {
    return this.eventTeamService.getOptionsMaxEventTeams(this.eventTeamHeader);
  }

  public get getShowMaxEventTeams(): boolean {
    return this.getOptionsMaxEventTeams > 0;
  }

  public get canAddTeam(): boolean {
    // if (this.eventTeamHeader.ceoptions.excludeFromCntRule) {
    //   return true;
    // }

    // if (this.suppressAddTeam) {
    //   return false;
    // }

    if (this.eventTeamService.maxTeamsReached(this.eventTeamHeader)) {
      return false;
    }
    if (this.eventTeamHeader.ceoptions.excludeFromCntRule) {
      return true;
    }

    if (this.suppressAddTeam) {
      return false;
    }

    if (this.eventTeamService.getTeamEventHasSecurity(this.eventTeamHeader)) {
      if (this.userEntities.length > 1 && this.userEntitySelected.id > 0) {
        return true;
      }
      if (this.userEntities.length === 1 && this.userEntities[0].id > 0) {
        return true;
      }
    }
    return this.eventTeamService.canAddTeam(this.eventTeamHeader);
  }

  public get getWarningMessage(): string {
    if (this.eventTeamHeader.isDisabled) {
      if (this.doesUserHaveAccessToThisEvent) {
        // return "Please select from drop down above.";
        return "";
      }
      return this.eventTeamHeader.ceoptions.warningMessage;
    }
    return this.eventTeamService.maxTeamsReached(this.eventTeamHeader)
      ? "Max Teams Entered"
      : "";
  }

  public get doesUserHaveAccessToThisEvent(): boolean {
    return this.eventTeamService.doesUserHaveAccessToThisEvent(
      this.configApp.userInfo,
      this.eventTeamHeader
    );
  }

  public get getShowAddTeamButton(): boolean {
    return this.canAddTeam && !this.showNewTeam;
  }

  public get getShowCancelTeamButton(): boolean {
    return this.canAddTeam && this.showNewTeam;
  }

  public addCompEventTeam() {
    console.log("add team");
    const compEventTeam: ICompEventTeam =
      this.eventTeamService.factoryGetCompEventTeam(
        this.eventTeamHeader,
        this.getEntityForUser,
        this.clubCompInfo
      );
    this.compEventTeamNew = compEventTeam;

    this.setUpEditObject();

    // this.showMoreInfo(true);
    this.showNewTeam = true;
  }

  public editTeam(compEventTeam: ICompEventTeamBase) {
    this.editCompEventTeam = R.clone(compEventTeam);
    // this.showMoreInfo(true)

    this.showNewTeam = true;
  }

  public cancelTeam() {
    this.setUpEditObject();
    // this.showMoreInfo(false);
    this.showNewTeam = false;
  }

  public closeAddCompEventTeam() {
    this.showNewTeam = false;
    if (this.eventTeamHeader.compEventTeams.length === 0) {
      this.showMoreInfo(false);
    }
  }

  public submitDefault(compEventTeam: ICompEventTeam) {
    const compEventTeamData: CompEventTeamData = new CompEventTeamData();
    const compEventTeamStripped: ICompEventTeam =
      this.eventTeamService.stripEventCompEventTeam(compEventTeam);
    console.log(compEventTeam);
    this.submitTeam(compEventTeamStripped, compEventTeamData);
  }

  public submitSchool(compEventTeam: ICompEventTeamSchool) {
    const schoolTeamData: SchoolTeamData = new SchoolTeamData();
    this.submitTeam(compEventTeam, schoolTeamData);
  }

  public submitTeam(
    compEventTeam: ICompEventTeamBase,
    resourceData: ResourceData<ICompEventTeamBase>
  ) {
    let prom;
    if (compEventTeam.id > 0) {
      if (this.addToUserCartOutput) {
        // Organiser entering a team for another user.
        compEventTeam.userId = this.addToUserCartOutput.userProfile.user.id;
        compEventTeam.userName =
          this.addToUserCartOutput.userProfile.user.displayName;

        // const userEntity: IEntity =
        //   this.addToUserCartOutput.userEntity as any as IEntity;
        compEventTeam.entity = this.addToUserCartOutput.userEntity as any;
      }

      prom = resourceData.update(compEventTeam);
    } else {
      // if (ClubCompInfoService.hasClubCompInfoCompetition(this.clubCompInfo)) {
      //   compEventTeam.entityLevel = this.clubCompInfo.configData.categoryId;
      //   compEventTeam.entityId = this.getEntityForUser.id;
      // } else {
      //   compEventTeam.entityLevel = this.getEntityForUser.entityLevel;
      //   compEventTeam.entityId = this.getEntityForUser.id;
      // }
      compEventTeam.entityLevel = this.getEntityForUser.entityLevel;
      compEventTeam.entityId = this.getEntityForUser.id;

      if (this.addToUserCartOutput) {
        // Organiser entering a team for another user.
        compEventTeam.userId = this.addToUserCartOutput.userProfile.user.id;
        compEventTeam.userName =
          this.addToUserCartOutput.userProfile.user.displayName;

        compEventTeam.entityId = this.addToUserCartOutput.userEntity.id;
        compEventTeam.entityLevel =
          this.addToUserCartOutput.userEntity.entityLevel;
      }

      prom = resourceData.create(compEventTeam);
    }

    this.isLoading = true;
    prom
      .then((response: IServerResponse<ICompEventTeamBase>) => {
        this.isLoading = false;
        if (response.errNo > 0) {
          messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR);
          return;
        }
        messageDispatchHelper(
          response.message && response.message.length > 0
            ? response.message
            : "Team " + compEventTeam.teamName + " saved to Order Summary",
          USER_MESSAGE_LEVEL.INFO
        );

        this.standardSuccess();
        this.closeAddCompEventTeam();

        if (this.addToUserCartOutput) {
          this.addToUserCartOutput = null;
        }
      })
      .catch((error) => {
        this.standardCatch(error);
        return;
      });
  }

  public deleteCompEventTeam(compEventTeam: ICompEventTeam) {
    const compEventTeamData: CompEventTeamData = new CompEventTeamData();

    if (this.configApp.userId !== compEventTeam.userId) {
      messageDispatchHelper(
        "Team " +
          compEventTeam.teamName +
          " not deleted, entered by: " +
          compEventTeam.userName,
        USER_MESSAGE_LEVEL.WARN
      );
      return false;
    }

    this.isLoading = true;
    return compEventTeamData
      .delete(compEventTeam.id)
      .then((response: IServerResponse<ICompEventTeam>) => {
        this.isLoading = false;
        if (response.errNo > 0) {
          messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR);
          return;
        }
        messageDispatchHelper(
          "Team " + compEventTeam.teamName + " deleted.",
          USER_MESSAGE_LEVEL.INFO
        );
        this.standardSuccess();
        return;
      })
      .catch((error) => {
        this.standardCatch(error);
        return;
      });
  }

  public doShowAddUserCart(compEventTeam: ICompEventTeam) {
    this.editCompEventTeam = simpleClone(compEventTeam);
    this.showAddToUserCart = true;
  }

  public cancelAddUserToTeam() {
    this.showAddToUserCart = false;
    this.addToUserCartOutput = null;
  }

  public addUserToTeam(addToUserCartOutput: AddToUserCartOutput) {
    this.addToUserCartOutput = addToUserCartOutput;

    // this.userEntitySelected = output.userEntityBase;
    // this.compEventTeamNew.userId = addToUserCartOutput.userProfile.user.id;
    // this.compEventTeamNew.userName =
    //   addToUserCartOutput.userProfile.user.displayName;
    //
    // this.compEventTeamNew.entityId = addToUserCartOutput.userEntity.id;
    // this.compEventTeamNew.entityLevel =
    //   addToUserCartOutput.userEntity.entityLevel;
    //
    // this.userEntitySelected = addToUserCartOutput.userEntity;
    this.showAddToUserCart = false;
  }

  public addTeamToUserCart() {
    this.showAddToUserCart = false;
    this.standardSuccess();
  }

  public standardSuccess() {
    this.$store.dispatch(
      COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME +
        "/" +
        COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_ACTIONS_GET_TEAM_HEADERS,
      { compId: this.competition.id }
    );

    this.$store.dispatch(
      ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_GET_CART
    );

    if (
      ClubCompInfoService.hasClubCompInfoCompetition(
        this.clubCompInfo as IClubCompInfo
      )
    ) {
      this.$store.dispatch(
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
          "/" +
          ENTRY_STORE_CONST.ENTRY_STORE_ACTIONS_GET_CLUB_COMP_INFO,
        { compId: this.competition.id }
      );
    }
  }

  public get displayWhichFormType() {
    return this.eventTeamService.displayWhichFormType(this.eventTeamHeader);
  }

  public get getXiText() {
    return this.eventTeamHeader.ceoptions &&
      this.eventTeamHeader.ceoptions.xiText
      ? this.eventTeamHeader.ceoptions.xiText
      : "";
  }

  public get hasCompEventHelpText() {
    return this.eventTeamHeader.ceoptions &&
      this.eventTeamHeader.ceoptions.helpText.length > 0
      ? true
      : false;
  }

  public onUserEntitySelected(entity: IEntity) {
    this.userEntitySelected = { ...entity };
  }

  public standardCatch(error: any) {
    this.isLoading = false;
    messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR);
    return;
  }

  public get getShowEntitySelect(): boolean {
    return this.userEntities.length > 1 && !this.showNewTeam;
  }

  public get getEventSecurityLabel() {
    const label = this.eventTeamService.getEventSecurityLabel(
      this.eventTeamHeader
    );
    return "Security: " + (label.length === 0 ? "Public" : label);
  }

  public get getUserEntityLabel() {
    return this.userEntities
      .map((ent) => {
        return "[" + ent.id + "] " + ent.entityName + " - " + ent.name;
      })
      .join(", ");
  }

  public get hasClubCompInfoCompetition(): boolean {
    return ClubCompInfoService.hasClubCompInfoCompetition(this.clubCompInfo);
  }

  public get getCompEventTeamSorted(): ICompEventTeam[] {
    const teams = sortArray("teamName", this.eventTeamHeader.compEventTeams);
    return teams;
  }

  //
  // public get getMaxTeamReached() {
  //     return this.eventTeamService.maxTeamsReached(this.eventTeamHeader);
  // }
}
</script>
