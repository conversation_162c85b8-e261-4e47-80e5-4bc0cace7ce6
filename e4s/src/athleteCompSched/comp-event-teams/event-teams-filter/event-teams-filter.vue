<template>
    <div>

<!--        <div class="row">-->
<!--            <div class="col s12 m12 l12">-->
<!--                <input id="filter"-->
<!--                       placeholder="FILTER: Enter filter term here e.g. competition number, organiser, name, location"-->
<!--                       v-model="eventTeamHeaderFilter.eventName"-->
<!--                       v-on:keyup="onFilterChange"/>-->
<!--            </div>-->
<!--        </div>-->

        <div class="row">
            <div class="col s12 m12 l12">
                Team Filter
            </div>
        </div>


        <div class="row">

            <div class="col s6 m6 l6">
                <label class="active" for="gender">Event</label>
                <select class="generic-select"
                        v-on:change="onFilterChange"
                        name="gender" v-model="eventTeamHeaderFilter.eventName">
                    <option v-for="eventName in getEventNames"
                            :value="eventName"
                            v-text="eventName === '' ? 'ALL' : eventName">
                    </option>

                </select>
            </div>

            <div class="col s6 m6 l6">
                <label class="active" for="gender">Teams Entered</label>
                <select id="has-team"
                        class="generic-select"
                        v-on:change="onFilterChange"
                        name="gender" v-model="eventTeamHeaderFilter.hasTeams">
                    <option selected value="ALL">All</option>
                    <option value="WITH">With Teams</option>
                    <option value="WITHOUT">Without Teams</option>
                </select>
            </div>
        </div>

        <div class="row">
            <div class="col s6 m6 l6">
                <label class="active" for="gender">Gender</label>
                <select id="gender"
                        class="generic-select"
                        v-on:change="onFilterChange"
                        name="gender" v-model="eventTeamHeaderFilter.gender">
                    <option :value="''">Select Option</option>
                    <option :value="gender.FEMALE">Female</option>
                    <option :value="gender.MALE">Male</option>
                </select>
            </div>

            <div class="col s6 m6 l6">
                <label class="active" for="gender">Age Group</label>
                <select class="generic-select"
                        v-on:change="onFilterChange"
                        name="gender" v-model="eventTeamHeaderFilter.ageGroup">
                    <option v-for="ageGroup in getAgeGroups" :value="ageGroup" v-text="ageGroup.name"></option>

                </select>
            </div>
        </div>

    </div>
</template>

<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";
    import {IEventTeamHeaderFilter} from "./event-teams-filter-models";
    import { EventTeamsFilterService } from "./event-teams-service";
    import {Prop, Watch} from "vue-property-decorator";
    import {IEventTeamHeader} from "../event-teams-models";
    import {EventTeamService} from "../event-team-service";
    import * as R from "ramda";
    import {GENDER, IObjectKeyType} from "../../../common/common-models";
    import {IAgeGroupBase} from "../../../agegroup/agegroup-models";
    import {CommonService} from "../../../common/common-service";

    const eventTeamService: EventTeamService = new EventTeamService();
    const eventTeamsFilterService: EventTeamsFilterService = new EventTeamsFilterService();
    const commonService: CommonService = new CommonService();

    @Component({
        name: "event-teams-filter"
    })
    export default class EventTeamsFilter extends Vue {
        @Prop({
            default: () => {
                return [];
            }
        }) public readonly eventTeamHeaders: IEventTeamHeader[];

        public gender = GENDER;
        public eventTeamHeaderFilter: IEventTeamHeaderFilter;
        public eventTeamHeadersInternal: IEventTeamHeader[] = [];

        public created() {
            this.eventTeamHeaderFilter = eventTeamsFilterService.factoryEventTeamHeaderFilter();
            this.eventTeamHeadersInternal = R.clone(this.eventTeamHeaders);
        }

        public get getEventNames(): string[] {
            const eventObj: IObjectKeyType<string> = this.eventTeamHeaders.reduce( (accum, eventTeamHeader) => {
                const key = eventTeamHeader.eventName;
                if (!accum[key]) {
                    accum[key] = eventTeamHeader.eventName;
                }
                return accum;
            }, {} as IObjectKeyType<string>);

            const eventNames: string[] = commonService.convertObjectToArray(eventObj).sort( (a, b) => {
                return a.localeCompare(b);
            });
            eventNames.unshift("");
            return eventNames;
        }

        public get getAgeGroups(): IAgeGroupBase[] {
            const ageGroupBaseObj: IObjectKeyType<IAgeGroupBase> = this.eventTeamHeaders.reduce( (accum, eventTeamHeader) => {
                const agId = eventTeamHeader.ageGroup.id;
                if (!accum[agId]) {
                    accum[agId] = eventTeamHeader.ageGroup;
                }
                return accum;
            }, {} as IObjectKeyType<IAgeGroupBase>);
            const agAll: IAgeGroupBase = {
                id: 0,
                name: "ALL"
            };

            const ageGroups: IAgeGroupBase[] = commonService.convertObjectToArray(ageGroupBaseObj).sort( (a, b) => {
                return a.name.localeCompare(b.name);
            });
            ageGroups.unshift(agAll);
            return ageGroups;
        }

        @Watch("eventTeamHeaders")
        public onEventTeamHeadersChanged(newValue: IEventTeamHeader[]) {
            this.eventTeamHeadersInternal = R.clone(newValue);
        }

        public onFilterChange() {
            this.eventTeamHeadersInternal = eventTeamService.filterEventTeamHeaders(this.eventTeamHeaderFilter, this.eventTeamHeaders);
            this.$emit("onChange", this.eventTeamHeadersInternal);
        }
    }
</script>
