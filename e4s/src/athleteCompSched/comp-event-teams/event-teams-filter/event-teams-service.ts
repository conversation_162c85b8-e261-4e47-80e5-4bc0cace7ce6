import {
  IEventTeamHeaderFilter,
  IEventTeamHeaderFilterV2,
} from "./event-teams-filter-models";

export class EventTeamsFilterService {
  public factoryEventTeamHeaderFilter(): IEventTeamHeaderFilter {
    return {
      eventName: "",
      gender: "ALL",
      ageGroup: {
        id: 0,
        name: "ALL",
      },
      hasTeams: "ALL",
    };
  }

  public factoryEventTeamHeaderFilterV2(): IEventTeamHeaderFilterV2 {
    return {
      freeText: "",
      eventName: "",
      gender: "ALL",
      ageGroup: {
        id: 0,
        name: "ALL",
      },
      hasTeams: "ALL",
    };
  }
}
