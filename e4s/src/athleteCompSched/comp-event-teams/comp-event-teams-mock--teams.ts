import { IEventTeamHeader } from "./event-teams-models";

export const mockEventTeamHeaders: IEventTeamHeader[] = [
  {
    id: 73828,
    eventid: 947,
    eventName: "Mixed Relay",
    startdate: "2023-10-15T12:50:00",
    tf: "X",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        teamPositionLabel: "Leg",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: true,
        mustBeIndivEntered: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "Available to clubs authorised users only. ",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          ageGroup: 47,
        },
      ],
      singleAge: false,
      security: {
        clubs: [0],
      },
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      availableToStatus: 0,
      availableFromStatus: 0,
    },
    maxAthletes: 0,
    eoptions: {
      isTeamEvent: true,
      rowOptions: {
        showPB: true,
      },
    },
    gender: "O",
    ageGroup: {
      id: 48,
      name: "Under 12",
    },
    isDisabled: true,
    eventGroup: {
      id: 17162,
      name: "U12 4x500m Mixed Relay",
      eventNo: 3,
      startDate: "2023-10-15T12:50:00",
    },
    Gender: "O",
    ageInfo: {
      id: 48,
      name: "Under 12",
    },
    compEventTeams: [],
    price: {
      id: 2073,
      stdPrice: 20,
      curPrice: 20,
      salePrice: 15,
      saleDate: "2022-10-06",
      actualPrice: 20,
    },
  },
  {
    id: 73829,
    eventid: 947,
    eventName: "Mixed Relay",
    startdate: "2023-10-15T13:20:00",
    tf: "X",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        teamPositionLabel: "Leg",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: true,
        mustBeIndivEntered: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "Available to clubs authorised users only. ",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          ageGroup: 49,
        },
      ],
      singleAge: false,
      security: {
        clubs: [0],
      },
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      availableToStatus: 0,
      availableFromStatus: 0,
    },
    maxAthletes: 0,
    eoptions: {
      isTeamEvent: true,
      rowOptions: {
        showPB: true,
      },
    },
    gender: "O",
    ageGroup: {
      id: 50,
      name: "Under 14",
    },
    isDisabled: true,
    eventGroup: {
      id: 17163,
      name: "U14 4x400m Mixed Relay",
      eventNo: 6,
      startDate: "2023-10-15T13:20:00",
    },
    Gender: "O",
    ageInfo: {
      id: 50,
      name: "Under 14",
    },
    compEventTeams: [
      {
        id: 20986,
        teamName: "Mixed Relay  Under 14 A",
        paid: 0,
        prodId: 275158,
        userId: 18777,
        userName: "jameswelsh",
        entity: {
          id: 544,
          entityLevel: 1,
          name: "Dunleer A.C.",
          entity: "Club",
        },
        athletes: [
          {
            id: 57519,
            clubid: 544,
            aocode: "IRL",
            club: "Dunleer A.C.",
            pos: 1,
            firstName: "Loughlin",
            surName: "WELSH",
            URN: 287887,
            dob: "2011-04-21",
            ageInfo: {
              ageGroups: [
                {
                  compDate: "2023-10-15",
                  agid: 49,
                  minAge: 12,
                  toDate: "2011-12-31",
                  fromDate: "2011-01-01",
                  id: 49,
                  Name: "Under 13",
                  MaxAge: 12,
                  AtDay: 31,
                  AtMonth: 12,
                  year: 0,
                  minAtDay: null,
                  minAtMonth: null,
                  minYear: null,
                  keyName: "Under 13 Eire",
                  options: [
                    {
                      aocode: "IRL",
                      default: true,
                      base: 1,
                    },
                    {
                      aocode: "ANI",
                      default: true,
                      base: 1,
                    },
                  ],
                  name: "Under 13",
                  shortName: "U13",
                },
              ],
              vetAgeGroup: null,
              currentAge: 12,
              competitionAge: 12,
              ageGroup: {
                compDate: "2023-10-15",
                agid: 49,
                minAge: 12,
                toDate: "2011-12-31",
                fromDate: "2011-01-01",
                id: 49,
                Name: "Under 13",
                MaxAge: 12,
                AtDay: 31,
                AtMonth: 12,
                year: 0,
                minAtDay: null,
                minAtMonth: null,
                minYear: null,
                keyName: "Under 13 Eire",
                options: [
                  {
                    aocode: "IRL",
                    default: true,
                    base: 1,
                  },
                  {
                    aocode: "ANI",
                    default: true,
                    base: 1,
                  },
                ],
                name: "Under 13",
                shortName: "U13",
              },
            },
          },
          {
            id: 31818,
            clubid: 544,
            aocode: "IRL",
            club: "Dunleer A.C.",
            pos: 2,
            firstName: "Lucas",
            surName: "LAVERY",
            URN: 289907,
            dob: "2011-09-07",
            ageInfo: {
              ageGroups: [
                {
                  compDate: "2023-10-15",
                  agid: 49,
                  minAge: 12,
                  toDate: "2011-12-31",
                  fromDate: "2011-01-01",
                  id: 49,
                  Name: "Under 13",
                  MaxAge: 12,
                  AtDay: 31,
                  AtMonth: 12,
                  year: 0,
                  minAtDay: null,
                  minAtMonth: null,
                  minYear: null,
                  keyName: "Under 13 Eire",
                  options: [
                    {
                      aocode: "IRL",
                      default: true,
                      base: 1,
                    },
                    {
                      aocode: "ANI",
                      default: true,
                      base: 1,
                    },
                  ],
                  name: "Under 13",
                  shortName: "U13",
                },
              ],
              vetAgeGroup: null,
              currentAge: 12,
              competitionAge: 12,
              ageGroup: {
                compDate: "2023-10-15",
                agid: 49,
                minAge: 12,
                toDate: "2011-12-31",
                fromDate: "2011-01-01",
                id: 49,
                Name: "Under 13",
                MaxAge: 12,
                AtDay: 31,
                AtMonth: 12,
                year: 0,
                minAtDay: null,
                minAtMonth: null,
                minYear: null,
                keyName: "Under 13 Eire",
                options: [
                  {
                    aocode: "IRL",
                    default: true,
                    base: 1,
                  },
                  {
                    aocode: "ANI",
                    default: true,
                    base: 1,
                  },
                ],
                name: "Under 13",
                shortName: "U13",
              },
            },
          },
          {
            id: 209399,
            clubid: 544,
            aocode: "IRL",
            club: "Dunleer A.C.",
            pos: 3,
            firstName: "Megan",
            surName: "CONNOR",
            URN: 403722,
            dob: "2011-03-01",
            ageInfo: {
              ageGroups: [
                {
                  compDate: "2023-10-15",
                  agid: 49,
                  minAge: 12,
                  toDate: "2011-12-31",
                  fromDate: "2011-01-01",
                  id: 49,
                  Name: "Under 13",
                  MaxAge: 12,
                  AtDay: 31,
                  AtMonth: 12,
                  year: 0,
                  minAtDay: null,
                  minAtMonth: null,
                  minYear: null,
                  keyName: "Under 13 Eire",
                  options: [
                    {
                      aocode: "IRL",
                      default: true,
                      base: 1,
                    },
                    {
                      aocode: "ANI",
                      default: true,
                      base: 1,
                    },
                  ],
                  name: "Under 13",
                  shortName: "U13",
                },
              ],
              vetAgeGroup: null,
              currentAge: 12,
              competitionAge: 12,
              ageGroup: {
                compDate: "2023-10-15",
                agid: 49,
                minAge: 12,
                toDate: "2011-12-31",
                fromDate: "2011-01-01",
                id: 49,
                Name: "Under 13",
                MaxAge: 12,
                AtDay: 31,
                AtMonth: 12,
                year: 0,
                minAtDay: null,
                minAtMonth: null,
                minYear: null,
                keyName: "Under 13 Eire",
                options: [
                  {
                    aocode: "IRL",
                    default: true,
                    base: 1,
                  },
                  {
                    aocode: "ANI",
                    default: true,
                    base: 1,
                  },
                ],
                name: "Under 13",
                shortName: "U13",
              },
            },
          },
          {
            id: 32138,
            clubid: 544,
            aocode: "IRL",
            club: "Dunleer A.C.",
            pos: 4,
            firstName: "Dearbhla",
            surName: "LEAVY",
            URN: 287885,
            dob: "2011-01-15",
            ageInfo: {
              ageGroups: [
                {
                  compDate: "2023-10-15",
                  agid: 49,
                  minAge: 12,
                  toDate: "2011-12-31",
                  fromDate: "2011-01-01",
                  id: 49,
                  Name: "Under 13",
                  MaxAge: 12,
                  AtDay: 31,
                  AtMonth: 12,
                  year: 0,
                  minAtDay: null,
                  minAtMonth: null,
                  minYear: null,
                  keyName: "Under 13 Eire",
                  options: [
                    {
                      aocode: "IRL",
                      default: true,
                      base: 1,
                    },
                    {
                      aocode: "ANI",
                      default: true,
                      base: 1,
                    },
                  ],
                  name: "Under 13",
                  shortName: "U13",
                },
              ],
              vetAgeGroup: null,
              currentAge: 12,
              competitionAge: 12,
              ageGroup: {
                compDate: "2023-10-15",
                agid: 49,
                minAge: 12,
                toDate: "2011-12-31",
                fromDate: "2011-01-01",
                id: 49,
                Name: "Under 13",
                MaxAge: 12,
                AtDay: 31,
                AtMonth: 12,
                year: 0,
                minAtDay: null,
                minAtMonth: null,
                minYear: null,
                keyName: "Under 13 Eire",
                options: [
                  {
                    aocode: "IRL",
                    default: true,
                    base: 1,
                  },
                  {
                    aocode: "ANI",
                    default: true,
                    base: 1,
                  },
                ],
                name: "Under 13",
                shortName: "U13",
              },
            },
          },
        ],
      },
    ],
    price: {
      id: 2073,
      stdPrice: 20,
      curPrice: 20,
      salePrice: 15,
      saleDate: "2022-10-06",
      actualPrice: 20,
    },
  },
  {
    id: 73809,
    eventid: 920,
    eventName: "U11 Boys 4x500m Relay",
    startdate: "2023-10-15T12:40:00",
    tf: "X",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 5,
        teamPositionLabel: "Leg",
        singleClub: true,
        teamSubstituteLabel: "Substitute",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: true,
        mustBeIndivEntered: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "Available to clubs authorised users only. ",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          ageGroup: 46,
        },
      ],
      singleAge: false,
      security: {
        clubs: [0],
      },
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      availableToStatus: 0,
      availableFromStatus: 0,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: {
      id: 47,
      name: "Under 11",
    },
    isDisabled: true,
    eventGroup: {
      id: 17152,
      name: "U11 Boys 4x500m Relay",
      eventNo: 2,
      startDate: "2023-10-15T12:40:00",
    },
    Gender: "M",
    ageInfo: {
      id: 47,
      name: "Under 11",
    },
    compEventTeams: [],
    price: {
      id: 2073,
      stdPrice: 20,
      curPrice: 20,
      salePrice: 15,
      saleDate: "2022-10-06",
      actualPrice: 20,
    },
  },
  {
    id: 73810,
    eventid: 921,
    eventName: "U11 Girls 4x500m Relay",
    startdate: "2023-10-15T12:30:00",
    tf: "X",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 5,
        teamPositionLabel: "Leg",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: true,
        mustBeIndivEntered: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "Available to clubs authorised users only. ",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          ageGroup: 46,
        },
      ],
      singleAge: false,
      security: {
        clubs: [0],
      },
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      availableToStatus: 0,
      availableFromStatus: 0,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: {
      id: 47,
      name: "Under 11",
    },
    isDisabled: true,
    eventGroup: {
      id: 17153,
      name: "U11 Girls 4x500m Relay",
      eventNo: 1,
      startDate: "2023-10-15T12:30:00",
    },
    Gender: "F",
    ageInfo: {
      id: 47,
      name: "Under 11",
    },
    compEventTeams: [],
    price: {
      id: 2073,
      stdPrice: 20,
      curPrice: 20,
      salePrice: 15,
      saleDate: "2022-10-06",
      actualPrice: 20,
    },
  },
  {
    id: 73811,
    eventid: 922,
    eventName: "U13 Boys 4x500m Relay",
    startdate: "2023-10-15T13:10:00",
    tf: "X",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 5,
        teamPositionLabel: "Leg",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: true,
        mustBeIndivEntered: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "Available to clubs authorised users only. ",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          ageGroup: 48,
        },
      ],
      singleAge: false,
      security: {
        clubs: [0],
      },
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      availableToStatus: 0,
      availableFromStatus: 0,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: {
      id: 49,
      name: "Under 13",
    },
    isDisabled: true,
    eventGroup: {
      id: 17154,
      name: "U13 Boys 4x500m Relay",
      eventNo: 5,
      startDate: "2023-10-15T13:10:00",
    },
    Gender: "M",
    ageInfo: {
      id: 49,
      name: "Under 13",
    },
    compEventTeams: [
      {
        id: 20985,
        teamName: "U13 Boys 4x500m Relay Male Under 13 A",
        paid: 0,
        prodId: 275157,
        userId: 18777,
        userName: "jameswelsh",
        entity: {
          id: 544,
          entityLevel: 1,
          name: "Dunleer A.C.",
          entity: "Club",
        },
        athletes: [
          {
            id: 57519,
            clubid: 544,
            aocode: "IRL",
            club: "Dunleer A.C.",
            pos: 1,
            firstName: "Loughlin",
            surName: "WELSH",
            URN: 287887,
            dob: "2011-04-21",
            ageInfo: {
              ageGroups: [
                {
                  compDate: "2023-10-15",
                  agid: 49,
                  minAge: 12,
                  toDate: "2011-12-31",
                  fromDate: "2011-01-01",
                  id: 49,
                  Name: "Under 13",
                  MaxAge: 12,
                  AtDay: 31,
                  AtMonth: 12,
                  year: 0,
                  minAtDay: null,
                  minAtMonth: null,
                  minYear: null,
                  keyName: "Under 13 Eire",
                  options: [
                    {
                      aocode: "IRL",
                      default: true,
                      base: 1,
                    },
                    {
                      aocode: "ANI",
                      default: true,
                      base: 1,
                    },
                  ],
                  name: "Under 13",
                  shortName: "U13",
                },
              ],
              vetAgeGroup: null,
              currentAge: 12,
              competitionAge: 12,
              ageGroup: {
                compDate: "2023-10-15",
                agid: 49,
                minAge: 12,
                toDate: "2011-12-31",
                fromDate: "2011-01-01",
                id: 49,
                Name: "Under 13",
                MaxAge: 12,
                AtDay: 31,
                AtMonth: 12,
                year: 0,
                minAtDay: null,
                minAtMonth: null,
                minYear: null,
                keyName: "Under 13 Eire",
                options: [
                  {
                    aocode: "IRL",
                    default: true,
                    base: 1,
                  },
                  {
                    aocode: "ANI",
                    default: true,
                    base: 1,
                  },
                ],
                name: "Under 13",
                shortName: "U13",
              },
            },
          },
          {
            id: 51726,
            clubid: 544,
            aocode: "IRL",
            club: "Dunleer A.C.",
            pos: 2,
            firstName: "Darragh",
            surName: "ROONEY",
            URN: 301834,
            dob: "2011-03-07",
            ageInfo: {
              ageGroups: [
                {
                  compDate: "2023-10-15",
                  agid: 49,
                  minAge: 12,
                  toDate: "2011-12-31",
                  fromDate: "2011-01-01",
                  id: 49,
                  Name: "Under 13",
                  MaxAge: 12,
                  AtDay: 31,
                  AtMonth: 12,
                  year: 0,
                  minAtDay: null,
                  minAtMonth: null,
                  minYear: null,
                  keyName: "Under 13 Eire",
                  options: [
                    {
                      aocode: "IRL",
                      default: true,
                      base: 1,
                    },
                    {
                      aocode: "ANI",
                      default: true,
                      base: 1,
                    },
                  ],
                  name: "Under 13",
                  shortName: "U13",
                },
              ],
              vetAgeGroup: null,
              currentAge: 12,
              competitionAge: 12,
              ageGroup: {
                compDate: "2023-10-15",
                agid: 49,
                minAge: 12,
                toDate: "2011-12-31",
                fromDate: "2011-01-01",
                id: 49,
                Name: "Under 13",
                MaxAge: 12,
                AtDay: 31,
                AtMonth: 12,
                year: 0,
                minAtDay: null,
                minAtMonth: null,
                minYear: null,
                keyName: "Under 13 Eire",
                options: [
                  {
                    aocode: "IRL",
                    default: true,
                    base: 1,
                  },
                  {
                    aocode: "ANI",
                    default: true,
                    base: 1,
                  },
                ],
                name: "Under 13",
                shortName: "U13",
              },
            },
          },
          {
            id: 218288,
            clubid: 544,
            aocode: "IRL",
            club: "Dunleer A.C.",
            pos: 3,
            firstName: "Daithi",
            surName: "CALLAGHAN",
            URN: 412780,
            dob: "2011-07-17",
            ageInfo: {
              ageGroups: [
                {
                  compDate: "2023-10-15",
                  agid: 49,
                  minAge: 12,
                  toDate: "2011-12-31",
                  fromDate: "2011-01-01",
                  id: 49,
                  Name: "Under 13",
                  MaxAge: 12,
                  AtDay: 31,
                  AtMonth: 12,
                  year: 0,
                  minAtDay: null,
                  minAtMonth: null,
                  minYear: null,
                  keyName: "Under 13 Eire",
                  options: [
                    {
                      aocode: "IRL",
                      default: true,
                      base: 1,
                    },
                    {
                      aocode: "ANI",
                      default: true,
                      base: 1,
                    },
                  ],
                  name: "Under 13",
                  shortName: "U13",
                },
              ],
              vetAgeGroup: null,
              currentAge: 12,
              competitionAge: 12,
              ageGroup: {
                compDate: "2023-10-15",
                agid: 49,
                minAge: 12,
                toDate: "2011-12-31",
                fromDate: "2011-01-01",
                id: 49,
                Name: "Under 13",
                MaxAge: 12,
                AtDay: 31,
                AtMonth: 12,
                year: 0,
                minAtDay: null,
                minAtMonth: null,
                minYear: null,
                keyName: "Under 13 Eire",
                options: [
                  {
                    aocode: "IRL",
                    default: true,
                    base: 1,
                  },
                  {
                    aocode: "ANI",
                    default: true,
                    base: 1,
                  },
                ],
                name: "Under 13",
                shortName: "U13",
              },
            },
          },
          {
            id: 31818,
            clubid: 544,
            aocode: "IRL",
            club: "Dunleer A.C.",
            pos: 4,
            firstName: "Lucas",
            surName: "LAVERY",
            URN: 289907,
            dob: "2011-09-07",
            ageInfo: {
              ageGroups: [
                {
                  compDate: "2023-10-15",
                  agid: 49,
                  minAge: 12,
                  toDate: "2011-12-31",
                  fromDate: "2011-01-01",
                  id: 49,
                  Name: "Under 13",
                  MaxAge: 12,
                  AtDay: 31,
                  AtMonth: 12,
                  year: 0,
                  minAtDay: null,
                  minAtMonth: null,
                  minYear: null,
                  keyName: "Under 13 Eire",
                  options: [
                    {
                      aocode: "IRL",
                      default: true,
                      base: 1,
                    },
                    {
                      aocode: "ANI",
                      default: true,
                      base: 1,
                    },
                  ],
                  name: "Under 13",
                  shortName: "U13",
                },
              ],
              vetAgeGroup: null,
              currentAge: 12,
              competitionAge: 12,
              ageGroup: {
                compDate: "2023-10-15",
                agid: 49,
                minAge: 12,
                toDate: "2011-12-31",
                fromDate: "2011-01-01",
                id: 49,
                Name: "Under 13",
                MaxAge: 12,
                AtDay: 31,
                AtMonth: 12,
                year: 0,
                minAtDay: null,
                minAtMonth: null,
                minYear: null,
                keyName: "Under 13 Eire",
                options: [
                  {
                    aocode: "IRL",
                    default: true,
                    base: 1,
                  },
                  {
                    aocode: "ANI",
                    default: true,
                    base: 1,
                  },
                ],
                name: "Under 13",
                shortName: "U13",
              },
            },
          },
        ],
      },
    ],
    price: {
      id: 2073,
      stdPrice: 20,
      curPrice: 20,
      salePrice: 15,
      saleDate: "2022-10-06",
      actualPrice: 20,
    },
  },
  {
    id: 73812,
    eventid: 923,
    eventName: "U13 Girls 4x500m Relay",
    startdate: "2023-10-15T13:00:00",
    tf: "X",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 5,
        teamPositionLabel: "Leg",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: true,
        mustBeIndivEntered: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "Available to clubs authorised users only. ",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          ageGroup: 48,
        },
      ],
      singleAge: false,
      security: {
        clubs: [0],
      },
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      availableToStatus: 0,
      availableFromStatus: 0,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: {
      id: 49,
      name: "Under 13",
    },
    isDisabled: true,
    eventGroup: {
      id: 17155,
      name: "U13 Girls 4x500m Relay",
      eventNo: 4,
      startDate: "2023-10-15T13:00:00",
    },
    Gender: "F",
    ageInfo: {
      id: 49,
      name: "Under 13",
    },
    compEventTeams: [],
    price: {
      id: 2073,
      stdPrice: 20,
      curPrice: 20,
      salePrice: 15,
      saleDate: "2022-10-06",
      actualPrice: 20,
    },
  },
  {
    id: 73813,
    eventid: 924,
    eventName: "U15 Boys 4x1000m Relay",
    startdate: "2023-10-15T13:50:00",
    tf: "X",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 5,
        teamPositionLabel: "Leg",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: true,
        mustBeIndivEntered: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "Available to clubs authorised users only. ",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          ageGroup: 50,
        },
      ],
      singleAge: false,
      security: {
        clubs: [0],
      },
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      availableToStatus: 0,
      availableFromStatus: 0,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "M",
    ageGroup: {
      id: 83,
      name: "Under 15",
    },
    isDisabled: true,
    eventGroup: {
      id: 17156,
      name: "U15 Boys 4x1000m Relay",
      eventNo: 8,
      startDate: "2023-10-15T13:50:00",
    },
    Gender: "M",
    ageInfo: {
      id: 83,
      name: "Under 15",
    },
    compEventTeams: [],
    price: {
      id: 2073,
      stdPrice: 20,
      curPrice: 20,
      salePrice: 15,
      saleDate: "2022-10-06",
      actualPrice: 20,
    },
  },
  {
    id: 73814,
    eventid: 925,
    eventName: "U15 Girls 4x1000m Relay",
    startdate: "2023-10-15T13:30:00",
    tf: "X",
    IsOpen: 1,
    ceoptions: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 5,
        teamPositionLabel: "Leg",
        singleClub: true,
        teamSubstituteLabel: "",
        formType: "DEFAULT",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: true,
        mustBeIndivEntered: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "Available to clubs authorised users only. ",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          ageGroup: 50,
        },
      ],
      singleAge: false,
      security: {
        clubs: [0],
      },
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      availableToStatus: 0,
      availableFromStatus: 0,
    },
    maxAthletes: 0,
    eoptions: {},
    gender: "F",
    ageGroup: {
      id: 83,
      name: "Under 15",
    },
    isDisabled: true,
    eventGroup: {
      id: 17157,
      name: "U15 Girls 4x1000m Relay",
      eventNo: 7,
      startDate: "2023-10-15T13:30:00",
    },
    Gender: "F",
    ageInfo: {
      id: 83,
      name: "Under 15",
    },
    compEventTeams: [],
    price: {
      id: 2073,
      stdPrice: 20,
      curPrice: 20,
      salePrice: 15,
      saleDate: "2022-10-06",
      actualPrice: 20,
    },
  },
] as any as IEventTeamHeader[];
