import * as R from "ramda";
import {
  IAgeInfo,
  IAthleteCompSchedResponse,
  IAthleteCompSchedRuleEvent,
  ICeoptions,
  ICompShedRuleConfig,
  IOrderSummary,
  IPbKey,
  ISchedInfo,
  RULE_TYPE,
} from "./athletecompsched-models";
import { AthleteCompSchedService } from "./athletecompsched-service";

import { addHours, format, isBefore, parse } from "date-fns";

import { IAthlete } from "../athlete/athlete-models";
import { IObjectKey } from "../common/common-models";
import { IUserMessage } from "../user-message/user-message-models";
import dataAthlete from "./mock-athlete.json";
import data from "./mock.json";
import { CommonService } from "../common/common-service";
import { ICompetition } from "../competition/competition-models";
import { IUserApplication } from "../config/config-app-models";
import { CompetitionService } from "../competition/competiton-service";
import { simpleClone } from "../common/common-service-utils";
import { mockPerfInfo } from "./test/mock-perf-info";

const athleteCompSchedResponse: IAthleteCompSchedResponse = { ...data } as any as IAthleteCompSchedResponse;
const athleteCompSchedService: AthleteCompSchedService =
  new AthleteCompSchedService();
const athleteCompSchedResponseTidy =
  athleteCompSchedService.tidyUpServerResponse(athleteCompSchedResponse);
const athleteCompSchedRuleEvents: IAthleteCompSchedRuleEvent[] =
  athleteCompSchedService.factoryEventsToEventRule(
    athleteCompSchedResponseTidy.events
  );
const competitionService: CompetitionService = new CompetitionService();

const athleteMock: IAthlete = { ...dataAthlete } as any as IAthlete;

describe("Event Rules", () => {
  //  const athleteCompSchedService: AthleteCompSchedService = new AthleteCompSchedService()
  const athleteCompSchedRuleEvent: IAthleteCompSchedRuleEvent = {
    ceid: 22222,
    description: "",
    startdate: "2018-08-25T10:15:00+01:00",
    IsOpen: 1,
    tf: "F",
    saleprice: 7,
    price: {},
    saledate: "2018-08-20T00:00:00+01:00",
    name: "_std",
    eventid: 18,
    multiid: 0,
    Name: "Discus",
    split: -30,
    maxgroup: 30,
    maxathletes: 16,
    ceoptions: {
      xeText: "",
      xiText: "",
    },
    eoptions: {
      min: 1.0,
      max: 100.0,
      unit: "mtrs",
      unique: [{ e: 52 }],
      class: "",
    },
    entered: false,
    paid: 0,
    athleteid: 2,
    ruleIsDisabledBy: false,
    ruleMessage: "",
    userSelected: false,
    ruleType: RULE_TYPE.NONE,
    pb: null,
  } as IAthleteCompSchedRuleEvent;

  const compInfo = competitionService.factoryCompetitionInfo();

  test("Rule: Already Entered", () => {
    const athleteCompSchedRuleEventTest = { ...athleteCompSchedRuleEvent };
    const actual = athleteCompSchedService.applyRulesToEvent(
      athleteCompSchedRuleEventTest,
      compInfo
    );
    expect(actual.ruleIsDisabledBy).toBe(false);
  });

  test("Rule: Already Entered", () => {
    const athleteCompSchedRuleEventTest = { ...athleteCompSchedRuleEvent };
    const actual = athleteCompSchedService.applyRulesToEvent(
      athleteCompSchedRuleEventTest,
      compInfo
    );

    expect(actual.ruleIsDisabledBy).toBe(false);
  });

  test("Rule: Event Open", () => {
    const athleteCompSchedRuleEventTest = { ...athleteCompSchedRuleEvent };
    athleteCompSchedRuleEventTest.IsOpen = 0;
    const actual = athleteCompSchedService.applyRulesToEvent(
      athleteCompSchedRuleEventTest,
      compInfo
    );

    expect(actual.ruleIsDisabledBy).toBe(true);
  });

  test("Rule: Schedule Only true", () => {
    const athleteCompSchedRuleEventTest = { ...athleteCompSchedRuleEvent };
    athleteCompSchedRuleEventTest.maxathletes = -1;
    const actual = athleteCompSchedService.applyRulesToEvent(
      athleteCompSchedRuleEventTest,
      compInfo
    );

    expect(actual.ruleIsDisabledBy).toBe(true);
  });

  test("Rule: Schedule Only false", () => {
    const athleteCompSchedRuleEventTest = { ...athleteCompSchedRuleEvent };
    athleteCompSchedRuleEventTest.maxathletes = 33;
    const actual = athleteCompSchedService.applyRulesToEvent(
      athleteCompSchedRuleEventTest,
      compInfo
    );

    expect(actual.ruleIsDisabledBy).toBe(false);
  });
});

describe("Event factory creation", () => {
  //     test("Add athlete details", () => {
  //        const events = [...athleteCompSchedRuleEvents];
  //        const eventId: number = 20;
  //        const eventId10 = events.event-teams-filter((evt) => {
  //            return evt.eventid === eventId;
  //        });
  //        expect(eventId10.length).toBe(2);
  //        let eventTest = eventId10[0];

  //        const athlete: IAthlete = {
  //            firstName: "Bob",
  //            surName: "Blah",
  //            URN: "Blah",
  //            dob: "2018-11-22",
  //            club: "Blah club",
  //            gender: "M",
  //            classification: "",
  //            schoolid: 0,
  //            school: "",
  //            clubid: 0
  //        } as IAthlete;

  //        eventTest = athleteCompSchedService.addAthleteDetailsToEvent(eventTest, athlete);
  //        expect(eventTest.firstName).toBe("Bob");

  //    });

  test("getUserEventKey", () => {
    const events = [...athleteCompSchedRuleEvents];
    const eventTest = events[0];
    const userEventKey = athleteCompSchedService.getUserEventKey(eventTest);

    expect(userEventKey).toBe("2-22222");
  });

  test("getEventsAsKeyUniqueUserObject", () => {
    const events = [...athleteCompSchedRuleEvents];

    const eventsTestKeyObj =
      athleteCompSchedService.getEventsAsKeyUniqueUserObject(events);
    expect(eventsTestKeyObj["2-22233"].athleteid).toBe(2);
    expect(eventsTestKeyObj["2-22233"].ceid).toBe(22233);
  });
});

describe("Entered Not Paid", () => {
  test("setEnteredNotPaidAsSelected", () => {
    const events = [...athleteCompSchedRuleEvents];
    const eventsEnteredNotPaid =
      athleteCompSchedService.getEventsEnteredNotPaid(events);

    //  console.log("vvvvvv", eventsEnteredNotPaid);
    expect(eventsEnteredNotPaid.length).toBe(3);

    const eventTest = eventsEnteredNotPaid[0];
    const userEventKey = athleteCompSchedService.getUserEventKey(eventTest);
    expect(userEventKey).toBe("2-22106");

    const eventsNew =
      athleteCompSchedService.setEnteredNotPaidAsSelected(events);
    expect(eventsNew.length).toBe(23);
    const eventTestsNew = eventsNew.filter((evt) => {
      const userKey = athleteCompSchedService.getUserEventKey(evt);
      return userKey === userEventKey;
    });
    //  const eventTestNew = eventTestsNew[0];
    expect(eventTestsNew.length).toBe(1);
    const eventTestNew = eventTestsNew[0];
    expect(eventTestNew.userSelected).toBe(true);
  });

  test("toggleEventInSelectedList", () => {
    const events = [...athleteCompSchedRuleEvents];
    const eventsEnteredNotPaid =
      athleteCompSchedService.getEventsEnteredNotPaid(events);

    expect(eventsEnteredNotPaid.length).toBe(3);

    const eventsNewSelected = eventsEnteredNotPaid.reduce(
      (accum: any, evt: IAthleteCompSchedRuleEvent) => {
        accum = athleteCompSchedService.toggleEventInSelectedList(evt, accum);
        return accum;
      },
      []
    );
    expect(eventsNewSelected.length).toBe(3);
  });

  test("forceIntoSelected", () => {
    const events = [...athleteCompSchedRuleEvents];
    const eventsEnteredNotPaid =
      athleteCompSchedService.getEventsEnteredNotPaid(events);

    expect(eventsEnteredNotPaid.length).toBe(3);
    const eventsNewSelected = eventsEnteredNotPaid.reduce(
      (accum: any, evt: IAthleteCompSchedRuleEvent) => {
        accum = athleteCompSchedService.forceIntoSelected(evt, accum);
        return accum;
      },
      []
    );
    expect(eventsNewSelected.length).toBe(3);
  });

  test("removeEventFromSelected", () => {
    const events = [...athleteCompSchedRuleEvents];
    const eventsEnteredNotPaid =
      athleteCompSchedService.getEventsEnteredNotPaid(events);

    expect(eventsEnteredNotPaid.length).toBe(3);
    const eventsNewSelected = eventsEnteredNotPaid.reduce(
      (accum: any, evt: IAthleteCompSchedRuleEvent) => {
        accum = athleteCompSchedService.forceIntoSelected(evt, accum);
        return accum;
      },
      []
    );
    expect(eventsNewSelected.length).toBe(3);

    const eventToRemove = eventsNewSelected[0];
    const eventsAfterRemove = athleteCompSchedService.removeEventFromSelected(
      eventToRemove,
      eventsNewSelected
    );
    expect(eventsAfterRemove.length).toBe(2);
  });
});

describe("Event Rules with selected", () => {
  const athleteCompSchedRuleEvent: IAthleteCompSchedRuleEvent = {
    ceid: 22222,
    description: "",
    startdate: "2018-08-25T10:15:00+01:00",
    IsOpen: 1,
    tf: "F",
    saleprice: 7,
    price: {},
    saledate: "2018-08-20T00:00:00+01:00",
    name: "_std",
    eventid: 22,
    multiid: 0,
    Name: "Discus",
    split: -30,
    maxgroup: 30,
    maxathletes: 16,
    ceoptions: {
      xeText: "",
      xiText: "",
    },
    eoptions: {
      min: 1.0,
      max: 100.0,
      unit: "mtrs",
      unique: [{ e: 30 }],
      class: "",
    },
    entered: false,
    paid: 0,
    athleteid: 2,
    ruleIsDisabledBy: false,
    ruleMessage: "",
    userSelected: false,
    ruleType: RULE_TYPE.NONE,
    pb: null,
  } as IAthleteCompSchedRuleEvent;

  test("Rule: check events", () => {
    const athleteCompSchedResponseTest: IAthleteCompSchedResponse = {
      ...athleteCompSchedResponse,
    };
    expect(athleteCompSchedResponseTest.events.length > 0).toBe(true);
  });

  test("Tidy server response", () => {
    let athleteCompSchedResponseTest: IAthleteCompSchedResponse = {
      ...athleteCompSchedResponse,
    };
    athleteCompSchedResponseTest = athleteCompSchedService.tidyUpServerResponse(
      athleteCompSchedResponseTest
    );
    expect(athleteCompSchedResponseTest.events.length > 0).toBe(true);
    expect(athleteCompSchedRuleEvent.eoptions.unique.length > 0).toBe(true);
  });

  test("Rule: unique", () => {
    let athleteCompSchedResponseTest: IAthleteCompSchedResponse = {
      ...athleteCompSchedResponse,
    };
    athleteCompSchedResponseTest = athleteCompSchedService.tidyUpServerResponse(
      athleteCompSchedResponseTest
    );
    expect(athleteCompSchedResponseTest.events.length > 0).toBe(true);
    expect(athleteCompSchedRuleEvent.eoptions.unique.length > 0).toBe(true);

    const athleteCompSchedRuleEventGiven: IAthleteCompSchedRuleEvent[] =
      athleteCompSchedService.factoryEventsToEventRule(
        athleteCompSchedResponseTest.events
      );
    const eventBefore = athleteCompSchedRuleEventGiven.find((evt) => {
      return evt.eventid === 30;
    }) as IAthleteCompSchedRuleEvent;
    expect(eventBefore === undefined).toBe(false);
    expect(eventBefore.ruleIsDisabledBy).toBe(false);

    const athleteCompSchedRuleEventResult = athleteCompSchedService.ruleUnique(
      athleteCompSchedRuleEvent,
      athleteCompSchedRuleEventGiven
    );
    const eventAfter = athleteCompSchedRuleEventResult.find((evt) => {
      return evt.eventid === 30;
    }) as IAthleteCompSchedRuleEvent;
    expect(eventAfter === undefined).toBe(false);
    expect(eventAfter.ruleIsDisabledBy).toBe(true);
  });

  describe("date-fns", () => {
    test("Rule: check events", () => {
      const dateIso: string = "2018-10-09T13:29:12+01:00";
      const dateJs: Date = parse(dateIso); // , "YYYY-MM-DDTHH:mm:ssZ");
      const result: string = format(dateJs, "Do MMM YY");
      expect(result).toBe("9th Oct 18");
    });

    test("Date fns weirdness", () => {
      const dateIso: string = "2019-07-19T00:00:00+01:00";
      const dateJs: Date = parse(dateIso);
      const result: string = format(dateJs, "HH:mm");
      expect(result).toBe("00:00");
    });
  });

  describe("General", () => {
    test("getOrderSummaryTotals()", () => {
      const events = [...athleteCompSchedRuleEvents];

      const orderSummary: IOrderSummary =
        athleteCompSchedService.getOrderSummaryTotals(events);
      //  console.log("orderSummary", orderSummary);
      expect(orderSummary.athleteCount).toBe(1);
      expect(orderSummary.totalPrice).toBe(22);
    });
  });

  describe("Comp Rules", () => {
    const compRule: ICompShedRuleConfig = { ...athleteCompSchedResponse.rules };
    const event: IAthleteCompSchedRuleEvent = {
      ceid: 22222,
      description: "",
      startdate: "2018-08-25T10:15:00+01:00",
      IsOpen: 1,
      tf: "F",
      saleprice: 7,
      price: {},
      saledate: "2018-08-20T00:00:00+01:00",
      name: "_std",
      eventid: 22,
      multiid: 0,
      Name: "Discus",
      split: -30,
      maxgroup: 30,
      maxathletes: 16,
      ceoptions: {
        xeText: "",
        xiText: "",
      },
      eoptions: {
        min: 1.0,
        max: 100.0,
        unit: "mtrs",
        unique: [{ e: 30 }],
        class: "",
      },
      entered: false,
      paid: 0,
      athleteid: 2,
      ruleIsDisabledBy: false,
      ruleMessage: "",
      userSelected: false,
      ruleType: RULE_TYPE.NONE,
      pb: null,
    } as IAthleteCompSchedRuleEvent;

    test("Rule: Max in 1 day", () => {
      const athleteCompSchedRuleEventsTest = [...athleteCompSchedRuleEvents];
      const eventsOnDate = athleteCompSchedService.getEventsEnteredForDate(
        "2018-08-25",
        athleteCompSchedRuleEventsTest
      );
      expect(eventsOnDate.length).toBe(3);
      expect(athleteCompSchedRuleEventsTest.length).toBe(23);
    });

    test("Rule: Max in comp", () => {
      const athleteCompSchedRuleEventsTest = [...athleteCompSchedRuleEvents];
      const eventsOnDate = athleteCompSchedService.getEventsEntered(
        "",
        athleteCompSchedRuleEventsTest
      );
      expect(eventsOnDate.length).toBe(3);
      expect(athleteCompSchedRuleEventsTest.length).toBe(23);
    });

    test("Rule: Max Track in comp", () => {
      const athleteCompSchedRuleEventsTest = [...athleteCompSchedRuleEvents];
      const eventsOnDate = athleteCompSchedService.getEventsEntered(
        "T",
        athleteCompSchedRuleEventsTest
      );
      expect(eventsOnDate.length).toBe(2);
    });

    test("Rule: getSelectedEvents()", () => {
      let events = [...athleteCompSchedRuleEvents];
      const eventTest = { ...event };

      expect(eventTest.userSelected).toBe(false);
      events = athleteCompSchedService.getSelectedEvents("", events);
      expect(events.length).toBe(3);

      events = [...athleteCompSchedRuleEvents];
      events[0].userSelected = true;
      events[1] = athleteCompSchedService.setSelectedEvent(events[1], true);
      events = athleteCompSchedService.getSelectedEvents("", events);

      expect(events.length).toBe(5);
      events[1] = athleteCompSchedService.setSelectedEvent(events[1], false);
      events = athleteCompSchedService.getSelectedEvents("", events);
      expect(events.length).toBe(4);
    });

    test("Rule: compRuleMax()", () => {
      let events = [...athleteCompSchedRuleEvents];
      //  const eventTest = {...event};
      const compRuleTest = { ...compRule };

      compRuleTest.options.maxEvents = 6;
      compRuleTest.options.maxCompEvents = 6;

      //  console.log("compRuleTest", compRuleTest);
      expect(events.length).toBe(23);
      expect(compRuleTest.options.maxEvents).toBe(6);
      expect(compRuleTest.options.maxField).toBe(2);

      events = [...athleteCompSchedRuleEvents];
      events = athleteCompSchedService.getEntered(events);
      expect(events.length).toBe(4);

      events = [...athleteCompSchedRuleEvents];
      events = athleteCompSchedService.getEventsEnteredForDate("", events);
      expect(events.length).toBe(4);
    });

    test("Rule: compRuleMaxInDay()", () => {
      let events = [...athleteCompSchedRuleEvents];
      const eventTest = { ...event };
      const compRuleTest = { ...compRule };

      expect(events.length).toBe(23);

      const compInfo = competitionService.factoryCompetitionInfo();

      compRuleTest.options.maxEvents = 6;
      events = [...athleteCompSchedRuleEvents];
      events = athleteCompSchedService.applyRulesToEvents(events, compInfo);
      events = athleteCompSchedService.compRuleMaxInDay(
        compRuleTest,
        eventTest,
        events
      );
      events = athleteCompSchedService.getAvailableEvents(
        eventTest.startdate.substring(0, 10),
        events
      );
      expect(events.length).toBe(16);

      compRuleTest.options.maxEvents = 1;
      events = [...athleteCompSchedRuleEvents];
      events = athleteCompSchedService.applyRulesToEvents(events, compInfo);
      events = athleteCompSchedService.compRuleMaxInDay(
        compRuleTest,
        eventTest,
        events
      );
      events = athleteCompSchedService.getAvailableEvents(
        eventTest.startdate.substring(0, 10),
        events
      );
      expect(events.length).toBe(0); // was 16
    });

    // test("Rule: compRuleTrackFieldMaxInDay()", () => {
    //   let events = [...athleteCompSchedRuleEvents];
    //   const compRuleTest = { ...compRule };
    //
    //   compRuleTest.options.maxEvents = 6;
    //   compRuleTest.options.maxCompEvents = 6;
    //
    //   expect(events.length).toBe(23);
    //   expect(compRuleTest.options.maxEvents).toBe(6);
    //   expect(compRuleTest.options.maxField).toBe(2);
    //
    //   events = [...athleteCompSchedRuleEvents];
    //   events = athleteCompSchedService.getEntered(events);
    //   expect(events.length).toBe(3);
    //
    //   events = [...athleteCompSchedRuleEvents];
    //   events = athleteCompSchedService.getTrackFieldEventsEntered(
    //     "",
    //     EVENT_TYPE.FIELD,
    //     events
    //   );
    //   expect(events.length).toBe(1);
    //
    //   events = [...athleteCompSchedRuleEvents];
    //   events = athleteCompSchedService.getTrackFieldEventsEntered(
    //     "",
    //     EVENT_TYPE.TRACK,
    //     events
    //   );
    //   expect(events.length).toBe(2);
    //
    //   events = [...athleteCompSchedRuleEvents];
    //   events = athleteCompSchedService.getTrackFieldEventsAvailable(
    //     "",
    //     EVENT_TYPE.TRACK,
    //     events
    //   );
    //   expect(events.length).toBe(7);
    // });

    test("Rule: compRuleUnique()", () => {
      let events = [...athleteCompSchedRuleEvents];
      //  const eventTest = {...event};
      const compRuleTest = { ...compRule };

      compRuleTest.options.maxEvents = 6;
      compRuleTest.options.maxCompEvents = 6;

      //  console.log("compRuleTest", compRuleTest);
      expect(events.length).toBe(23);
      expect(compRuleTest.options.maxEvents).toBe(6);
      expect(compRuleTest.options.maxField).toBe(2);

      events = [...athleteCompSchedRuleEvents];
      events = athleteCompSchedService.getEntered(events);
      expect(events.length).toBe(4);
    });

    test("Rule: PB min max", () => {
      const events = simpleClone(athleteCompSchedRuleEvents).map((evt) => {
        evt.perfInfo = simpleClone(mockPerfInfo);
        return evt;
      });
      const athlete = { ...athleteMock };
      let eventResult;

      expect(events.length).toBe(23);
      expect(athlete.id).toBe(2);
      expect(athlete.firstName).toBe("Jessica");

      const pbKey: IPbKey = athleteCompSchedService.getPersonalBestAsKeyObject(
        athlete.pbInfo
      );
      const eventId: number = 3;
      expect(pbKey[eventId]).toBe(13.22);

      const eventId3 = events.filter((evt) => {
        return evt.eventid === eventId;
      });
      expect(eventId3.length).toBe(2);

      const eventTests = eventId3.filter((evt) => {
        return evt.eventid === eventId && evt.ceid === 22106;
      });
      expect(eventTests.length).toBe(1);

      const eventTest: IAthleteCompSchedRuleEvent = eventTests[0];

      eventResult = athleteCompSchedService.applyPbRule(simpleClone(eventTest));
      expect(eventResult.ruleIsDisabledBy).toBe(false);

      pbKey[eventId] = 400;
      //  split is ZERO, so don't apply rule
      eventResult = athleteCompSchedService.applyPbRule(simpleClone(eventTest));
      expect(eventResult.ruleIsDisabledBy).toBe(false);
    });

    test("Rule: PB split", () => {
      const events = simpleClone(athleteCompSchedRuleEvents).map((evt) => {
        evt.perfInfo = simpleClone(mockPerfInfo);
        return evt;
      });
      const athlete = { ...athleteMock };
      let eventResult;

      expect(events.length).toBe(23);
      expect(athlete.id).toBe(2);
      expect(athlete.firstName).toBe("Jessica");

      const pbKey: IPbKey = athleteCompSchedService.getPersonalBestAsKeyObject(
        athlete.pbInfo
      );
      const eventId: number = 20;
      expect(pbKey[eventId]).toBe(10.19);

      const eventId10 = events.filter((evt) => {
        return evt.eventid === eventId;
      });
      expect(eventId10.length).toBe(2);

      //  Test below split
      const eventTests = eventId10.filter((evt) => {
        return evt.eventid === eventId && evt.ceid === 22323;
      });
      expect(eventTests.length).toBe(1);

      let eventTest: IAthleteCompSchedRuleEvent = simpleClone(eventTests[0]);

      eventTest.perfInfo.perf = 10.19;
      eventTest.split = 20;
      eventTest.perfInfo.limits = {
        min: 20,
        max: 40,
      };

      expect(10.18 < 20).toBe(true);

      //  E.g. split is 20, pb is 19.
      eventResult = athleteCompSchedService.applyPbRule(simpleClone(eventTest));
      expect(eventResult.ruleIsDisabledBy).toBe(true);
      expect(eventResult.ruleMessage).toBe(
        "1. Performance 10.19 less than required 20"
      );

      //  E.g. split 20, pb 25
      eventTest = simpleClone(eventTests[0]);
      eventTest.perfInfo.perf = 25;
      eventTest.split = 20;
      eventTest.perfInfo.limits = {
        min: 20,
        max: 40,
      };
      eventResult = athleteCompSchedService.applyPbRule(simpleClone(eventTest));
      expect(eventResult.ruleIsDisabledBy).toBe(false);
      expect(eventResult.ruleMessage).toBe("");

      //  E.g. split is -20, pb is 19, is OK.
      eventTest = simpleClone(eventTests[0]);
      eventTest.perfInfo.perf = 19;
      eventTest.split = -20;
      eventTest.perfInfo.limits = {
        min: 10,
        max: 20,
      };
      eventResult = athleteCompSchedService.applyPbRule(simpleClone(eventTest));
      expect(eventResult.ruleMessage).toBe("");
      expect(eventResult.ruleIsDisabledBy).toBe(false);

      //  E.g. split is -20, pb is 21, pb is greater than max split allowed.
      eventTest = simpleClone(eventTests[0]);
      eventTest.perfInfo.perf = 21;
      eventTest.split = -20;
      eventTest.perfInfo.limits = {
        min: 10,
        max: 20,
      };
      eventResult = athleteCompSchedService.applyPbRule(simpleClone(eventTest));
      expect(eventResult.ruleMessage).toBe(
        "2. Performance 21 greater than required 20"
      );
      expect(eventResult.ruleIsDisabledBy).toBe(true);

      //  E.g. split is +20, pb is zero.
      eventTest = simpleClone(eventTests[0]);
      eventTest.perfInfo.perf = 0;
      eventTest.split = 20;
      eventTest.perfInfo.limits = {
        min: 20,
        max: 30,
      };
      eventResult = athleteCompSchedService.applyPbRule(simpleClone(eventTest));
      //  console.log("", eventResult);
      expect(eventResult.ruleMessage).toBe(
        "1. Performance 0 less than required 20"
      );
      expect(eventResult.ruleIsDisabledBy).toBe(true);
    });
  });

  describe("cart confirm", () => {
    test("canCartBeSubmittedToBasket()", () => {
      const cartEvents: IAthleteCompSchedRuleEvent[] = [
        {
          eventid: 999,
          ceid: 999,
          name: "4 x 400 relay",
          eoptions: { eventTeam: { min: 1, max: 4 } },
          ceoptions: {
            isTeamEvent: true,
          } as ICeoptions,
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 999,
          ceid: 999,
          name: "4 x 400 relay",
          eoptions: { eventTeam: { min: 1, max: 4 } },
          ceoptions: {
            isTeamEvent: true,
          } as ICeoptions,
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 998,
          ceid: 998,
          eoptions: {},
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 111,
          ceid: 111,
          name: "4 x 100 relay",
          eoptions: { eventTeam: { min: 1, max: 2 } },
          ceoptions: {
            isTeamEvent: true,
          } as ICeoptions,
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 111,
          ceid: 111,
          name: "4 x 100 relay",
          eoptions: { eventTeam: { min: 1, max: 2 } },
          ceoptions: {
            isTeamEvent: true,
          } as ICeoptions,
        } as IAthleteCompSchedRuleEvent,
      ];

      //  const result: IUserMessage[] = athleteCompSchedService.canCartBeSubmittedToBasket([event]);
      const teamEvents: IAthleteCompSchedRuleEvent[] =
        athleteCompSchedService.getTeamEvents(cartEvents);
      //  console.log("canCartBeSubmittedToBasket() teamEvents", teamEvents);
      expect(teamEvents.length).toBe(4);
      expect(cartEvents.length).toBe(5);

      const result: IUserMessage[] =
        athleteCompSchedService.canCartBeSubmittedToBasket(cartEvents);
      //  console.log("canCartBeSubmittedToBasket() result", result);
      expect(result.length).toBe(0);
    });

    test("canCartBeSubmittedToBasket() secondary spend", () => {
      const cartEvents: IAthleteCompSchedRuleEvent[] = [
        {
          eventid: 999,
          ceid: 999,
          name: "4 x 400 relay",
          eoptions: { eventTeam: { min: 1, max: 4 } },
          userSelected: true,
          order: {
            productId: 0,
          },
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 1234,
          ceid: 999333,
          name: "bib",
          ceoptions: {
            secondarySpend: {
              parentCeid: 999,
            },
          },
          userSelected: true,
          order: {
            productId: 44,
          },
        } as IAthleteCompSchedRuleEvent,
      ];

      //  const result: IUserMessage[] = athleteCompSchedService.canCartBeSubmittedToBasket([event]);
      const spendEvents: IAthleteCompSchedRuleEvent[] =
        athleteCompSchedService.getSelectedSecondarySpendItems(cartEvents);
      expect(spendEvents.length).toBe(1);
      const commonService: CommonService = new CommonService();
      const cartEventsObj = commonService.convertArrayToObject(
        "ceid",
        cartEvents
      );
      let isParentSelected =
        athleteCompSchedService.isSecondarySpendEventParentUserSelected(
          spendEvents[0],
          cartEventsObj
        );
      expect(isParentSelected).toBe(true);

      cartEvents[0].userSelected = false;
      isParentSelected =
        athleteCompSchedService.isSecondarySpendEventParentUserSelected(
          spendEvents[0],
          cartEventsObj
        );
      expect(isParentSelected).toBe(false);

      const result: IUserMessage[] =
        athleteCompSchedService.canCartBeSubmittedToBasket(cartEvents);
      expect(result.length).toBe(1);
    });

    test("canCartBeSubmittedToBasket() secondary spend nothing selected", () => {
      const cartEvents: IAthleteCompSchedRuleEvent[] = [
        {
          eventid: 999,
          ceid: 999,
          name: "4 x 400 relay",
          eoptions: { eventTeam: { min: 1, max: 4 } },
          userSelected: true,
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 1234,
          ceid: 999333,
          name: "bib",
          ceoptions: {
            secondarySpend: {
              parentCeid: 999,
            },
          },
          userSelected: false,
        } as IAthleteCompSchedRuleEvent,
      ];

      //  const result: IUserMessage[] = athleteCompSchedService.canCartBeSubmittedToBasket([event]);
      const spendEvents: IAthleteCompSchedRuleEvent[] =
        athleteCompSchedService.getSelectedSecondarySpendItems(cartEvents);
      expect(spendEvents.length).toBe(0);

      const result: IUserMessage[] =
        athleteCompSchedService.canCartBeSubmittedToBasket(cartEvents);
      expect(result.length).toBe(0);
    });

    test("canCartBeSubmittedToBasket() secondary spend", () => {
      const cartEvents: IAthleteCompSchedRuleEvent[] = [
        {
          eventid: 999,
          ceid: 999,
          name: "4 x 400 relay",
          eoptions: { eventTeam: { min: 1, max: 4 } },
          userSelected: false,
          order: {
            productId: 0,
          },
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 1234,
          ceid: 999333,
          name: "bib",
          ceoptions: {
            secondarySpend: {
              parentCeid: 999,
            },
          },
          userSelected: true,
          order: {
            productId: 44,
          },
        } as IAthleteCompSchedRuleEvent,
      ];

      //  const result: IUserMessage[] = athleteCompSchedService.canCartBeSubmittedToBasket([event]);
      const spendEvents: IAthleteCompSchedRuleEvent[] =
        athleteCompSchedService.getSelectedSecondarySpendItems(cartEvents);
      expect(spendEvents.length).toBe(1);
      const commonService: CommonService = new CommonService();
      const cartEventsObj = commonService.convertArrayToObject(
        "ceid",
        cartEvents
      );
      const isParentSelected =
        athleteCompSchedService.isSecondarySpendEventParentUserSelected(
          spendEvents[0],
          cartEventsObj
        );
      expect(isParentSelected).toBe(false);

      const result: IUserMessage[] =
        athleteCompSchedService.canCartBeSubmittedToBasket(cartEvents);
      //  console.log("canCartBeSubmittedToBasket() result", result);
      expect(result.length).toBe(1);
    });

    test("canCartBeSubmittedToBasket() secondary spend nothing", () => {
      const cartEvents: IAthleteCompSchedRuleEvent[] = [
        {
          eventid: 1234,
          ceid: 999333,
          Name: "bib",
          ceoptions: {
            secondarySpend: {
              parentCeid: 999,
            },
          },
          userSelected: true,
          order: {
            productId: 44,
          },
        } as IAthleteCompSchedRuleEvent,
      ];

      //  const result: IUserMessage[] = athleteCompSchedService.canCartBeSubmittedToBasket([event]);
      const spendEvents: IAthleteCompSchedRuleEvent[] =
        athleteCompSchedService.getSelectedSecondarySpendItems(cartEvents);
      expect(spendEvents.length).toBe(1);
      const commonService: CommonService = new CommonService();
      const cartEventsObj = commonService.convertArrayToObject(
        "ceid",
        cartEvents
      );
      const isParentSelected =
        athleteCompSchedService.isSecondarySpendEventParentUserSelected(
          spendEvents[0],
          cartEventsObj
        );
      expect(isParentSelected).toBe(false);

      const result: IUserMessage[] =
        athleteCompSchedService.canCartBeSubmittedToBasket(cartEvents);
      //  console.log("canCartBeSubmittedToBasket() result", result);
      expect(result.length).toBe(1);
    });
  });

  describe("Team Event Min Reached", () => {
    test("ruleTeamEventMinReached()", () => {
      const cartEvents: IAthleteCompSchedRuleEvent[] = [
        {
          ceid: 999,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2, isTeamEvent: true },
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 999,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2, isTeamEvent: true },
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 999,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2, isTeamEvent: true },
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 999,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2, isTeamEvent: true },
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 111,
          ageGroupId: 11,
          name: "4 x 400 relay U11",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2, isTeamEvent: true },
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 111,
          ageGroupId: 11,
          name: "4 x 400 relay U11",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2, isTeamEvent: true },
        } as IAthleteCompSchedRuleEvent,
      ];

      const userEventU11: IAthleteCompSchedRuleEvent = {
        ceid: 111,
        ruleIsDisabledBy: false,
        ageGroupId: 11,
        name: "4 x 400 relay U11",
        eoptions: { eventTeam: { min: 3, max: 4 } },
        ceoptions: { isTeamEvent: true },
      } as IAthleteCompSchedRuleEvent;

      const userEventU13: IAthleteCompSchedRuleEvent = {
        ceid: 999,
        ruleIsDisabledBy: false,
        ageGroupId: 13,
        name: "4 x 400 relay U13",
        eoptions: { eventTeam: { min: 3, max: 4 } },
        ceoptions: { maxAgeGroupCnt: 2, isTeamEvent: true },
      } as IAthleteCompSchedRuleEvent;

      const result =
        athleteCompSchedService.isTeamEventWithMaxAgeGroupCount(userEventU11);
      expect(result).toBe(false);

      expect(
        athleteCompSchedService.isTeamEventWithMaxAgeGroupCount(userEventU13)
      ).toBe(true);
      expect(athleteCompSchedService.getTeamEventMax(userEventU13)).toBe(4);

      const ageInfo13 = {
        currentAge: 13,
        ageGroupId: 13,
        ageGroup: "13",
        vetAgeGroupId: 0,
        vetAgeGroup: "",
      };
      let resultEvent = athleteCompSchedService.applyRulesTeamCompEvent(
        userEventU13,
        ageInfo13,
        cartEvents
      );
      expect(resultEvent.ruleIsDisabledBy).toBe(false);

      resultEvent = athleteCompSchedService.applyRulesTeamCompEvent(
        userEventU13,
        ageInfo13,
        cartEvents.slice(0, 2)
      );
      // console.log("resultEvent", resultEvent);
      expect(resultEvent.ruleIsDisabledBy).toBe(false);

      let availableSlots: number;
      const ageInfo11 = {
        currentAge: 11,
        ageGroupId: 11,
        ageGroup: "11",
        vetAgeGroupId: 0,
        vetAgeGroup: "",
      };

      //  1 x U11 in cart, 1 slot is available for U11
      let cart = [
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2, isTeamEvent: true },
          ageInfo: ageInfo11,
        } as IAthleteCompSchedRuleEvent,
      ];
      availableSlots = athleteCompSchedService.availableTeamCompSlots(
        userEventU13,
        ageInfo11,
        cart
      );
      expect(availableSlots).toBe(1);

      //  2 x U11 in cart, 0 slot is available for U11
      cart = [
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2, isTeamEvent: true },
          ageInfo: ageInfo11,
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2, isTeamEvent: true },
          ageInfo: ageInfo11,
        } as IAthleteCompSchedRuleEvent,
      ];
      availableSlots = athleteCompSchedService.availableTeamCompSlots(
        userEventU13,
        ageInfo11,
        cart
      );
      expect(availableSlots).toBe(0);

      //  1 x U11 and 1 x U13 in cart, 1 slot is available for U11
      cart = [
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2, isTeamEvent: true },
          ageInfo: ageInfo11,
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2, isTeamEvent: true },
          ageInfo: ageInfo13,
        } as IAthleteCompSchedRuleEvent,
      ];
      availableSlots = athleteCompSchedService.availableTeamCompSlots(
        userEventU13,
        ageInfo11,
        cart
      );
      expect(availableSlots).toBe(1);

      //  1 x U11 and 2 x U13 in cart, 1 slot is available for U11
      cart = [
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2 },
          ageInfo: ageInfo11,
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2 },
          ageInfo: ageInfo13,
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2 },
          ageInfo: ageInfo13,
        } as IAthleteCompSchedRuleEvent,
      ];
      availableSlots = athleteCompSchedService.availableTeamCompSlots(
        userEventU13,
        ageInfo11,
        cart
      );
      expect(availableSlots).toBe(1);

      //  2 x U11 and 2 x U13 in cart, 0 slot is available for U11
      cart = [
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2 },
          ageInfo: ageInfo11,
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2 },
          ageInfo: ageInfo11,
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2 },
          ageInfo: ageInfo13,
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 3, max: 4 } },
          ceoptions: { maxAgeGroupCnt: 2 },
          ageInfo: ageInfo13,
        } as IAthleteCompSchedRuleEvent,
      ];
      availableSlots = athleteCompSchedService.availableTeamCompSlots(
        userEventU13,
        ageInfo11,
        cart
      );
      expect(availableSlots).toBe(0);

      //  2 x U11 and 2 x U13 in cart, 0 slot is available for U11
      cart = [
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 4, max: 6 } },
          ceoptions: { maxAgeGroupCnt: 2 },
          ageInfo: ageInfo11,
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 4, max: 6 } },
          ceoptions: { maxAgeGroupCnt: 2 },
          ageInfo: ageInfo11,
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 4, max: 6 } },
          ceoptions: { maxAgeGroupCnt: 2 },
          ageInfo: ageInfo13,
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 111,
          ageGroupId: 13,
          name: "4 x 400 relay U13",
          eoptions: { eventTeam: { min: 4, max: 6 } },
          ceoptions: { maxAgeGroupCnt: 2 },
          ageInfo: ageInfo13,
        } as IAthleteCompSchedRuleEvent,
      ];
      availableSlots = athleteCompSchedService.availableTeamCompSlots(
        userEventU13,
        ageInfo13,
        cart
      );
      expect(availableSlots).toBe(0);
    });
  });

  describe("General", () => {
    test("getEventsKeyByProp()", () => {
      const cartEvents: IAthleteCompSchedRuleEvent[] = [
        {
          ceid: 1,
          name: "4 x 400 relay",
          eoptions: { eventTeam: { min: 1, max: 4 } },
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 2,
          name: "4 x 400 relay",
          eoptions: { eventTeam: { min: 1, max: 4 } },
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 3,
          eoptions: {},
        } as IAthleteCompSchedRuleEvent,
      ];

      const result: any = athleteCompSchedService.getEventsKeyByProp(
        "ceid",
        cartEvents
      );
      //  console.log("canCartBeSubmittedToBasket() result", result);
      expect(result[2].ceid).toBe(2);
    });
  });

  describe("Secondary Spend", () => {
    test("hasSecondarySpend()", () => {
      let event: IAthleteCompSchedRuleEvent = {
        eventid: 999,
        ceid: 999,
        name: "4 x 400 relay",
        ceoptions: {
          xeText: "",
          xiText: "",
        },
      } as IAthleteCompSchedRuleEvent;

      expect(athleteCompSchedService.hasSecondarySpend(event)).toBe(false);

      event = {
        eventid: 999,
        ceid: 999,
        name: "4 x 400 relay",
        ceoptions: {
          xeText: "",
          xiText: "",
          secondarySpend: {},
        },
      } as IAthleteCompSchedRuleEvent;

      expect(athleteCompSchedService.hasSecondarySpend(event)).toBe(true);
    });

    test("getAssociatedSecondarySpendEvents()", () => {
      const event: IAthleteCompSchedRuleEvent = {
        eventid: 999,
        ceid: 999,
        name: "XC 5k",
      } as IAthleteCompSchedRuleEvent;

      let events = [
        {
          eventid: 111,
          ceid: 111,
          name: "T Shirt",
        } as IAthleteCompSchedRuleEvent,
      ];

      let result = athleteCompSchedService.getAssociatedSecondarySpendEvents(
        event,
        events
      );
      expect(result.length).toBe(0);

      events = [
        {
          eventid: 111,
          ceid: 111,
          name: "T shirt",
          ceoptions: {
            xeText: "",
            xiText: "",
            secondarySpend: {
              parentCeid: 999,
            },
          },
        } as IAthleteCompSchedRuleEvent,
      ];

      result = athleteCompSchedService.getAssociatedSecondarySpendEvents(
        event,
        events
      );
      expect(result.length).toBe(1);

      events = [
        {
          eventid: 111,
          ceid: 111,
          name: "T shirt small",
          ceoptions: {
            xeText: "",
            xiText: "",
            secondarySpend: {
              parentCeid: 999,
            },
          },
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 112,
          ceid: 222,
          name: "T shirt medium",
          ceoptions: {
            xeText: "",
            xiText: "",
            secondarySpend: {
              parentCeid: 999,
            },
          },
        } as IAthleteCompSchedRuleEvent,
      ];

      result = athleteCompSchedService.getAssociatedSecondarySpendEvents(
        event,
        events
      );
      expect(result.length).toBe(2);
    });

    test("getSecondarySpendNotSelected()", () => {
      const event: IAthleteCompSchedRuleEvent = {
        eventid: 999,
        ceid: 999,
        name: "XC 5K",
        ceoptions: {
          xeText: "",
          xiText: "",
        },
      } as IAthleteCompSchedRuleEvent;

      let events = [
        {
          eventid: 111,
          ceid: 111,
          name: "T shirt small",
          ceoptions: {
            secondarySpend: {
              parentCeid: 999,
              mandatoryGroupId: 567,
            },
          },
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 112,
          ceid: 112,
          name: "T shirt medium",
          ceoptions: {
            secondarySpend: {
              parentCeid: 999,
              mandatoryGroupId: 567,
            },
          },
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 113,
          ceid: 113,
          name: "Bib pick up on day",
          ceoptions: {
            secondarySpend: {
              parentCeid: 999,
              mandatoryGroupId: 56789,
            },
          },
        } as IAthleteCompSchedRuleEvent,
      ];
      let result =
        athleteCompSchedService.getMandatorySecondarySpendObjectGroupedByMandId(
          event,
          events
        );
      // console.log(".....", result);
      expect(result["567"].length).toBe(2);

      let eventsNeedingSelecting =
        athleteCompSchedService.getMandatorySpendEventsRequiringAction(result);
      // console.log(".....eventsNeedingSelecting", eventsNeedingSelecting);
      expect(eventsNeedingSelecting.length).toBe(3);

      //
      events = [
        {
          eventid: 111,
          ceid: 111,
          name: "T shirt small",
          ceoptions: {
            secondarySpend: {
              parentCeid: 999,
              mandatoryGroupId: 567,
            },
          },
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 112,
          ceid: 112,
          name: "T shirt medium",
          ceoptions: {
            secondarySpend: {
              parentCeid: 999,
              mandatoryGroupId: 567,
            },
          },
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 113,
          ceid: 113,
          name: "Bib pick up on day",
          userSelected: true,
          ceoptions: {
            secondarySpend: {
              parentCeid: 999,
              mandatoryGroupId: 56789,
            },
          },
        } as IAthleteCompSchedRuleEvent,
      ];
      result =
        athleteCompSchedService.getMandatorySecondarySpendObjectGroupedByMandId(
          event,
          events
        );
      expect(result["567"].length).toBe(2);

      eventsNeedingSelecting =
        athleteCompSchedService.getMandatorySpendEventsRequiringAction(result);
      expect(eventsNeedingSelecting.length).toBe(2);

      //
      events = [
        {
          eventid: 111,
          ceid: 111,
          name: "T shirt small",
          ceoptions: {
            secondarySpend: {
              parentCeid: 999,
              mandatoryGroupId: 567,
            },
          },
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 112,
          ceid: 112,
          name: "T shirt medium",
          userSelected: true,
          ceoptions: {
            secondarySpend: {
              parentCeid: 999,
              mandatoryGroupId: 567,
            },
          },
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 113,
          ceid: 113,
          name: "Bib pick up on day",
          userSelected: true,
          ceoptions: {
            secondarySpend: {
              parentCeid: 999,
              mandatoryGroupId: 56789,
            },
          },
        } as IAthleteCompSchedRuleEvent,
      ];
      result =
        athleteCompSchedService.getMandatorySecondarySpendObjectGroupedByMandId(
          event,
          events
        );
      expect(result["567"].length).toBe(2);

      eventsNeedingSelecting =
        athleteCompSchedService.getMandatorySpendEventsRequiringAction(result);
      expect(eventsNeedingSelecting.length).toBe(0);
    });

    test("getEventsForAthlete", () => {
      const events: IAthleteCompSchedRuleEvent[] = [
        {
          athleteid: 4458,
          pb: 0,
          clubid: 3000,
          orderid: 0,
          paid: 0,
          price: {
            id: 1273,
            priceName: "",
            stdPrice: 10,
            curPrice: 10,
            salePrice: 5,
            saleDate: "2020-12-01T00:00:00+00:00",
            actualPrice: 5,
          },
          discountid: 0,
          vetAgeGroupID: 0,
          teamid: 0,
          userid: 1495,
          options: "",
          checkedin: 1,
          confirmed: null,
          present: 1,
          timeSelected: "2020-11-12T21:48:24+00:00",
          firstName: "Hugh",
          surName: "0 Connor",
          dob: "2009-08-04",
          ceid: 46275,
          compid: 351,
          compName: "Acme Winter Comp",
          ceoptions: {
            unregisteredAthletes: true,
            xrText: "",
            xbText: "",
            mandatoryPB: false,
            rowOptions: { showPrice: true },
            checkIn: { from: -1, to: -1, checkInMins: 60 },
          },
          IsOpen: 1,
          maxathletes: 0,
          maxgroup: 25476,
          multiid: 0,
          club: "Celtic DCH A.C.",
          ageGroup: "Under 13",
          Name: "100m",
          eoptions: { maxInHeat: 8, min: 9.6, max: 20, helpText: "" },
          tf: "T",
          split: 0,
          startdate: "2020-12-29T13:15:00+00:00",
          vetAgeGroup: "",
          uomType: "T",
          post_date: null,
          prodId: 23342,
          ageGroupId: 2,
          ageInfo: { ageGroupId: 2 },
          eventid: 268,
          uom: [{ pattern: "s.SS", text: "seconds", short: "s", uomType: "T" }],
          entered: true,
          entrycnt: 0,
          description: "",
        },
        {
          athleteid: 4459,
          pb: 0,
          clubid: 199,
          orderid: 0,
          paid: 0,
          price: {
            id: 1273,
            priceName: "",
            stdPrice: 10,
            curPrice: 10,
            salePrice: 5,
            saleDate: "2020-12-01T00:00:00+00:00",
            actualPrice: 5,
          },
          discountid: 0,
          vetAgeGroupID: 0,
          teamid: 0,
          userid: 1495,
          options: "",
          checkedin: 1,
          confirmed: null,
          present: 1,
          timeSelected: "2020-11-29T09:53:00+00:00",
          firstName: "Emily",
          surName: "0'Brien",
          dob: "2009-11-08",
          ceid: 46274,
          compid: 351,
          compName: "Acme Winter Comp",
          ceoptions: {
            unregisteredAthletes: true,
            xrText: "",
            xbText: "",
            mandatoryPB: false,
            rowOptions: { showPrice: true },
            checkIn: { from: -1, to: -1, checkInMins: 60 },
          },
          IsOpen: 1,
          maxathletes: 0,
          maxgroup: 25476,
          multiid: 0,
          club: "Bohermeen A.C.",
          ageGroup: "Under 13",
          Name: "100m",
          eoptions: { maxInHeat: 8, min: 9.6, max: 20, helpText: "" },
          tf: "T",
          split: 0,
          startdate: "2020-12-29T13:15:00+00:00",
          vetAgeGroup: "",
          uomType: "T",
          post_date: null,
          prodId: 23560,
          ageGroupId: 2,
          ageInfo: { ageGroupId: 2 },
          eventid: 270,
          uom: [{ pattern: "s.SS", text: "seconds", short: "s", uomType: "T" }],
          entered: true,
          entrycnt: 0,
          description: "",
        },
        {
          athleteid: 1298,
          pb: 9.25,
          clubid: 736,
          orderid: 0,
          paid: 0,
          price: {
            id: 1273,
            priceName: "",
            stdPrice: 10,
            curPrice: 10,
            salePrice: 5,
            saleDate: "2020-12-01T00:00:00+00:00",
            actualPrice: 10,
          },
          discountid: 0,
          vetAgeGroupID: 0,
          teamid: 0,
          userid: 1495,
          options: "",
          checkedin: 1,
          confirmed: null,
          present: 1,
          timeSelected: "2020-12-01T09:16:41+00:00",
          firstName: "Lewis",
          surName: "Ackroyd",
          dob: "1999-05-04",
          ceid: 46281,
          compid: 351,
          compName: "Acme Winter Comp",
          ceoptions: {
            unregisteredAthletes: true,
            xrText: "",
            xbText: "",
            mandatoryPB: false,
            rowOptions: { showPrice: true },
            checkIn: { from: -1, to: -1, checkInMins: 60 },
          },
          IsOpen: 1,
          maxathletes: 0,
          maxgroup: 25477,
          multiid: 0,
          club: "Halifax Harriers and A.C.",
          ageGroup: "Under 23",
          Name: "Shotput",
          eoptions: { min: 1, max: 30, unique: [{ e: 37 }, { e: 49 }] },
          tf: "F",
          split: 0,
          startdate: "2020-12-29T12:00:00+00:00",
          vetAgeGroup: "",
          uomType: "D",
          post_date: null,
          prodId: 23563,
          ageGroupId: 12,
          ageInfo: { ageGroupId: 12 },
          eventid: 272,
          uom: [{ pattern: 0.99, text: "metres", short: "mt", uomType: "D" }],
          entered: true,
          entrycnt: 0,
          description: "",
        },
      ] as any as IAthleteCompSchedRuleEvent[];

      const eventsTestKeyObj = athleteCompSchedService.getEventsForAthlete(
        4458,
        events
      );
      expect(eventsTestKeyObj.length).toBe(1);
    });

    test("removeExcludeFromCountRule()", () => {
      const events: IAthleteCompSchedRuleEvent[] = [
        {
          ceid: 1,
          name: "4 x 400 relay",
          eoptions: { eventTeam: { min: 1, max: 4 } },
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 2,
          name: "4 x 400 relay",
          eoptions: { excludeFromCntRule: true, eventTeam: { min: 1, max: 4 } },
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 3,
          ceoptions: { excludeFromCntRule: true },
        } as IAthleteCompSchedRuleEvent,
      ];

      const result: IAthleteCompSchedRuleEvent[] =
        athleteCompSchedService.removeExcludeFromCountRule(events);
      expect(result.length).toBe(1);
    });

    test("maxExcludedEvents()", () => {
      let result;
      let events: IAthleteCompSchedRuleEvent[];
      // let eventsSelected: IAthleteCompSchedRuleEvent[];
      const availablexludedEventsPred = (evt: IAthleteCompSchedRuleEvent) => {
        return (
          !evt.userSelected &&
          athleteCompSchedService.hasExcludeFromCountRule(evt) &&
          !evt.ruleIsDisabledBy
        );
      };
      const availablExludedEvents = R.filter(availablexludedEventsPred);
      let compRule: ICompShedRuleConfig = {
        id: 3,
        options: {
          maxExcludedEvents: 0,
        },
      } as ICompShedRuleConfig;

      events = [
        {
          ceid: 1,
          name: "4 x 400 relay A",
          eoptions: { eventTeam: { min: 1, max: 4 } },
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 2,
          name: "4 x 400 relay B",
          eoptions: { excludeFromCntRule: true, eventTeam: { min: 1, max: 4 } },
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 3,
          name: "4 x 400 relay C",
          ceoptions: { excludeFromCntRule: true },
        } as IAthleteCompSchedRuleEvent,
      ];

      expect(availablExludedEvents(events).length).toBe(2);

      result = athleteCompSchedService.compRuleMaxExcludedEvents(
        compRule,
        events
      );
      //  console.log("a...", result);
      expect(availablExludedEvents(events).length).toBe(2);
      expect(availablExludedEvents(result).length).toBe(2);

      compRule = {
        id: 3,
        options: {
          maxExcludedEvents: 1,
        },
      } as ICompShedRuleConfig;
      result = athleteCompSchedService.compRuleMaxExcludedEvents(
        compRule,
        events
      );
      expect(availablExludedEvents(result).length).toBe(2);

      events = [
        {
          ceid: 1,
          name: "4 x 400 relay A",
          eoptions: { eventTeam: { min: 1, max: 4 } },
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 2,
          userSelected: true,
          name: "4 x 400 relay B",
          eoptions: { excludeFromCntRule: true, eventTeam: { min: 1, max: 4 } },
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 3,
          name: "4 x 400 relay C",
          ceoptions: { excludeFromCntRule: true },
        } as IAthleteCompSchedRuleEvent,
        {
          ceid: 4,
          name: "4 x 400 relay D",
          ceoptions: { excludeFromCntRule: true },
        } as IAthleteCompSchedRuleEvent,
      ];
      // eventsSelected = [
      //     {
      //         ceid: 2,
      //         userSelected: true,
      //         name: "4 x 400 relay B",
      //         eoptions: { excludeFromCntRule: true, eventTeam: {min: 1, max: 4} }
      //     } as IAthleteCompSchedRuleEvent
      // ];
      compRule = {
        id: 3,
        options: {
          maxExcludedEvents: 1,
        },
      } as ICompShedRuleConfig;
      result = athleteCompSchedService.compRuleMaxExcludedEvents(
        compRule,
        events
      );
      expect(availablExludedEvents(events).length).toBe(2);
      // console.log("a...", result);
      expect(availablExludedEvents(result).length).toBe(0);
    });

    test("isSecondarySpendParentSelected()", () => {
      const event = {
        eventid: 876,
        ceid: 1,
        name: "5k run",
        userSelected: true,
        ceoptions: {},
      } as IAthleteCompSchedRuleEvent;

      const events = [
        event,
        {
          eventid: 111,
          ceid: 111,
          name: "T shirt small",
          ceoptions: {
            secondarySpend: {
              parentCeid: 1,
              mandatoryGroupId: 567,
            },
          },
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 112,
          ceid: 112,
          name: "T shirt medium",
          ceoptions: {
            secondarySpend: {
              parentCeid: 1,
              mandatoryGroupId: 567,
            },
          },
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 113,
          ceid: 113,
          name: "Bib pick up on day",
          userSelected: true,
          ceoptions: {
            secondarySpend: {
              parentCeid: 2,
              mandatoryGroupId: 56789,
            },
          },
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 114,
          ceid: 114,
          name: "Some real event 1",
          userSelected: true,
          ceoptions: {},
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 115,
          ceid: 115,
          name: "Some real event 2",
          userSelected: true,
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 876,
          ceid: 2,
          name: "15k run",
          userSelected: false,
          ceoptions: {},
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 116,
          ceid: 116,
          name: "Some T shirt for 15k run",
          ceoptions: {
            secondarySpend: {
              parentCeid: 2,
              mandatoryGroupId: 56789,
              alwaysShow: false,
            },
          },
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 117,
          ceid: 117,
          name: "Some T shirt for 15k run",
          ceoptions: {
            secondarySpend: {
              parentCeid: 2,
              mandatoryGroupId: 56789,
              alwaysShow: true,
            },
          },
        } as IAthleteCompSchedRuleEvent,
        {
          eventid: 118,
          ceid: 118,
          name: "Some T shirt for 15k run",
          ceoptions: {
            secondarySpend: {
              parentCeid: 2,
              mandatoryGroupId: 56789,
            },
          },
        } as IAthleteCompSchedRuleEvent,
      ];

      const result: IObjectKey =
        athleteCompSchedService.getSelectedAsObjectByCeid(events);
      // console.log("isSecondarySpendParentSelected() result", result);
      expect(result["1"].name).toBe("5k run");

      let isSelected: boolean =
        athleteCompSchedService.isSecondarySpendParentSelected(
          result,
          events[1]
        );
      // const parentCeid = events[1].ceoptions.secondarySpend.parentCeid;
      // console.log("isSecondarySpendParentSelected() isSelected: " + isSelected + ", ceid: " + events[1].ceid, result);
      expect(isSelected).toBe(true);

      isSelected = athleteCompSchedService.isSecondarySpendParentSelected(
        result,
        events[3]
      );
      expect(isSelected).toBe(false);

      //  real event with ceoptions...
      isSelected = athleteCompSchedService.isSecondarySpendParentSelected(
        result,
        events[4]
      );
      expect(isSelected).toBe(false);

      isSelected = athleteCompSchedService.hideThisEvent(result, events[6]);
      expect(isSelected).toBe(false);

      isSelected = athleteCompSchedService.hideThisEvent(result, events[7]);
      expect(isSelected).toBe(true);

      isSelected = athleteCompSchedService.hideThisEvent(result, events[8]);
      expect(isSelected).toBe(false);

      isSelected = athleteCompSchedService.hideThisEvent(result, events[9]);
      expect(isSelected).toBe(true);

      //  real event with no ceoptions...
      // isSelected = athleteCompSchedService.isSecondarySpendParentSelected(result, events[4]);
      // expect(isSelected).toBe(false);
    });

    test("showPbInput()", () => {
      let event: IAthleteCompSchedRuleEvent = {
        eventid: 1234,
        ceid: 999333,
        name: "bib",
        ceoptions: {
          secondarySpend: {
            parentCeid: 999,
          },
          rowOptions: {
            showPB: false,
          },
        },
        userSelected: true,
      } as IAthleteCompSchedRuleEvent;

      let result = athleteCompSchedService.showPbInput(event);
      expect(result).toBe(false);

      event = {
        eventid: 1234,
        ceid: 999333,
        name: "bib",
        ceoptions: {
          rowOptions: {
            showPB: false,
          },
          mandatoryPB: false,
        },
        userSelected: true,
      } as IAthleteCompSchedRuleEvent;
      result = athleteCompSchedService.showPbInput(event);
      expect(result).toBe(false);

      event = {
        eventid: 1234,
        ceid: 999333,
        name: "bib",
        ceoptions: {
          rowOptions: {
            showPB: true,
          },
          mandatoryPB: true,
        },
        userSelected: true,
      } as IAthleteCompSchedRuleEvent;
      result = athleteCompSchedService.showPbInput(event);
      expect(result).toBe(true);

      event = {
        eventid: 1234,
        ceid: 999333,
        name: "bib",
        ceoptions: {
          rowOptions: {},
          mandatoryPB: true,
        },
        userSelected: true,
      } as IAthleteCompSchedRuleEvent;
      result = athleteCompSchedService.showPbInput(event);
      expect(result).toBe(true);

      event = {
        eventid: 1234,
        ceid: 999333,
        name: "bib",
        ceoptions: { mandatoryPB: true },
        userSelected: true,
      } as IAthleteCompSchedRuleEvent;
      result = athleteCompSchedService.showPbInput(event);
      expect(result).toBe(true);

      event = {
        eventid: 1234,
        ceid: 999333,
        name: "bib",
        ceoptions: {
          secondarySpend: {
            parentCeid: 9992,
          },
          mandatoryPB: false,
        },
        userSelected: true,
      } as IAthleteCompSchedRuleEvent;
      result = athleteCompSchedService.showPbInput(event);
      expect(result).toBe(false);

      event = {
        eventid: 1234,
        ceid: 999333,
        name: "bib",
        maxathletes: -1,
        ceoptions: {},
        userSelected: true,
      } as IAthleteCompSchedRuleEvent;
      result = athleteCompSchedService.showPbInput(event);
      expect(result).toBe(false);
    });
  });
});

describe("Ramda test", () => {
  test("Sort", () => {
    const alice = {
      name: "alice",
      age: 40,
    } as any;
    const bob = {
      name: "bob",
      age: 30,
    } as any;
    const clara = {
      name: "clara",
      age: 40,
    } as any;
    const people = [clara, bob, alice];

    // @ts-ignore
    const ageNameSort: any = R.sortWith([
      R.ascend(R.prop("age") as any),
      R.ascend(R.prop("name") as any),
    ]) as any;
    const result = ageNameSort(people); // => [alice, clara, bob]

    expect(result[0].name).toBe("bob");
  });
});

// describe("Output tester", () => {
//
//     test("showPbInput()", () => {
//         const cartEvents: IAthleteCompSchedRuleEvent[] = [
//             {
//                 eventid: 1234,
//                 ceid: 999333,
//                 name: "bib",
//                 ceoptions: {
//                     secondarySpend: {
//                         parentCeid: 999
//                     }
//                 },
//                 userSelected: true
//             } as IAthleteCompSchedRuleEvent
//         ];
//
//         //  const result: IUserMessage[] = athleteCompSchedService.canCartBeSubmittedToBasket([event]);
//         const spendEvents: IAthleteCompSchedRuleEvent[] = athleteCompSchedService.getSelectedSecondarySpendItems(cartEvents);
//         expect(spendEvents.length ).toBe(1);
//         const commonService: CommonService = new CommonService();
//         const cartEventsObj = commonService.convertArrayToObject("ceid", cartEvents);
//         const isParentSelected = athleteCompSchedService.isSecondarySpendEventParentUserSelected(spendEvents[0], cartEventsObj);
//         expect( isParentSelected ).toBe(false);
//
//         const result: IUserMessage[] = athleteCompSchedService.canCartBeSubmittedToBasket(cartEvents);
//         //  console.log("canCartBeSubmittedToBasket() result", result);
//         expect(result.length ).toBe(1);
//     });
//
// });

describe("Sched info", () => {
  test("athleteCompSchedService.factorySchedInfo()", () => {
    let schedInfo: ISchedInfo = {
      title: "test",
      shortDescription: "some desc",
    } as ISchedInfo;

    expect(R.isNil(schedInfo.schedInfoDetails)).toBe(true);

    const schedInfoTest: ISchedInfo = {
      title: "test",
      shortDescription: "some desc",
    } as ISchedInfo;
    schedInfo = {
      ...athleteCompSchedService.factorySchedInfo(),
      ...schedInfoTest,
    };
    // console.log("schedInfo", schedInfo);
    expect(R.isNil(schedInfoTest.schedInfoDetails)).toBe(true);
    expect(R.isNil(schedInfo.schedInfoDetails)).toBe(false);
    expect(schedInfo.schedInfoDetails.length).toBe(0);
  });
});

describe("Sched info one off", () => {
  test("athleteCompSchedService.processEvents()", () => {
    const dataX: IAthleteCompSchedRuleEvent = {
      ceid: 46425,
      ageGroupId: 14,
      ageGroupName: "Masters",
      description: "free",
      startdate: "2021-03-01T00:00:00+00:00",
      IsOpen: 1,
      tf: "F",
      uomType: "D",
      price: {
        id: 1278,
        priceName: "",
        stdPrice: 0,
        curPrice: 0,
        salePrice: 0,
        saleDate: "+00:00",
      },
      eventid: 19,
      multiid: 0,
      Name: "Discus",
      eventGroup: "Discus",
      split: 0,
      maxgroup: 25487,
      maxathletes: 5,
      ceoptions: {
        min: 1,
        max: 100,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 0,
        heatInfo: {
          useLanes: "A",
        },
        xiText: "",
        xeText: "",
        warningMessage: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        checkIn: {
          from: 180,
          to: 60,
          checkInMins: 60,
        },
        unregisteredAthletes: true,
        xrText: "",
        xbText: "",
        mandatoryPB: false,
      },
      eoptions: {
        min: 1,
        max: 100,
      },
      ageGroup: {
        id: 14,
        minAge: 35,
        minAtDay: 31,
        minAtMonth: 12,
        keyName: "Masters",
        options: [
          {
            aocode: "EA",
            default: true,
            base: 2,
          },
          {
            aocode: "AAI",
            default: false,
            base: 2,
          },
          {
            aocode: "IRL",
            default: false,
            base: 2,
          },
        ],
        name: "Masters",
        maxAge: 100,
        maxAtDay: 0,
        maxAtMonth: 0,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Masters",
      },
      entered: false,
      entryId: 0,
      paid: 0,
      athleteid: 4463,
      entryInfo: {
        unpaidCount: 2,
        paidCount: 6,
        entryPosition: 0,
        entryCreated: "+00:00",
        totalCount: 8,
      },
      entrycnt: 6,
      compName: "Acme Winter Comp",
      user: {
        userId: 0,
        userName: "",
        userEmail: "",
      },
      firstName: "Brendan",
      surName: "Abbott",
      club: "Tullamore Harriers A.C.",
      uom: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
          uomType: "D",
        },
      ],
    } as any as IAthleteCompSchedRuleEvent;
    expect(athleteCompSchedService.reachedMaxAllowedAthletes(dataX)).toBe(true);

    expect(athleteCompSchedService.applyMaxReachedRule(dataX).ruleMessage).toBe(
      "Event full. Waiting list disabled by organiser."
    );
  });
});

describe("One off tests", () => {
  test("ruleApplyIsOpen()", () => {
    const compEvent = {
      ceid: 46350,
      ageGroupId: 9,
      ageGroupName: "Under 15",
      description: "free",
      startdate: "2021-03-20T00:00:00+00:00",
      IsOpen: 0,
      tf: "F",
      uomType: "H",
      price: {
        id: 1278,
        priceName: "",
        stdPrice: 0,
        curPrice: 0,
        salePrice: 0,
        saleDate: "+00:00",
      },
      eventid: 23,
      multiid: 0,
      Name: "Long Jump",
      eventGroup: "Long Jump",
      split: 0,
      maxgroup: 25479,
      maxathletes: 20,
      ceoptions: {
        min: 1,
        max: 10,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        excludeFromCntRule: false,
        unique: [
          {
            e: 52,
          },
        ],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: true,
          showEntryCount: true,
        },
        maxInHeat: 0,
        heatInfo: {
          useLanes: "A",
        },
        xiText: "",
        xeText: "",
        warningMessage: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        checkIn: {
          from: 180,
          to: 60,
          checkInMins: 60,
        },
        class: "0,11-13,20,35-47,61-64",
        unregisteredAthletes: true,
        xrText: "",
        xbText: "",
        mandatoryPB: false,
      },
      eoptions: {
        min: 1,
        max: 10,
        class: "0,11-13,20,35-47,61-64",
        unique: [
          {
            e: 52,
          },
        ],
      },
      ageGroup: {
        id: 9,
        minAge: 13,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 15",
        options: [
          {
            aocode: "EA",
            default: true,
            base: 1,
          },
        ],
        name: "Under 15",
        maxAge: 14,
        maxAtDay: 31,
        maxAtMonth: 8,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 15",
      },
      entered: false,
      entryId: 0,
      paid: 0,
      athleteid: 4456,
      entryInfo: {
        unpaidCount: 3,
        paidCount: 24,
        entryPosition: 0,
        entryCreated: "+00:00",
        totalCount: 27,
      },
      entrycnt: 24,
      compName: "Acme Winter Comp",
      user: {
        userId: 0,
        userName: "",
        userEmail: "",
      },
      order: {
        orderId: 0,
        productId: 0,
        e4sLineValue: 0,
        wcLineValue: 0,
        isRefund: false,
        refundValue: 0,
        isCredit: false,
        creditValue: 0,
        discountId: 0,
        dateOrdered: "",
      },
      firstName: "Donnacha",
      surName: "'Mote",
      club: "Navan A.C.",
      uom: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
          uomType: "H",
        },
      ],
    } as any as IAthleteCompSchedRuleEvent;

    const comp = {
      date: "2021-03-20T00:00:00",
      today: "2021-03-17",
      systemtime: "2021-03-17 17:04:51",
      compDate: "2021-03-20",
      visibledate: "2021-03-20",
      lastentrymod: "2021-03-17 16:50:00",
      locationid: 113,
      link: "/wp-json/view/23538/1606252968",
      yearFactor: 0,
      compOrgId: 117,
      entriesClose: "2021-03-18T11:00:00",
      options: {
        live: true,
        disabled: false,
        stripeMandatory: false,
        priority: {
          required: false,
          code: "",
          dateTime: "",
          message: "",
        },
        bibNos: 1,
        bibSort1: "",
        bibSort2: "",
        bibSort3: "dob",
        contact: {
          id: 1,
          userName: "paul test",
          tel: "",
          email: "<EMAIL>",
          visible: false,
        },
        checkIn: {
          enabled: true,
          checkInDateTimeOpens: "2021-01-01T00:00:00+00:00",
          defaultFrom: 180,
          defaultTo: 60,
          qrCode: true,
          text: "",
          terms: "",
          useTerms: false,
        },
        school: false,
        cheques: {
          allow: false,
          ends: "",
        },
        allowAdd: {
          registered: true,
          unregistered: true,
        },
        timetable: "provisional",
        helpText: {
          schedule: "",
          teams: "",
          cart: "",
        },
        showTeamAthletes: true,
        singleAge: false,
        stopReport: false,
        showAthleteAgeInEntries: false,
        report: {
          seqeventno: true,
          summary: true,
          athletes: true,
          ttathletes: true,
          ttentries: true,
          individual_entries: true,
          teams: true,
          subscriptions: true,
          orders: true,
          events: true,
        },
        athleteSecurity: {
          areas: [],
          clubs: [],
        },
        athleteQrData: true,
        disabledReason: "",
        paymentCode: "",
        laneCount: 8,
        heatOrder: "s",
        compLimits: {
          athletes: 0,
          entries: 0,
          teams: 0,
        },
        subscription: {
          enabled: true,
          timeCloses: "2021-02-17T17:00:00+00:00",
          organiserMessage: "org message",
          e4sMessage: "<p>e4s message</p>",
          popUpMessage: "",
        },
        cancelEvent: {
          hrsBeforeClose: 32,
          refund: {
            allow: true,
            type: "E4S_FEES",
          },
          credit: {
            allow: true,
          },
        },
        categoryId: 3716,
        organiser: true,
        dates: ["2021-03-02", "2021-03-20"],
        saleEndDate: "2020-12-01 00:00:00",
        selfService: [],
      },
      logo: "/resources/ai_cardlogo.png",
      areaid: 0,
      teamid: 0,
      ctcid: 1,
      maxmale: 2,
      name: "Acme Winter Comp",
      tandc: {
        link: "",
        description:
          "I agree that entry fees paid are non transferable and non refundable for this event.",
      },
      areaname: "",
      entity: "",
      entityid: 0,
      ctc: {
        ctcid: 1,
        maxMale: 2,
        maxFemale: 2,
        maxTeams: 4,
        maxAthletes: 4,
        maxAgeGroup: 1,
        uniqueEvents: 0,
        singleAgeGroup: 0,
      },
      compRules: [
        {
          id: 1,
          agid: 2,
          options: {
            maxCompEvents: 8,
            maxCompField: 3,
            maxCompTrack: 0,
            maxExcludedEvents: 0,
            displayMaxDayFields: false,
          },
        },
        {
          id: 2,
          agid: 9,
          options: {
            maxCompEvents: 6,
            maxCompField: 4,
            maxCompTrack: 0,
            maxExcludedEvents: 0,
            displayMaxDayFields: false,
          },
        },
        {
          id: 4,
          agid: 12,
          options: {
            maxCompEvents: 1,
            maxCompField: 0,
            maxCompTrack: 0,
            maxExcludedEvents: 0,
            displayMaxDayFields: false,
          },
        },
      ],
      id: 351,
      indivEvents: true,
      teamEvents: false,
    } as any as ICompetition;

    //  subscription.timeCloses = "2021-02-17T17:00:00+00:00"
    expect(
      athleteCompSchedService.isBeforeSubscriptionCutOff(
        comp,
        parse("2021-02-17T16:00:00+00:00")
      )
    ).toBe(true);
    expect(
      athleteCompSchedService.isBeforeSubscriptionCutOff(
        comp,
        parse("2021-02-17T17:00:00+00:00")
      )
    ).toBe(false);

    expect(
      athleteCompSchedService.isSubscriptionKeepingOpen(
        compEvent,
        comp,
        parse("2021-02-17T16:00:00+00:00")
      )
    ).toBe(true);
    expect(
      athleteCompSchedService.isSubscriptionKeepingOpen(
        compEvent,
        comp,
        parse("2021-02-17T17:00:00+00:00")
      )
    ).toBe(false);

    // @ts-ignore
    comp.options.subscription.timeCloses = format(
      addHours(new Date(), 2),
      "YYYY-MM-DD[T]HH:mm:ssZ"
    );
    expect(
      athleteCompSchedService.ruleApplyIsOpen(compEvent, comp).IsOpen
    ).toBe(0);
    expect(
      athleteCompSchedService.ruleApplyIsOpen(compEvent, comp).ruleMessage
    ).toBe(undefined);

    // @ts-ignore
    comp.options.subscription.timeCloses = format(
      addHours(new Date(), -2),
      "YYYY-MM-DD[T]HH:mm:ssZ"
    );
    expect(
      athleteCompSchedService.ruleApplyIsOpen(compEvent, comp).IsOpen
    ).toBe(0);
    expect(
      athleteCompSchedService.ruleApplyIsOpen(compEvent, comp).ruleMessage
    ).toBe("Event Closed");

    const userApplication = {
      id: 1495,
      user_login: "E4SAdmin",
      user_nicename: "e4sadmin",
      user_email: "<EMAIL>",
      display_name: "E4S Admin",
      role: "E4SUSER",
      google_email: "",
      impersonating: false,
      e4sCredit: [0],
      permissions: [
        {
          id: 43,
          userid: 1495,
          role: { id: 4, name: "admin" },
          comp: { id: 0, name: "All" },
          org: { id: 0, name: "All" },
          permLevels: [],
        },
        {
          id: 44,
          userid: 1495,
          role: { id: 7, name: "Builder" },
          comp: { id: 0, name: "All" },
          org: { id: 0, name: "All" },
          permLevels: [
            { roleid: 7, role: "Builder", id: 1, name: "ADMIN" },
            { roleid: 7, role: "Builder", id: 2, name: "FRONT" },
            { roleid: 7, role: "Builder", id: 3, name: "OPTIONS" },
            { roleid: 7, role: "Builder", id: 4, name: "COMP_EVENTS" },
            { roleid: 7, role: "Builder", id: 5, name: "SCHEDULE" },
          ],
        },
      ],
    } as any as IUserApplication;

    const eventsFromFactory: IAthleteCompSchedRuleEvent[] =
      athleteCompSchedService.factoryEventsToEventRule([compEvent]).map((x) => {
        x.perfInfo = simpleClone(mockPerfInfo);
        return x;
      });

    const athlete = {
      id: 3145,
      firstName: "Kht,",
      surName: ",kg",
      aocode: "",
      URN: null,
      dob: "2004-04-05",
      gender: "M",
      email: "<EMAIL>",
      classification: 0,
      schoolid: 1,
      clubid: 544,
      club2id: 0,
      activeEndDate: "2020-12-31",
      type: "A",
      options: {
        noEntryReason: "",
      },
      club: "Dunleer A.C.",
      club2: "",
      events: [],
      userAthletes: [
        {
          id: 1495,
          email: "<EMAIL>",
          userName: "e4sadmin",
        },
      ],
      pbInfo: [],
    } as any as IAthlete;

    //  Before sub expire time
    // @ts-ignore
    comp.options.subscription.timeCloses = format(
      addHours(new Date(), 2),
      "YYYY-MM-DD[T]HH:mm:ssZ"
    );
    let results = athleteCompSchedService.processEvents(
      athlete,
      {} as IPbKey,
      {} as ICompShedRuleConfig,
      null,
      [],
      eventsFromFactory,
      userApplication,
      {
        ageGroupId: 3,
      } as IAgeInfo,
      comp
    );

    let res = results[0];
    expect(res.ruleMessage).toBe("");

    //  After sub expire time.
    // @ts-ignore
    comp.options.subscription.timeCloses = format(
      addHours(new Date(), -2),
      "YYYY-MM-DD[T]HH:mm:ssZ"
    );
    results = athleteCompSchedService.processEvents(
      athlete,
      {} as IPbKey,
      {} as ICompShedRuleConfig,
      null,
      [],
      eventsFromFactory,
      userApplication,
      {
        ageGroupId: 3,
      } as IAgeInfo,
      comp
    );

    res = results[0];
    expect(res.ruleMessage).toBe("Event Closed");

    const date = new Date();
    const subsClose = "2021-02-17T22:00:00+00:00";
    expect(isBefore(date, parse(subsClose))).toBe(false);
  });
});

describe("One off tests 2", () => {
  test("ruleApplyIsOpen()", () => {
    const compEvent = {
      ceid: 46417,
      ageGroupId: 9,
      ageGroupName: "Under 15",
      description: "free",
      startdate: "2021-03-29T00:00:00+01:00",
      IsOpen: 1,
      tf: "F",
      uomType: "D",
      price: {
        id: 1278,
        priceName: "",
        stdPrice: 0,
        curPrice: 0,
        salePrice: 0,
        saleDate: "+00:00",
      },
      eventid: 19,
      multiid: 0,
      Name: "Discus",
      eventGroup: "Discus",
      split: 0,
      maxgroup: 25487,
      maxathletes: 5,
      ceoptions: {
        min: 1,
        max: 100,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 0,
        heatInfo: {
          useLanes: "A",
        },
        xiText: "",
        xeText: "",
        warningMessage: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        checkIn: {
          from: 180,
          to: 60,
          checkInMins: 60,
        },
        unregisteredAthletes: true,
        xrText: "",
        xbText: "",
        mandatoryPB: false,
      },
      eoptions: {
        min: 1,
        max: 100,
      },
      ageGroup: {
        id: 9,
        minAge: 13,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 15",
        options: [
          {
            aocode: "EA",
            default: true,
            base: 1,
          },
        ],
        name: "Under 15",
        maxAge: 14,
        maxAtDay: 31,
        maxAtMonth: 8,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 15",
      },
      entered: false,
      entryId: 0,
      paid: 0,
      athleteid: 4456,
      entryInfo: {
        unpaidCount: 3,
        paidCount: 7,
        entryPosition: 0,
        entryCreated: "+00:00",
        totalCount: 10,
      },
      entrycnt: 7,
      compName: "Acme Winter Comp",
      user: {
        userId: 0,
        userName: "",
        userEmail: "",
      },
      order: {
        orderId: 0,
        productId: 0,
        e4sLineValue: 0,
        wcLineValue: 0,
        isRefund: false,
        refundValue: 0,
        isCredit: false,
        creditValue: 0,
        discountId: 0,
        dateOrdered: "",
      },
      firstName: "Donnacha",
      surName: "'Mote",
      club: "Navan A.C.",
      uom: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
          uomType: "D",
        },
      ],
      perfInfo: simpleClone(mockPerfInfo),
    } as any as IAthleteCompSchedRuleEvent;

    const comp = {
      date: "2021-03-29T00:00:00",
      today: "2021-03-24",
      systemtime: "2021-03-24 15:25:16",
      compDate: "2021-03-29",
      visibledate: "2021-03-29",
      lastentrymod: "2021-03-24 15:25:13",
      locationid: 113,
      link: "/wp-json/view/23538/1606252968",
      yearFactor: 0,
      compOrgId: 117,
      entriesClose: "2021-03-28T11:00:00",
      options: {
        live: false,
        disabled: false,
        stripeMandatory: false,
        priority: {
          required: false,
          code: "abc",
          dateTime: "2021-03-24T00:00:00+00:00",
          message: "passed priority code",
        },
        bibNos: 1,
        bibSort1: "",
        bibSort2: "",
        bibSort3: "dob",
        contact: {
          id: 1,
          userName: "paul test",
          tel: "",
          email: "<EMAIL>",
          visible: false,
        },
        checkIn: {
          enabled: true,
          checkInDateTimeOpens: "2021-01-01T00:00:00+00:00",
          defaultFrom: 180,
          defaultTo: 60,
          qrCode: true,
          text: "",
          terms: "",
          useTerms: false,
        },
        school: false,
        cheques: {
          allow: false,
          ends: "",
        },
        allowAdd: {
          registered: true,
          unregistered: true,
        },
        timetable: "provisional",
        helpText: {
          schedule: "",
          teams: "",
          cart: "",
        },
        showTeamAthletes: true,
        singleAge: false,
        stopReport: false,
        showAthleteAgeInEntries: false,
        report: {
          seqeventno: true,
          summary: true,
          athletes: true,
          ttathletes: true,
          ttentries: true,
          individual_entries: true,
          teams: true,
          subscriptions: true,
          orders: true,
          events: true,
        },
        athleteSecurity: {
          areas: [],
          clubs: [],
        },
        athleteQrData: true,
        disabledReason: "Under construction",
        paymentCode: "",
        laneCount: 8,
        heatOrder: "s",
        compLimits: {
          athletes: 0,
          entries: 0,
          teams: 0,
        },
        subscription: {
          enabled: true,
          timeCloses: "2021-03-18T11:00:00+00:00",
          organiserMessage: "org message",
          e4sMessage: "<p>e4s message</p>",
          popUpMessage: "",
        },
        cancelEvent: {
          hrsBeforeClose: 32,
          refund: {
            allow: true,
            type: "E4S_FEES",
          },
          credit: {
            allow: true,
          },
        },
        categoryId: 3716,
        organiser: true,
        dates: ["2021-03-02", "2021-03-29"],
        saleEndDate: "2020-12-01 00:00:00",
        selfService: [],
      },
      logo: "/resources/ai_cardlogo.png",
      areaid: 0,
      teamid: 0,
      ctcid: 1,
      maxmale: 2,
      name: "Acme Winter Comp",
      tandc: {
        link: "",
        description:
          "I agree that entry fees paid are non transferable and non refundable for this event.",
      },
      areaname: "",
      entity: "",
      entityid: 0,
      ctc: {
        ctcid: 1,
        maxMale: 2,
        maxFemale: 2,
        maxTeams: 4,
        maxAthletes: 4,
        maxAgeGroup: 1,
        uniqueEvents: 0,
        singleAgeGroup: 0,
      },
      compRules: [
        {
          id: 1,
          agid: 2,
          options: {
            maxCompEvents: 8,
            maxCompField: 3,
            maxCompTrack: 0,
            maxExcludedEvents: 0,
            displayMaxDayFields: false,
          },
        },
        {
          id: 2,
          agid: 9,
          options: {
            maxCompEvents: 6,
            maxCompField: 4,
            maxCompTrack: 0,
            maxExcludedEvents: 0,
            displayMaxDayFields: false,
          },
        },
        {
          id: 4,
          agid: 12,
          options: {
            maxCompEvents: 1,
            maxCompField: 0,
            maxCompTrack: 0,
            maxExcludedEvents: 0,
            displayMaxDayFields: false,
          },
        },
      ],
      id: 351,
      indivEvents: true,
      teamEvents: false,
    } as any as ICompetition;

    const userApplication = {
      id: 1495,
      user_login: "E4SAdmin",
      user_nicename: "e4sadmin",
      user_email: "<EMAIL>",
      display_name: "E4S Admin",
      role: "E4SUSER",
      google_email: "",
      impersonating: false,
      e4sCredit: [0],
      permissions: [
        {
          id: 43,
          userid: 1495,
          role: { id: 4, name: "admin" },
          comp: { id: 0, name: "All" },
          org: { id: 0, name: "All" },
          permLevels: [],
        },
        {
          id: 44,
          userid: 1495,
          role: { id: 7, name: "Builder" },
          comp: { id: 0, name: "All" },
          org: { id: 0, name: "All" },
          permLevels: [
            { roleid: 7, role: "Builder", id: 1, name: "ADMIN" },
            { roleid: 7, role: "Builder", id: 2, name: "FRONT" },
            { roleid: 7, role: "Builder", id: 3, name: "OPTIONS" },
            { roleid: 7, role: "Builder", id: 4, name: "COMP_EVENTS" },
            { roleid: 7, role: "Builder", id: 5, name: "SCHEDULE" },
          ],
        },
      ],
    } as any as IUserApplication;

    const eventsFromFactory: IAthleteCompSchedRuleEvent[] =
      athleteCompSchedService.factoryEventsToEventRule([compEvent]).map((x) => {
        x.perfInfo = simpleClone(mockPerfInfo);
        return x;
      });

    const athlete = {
      id: 3145,
      firstName: "Kht,",
      surName: ",kg",
      aocode: "",
      URN: null,
      dob: "2004-04-05",
      gender: "M",
      email: "<EMAIL>",
      classification: 0,
      schoolid: 1,
      clubid: 544,
      club2id: 0,
      activeEndDate: "2020-12-31",
      type: "A",
      options: {
        noEntryReason: "",
      },
      club: "Dunleer A.C.",
      club2: "",
      events: [],
      userAthletes: [
        {
          id: 1495,
          email: "<EMAIL>",
          userName: "e4sadmin",
        },
      ],
      pbInfo: [],
    } as any as IAthlete;

    //  Before sub expire time
    // @ts-ignore
    comp.options.subscription.timeCloses = format(
      addHours(new Date(), 2),
      "YYYY-MM-DD[T]HH:mm:ssZ"
    );
    let results = athleteCompSchedService.processEvents(
      athlete,
      {} as IPbKey,
      {} as ICompShedRuleConfig,
      null,
      [],
      eventsFromFactory,
      userApplication,
      {
        ageGroupId: 3,
      } as IAgeInfo,
      comp
    );

    let res = results[0];
    expect(res.ruleMessage).toBe("");

    //  After sub expire time.
    // @ts-ignore
    comp.options.subscription.timeCloses = format(
      addHours(new Date(), -2),
      "YYYY-MM-DD[T]HH:mm:ssZ"
    );
    results = athleteCompSchedService.processEvents(
      athlete,
      {} as IPbKey,
      {} as ICompShedRuleConfig,
      null,
      [],
      eventsFromFactory,
      userApplication,
      {
        ageGroupId: 3,
      } as IAgeInfo,
      comp
    );

    res = results[0];
    expect(res.ruleMessage).toBe("Waiting List now closed");
  });
});
