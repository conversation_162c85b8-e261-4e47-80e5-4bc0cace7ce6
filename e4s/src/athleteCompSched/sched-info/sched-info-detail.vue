<template>
    <div>
        <span class="e4s-bold" v-text="schedInfoDetail.title"></span>
        <span v-html="schedInfoDetail.body"></span>
    </div>
</template>

<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop} from "vue-property-decorator";
    import { ISchedInfoDetail } from "../athletecompsched-models";
    // import {ISchedInfoDetail} from "../athletecompsched-models";

    @Component({
        name: "sched-info-detail"
    })
    export default class SchedInfoDetail extends Vue {
        @Prop() public schedInfoDetail: ISchedInfoDetail;
    }
</script>
