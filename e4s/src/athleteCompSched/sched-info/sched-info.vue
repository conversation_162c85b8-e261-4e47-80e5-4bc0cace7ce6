<template>
  <div class="sched-info">
    <!--schedInfo{{schedInfo.schedInfoDetails}}-->
    <!--displayMore{{displayMore}}<br>-->
    <!--shouldAutoExpand{{shouldAutoExpand}}<br>-->
    <!--showDetailsSection{{showDetailsSection}}-->
    <div>
      <i class="material-icons e4s-icon">info_outline</i>
      <div class="e4s-force-inline-block event-card-title">
        <slot name="title">
          <span v-text="schedInfo.title"></span>
        </slot>
      </div>
    </div>

    <slot name="short-description">
      <div
        v-if="schedInfo.shortDescription.length > 0"
        v-text="schedInfo.shortDescription"
      ></div>
    </slot>

    <!--        <slot name="links">-->
    <!--            <div v-if="showLinks">-->
    <!--                <a v-if="!displayMore" href="#" @click.prevent="handleDisplayMore(true)">More Info...</a>-->
    <!--                <a v-if="displayMore" href="#" @click.prevent="handleDisplayMore(false)">Close Info...</a>-->
    <!--            </div>-->
    <!--        </slot>-->

    <slot name="detail">
      <div class="sched-info-details" v-show="showDetailsSection">
        <sched-info-detail
          v-for="(schedInfoDetail, index) in schedInfo.schedInfoDetails"
          :key="index"
          class="sched-info-detail"
          v-bind:schedInfoDetail="schedInfoDetail"
        >
        </sched-info-detail>
      </div>
    </slot>

    <slot name="extra"></slot>

    <!--        <div class="e4s-card-standard-sep"></div>-->
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {
  ISchedInfo,
  ISchedInfoDetail,
  IRowOptions,
} from "../athletecompsched-models";
import SchedInfoDetail from "./sched-info-detail.vue";
import { SchedInfoService } from "./sched-info-service";

@Component({
  name: "sched-info",
  components: {
    "sched-info-detail": SchedInfoDetail,
  },
})
export default class SchedInfo extends Vue {
  @Prop({
    default: () => {
      return {
        title: "",
        shortDescription: "",
        autoExpand: false,
        showLinks: false,
        schedInfoDetails: [] as ISchedInfoDetail[],
        rowOptions: {} as IRowOptions,
      } as ISchedInfo;
    },
  })
  public schedInfo: ISchedInfo;

  public schedInfoService: SchedInfoService = new SchedInfoService();

  public displayMore: boolean = false;

  public mounted() {
    this.displayMore = this.schedInfoService.shouldAutoExpand(this.schedInfo);
  }

  @Watch("schedInfo")
  public onSchedInfoChanged(newValue: ISchedInfo) {
    this.displayMore = this.schedInfoService.shouldAutoExpand(newValue);
  }

  public get hasSchedInfoDetails(): boolean {
    return this.schedInfoService.hasSchedInfoDetails(this.schedInfo);
  }

  public get showLinks() {
    return this.schedInfoService.canShowLinks(this.schedInfo);
  }

  public handleDisplayMore(showMore: boolean) {
    this.displayMore = showMore;
  }

  public get shouldAutoExpand() {
    return this.schedInfoService.shouldAutoExpand(this.schedInfo);
  }

  public get showDetailsSection() {
    return this.displayMore;
  }
}
</script>
