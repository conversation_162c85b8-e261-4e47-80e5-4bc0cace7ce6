<template>
    <div>
        <AutoCompleteMat
                :field-label="fieldLabel"
                :custom="getCustomForAthleteAutoComplete"
                :data = "athletes"
                iconClassName=""
                :placeholder="fieldLabel"
                :is-loading="isLoadingAthletes"
                :user-input-preload="getAthleteDefaultName"
                v-on:searchTermChanged="athleteSearchTermChanged"
                v-on:autoSelectionMade="onAthleteSelected">
        </AutoCompleteMat>
    </div>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import { ICustom, IAutoCompleteValue } from "../common/ui/autocomplete/auto-complete-mat-models";
    import { EventTeamService } from "./comp-event-teams/event-team-service";
    import type { IAthleteSummary, IAthleteSearch } from "../athlete/athlete-models";
    import AutoCompleteMat from "../common/ui/autocomplete/auto-complete-mat.vue";
    import { Prop, Watch } from "vue-property-decorator";
    import { AthleteService } from "../athlete/athlete-service";
    import { AthleteData } from "../athlete/athlete-data";
    import { debounce } from "../common/debounce";
    import type {GenderType, IServerPagingResponseList} from "../common/common-models"
    import type {IEntity} from "../config/config-app-models";
    import {ConfigService} from "../config/config-service";

    let athleteService: AthleteService;
    let athleteData: AthleteData;
    let eventTeamService: EventTeamService;
    let configService: ConfigService;

    @Component({
        name: "athlete-type-ahead",
        components: {
            AutoCompleteMat
        }
    })
    export default class AthleteTypeAhead extends Vue {
        @Prop({default: () => {
                return {
                    id: 0,
                    URN: "",
                    aocode: "",
                    firstName: "",
                    surName: ""
                } as IAthleteSummary;
            }
        }) public readonly athleteDefault: IAthleteSummary;
        @Prop({
            default: 1
        }) public readonly position: number;
        @Prop({
            default: 0
        }) public readonly competitionId: number;
        @Prop({
            default: 0
        }) public readonly ceid: number;
        @Prop({
            default: 0
        }) public readonly teamId: number;
        @Prop({
            default: ""
        }) public readonly gender: GenderType;
        @Prop({
            default: ""
        }) public readonly fieldLabel: string;
        @Prop({
            default: () => []
        }) public readonly ageGroupIds: number[];
        @Prop({
            default: true
        }) public readonly showAllAthletes: boolean;
        @Prop({
            default: () => {
                return {} as IEntity;
            }
        }) public readonly userEntity: IEntity;


        public athlete: IAthleteSummary = {
            id: 0,
            URN: "",
            aocode: "",
            firstName: "",
            surName: "",
            dob: "",
            club: "",
            clubname: "",
            clubid: 0,
            clubId: 0,
            club2: "",
            club2id: 0,
            club2Id: 0,
            gender: "",
            classification: 0,
            schoolid: 0,
            school: "",
            inTeam: false,
            ageInfo: {
                ageGroup: { id: 0, name: "", Name: "", keyName: "", shortName: "", minAge: 0, minAtDay: 0, minAtMonth: 0, minAtYear: 0, maxAge: 0, maxAtDay: 0, maxAtMonth: 0, maxAtYear: 0, options: [] },
                ageGroups: [],
                vetAgeGroup: { Name: "", shortName: "" },
                competitionAge: null,
                currentAge: 0
            },
            activeEndDate: "",
            image: "",
            options: {
                noEntryReason: "",
                emergency: { name: "", tel: "", relationship: "" },
                socials: { tiktok: "", instagram: "", facebook: "" },
                coach: "",
                trainingGroup: "",
                genericCompAthleteEntities: {}
            },
            pbInfo: [],
            email: "",
            infoText: ""
        } as IAthleteSummary;

        public athletes: IAthleteSummary[] = [] as IAthleteSummary[];
        public isLoadingAthletes: boolean = false;
        public selectedAthlete: IAthleteSummary = {
            id: 0,
            URN: "",
            aocode: "",
            firstName: "",
            surName: "",
            dob: "",
            club: "",
            clubname: "",
            clubid: 0,
            clubId: 0,
            club2: "",
            club2id: 0,
            club2Id: 0,
            gender: "",
            classification: 0,
            schoolid: 0,
            school: "",
            inTeam: false,
            ageInfo: {
                ageGroup: { id: 0, name: "", Name: "", keyName: "", shortName: "", minAge: 0, minAtDay: 0, minAtMonth: 0, minAtYear: 0, maxAge: 0, maxAtDay: 0, maxAtMonth: 0, maxAtYear: 0, options: [] },
                ageGroups: [],
                vetAgeGroup: { Name: "", shortName: "" },
                competitionAge: null,
                currentAge: 0
            },
            activeEndDate: "",
            image: "",
            options: {
                noEntryReason: "",
                emergency: { name: "", tel: "", relationship: "" },
                socials: { tiktok: "", instagram: "", facebook: "" },
                coach: "",
                trainingGroup: "",
                genericCompAthleteEntities: {}
            },
            pbInfo: [],
            email: "",
            infoText: ""
        } as IAthleteSummary;
        public searchTextEntered: string = "";

        public debounceSearch: any = null;

        public mounted() {
            // Initialize services
            const athleteService = new AthleteService();
            const athleteData = new AthleteData();
            const eventTeamService = new EventTeamService();
            const configService = new ConfigService();
            
            // Initialize component state
            this.athlete = R.clone(this.athleteDefault);
            this.selectedAthlete = athleteService.factoryGetAthlete();
            this.debounceSearch =  debounce((
                id: number,
                teamId: number,
                pageNumber: number,
                pageSize: number,
                orderByProperty: string,
                athleteSearch: IAthleteSearch
            ) => {

                athleteData.findAthletes(this.competitionId, teamId, pageNumber, pageSize, orderByProperty, athleteSearch)
                    .then((response: IServerPagingResponseList<IAthleteSummary>) => {
                        this.athletes = R.clone( response.data as IAthleteSummary[]);
                        this.isLoadingAthletes = false;
                    });
            }, 100);
        }

        @Watch("athleteDefault")
        public onAthleteDefaultChanged(newValue: IAthleteSummary) {
            this.athlete = R.clone(newValue);
        }

        public getLabelForAthleteAutoComplete(athlete: IAthleteSummary): string {
            return new EventTeamService().getLabelForAthleteAutoComplete(athlete);
        }

        public get getCustomForAthleteAutoComplete(): ICustom {
            return {
                dropDownLabelFunc: this.getLabelForAthleteAutoComplete
            } as ICustom;
        }

        public athleteSearchTermChanged(searchKey: string) {
            console.log("AthleteTypeAhead.athleteSearchTermChanged()...searchKey: >" + searchKey + "<");
            if (searchKey.length === 0) {
                console.log("AthleteTypeAhead.athleteSearchTermChanged()...searchKey: >" + searchKey + "<  DO NOT SEARCH");
                this.$emit("athleteRemoved", this.position);
                return;
            }

            this.searchTextEntered = searchKey;

            const athleteSearch: IAthleteSearch = new AthleteService().factoryGetAthleteSearch();
            athleteSearch.search = searchKey;
            athleteSearch.gender = this.gender;
            athleteSearch.ageGroupId = this.ageGroupIds.join(",");
            athleteSearch.ceid = this.ceid;
            athleteSearch.showAllAthletes = this.showAllAthletes ? "1" : "0";

            if (this.userEntity.id > 0) {
                if (this.userEntity.entityName === "Region") {
                    athleteSearch.region = this.userEntity.name;
                }
                if (this.userEntity.entityName === "County") {
                    athleteSearch.county = this.userEntity.name;
                }
                if (this.userEntity.entityName === "Club") {
                    athleteSearch.club = this.userEntity.name;
                }
            }

            this.isLoadingAthletes = true;
            const teamId = 0;
            const pageNumber = 1;
            const pageSize = 5;
            const orderByProperty = "surname";

            this.debounceSearch(this.competitionId, teamId, pageNumber, pageSize, orderByProperty, athleteSearch);
        }

        public get getAthleteDefaultName() {
            if (this.athlete.firstName.length > 0 || this.athlete.surName.length > 0) {
                return this.athlete.firstName + " " + this.athlete.surName;
            }
            return "";
        }

        public onAthleteSelected(autoCompleteValue: IAutoCompleteValue) {
            this.athlete = R.clone(autoCompleteValue.value);
            this.$emit("athleteSelected", {
                athlete: R.clone(autoCompleteValue.value),
                position: this.position
            });
        }

        public get getPlaceholder() {
            return this.selectedAthlete && (this.selectedAthlete.id >  0) ? "" : "Enter athlete name";
        }

    }
</script>
