import { AthleteCompSchedService } from "../athletecompsched-service";
import {
  ageInfoRules,
  athleteRules,
  competitionRules,
  compRuleRules,
  eventsRules,
  pbkeyRules,
  userApplicationRules,
  userInfoRules,
} from "./mock-rules";
import { IAthleteCompSchedRuleEvent } from "../athletecompsched-models";
import { simpleClone } from "../../common/common-service-utils";
import { mockPerfInfo } from "./mock-perf-info";

const athleteCompSchedService: AthleteCompSchedService =
  new AthleteCompSchedService();

describe("AthleteCompSched-test", () => {
  test("rules test processEventsTest()", () => {
    const compEvents: IAthleteCompSchedRuleEvent[] = (
      simpleClone(eventsRules) as any as IAthleteCompSchedRuleEvent[]
    ).map((evt) => {
      evt.perfInfo = simpleClone(mockPerfInfo);
      return evt;
    });

    expect(compEvents.map((evt) => evt.eventGroup).join(",")).toBe(
      "100m,200m,400m,800m"
    );

    const eventSource400m = compEvents[2];
    expect(eventSource400m.eventGroup).toBe("400m");
    expect(eventSource400m.ruleMessage).toBe("");

    const result = athleteCompSchedService.processEventsTest(
      athleteRules,
      pbkeyRules,
      compRuleRules,
      null,
      [],
      compEvents,
      userApplicationRules,
      ageInfoRules,
      competitionRules,
      userInfoRules
    );
    expect(result.events.length).toBe(4);
    expect(result.events.map((evt) => evt.eventGroup).join(",")).toBe(
      "100m,200m,400m,800m"
    );

    //  Should not be mutated
    expect(eventSource400m.ruleMessage).toBe("");

    const event100m = result.events[0];
    expect(event100m.eventGroup).toBe("100m");

    const event200m = result.events[1];
    expect(event200m.eventGroup).toBe("200m");

    const event400m = result.events[2];
    expect(event400m.eventGroup).toBe("400m");
    expect(compEvents[2].eventGroup).toBe("400m");

    const event800m = result.events[3];
    expect(event800m.eventGroup).toBe("800m");

    //  In cart
    expect(event100m.order.productId > 0).toBe(true);
    expect(event200m.order.productId > 0).toBe(true);
    expect(event400m.order.productId > 0).toBe(false);
    expect(event800m.order.productId > 0).toBe(false);

    // expect(result.debug).toBe(123454);

    //  Rule applied
    expect(event100m.ruleMessage).toBe("Max in Day");
    expect(event200m.ruleMessage).toBe("Max in Day");
    expect(event400m.ruleMessage).toBe(
      "Entered into the maximum (2) events allowed."
    ); //  <TODO this is wrong
    expect(event800m.ruleMessage).toBe(
      "Entered into the maximum (2) events allowed."
    );

    //  Should not be mutated
    expect(eventSource400m.ruleMessage).toBe("");

    const applyCompRulesToEventsFromSelectedResult =
      athleteCompSchedService.applyCompRulesToEventsFromSelected(
        compRuleRules,
        event400m,
        compEvents,
        athleteRules
      );
    expect(applyCompRulesToEventsFromSelectedResult.length).toBe(4);
    //  Should not be mutated
    expect(eventSource400m.ruleMessage).toBe("");

    expect(applyCompRulesToEventsFromSelectedResult[2].ruleMessage).toBe(
      "Entered into the maximum (2) events allowed."
    );
  });

  test("rules test processEvents()", () => {
    const compEvents: IAthleteCompSchedRuleEvent[] = (
      simpleClone(eventsRules) as any as IAthleteCompSchedRuleEvent[]
    ).map((evt) => {
      evt.perfInfo = simpleClone(mockPerfInfo);
      return evt;
    });

    expect(compEvents.map((evt) => evt.eventGroup).join(",")).toBe(
      "100m,200m,400m,800m"
    );

    const eventSource400m = compEvents[2];
    expect(eventSource400m.eventGroup).toBe("400m");
    expect(eventSource400m.ruleMessage).toBe("");

    const result = athleteCompSchedService.processEvents(
      athleteRules,
      pbkeyRules,
      compRuleRules,
      null,
      [],
      compEvents,
      userApplicationRules,
      ageInfoRules,
      competitionRules,
      userInfoRules
    );
    expect(result.length).toBe(4);
    expect(result.map((evt) => evt.eventGroup).join(",")).toBe(
      "100m,200m,400m,800m"
    );

    //  Should not be mutated
    expect(eventSource400m.ruleMessage).toBe("");

    const event100m = result[0];
    expect(event100m.eventGroup).toBe("100m");

    const event200m = result[1];
    expect(event200m.eventGroup).toBe("200m");

    const event400m = result[2];
    expect(event400m.eventGroup).toBe("400m");
    expect(compEvents[2].eventGroup).toBe("400m");

    const event800m = result[3];
    expect(event800m.eventGroup).toBe("800m");

    //  In cart
    expect(event100m.order.productId > 0).toBe(true);
    expect(event200m.order.productId > 0).toBe(true);
    expect(event400m.order.productId > 0).toBe(false);
    expect(event800m.order.productId > 0).toBe(false);

    // expect(result.debug).toBe(123454);

    //  Rule applied
    expect(event100m.ruleMessage).toBe("Max in Day");
    expect(event200m.ruleMessage).toBe("Max in Day");
    expect(event400m.ruleMessage).toBe(
      "Entered into the maximum (2) events allowed."
    ); //  <TODO this is wrong
    expect(event800m.ruleMessage).toBe(
      "Entered into the maximum (2) events allowed."
    );

    //  Should not be mutated
    expect(eventSource400m.ruleMessage).toBe("");

    const applyCompRulesToEventsFromSelectedResult =
      athleteCompSchedService.applyCompRulesToEventsFromSelected(
        compRuleRules,
        event400m,
        compEvents,
        athleteRules
      );
    expect(applyCompRulesToEventsFromSelectedResult.length).toBe(4);
    //  Should not be mutated
    expect(eventSource400m.ruleMessage).toBe("");

    expect(applyCompRulesToEventsFromSelectedResult[2].ruleMessage).toBe(
      "Entered into the maximum (2) events allowed."
    );
  });
});
