import { IAthlete } from "../../../athlete/athlete-models";
import {
  IAgeInfo,
  IAthleteCompSchedRuleEvent,
  ICompShedRuleConfig,
  IPbKey,
} from "../../athletecompsched-models";
import { IUserApplication, IUserInfo } from "../../../config/config-app-models";
import { ICompetition } from "../../../competition/competition-models";

export const athleteMaxEntered: IAthlete = {
  id: 163139,
  firstName: "<PERSON>",
  surName: "AARON",
  aocode: "EA",
  URN: 3980160,
  pof10id: 1081388,
  dob: "2004-05-30",
  curAgeGroup: "U23",
  gender: "M",
  classification: 0,
  email: "",
  clubid: 1419,
  club2id: 0,
  type: "A",
  options: {
    urnChecked: null,
    noEntryReason: "",
    emergency: {
      name: "",
      tel: "",
      relationship: "",
    },
  },
  activeEndDate: "2024-03-31",
  lastChecked: "2024-02-29 04:31:00",
  lastCheckedPB: "2024-02-29 04:31:03",
  image:
    "https://www.thepowerof10.info/athletes/profilepic.aspx?athleteid=1081388",
  aai_id: null,
  club: "Shaftesbury Barnet Harriers",
  clubId: 1419,
  club2Id: 0,
  schoolId: 0,
  club2: "",
  school: "",
  pbInfo: [
    {
      eventid: 4,
      pb: 0,
      pbid: 0,
      pbText: "",
      pof10pb: 0,
      sb: 0,
      eventName: "100m",
      options: {
        maxInHeat: 8,
        min: 9.6,
        max: 20,
        helpText: "",
        wind: "E",
      },
      min: 10,
      max: 20,
      uomInfo: {
        id: 1,
        type: "T",
        options: [
          {
            pattern: "s.SS",
            text: "seconds",
            short: "secs",
          },
        ],
      },
    },
    {
      eventid: 11,
      pb: 61.55,
      pbid: 72166,
      pbText: 61.55,
      pof10pb: 61.55,
      sb: 61.55,
      eventName: "400m",
      options: {
        maxInHeat: 8,
        min: 40,
        max: 120,
      },
      min: 40,
      max: 120,
      uomInfo: {
        id: 11,
        type: "T",
        options: [
          {
            pattern: "m.ss.SS",
            text: "minutes",
            short: "mins",
          },
          {
            pattern: "m:ss.SS",
            text: "minutes",
            short: "mins",
          },
          {
            pattern: "s.SS",
            text: "seconds",
            short: "s",
          },
        ],
      },
    },
    {
      eventid: 15,
      pb: 268.26,
      pbid: 71930,
      pbText: 286.47,
      pof10pb: 268.26,
      sb: 0,
      eventName: "1500m",
      options: {
        maxInHeat: 17,
        min: 150,
        max: 420,
      },
      min: 150,
      max: 420,
      uomInfo: {
        id: 3,
        type: "T",
        options: [
          {
            pattern: "m.ss.SS",
            text: "mins",
            short: "mins",
          },
          {
            pattern: "m:ss.SS",
            text: "mins",
            short: "mins",
          },
        ],
      },
    },
    {
      eventid: 17,
      pb: 130.56,
      pbid: 59573,
      pbText: "2.20.00",
      pof10pb: 130.56,
      sb: 134.37,
      eventName: "800m",
      options: {
        maxInHeat: 8,
        min: 80,
        max: 240,
      },
      min: 80,
      max: 240,
      uomInfo: {
        id: 3,
        type: "T",
        options: [
          {
            pattern: "m.ss.SS",
            text: "mins",
            short: "mins",
          },
          {
            pattern: "m:ss.SS",
            text: "mins",
            short: "mins",
          },
        ],
      },
    },
  ],
} as any as IAthlete;

export const maxEnteredPbKey = {
  "4": 0,
  "11": 61.55,
  "15": 268.26,
  "17": 130.56,
} as any as IPbKey;

export const maxEnteredCompRules: ICompShedRuleConfig = {
  id: 215,
  agid: 215,
  options: {
    maxCompEvents: 2,
    maxCompField: 1,
    maxCompTrack: 1,
    maxExcludedEvents: 0,
    unique: [],
    // displayMaxDayFields: false,
    maxTeamEvents: 0,
  },
} as any as ICompShedRuleConfig;

export const maxEnteredEvents: IAthleteCompSchedRuleEvent[] = [
  {
    ceid: 41636,
    ageGroupId: 11,
    ageGroupName: "Under 20",
    description: "SSS",
    startdate: "2024-03-31T00:00:00+01:00",
    IsOpen: 1,
    tf: "T",
    uomType: "T",
    price: {
      id: 584,
      priceName: "",
      stdPrice: 5,
      curPrice: 5,
      curFee: 0.6,
      salePrice: 5,
      saleDate: "+00:00",
    },
    eventid: 4,
    Name: "100m",
    eventGroup: "100m ",
    egOptions: {
      maxAthletes: 0,
      maxInHeat: 8,
      includeEntriesFromEgId: 0,
      excludeFromCntRule: false,
      registration: {
        unregisteredAthletes: true,
        registeredAthletes: true,
      },
      combinedId: 0,
      entriesFrom: {
        id: 0,
      },
      display: {
        helpText: "",
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
        hideOnDisable: false,
      },
      heatInfo: {
        heatDurationMins: 0,
        useLanes: "A",
        unique: false,
        mandatoryPB: false,
      },
      seed: {
        gender: false,
        age: false,
        type: "O",
        firstLane: 1,
        laneCount: 8,
        waiting: false,
        seeded: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
          eventNo: 0,
          isMultiEvent: false,
        },
      },
      eventTeam: {
        isTeamEvent: false,
        min: 0,
        max: 0,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        teamPositionLabel: "",
        teamSubstituteLabel: "",
        singleClub: "",
        formType: "",
        disableTeamNameEdit: true,
        uniqueName: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
      },
      cardInfo: {
        reportInfo: "",
        trialInfo: "",
      },
      security: {
        clubs: "",
        counties: "",
        regions: {
          reportInfo: "",
          trialInfo: "",
        },
      },
      athleteSecurity: {
        clubs: [],
        counties: [],
        regions: [],
      },
      availability: {
        availableFrom: "",
        availableFromStatus: -1,
        availableTo: "",
        availableToStatus: -1,
      },
      checkIn: {
        enabled: false,
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
    },
    split: 0,
    within: null,
    split2: null,
    maxgroup: 6014,
    maxathletes: 0,
    ceoptions: {
      min: 9.6,
      max: 20,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "E",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        teamPositionLabel: "Leg",
        singleClub: true,
        teamSubstituteLabel: "Substitute",
        formType: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: true,
        mustBeIndivEntered: false,
        currCount: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: {},
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "2024-03-31T00:00:00+01:00",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: {
        id: 0,
        name: "",
        eventNo: 0,
        typeNo: "",
      },
    },
    eoptions: {
      min: 9.6,
      max: 20,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "E",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        disableTeamNameEdit: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
      },
      multiEventOptions: [],
    },
    eventGroupId: 6014,
    entered: true,
    entryId: 72830,
    paid: 0,
    athleteid: 163139,
    entryInfo: {
      unpaidCount: 1,
      paidCount: 0,
      totalCount: 1,
      entryPosition: 1,
      entryCreated: "2024-02-29T04:31:17+00:00",
      entryPositionDate: "2024-02-29T04:31:17+00:00",
      maxAthletes: 0,
    },
    entrycnt: 0,
    compName: "Rules Comp Nick",
    user: {
      userId: 1,
      userName: "E4S Admin",
      userEmail: "<EMAIL>",
    },
    order: {
      orderId: 0,
      productId: 138168,
      e4sLineValue: 72830,
      wcLineValue: 0,
      isRefund: false,
      refundValue: 0,
      isCredit: false,
      creditValue: 0,
      discountId: 0,
      dateOrdered: "",
    },
    teamBibNo: "",
    clubId: 1419,
    clubName: "Shaftesbury Barnet Harriers",
    firstName: "Raphael",
    surName: "AARON",
    uom: [
      {
        pattern: "s.SS",
        text: "seconds",
        short: "secs",
        uomType: "T",
      },
    ],
    perfInfo: {
      id: 10227,
      wind: 0,
      info: "",
      ageGroup: "U20",
      eventName: "100m",
      eventType: "T",
      tf: "T",
      min: 10,
      max: 20,
      curAgeGroup: "U23",
      athleteId: 163139,
      eventId: 4,
      sb: 0,
      pb: 12.64,
      sbAchieved: "",
      pbAchieved: "2023-07-19",
      pbText: 12.64,
      sbText: "!#!0",
      perf: 0,
      trackedPB: true,
      limits: {
        min: 10,
        max: 20,
      },
      uom: [
        {
          pattern: "s.SS",
          text: "seconds",
          short: "secs",
          uomType: "T",
        },
      ],
      perfText: "!#!0",
    },
    ruleIsDisabledBy: false,
    ruleMessage: "",
    userSelected: false,
    ruleType: "",
    post_date: "",
  },
  {
    ceid: 41656,
    ageGroupId: 11,
    ageGroupName: "Under 20",
    description: "SSS",
    startdate: "2024-03-31T00:00:00+01:00",
    IsOpen: 1,
    tf: "T",
    uomType: "T",
    price: {
      id: 584,
      priceName: "",
      stdPrice: 5,
      curPrice: 5,
      curFee: 0.6,
      salePrice: 5,
      saleDate: "+00:00",
    },
    eventid: 6,
    Name: "200m",
    eventGroup: "200m ",
    egOptions: {
      maxAthletes: 0,
      maxInHeat: 8,
      includeEntriesFromEgId: 0,
      excludeFromCntRule: false,
      registration: {
        unregisteredAthletes: true,
        registeredAthletes: true,
      },
      combinedId: 0,
      entriesFrom: {
        id: 0,
      },
      display: {
        helpText: "",
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
        hideOnDisable: false,
      },
      heatInfo: {
        heatDurationMins: 0,
        useLanes: "A",
        unique: false,
        mandatoryPB: false,
      },
      seed: {
        gender: false,
        age: false,
        type: "O",
        firstLane: 1,
        laneCount: 8,
        waiting: false,
        seeded: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
          eventNo: 0,
          isMultiEvent: false,
        },
      },
      eventTeam: {
        isTeamEvent: false,
        min: 0,
        max: 0,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        teamPositionLabel: "",
        teamSubstituteLabel: "",
        singleClub: "",
        formType: "",
        disableTeamNameEdit: true,
        uniqueName: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
      },
      cardInfo: {
        reportInfo: "",
        trialInfo: "",
      },
      security: {
        clubs: "",
        counties: "",
        regions: {
          reportInfo: "",
          trialInfo: "",
        },
      },
      athleteSecurity: {
        clubs: [],
        counties: [],
        regions: [],
      },
      availability: {
        availableFrom: "",
        availableFromStatus: -1,
        availableTo: "",
        availableToStatus: -1,
      },
      checkIn: {
        enabled: false,
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
    },
    split: 0,
    within: null,
    split2: null,
    maxgroup: 6015,
    maxathletes: 0,
    ceoptions: {
      min: 20,
      max: 60,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "E",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        teamPositionLabel: "Leg",
        singleClub: true,
        teamSubstituteLabel: "Substitute",
        formType: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: true,
        mustBeIndivEntered: false,
        currCount: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: {},
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "2024-03-31T00:00:00+01:00",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: {
        id: 0,
        name: "",
        eventNo: 0,
        typeNo: "",
      },
    },
    eoptions: {
      min: 20,
      max: 60,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "E",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        disableTeamNameEdit: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
      },
      multiEventOptions: [],
    },
    eventGroupId: 6015,
    entered: false,
    entryId: 0,
    paid: 0,
    athleteid: 163139,
    entryInfo: {
      unpaidCount: 0,
      paidCount: 0,
      totalCount: 0,
      entryPosition: 1,
      entryCreated: "",
      entryPositionDate: "",
      maxAthletes: 0,
    },
    entrycnt: 0,
    compName: "Rules Comp Nick",
    user: {
      userId: 0,
      userName: "",
      userEmail: "",
    },
    order: {
      orderId: 0,
      productId: 0,
      e4sLineValue: 0,
      wcLineValue: 0,
      isRefund: false,
      refundValue: 0,
      isCredit: false,
      creditValue: 0,
      discountId: 0,
      dateOrdered: "",
    },
    clubId: 1419,
    clubName: "Shaftesbury Barnet Harriers",
    firstName: "Raphael",
    surName: "AARON",
    uom: [
      {
        pattern: "s.SS",
        text: "seconds",
        short: "secs",
        uomType: "T",
      },
    ],
    perfInfo: {
      id: 10229,
      wind: 0.1,
      info: "",
      ageGroup: "U20",
      eventName: "200m",
      eventType: "T",
      tf: "T",
      min: 20,
      max: 60,
      curAgeGroup: "U23",
      athleteId: 163139,
      eventId: 6,
      sb: 0,
      pb: 25.88,
      sbAchieved: "",
      pbAchieved: "2023-07-19",
      pbText: 25.88,
      sbText: "!#!0",
      perf: 0,
      trackedPB: true,
      limits: {
        min: 20,
        max: 60,
      },
      uom: [
        {
          pattern: "s.SS",
          text: "seconds",
          short: "secs",
          uomType: "T",
        },
      ],
      perfText: "!#!0",
    },
    ruleIsDisabledBy: false,
    ruleMessage: "",
    userSelected: false,
    ruleType: "",
    post_date: "",
  },
  {
    ceid: 41676,
    ageGroupId: 11,
    ageGroupName: "Under 20",
    description: "SSS",
    startdate: "2024-03-31T00:00:00+01:00",
    IsOpen: 1,
    tf: "T",
    uomType: "T",
    price: {
      id: 584,
      priceName: "",
      stdPrice: 5,
      curPrice: 5,
      curFee: 0.6,
      salePrice: 5,
      saleDate: "+00:00",
    },
    eventid: 11,
    Name: "400m",
    eventGroup: "400m ",
    egOptions: {
      maxAthletes: 0,
      maxInHeat: 8,
      includeEntriesFromEgId: 0,
      excludeFromCntRule: false,
      registration: {
        unregisteredAthletes: true,
        registeredAthletes: true,
      },
      combinedId: 0,
      entriesFrom: {
        id: 0,
      },
      display: {
        helpText: "",
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
        hideOnDisable: false,
      },
      heatInfo: {
        heatDurationMins: 0,
        useLanes: "A",
        unique: false,
        mandatoryPB: false,
      },
      seed: {
        gender: false,
        age: false,
        type: "O",
        firstLane: 1,
        laneCount: 8,
        waiting: false,
        seeded: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
          eventNo: 0,
          isMultiEvent: false,
        },
      },
      eventTeam: {
        isTeamEvent: false,
        min: 0,
        max: 0,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        teamPositionLabel: "",
        teamSubstituteLabel: "",
        singleClub: "",
        formType: "",
        disableTeamNameEdit: true,
        uniqueName: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
      },
      cardInfo: {
        reportInfo: "",
        trialInfo: "",
      },
      security: {
        clubs: "",
        counties: "",
        regions: {
          reportInfo: "",
          trialInfo: "",
        },
      },
      athleteSecurity: {
        clubs: [],
        counties: [],
        regions: [],
      },
      availability: {
        availableFrom: "",
        availableFromStatus: -1,
        availableTo: "",
        availableToStatus: -1,
      },
      checkIn: {
        enabled: false,
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
    },
    split: 0,
    within: null,
    split2: null,
    maxgroup: 6016,
    maxathletes: 0,
    ceoptions: {
      min: 40,
      max: 120,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        teamPositionLabel: "Leg",
        singleClub: true,
        teamSubstituteLabel: "Substitute",
        formType: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: true,
        mustBeIndivEntered: false,
        currCount: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: {},
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "2024-03-31T00:00:00+01:00",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: {
        id: 0,
        name: "",
        eventNo: 0,
        typeNo: "",
      },
    },
    eoptions: {
      min: 40,
      max: 120,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        disableTeamNameEdit: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
      },
      multiEventOptions: [],
    },
    eventGroupId: 6016,
    entered: false,
    entryId: 0,
    paid: 0,
    athleteid: 163139,
    entryInfo: {
      unpaidCount: 0,
      paidCount: 0,
      totalCount: 0,
      entryPosition: 1,
      entryCreated: "",
      entryPositionDate: "",
      maxAthletes: 0,
    },
    entrycnt: 0,
    compName: "Rules Comp Nick",
    user: {
      userId: 0,
      userName: "",
      userEmail: "",
    },
    order: {
      orderId: 0,
      productId: 0,
      e4sLineValue: 0,
      wcLineValue: 0,
      isRefund: false,
      refundValue: 0,
      isCredit: false,
      creditValue: 0,
      discountId: 0,
      dateOrdered: "",
    },
    clubId: 1419,
    clubName: "Shaftesbury Barnet Harriers",
    firstName: "Raphael",
    surName: "AARON",
    uom: [
      {
        pattern: "m.ss.SS",
        text: "minutes",
        short: "mins",
        uomType: "T",
      },
      {
        pattern: "m:ss.SS",
        text: "minutes",
        short: "mins",
        uomType: "T",
      },
      {
        pattern: "s.SS",
        text: "seconds",
        short: "s",
        uomType: "T",
      },
    ],
    perfInfo: {
      id: 10232,
      wind: 0,
      info: "",
      ageGroup: "U20",
      eventName: "400m",
      eventType: "T",
      tf: "T",
      min: 40,
      max: 120,
      curAgeGroup: "U23",
      athleteId: 163139,
      eventId: 11,
      sb: 0,
      pb: 57.33,
      sbAchieved: "",
      pbAchieved: "2023-08-13",
      pbText: 57.33,
      sbText: "!#!0",
      perf: 0,
      trackedPB: true,
      limits: {
        min: 40,
        max: 120,
      },
      uom: [
        {
          pattern: "m.ss.SS",
          text: "minutes",
          short: "mins",
          uomType: "T",
        },
        {
          pattern: "m:ss.SS",
          text: "minutes",
          short: "mins",
          uomType: "T",
        },
        {
          pattern: "s.SS",
          text: "seconds",
          short: "s",
          uomType: "T",
        },
      ],
      perfText: "!#!0",
    },
    ruleIsDisabledBy: false,
    ruleMessage: "",
    userSelected: false,
    ruleType: "",
    post_date: "",
  },
  {
    ceid: 41696,
    ageGroupId: 11,
    ageGroupName: "Under 20",
    description: "SSS",
    startdate: "2024-03-31T00:00:00+01:00",
    IsOpen: 1,
    tf: "F",
    uomType: "D",
    price: {
      id: 584,
      priceName: "",
      stdPrice: 5,
      curPrice: 5,
      curFee: 0.6,
      salePrice: 5,
      saleDate: "+00:00",
    },
    eventid: 19,
    Name: "Discus",
    eventGroup: "Discus ",
    egOptions: {
      maxAthletes: 0,
      maxInHeat: 8,
      includeEntriesFromEgId: 0,
      excludeFromCntRule: false,
      registration: {
        unregisteredAthletes: true,
        registeredAthletes: true,
      },
      combinedId: 0,
      entriesFrom: {
        id: 0,
      },
      display: {
        helpText: "",
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
        hideOnDisable: false,
      },
      heatInfo: {
        heatDurationMins: 0,
        useLanes: "A",
        unique: false,
        mandatoryPB: false,
      },
      seed: {
        gender: false,
        age: false,
        type: "O",
        firstLane: 1,
        laneCount: 8,
        waiting: false,
        seeded: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
          eventNo: 0,
          isMultiEvent: false,
        },
      },
      eventTeam: {
        isTeamEvent: false,
        min: 0,
        max: 0,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        teamPositionLabel: "",
        teamSubstituteLabel: "",
        singleClub: "",
        formType: "",
        disableTeamNameEdit: true,
        uniqueName: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
      },
      cardInfo: {
        reportInfo: "",
        trialInfo: "",
      },
      security: {
        clubs: "",
        counties: "",
        regions: {
          reportInfo: "",
          trialInfo: "",
        },
      },
      athleteSecurity: {
        clubs: [],
        counties: [],
        regions: [],
      },
      availability: {
        availableFrom: "",
        availableFromStatus: -1,
        availableTo: "",
        availableToStatus: -1,
      },
      checkIn: {
        enabled: false,
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
    },
    split: 0,
    within: null,
    split2: null,
    maxgroup: 6017,
    maxathletes: 0,
    ceoptions: {
      min: 1,
      max: 100,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        teamPositionLabel: "Leg",
        singleClub: true,
        teamSubstituteLabel: "Substitute",
        formType: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: true,
        mustBeIndivEntered: false,
        currCount: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: {},
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "2024-03-31T00:00:00+01:00",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: {
        id: 0,
        name: "",
        eventNo: 0,
        typeNo: "",
      },
    },
    eoptions: {
      min: 1,
      max: 100,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        disableTeamNameEdit: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
      },
      multiEventOptions: [],
    },
    eventGroupId: 6017,
    entered: false,
    entryId: 0,
    paid: 0,
    athleteid: 163139,
    entryInfo: {
      unpaidCount: 0,
      paidCount: 0,
      totalCount: 0,
      entryPosition: 1,
      entryCreated: "",
      entryPositionDate: "",
      maxAthletes: 0,
    },
    entrycnt: 0,
    compName: "Rules Comp Nick",
    user: {
      userId: 0,
      userName: "",
      userEmail: "",
    },
    order: {
      orderId: 0,
      productId: 0,
      e4sLineValue: 0,
      wcLineValue: 0,
      isRefund: false,
      refundValue: 0,
      isCredit: false,
      creditValue: 0,
      discountId: 0,
      dateOrdered: "",
    },
    clubId: 1419,
    clubName: "Shaftesbury Barnet Harriers",
    firstName: "Raphael",
    surName: "AARON",
    uom: [
      {
        pattern: 0.99,
        text: "metres",
        short: "m",
        uomType: "D",
      },
    ],
    perfInfo: {
      perf: 0,
      limits: {
        min: 1,
        max: 100,
      },
      uom: [
        {
          pattern: 0.99,
          text: "metres",
          short: "m",
          uomType: "D",
        },
      ],
      ageGroup: "Under 20",
      curAgeGroup: "Under 20",
      athleteId: 163139,
      eventId: 19,
      eventName: "Discus",
      eventType: "D",
      id: 0,
      info: "",
      min: 1,
      max: 100,
      pbAchieved: "",
      pb: 0,
      sbAchieved: "",
      sb: 0,
      tf: "F",
      wind: 0,
      perfText: "!#!0",
      pbText: "!#!0",
      sbText: "!#!0",
    },
    ruleIsDisabledBy: false,
    ruleMessage: "",
    userSelected: false,
    ruleType: "",
    post_date: "",
  },
  {
    ceid: 41716,
    ageGroupId: 11,
    ageGroupName: "Under 20",
    description: "SSS",
    startdate: "2024-03-31T00:00:00+01:00",
    IsOpen: 1,
    tf: "F",
    uomType: "D",
    price: {
      id: 584,
      priceName: "",
      stdPrice: 5,
      curPrice: 5,
      curFee: 0.6,
      salePrice: 5,
      saleDate: "+00:00",
    },
    eventid: 29,
    Name: "Hammer",
    eventGroup: "Hammer ",
    egOptions: {
      maxAthletes: 0,
      maxInHeat: 8,
      includeEntriesFromEgId: 0,
      excludeFromCntRule: false,
      registration: {
        unregisteredAthletes: true,
        registeredAthletes: true,
      },
      combinedId: 0,
      entriesFrom: {
        id: 0,
      },
      display: {
        helpText: "",
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
        hideOnDisable: false,
      },
      heatInfo: {
        heatDurationMins: 0,
        useLanes: "A",
        unique: false,
        mandatoryPB: false,
      },
      seed: {
        gender: false,
        age: false,
        type: "O",
        firstLane: 1,
        laneCount: 8,
        waiting: false,
        seeded: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
          eventNo: 0,
          isMultiEvent: false,
        },
      },
      eventTeam: {
        isTeamEvent: false,
        min: 0,
        max: 0,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        teamPositionLabel: "",
        teamSubstituteLabel: "",
        singleClub: "",
        formType: "",
        disableTeamNameEdit: true,
        uniqueName: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
      },
      cardInfo: {
        reportInfo: "",
        trialInfo: "",
      },
      security: {
        clubs: "",
        counties: "",
        regions: {
          reportInfo: "",
          trialInfo: "",
        },
      },
      athleteSecurity: {
        clubs: [],
        counties: [],
        regions: [],
      },
      availability: {
        availableFrom: "",
        availableFromStatus: -1,
        availableTo: "",
        availableToStatus: -1,
      },
      checkIn: {
        enabled: false,
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
    },
    split: 0,
    within: null,
    split2: null,
    maxgroup: 6018,
    maxathletes: 0,
    ceoptions: {
      min: 0,
      max: 100,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        teamPositionLabel: "Leg",
        singleClub: true,
        teamSubstituteLabel: "Substitute",
        formType: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: true,
        mustBeIndivEntered: false,
        currCount: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      multiEventOptions: [],
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: true,
      security: {},
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      maxAgeGroupCnt: 0,
      trials: "",
      cardNotes: "",
      athleteEventCol: "",
      availableFrom: "",
      availableFromStatus: 0,
      availableTo: "2024-03-31T00:00:00+01:00",
      availableToStatus: 0,
      hideOnDisable: false,
      entriesFrom: {
        id: 0,
        name: "",
        eventNo: 0,
        typeNo: "",
      },
    },
    eoptions: {
      min: 0,
      max: 100,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        disableTeamNameEdit: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
      },
      multiEventOptions: [],
    },
    eventGroupId: 6018,
    entered: false,
    entryId: 0,
    paid: 0,
    athleteid: 163139,
    entryInfo: {
      unpaidCount: 0,
      paidCount: 0,
      totalCount: 0,
      entryPosition: 1,
      entryCreated: "",
      entryPositionDate: "",
      maxAthletes: 0,
    },
    entrycnt: 0,
    compName: "Rules Comp Nick",
    user: {
      userId: 0,
      userName: "",
      userEmail: "",
    },
    order: {
      orderId: 0,
      productId: 0,
      e4sLineValue: 0,
      wcLineValue: 0,
      isRefund: false,
      refundValue: 0,
      isCredit: false,
      creditValue: 0,
      discountId: 0,
      dateOrdered: "",
    },
    clubId: 1419,
    clubName: "Shaftesbury Barnet Harriers",
    firstName: "Raphael",
    surName: "AARON",
    uom: [
      {
        pattern: 0.99,
        text: "metres",
        short: "m",
        uomType: "D",
      },
    ],
    perfInfo: {
      perf: 0,
      limits: {
        min: 0,
        max: 0,
      },
      uom: [
        {
          pattern: 0.99,
          text: "metres",
          short: "m",
          uomType: "D",
        },
      ],
      ageGroup: "Under 20",
      curAgeGroup: "Under 20",
      athleteId: 163139,
      eventId: 29,
      eventName: "Hammer",
      eventType: "D",
      id: 0,
      info: "",
      min: 0,
      max: 0,
      pbAchieved: "",
      pb: 0,
      sbAchieved: "",
      sb: 0,
      tf: "F",
      wind: 0,
      perfText: "!#!0",
      pbText: "!#!0",
      sbText: "!#!0",
    },
    ruleIsDisabledBy: false,
    ruleMessage: "",
    userSelected: false,
    ruleType: "",
    post_date: "",
  },
] as any as IAthleteCompSchedRuleEvent[];

export const maxEnteredUserApplication: IUserApplication = {
  id: 1,
  user_login: "E4SAdmin",
  user_nicename: "e4sadmin",
  user_email: "<EMAIL>",
  display_name: "E4S Admin",
  role: "E4SUSER",
  google_email: "",
  impersonating: false,
  e4sCredit: [977],
  permissions: [
    {
      id: 5,
      userid: 1,
      role: {
        id: 0,
        name: "All",
      },
      comp: {
        id: 0,
        name: "",
      },
      org: {
        id: 0,
        name: "All",
      },
      permLevels: [],
    },
    {
      id: 186,
      userid: 1,
      role: {
        id: 6,
        name: "admin",
      },
      comp: {
        id: 0,
        name: "",
      },
      org: {
        id: 54,
        name: "Nuneaton Opens",
      },
      permLevels: [],
    },
  ],
  version: {
    current: "v1",
    toggle: true,
  },
} as any as IUserApplication;

export const maxEnteredAgeInfo: IAgeInfo = {
  currentAge: 19,
  ageGroupId: 11,
  ageGroup: "Under 20",
  vetAgeGroupId: 0,
  vetAgeGroup: "",
} as any as IAgeInfo;

export const maxEnteredCompetition: ICompetition = {
  date: "2024-03-31T00:00:00",
  today: "2024-02-29",
  systemtime: "2024-02-29 04:30:52",
  compDate: "2024-03-31",
  visibledate: "2024-03-31",
  lastentrymod: null,
  locationid: 20,
  link: "",
  yearFactor: -1,
  compOrgId: 54,
  entriesClose: "2024-03-28T00:00:00",
  options: {
    isClubComp: false,
    sourceId: 0,
    anonymize: false,
    standards: [],
    allowExpiredRegistration: true,
    useTeamBibs: false,
    bacs: {
      enabled: false,
      msg: "",
    },
    scoreboard: {
      image: "/results/blank.jpg",
    },
    seqEventNo: true,
    disabled: false,
    stripeMandatory: false,
    homeInfo: "",
    shortCode: "",
    priority: {
      required: false,
      code: "",
      dateTime: "",
      message: "",
    },
    bibNos: 1,
    bibSort1: "surname",
    bibSort2: "firstname",
    bibSort3: "dob",
    heatOrder: "s",
    stadium: "",
    adjustEventNo: 0,
    athleteType: "A",
    tickets: {
      enabled: false,
    },
    pf: {
      pfTargetDirectory: "",
      type: "",
    },
    contact: {
      id: 147,
      userName: "nivk",
      tel: "",
      email: "<EMAIL>",
      visible: true,
    },
    cardInfo: {
      enabled: true,
      availableFrom: "",
      track: "",
      field: "",
      callRoom: true,
    },
    subscription: {
      enabled: false,
      timeCloses: "",
      organiserMessage: "",
      e4sMessage: "",
      refunded: "",
      process: true,
      processRefundTime: "",
    },
    cloneInfo: {
      fromId: 0,
      saved: true,
    },
    card: {
      header: {
        field: "",
        track: "",
      },
      footer: {
        field: "",
        track: "",
      },
    },
    checkIn: {
      enabled: false,
      checkInDateTimeOpens: "",
      defaultFrom: 180,
      defaultTo: 60,
      qrCode: true,
      text: "",
      terms: "",
      useTerms: false,
      seedOnEntries: false,
    },
    school: false,
    orgFreeEntry: false,
    autoPayFreeEntry: false,
    cheques: {
      allow: false,
      ends: "",
    },
    allowAdd: {
      unregistered: false,
      registered: true,
    },
    timetable: "Provisional",
    helpText: {
      schedule: "",
      teams: "",
      cart: "",
    },
    showTeamAthletes: true,
    singleAge: false,
    showAthleteAgeInEntries: false,
    report: {
      summary: true,
      athletes: true,
      ttathletes: true,
      ttentries: true,
      individual_entries: true,
      teams: true,
      subscriptions: true,
      orders: true,
      events: true,
    },
    athleteSecurity: {
      areas: [],
      clubs: [],
      onlyClubsUpTo: "",
    },
    ui: {
      enterButtonText: "Enter",
      entryDefaultPanel: "SCHEDULE",
      ticketComp: 0,
      ticketCompButtonText: "Buy Tickets",
      sectionsToHide: {
        SCHEDULE: false,
        ATHLETES: false,
        SHOP: true,
        TEAMS: true,
      },
      ticketCompBase: {
        id: 0,
        name: "",
      },
    },
    athleteQrData: false,
    nonGuests: [],
    disabledReason: "",
    paymentCode: "",
    laneCount: 8,
    compLimits: {
      athletes: 0,
      entries: 0,
      teams: 0,
    },
    stopReport: false,
    cancelEvent: {
      hrsBeforeClose: 48,
      refund: {
        allow: true,
        type: "E4S_FEES",
      },
      credit: {
        allow: true,
      },
    },
    resultsAvailable: false,
    autoEntries: {
      selectedTargetComp: {
        id: 0,
        name: "",
        timeTronicsUrl: "",
      },
      targetable: {
        allowedSources: [],
        enabled: false,
      },
    },
    level: "",
    pfTargetDirectory: "",
    dates: ["2024-03-31"],
    clubComp: false,
    pbMandatory: true,
    categoryId: 3898,
    selfService: [],
  },
  logo: "/resources/nuneaton_opens_logo.jpg",
  areaid: 0,
  teamid: 0,
  ctcid: null,
  maxmale: null,
  name: "Rules Comp Nick",
  tandc: {
    link: "",
    description:
      "I agree that entry fees paid are non transferable and non refundable for this event.",
  },
  areaname: "",
  entity: "",
  entityid: 0,
  ctc: {
    ctcid: null,
    maxMale: null,
    maxFemale: null,
    maxTeams: null,
    maxAthletes: null,
    maxAgeGroup: null,
    uniqueEvents: null,
    singleAgeGroup: null,
  },
  compRules: [
    {
      id: 215,
      agid: 0,
      options: {
        maxCompEvents: 2,
        maxCompField: 1,
        maxCompTrack: 1,
        maxExcludedEvents: 0,
        displayMaxDayFields: false,
        maxTeamEvents: 0,
      },
    },
  ],
  id: 419,
  indivEvents: true,
  hasSecureEvents: false,
  teamEvents: false,
  clubCompInfo: {},
  compAgeGroups: [
    {
      id: 1,
      name: "Under 11",
      fromDate: "2012-09-01",
      toDate: "2014-08-31",
    },
    {
      id: 2,
      name: "Under 13",
      fromDate: "2010-09-01",
      toDate: "2012-08-31",
    },
    {
      id: 3,
      name: "Under 9",
      fromDate: "2014-09-01",
      toDate: "2017-08-31",
    },
    {
      id: 8,
      name: "Under 17",
      fromDate: "2006-09-01",
      toDate: "2008-08-31",
    },
    {
      id: 9,
      name: "Under 15",
      fromDate: "2008-09-01",
      toDate: "2010-08-31",
    },
    {
      id: 11,
      name: "Under 20",
      fromDate: "2004-01-01",
      toDate: "2006-08-31",
    },
    {
      id: 12,
      name: "Under 23",
      fromDate: "2001-01-01",
      toDate: "2003-12-31",
    },
    {
      id: 14,
      name: "Masters",
      fromDate: "1922-04-01",
      toDate: "1988-12-31",
    },
    {
      id: 28,
      name: "Senior",
      fromDate: "1989-01-01",
      toDate: "2003-12-31",
    },
    {
      id: 209,
      name: "Under 13",
      fromDate: "2010-09-01",
      toDate: "2011-10-01",
    },
  ],
  humanReadableDate: "31st Mar 24",
} as any as ICompetition;

export const maxEnteredUserInfo: IUserInfo = {
  e4s_pagessize: 25,
  e4s_schedInfoState: 1,
  e4s_user: 1,
  e4s_credit: 977,
  e4s_useraccess: 1,
  e4s_stripe_connected_date: "2020-01-01 12:00:00",
  e4s_stripe_approved_date: "2020-01-01 12:00:00",
  e4s_clubcomp_351: 32,
  e4s_lastCheckedPB: "2024-02-29 03:11:27",
  orgs: [
    {
      id: 38,
      name: "National",
      locations: {
        "1": {
          id: 1,
          postcode: "",
          website: "",
          directions: "",
          contact: "",
          address1: "",
          address2: "",
          town: "Athlone",
          county: "",
          name: "Athlone IT",
        },
      },
    },
  ],
  areas: [
    {
      areaid: 9,
      areaname: "Leinster",
      areashortname: "Len",
      areaparentid: 6,
      entityLevel: 3,
      entitylevel: 3,
      entityName: "Region",
    },
    {
      areaid: 30,
      areaname: "Dublin",
      areashortname: "Dub",
      areaparentid: 9,
      entityLevel: 2,
      entitylevel: 2,
      entityName: "County",
    },
  ],
  clubs: [
    {
      id: 10600,
      externid: null,
      Clubname: "Avon",
      shortcode: "Avon",
      Region: "Avon",
      Country: "England",
      areaid: 2,
      clubtype: "A",
      active: 1,
      options: {},
      modified: "2023-04-19 07:16:03",
      entityName: "Club",
      entityLevel: 1,
    },
  ],
  user: {
    id: 1,
    user_login: "E4SAdmin",
    user_nicename: "e4sadmin",
    user_email: "<EMAIL>",
    display_name: "E4S Admin",
    role: "E4SUSER",
    google_email: "",
    impersonating: false,
    e4sCredit: [977],
    permissions: [
      {
        id: 5,
        userid: 1,
        role: {
          id: 0,
          name: "All",
        },
        comp: {
          id: 0,
          name: "",
        },
        org: {
          id: 0,
          name: "All",
        },
        permLevels: [],
      },
      {
        id: 186,
        userid: 1,
        role: {
          id: 6,
          name: "admin",
        },
        comp: {
          id: 0,
          name: "",
        },
        org: {
          id: 54,
          name: "Nuneaton Opens",
        },
        permLevels: [],
      },
    ],
    version: {
      current: "v1",
      toggle: true,
    },
  },
  wp_role: "administrator",
  security: {
    permissions: [
      {
        id: 5,
        userid: 1,
        roleid: 0,
        orgid: 0,
        compid: 0,
        role: "all",
        orgname: "All",
      },
      {
        id: 186,
        userid: 1,
        roleid: 6,
        orgid: 54,
        compid: 0,
        role: "admin",
        orgname: "Nuneaton Opens",
      },
    ],
    permLevels: {
      "": [],
    },
  },
} as any as IUserInfo;
