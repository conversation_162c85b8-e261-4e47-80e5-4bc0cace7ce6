import { <PERSON>Athlete } from "../../athlete/athlete-models";
import { ICompShedRuleConfig } from "../athletecompsched-models";
import { IUserApplication, IUserInfo } from "../../config/config-app-models";
import { ICompetition } from "../../competition/competition-models";

export const athleteRules: IAthlete = {
  id: 244622,
  firstName: "Test2",
  surName: "WALL",
  aocode: "",
  URN: 1234546,
  pof10id: null,
  dob: "2007-02-04",
  gender: "F",
  classification: 0,
  email: "",
  clubid: 1198,
  club2id: 0,
  type: "A",
  options: {
    urnChecked: "Test2Wall2007-02-04",
    noEntryReason: "",
    emergency: {
      name: "",
      tel: "",
      relationship: "",
    },
  },
  activeEndDate: "2023-12-31",
  image:
    "https://www.thepowerof10.info/athletes/profilepic.aspx?athleteid=unknown",
  modified: "2023-05-11 07:05:18",
  club: "Nuneaton Harriers",
  clubId: 1198,
  club2Id: 0,
  schoolId: 0,
  club2: "",
  school: "",
  pbInfo: [
    {
      eventid: 3,
      pb: 0,
      pbid: 0,
      pbText: "",
      pof10pb: 0,
      sb: 0,
      trackSb: false,
      eventName: "100m",
      options: {
        maxInHeat: 8,
        min: 9.6,
        max: 20,
        helpText: "",
        wind: "E",
      },
      min: 10,
      max: 20,
      uomInfo: {
        id: 1,
        type: "T",
        options: [
          {
            pattern: "s.SS",
            text: "seconds",
            short: "secs",
          },
        ],
      },
    },
    {
      eventid: 5,
      pb: 0,
      pbid: 0,
      pbText: "",
      pof10pb: 0,
      sb: 0,
      trackSb: false,
      eventName: "200m",
      options: {
        maxInHeat: 8,
        min: 20,
        max: 60,
        wind: "E",
      },
      min: 20,
      max: 60,
      uomInfo: {
        id: 1,
        type: "T",
        options: [
          {
            pattern: "s.SS",
            text: "seconds",
            short: "secs",
          },
        ],
      },
    },
  ],
} as any as IAthlete;

export const pbkeyRules = {
  "3": 0,
  "5": 0,
};

// agid: number;
export const compRuleRules: ICompShedRuleConfig = {
  id: 212,
  options: {
    maxCompEvents: 2,
    maxCompField: 0,
    maxCompTrack: 0,
    maxExcludedEvents: 0,
    displayMaxDayFields: false,
  },
} as any as ICompShedRuleConfig;

export const eventsRules = [
  {
    ceid: 38716,
    ageGroupId: 8,
    ageGroupName: "Under 17",
    description: "efewfwef",
    startdate: "2023-05-31T00:00:00+01:00",
    IsOpen: 1,
    tf: "T",
    uomType: "T",
    price: {
      id: 509,
      priceName: "",
      stdPrice: 0,
      curPrice: 0,
      curFee: 0,
      salePrice: 0,
      saleDate: "+00:00",
    },
    eventid: 3,
    multiid: 0,
    Name: "100m",
    eventGroup: "100m",
    egOptions: {
      maxAthletes: 0,
      maxInHeat: 0,
      excludeFromCntRule: false,
      combinedId: 0,
      registration: {
        unregisteredAthletes: true,
        registeredAthletes: true,
      },
      display: {
        helpText: "",
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
        hideOnDisable: false,
      },
      heatInfo: {
        heatDurationMins: 0,
        useLanes: "A",
        unique: false,
        mandatoryPB: true,
      },
      seed: {
        gender: false,
        age: false,
        type: "O",
        firstLane: 1,
        laneCount: 8,
        waiting: false,
        seeded: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      eventTeam: {
        isTeamEvent: false,
        min: 0,
        max: 0,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        teamPositionLabel: "",
        teamSubstituteLabel: "",
        singleClub: "",
        formType: "",
        teamNameFormat: "",
      },
      cardInfo: {
        reportInfo: "",
        trialInfo: "",
      },
      security: {
        clubs: "",
        counties: "",
        regions: {
          reportInfo: "",
          trialInfo: "",
        },
      },
      athleteSecurity: {
        clubs: [],
        counties: [],
        regions: [],
      },
      upscaling: {
        ageGroups: "",
      },
      availability: {
        availableFrom: "",
        availableFromStatus: -1,
        availableTo: "",
        availableToStatus: -1,
      },
      checkIn: {
        enabled: false,
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
    },
    split: 0,
    split2: null,
    maxgroup: 5455,
    maxathletes: 0,
    ceoptions: {
      min: 9.6,
      max: 20,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "E",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {},
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
    },
    eoptions: {
      min: 9.6,
      max: 20,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "E",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
      },
    },
    eventGroupId: 5455,
    entered: true,
    entryId: 72362,
    paid: 0,
    athleteid: 244622,
    entryInfo: {
      unpaidCount: 3,
      paidCount: 0,
      totalCount: 3,
      entryPosition: 1,
      entryCreated: "2023-05-11T10:25:23+01:00",
      entryPositionDate: "2023-05-11T10:25:23+01:00",
      maxAthletes: 0,
    },
    entrycnt: 0,
    compName: "test rules",
    user: {
      userId: 19828,
      userName: "nick wall",
      userEmail: "<EMAIL>",
    },
    order: {
      orderId: 0,
      productId: 137791,
      e4sLineValue: 72362,
      wcLineValue: 0,
      isRefund: false,
      refundValue: 0,
      isCredit: false,
      creditValue: 0,
      discountId: 0,
      dateOrdered: "",
    },
    teamBibNo: "",
    pbInfo: {
      pb: 0,
      pbText: "",
    },
    clubId: 1198,
    clubName: "Nuneaton Harriers",
    firstName: "Test2",
    surName: "WALL",
    uom: [
      {
        pattern: "s.SS",
        text: "seconds",
        short: "secs",
        uomType: "T",
      },
    ],
    ruleIsDisabledBy: false,
    ruleMessage: "",
    userSelected: false,
    ruleType: "",
    post_date: "",
  },
  {
    ceid: 38736,
    ageGroupId: 8,
    ageGroupName: "Under 17",
    description: "efewfwef",
    startdate: "2023-05-31T00:00:00+01:00",
    IsOpen: 1,
    tf: "T",
    uomType: "T",
    price: {
      id: 509,
      priceName: "",
      stdPrice: 0,
      curPrice: 0,
      curFee: 0,
      salePrice: 0,
      saleDate: "+00:00",
    },
    eventid: 5,
    multiid: 0,
    Name: "200m",
    eventGroup: "200m",
    egOptions: {
      maxAthletes: 0,
      maxInHeat: 0,
      excludeFromCntRule: false,
      combinedId: 0,
      registration: {
        unregisteredAthletes: true,
        registeredAthletes: true,
      },
      display: {
        helpText: "",
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
        hideOnDisable: false,
      },
      heatInfo: {
        heatDurationMins: 0,
        useLanes: "A",
        unique: false,
        mandatoryPB: true,
      },
      seed: {
        gender: false,
        age: false,
        type: "O",
        firstLane: 1,
        laneCount: 8,
        waiting: false,
        seeded: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      eventTeam: {
        isTeamEvent: false,
        min: 0,
        max: 0,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        teamPositionLabel: "",
        teamSubstituteLabel: "",
        singleClub: "",
        formType: "",
        teamNameFormat: "",
      },
      cardInfo: {
        reportInfo: "",
        trialInfo: "",
      },
      security: {
        clubs: "",
        counties: "",
        regions: {
          reportInfo: "",
          trialInfo: "",
        },
      },
      athleteSecurity: {
        clubs: [],
        counties: [],
        regions: [],
      },
      upscaling: {
        ageGroups: "",
      },
      availability: {
        availableFrom: "",
        availableFromStatus: -1,
        availableTo: "",
        availableToStatus: -1,
      },
      checkIn: {
        enabled: false,
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
    },
    split: 0,
    split2: null,
    maxgroup: 5456,
    maxathletes: 0,
    ceoptions: {
      min: 20,
      max: 60,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "E",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {},
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
    },
    eoptions: {
      min: 20,
      max: 60,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "E",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
      },
    },
    eventGroupId: 5456,
    entered: true,
    entryId: 72363,
    paid: 0,
    athleteid: 244622,
    entryInfo: {
      unpaidCount: 3,
      paidCount: 0,
      totalCount: 3,
      entryPosition: 1,
      entryCreated: "2023-05-11T10:25:27+01:00",
      entryPositionDate: "2023-05-11T10:25:27+01:00",
      maxAthletes: 0,
    },
    entrycnt: 0,
    compName: "test rules",
    user: {
      userId: 19828,
      userName: "nick wall",
      userEmail: "<EMAIL>",
    },
    order: {
      orderId: 0,
      productId: 137792,
      e4sLineValue: 72363,
      wcLineValue: 0,
      isRefund: false,
      refundValue: 0,
      isCredit: false,
      creditValue: 0,
      discountId: 0,
      dateOrdered: "",
    },
    teamBibNo: "",
    pbInfo: {
      pb: 0,
      pbText: "",
    },
    clubId: 1198,
    clubName: "Nuneaton Harriers",
    firstName: "Test2",
    surName: "WALL",
    uom: [
      {
        pattern: "s.SS",
        text: "seconds",
        short: "secs",
        uomType: "T",
      },
    ],
    ruleIsDisabledBy: false,
    ruleMessage: "",
    userSelected: false,
    ruleType: "",
    post_date: "",
  },
  {
    ceid: 38756,
    ageGroupId: 8,
    ageGroupName: "Under 17",
    description: "efewfwef",
    startdate: "2023-05-31T00:00:00+01:00",
    IsOpen: 1,
    tf: "T",
    uomType: "T",
    price: {
      id: 509,
      priceName: "",
      stdPrice: 0,
      curPrice: 0,
      curFee: 0,
      salePrice: 0,
      saleDate: "+00:00",
    },
    eventid: 10,
    multiid: 0,
    Name: "400m",
    eventGroup: "400m",
    egOptions: {
      maxAthletes: 0,
      maxInHeat: 0,
      excludeFromCntRule: false,
      combinedId: 0,
      registration: {
        unregisteredAthletes: true,
        registeredAthletes: true,
      },
      display: {
        helpText: "",
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
        hideOnDisable: false,
      },
      heatInfo: {
        heatDurationMins: 0,
        useLanes: "A",
        unique: false,
        mandatoryPB: true,
      },
      seed: {
        gender: false,
        age: false,
        type: "O",
        firstLane: 1,
        laneCount: 8,
        waiting: false,
        seeded: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      eventTeam: {
        isTeamEvent: false,
        min: 0,
        max: 0,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        teamPositionLabel: "",
        teamSubstituteLabel: "",
        singleClub: "",
        formType: "",
        teamNameFormat: "",
      },
      cardInfo: {
        reportInfo: "",
        trialInfo: "",
      },
      security: {
        clubs: "",
        counties: "",
        regions: {
          reportInfo: "",
          trialInfo: "",
        },
      },
      athleteSecurity: {
        clubs: [],
        counties: [],
        regions: [],
      },
      upscaling: {
        ageGroups: "",
      },
      availability: {
        availableFrom: "",
        availableFromStatus: -1,
        availableTo: "",
        availableToStatus: -1,
      },
      checkIn: {
        enabled: false,
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
    },
    split: 0,
    split2: null,
    maxgroup: 5457,
    maxathletes: 0,
    ceoptions: {
      min: 40,
      max: 120,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {},
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
    },
    eoptions: {
      min: 40,
      max: 120,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
      },
    },
    eventGroupId: 5457,
    entered: false,
    entryId: 0,
    paid: 0,
    athleteid: 244622,
    entryInfo: {
      unpaidCount: 0,
      paidCount: 0,
      totalCount: 0,
      entryPosition: 1,
      entryCreated: "",
      entryPositionDate: "",
      maxAthletes: 0,
    },
    entrycnt: 0,
    compName: "test rules",
    user: {
      userId: 0,
      userName: "",
      userEmail: "",
    },
    order: {
      orderId: 0,
      productId: 0,
      e4sLineValue: 0,
      wcLineValue: 0,
      isRefund: false,
      refundValue: 0,
      isCredit: false,
      creditValue: 0,
      discountId: 0,
      dateOrdered: "",
    },
    pbInfo: {
      pb: 0,
      pbText: "",
    },
    clubId: 1198,
    clubName: "Nuneaton Harriers",
    firstName: "Test2",
    surName: "WALL",
    uom: [
      {
        pattern: "m.ss.SS",
        text: "minutes",
        short: "mins",
        uomType: "T",
      },
      {
        pattern: "m:ss.SS",
        text: "minutes",
        short: "mins",
        uomType: "T",
      },
      {
        pattern: "s.SS",
        text: "seconds",
        short: "s",
        uomType: "T",
      },
    ],
    ruleIsDisabledBy: false,
    ruleMessage: "",
    userSelected: false,
    ruleType: "",
    post_date: "",
  },
  {
    ceid: 38776,
    ageGroupId: 8,
    ageGroupName: "Under 17",
    description: "efewfwef",
    startdate: "2023-05-31T00:00:00+01:00",
    IsOpen: 1,
    tf: "T",
    uomType: "T",
    price: {
      id: 509,
      priceName: "",
      stdPrice: 0,
      curPrice: 0,
      curFee: 0,
      salePrice: 0,
      saleDate: "+00:00",
    },
    eventid: 16,
    multiid: 0,
    Name: "800m",
    eventGroup: "800m",
    egOptions: {
      maxAthletes: 0,
      maxInHeat: 0,
      excludeFromCntRule: false,
      combinedId: 0,
      registration: {
        unregisteredAthletes: true,
        registeredAthletes: true,
      },
      display: {
        helpText: "",
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
        hideOnDisable: false,
      },
      heatInfo: {
        heatDurationMins: 0,
        useLanes: "A",
        unique: false,
        mandatoryPB: true,
      },
      seed: {
        gender: false,
        age: false,
        type: "O",
        firstLane: 1,
        laneCount: 8,
        waiting: false,
        seeded: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      eventTeam: {
        isTeamEvent: false,
        min: 0,
        max: 0,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        maxEventTeams: 0,
        teamPositionLabel: "",
        teamSubstituteLabel: "",
        singleClub: "",
        formType: "",
        teamNameFormat: "",
      },
      cardInfo: {
        reportInfo: "",
        trialInfo: "",
      },
      security: {
        clubs: "",
        counties: "",
        regions: {
          reportInfo: "",
          trialInfo: "",
        },
      },
      athleteSecurity: {
        clubs: [],
        counties: [],
        regions: [],
      },
      upscaling: {
        ageGroups: "",
      },
      availability: {
        availableFrom: "",
        availableFromStatus: -1,
        availableTo: "",
        availableToStatus: -1,
      },
      checkIn: {
        enabled: false,
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
    },
    split: 0,
    split2: null,
    maxgroup: 5458,
    maxathletes: 0,
    ceoptions: {
      min: 80,
      max: 240,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {},
      athleteSecurity: {},
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
    },
    eoptions: {
      min: 80,
      max: 240,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: {
        useLanes: "A",
      },
    },
    eventGroupId: 5458,
    entered: false,
    entryId: 0,
    paid: 0,
    athleteid: 244622,
    entryInfo: {
      unpaidCount: 0,
      paidCount: 0,
      totalCount: 0,
      entryPosition: 1,
      entryCreated: "",
      entryPositionDate: "",
      maxAthletes: 0,
    },
    entrycnt: 0,
    compName: "test rules",
    user: {
      userId: 0,
      userName: "",
      userEmail: "",
    },
    order: {
      orderId: 0,
      productId: 0,
      e4sLineValue: 0,
      wcLineValue: 0,
      isRefund: false,
      refundValue: 0,
      isCredit: false,
      creditValue: 0,
      discountId: 0,
      dateOrdered: "",
    },
    pbInfo: {
      pb: 0,
      pbText: "",
    },
    clubId: 1198,
    clubName: "Nuneaton Harriers",
    firstName: "Test2",
    surName: "WALL",
    uom: [
      {
        pattern: "m.ss.SS",
        text: "mins",
        short: "mins",
        uomType: "T",
      },
      {
        pattern: "m:ss.SS",
        text: "mins",
        short: "mins",
        uomType: "T",
      },
    ],
    ruleIsDisabledBy: false,
    ruleMessage: "",
    userSelected: false,
    ruleType: "",
    post_date: "",
  },
];

export const userApplicationRules: IUserApplication = {
  id: 19828,
  // user_login: "nick wall",
  // user_nicename: "nick-wall",
  user_email: "<EMAIL>",
  display_name: "nick wall",
  role: "USER",
  google_email: "",
  impersonating: false,
  // e4sCredit: [3.5],
  permissions: [],
  version: {
    current: "v1",
    toggle: true,
  },
};

export const ageInfoRules = {
  currentAge: 16,
  ageGroupId: 8,
  ageGroup: "Under 17",
  vetAgeGroupId: 0,
  vetAgeGroup: "",
};

export const competitionRules: ICompetition = {
  date: "2023-05-31T00:00:00",
  today: "2023-05-11",
  systemtime: "2023-05-11 11:30:10",
  compDate: "2023-05-31",
  visibledate: "2023-05-31",
  lastentrymod: "2023-05-11 11:24:48",
  locationid: 20,
  link: "",
  yearFactor: 0,
  compOrgId: 54,
  entriesClose: "2023-05-30T00:00:00",
  options: {
    isClubComp: false,
    standards: [],
    allowExpiredRegistration: true,
    useTeamBibs: false,
    bacs: {
      enabled: false,
      msg: "",
    },
    scoreboard: {
      image: "/results/blank.png",
    },
    seqEventNo: true,
    disabled: false,
    stripeMandatory: false,
    resultsAvailable: false,
    homeInfo: "",
    shortCode: "",
    priority: {
      required: false,
      code: "",
      dateTime: "",
      message: "",
    },
    bibNos: 1,
    bibSort1: "surname",
    bibSort2: "firstname",
    bibSort3: "dob",
    heatOrder: "s",
    stadium: "",
    adjustEventNo: 0,
    pfTargetDirectory: "",
    athleteType: "A",
    tickets: {
      enabled: false,
    },
    contact: {
      id: 30,
      userName: "Nuneaton Open Events",
      tel: "02476 344 429",
      email: "<EMAIL>",
      visible: true,
    },
    cardInfo: {
      enabled: true,
      availableFrom: "",
      callRoom: true,
    },
    subscription: {
      enabled: false,
      timeCloses: "",
      organiserMessage: "",
      e4sMessage: "",
      refunded: "",
      process: true,
      processRefundTime: "",
    },
    checkIn: {
      enabled: false,
      checkInDateTimeOpens: "",
      defaultFrom: 180,
      defaultTo: 60,
      qrCode: true,
      text: "",
      terms: "",
      useTerms: false,
      seedOnEntries: false,
    },
    school: false,
    orgFreeEntry: false,
    autoPayFreeEntry: false,
    cheques: {
      allow: false,
      ends: "",
    },
    allowAdd: {
      unregistered: true,
      registered: true,
    },
    timetable: "Provisional",
    helpText: {
      schedule: "",
      teams: "",
      cart: "",
    },
    showTeamAthletes: true,
    singleAge: false,
    showAthleteAgeInEntries: false,
    report: {
      summary: true,
      athletes: true,
      ttathletes: true,
      ttentries: true,
      individual_entries: true,
      teams: true,
      subscriptions: true,
      orders: true,
      events: true,
    },
    athleteSecurity: {
      areas: [],
      clubs: [],
      onlyClubsUpTo: "",
    },
    ui: {
      enterButtonText: "Enter",
      entryDefaultPanel: "SCHEDULE",
      ticketComp: 0,
      ticketCompButtonText: "Buy Tickets",
      sectionsToHide: {
        ATHLETES: false,
        TEAMS: false,
        SCHEDULE: false,
        SHOP: false,
      },
    },
    athleteQrData: false,
    nonGuests: [],
    disabledReason: "",
    paymentCode: "",
    laneCount: 8,
    compLimits: {
      athletes: 0,
      entries: 0,
      teams: 0,
    },
    stopReport: false,
    cancelEvent: {
      hrsBeforeClose: 48,
      refund: {
        allow: true,
        type: "E4S_FEES",
      },
      credit: {
        allow: true,
      },
    },
    autoEntries: {
      selectedTargetComp: {
        id: 0,
        name: "",
        timeTronicsUrl: "",
      },
      targetable: {
        allowedSources: [],
        enabled: false,
      },
    },
    level: "",
    dates: ["2023-05-31"],
    clubComp: false,
    categoryId: 3865,
    selfService: [],
  },
  logo: "/resources/nuneaton_opens_logo.jpg",
  areaid: 0,
  teamid: 0,
  ctcid: null,
  maxmale: null,
  name: "test rules",
  tandc: {
    link: "",
    description:
      "I agree that entry fees paid are non transferable and non refundable for this event.",
  },
  areaname: "",
  entity: "",
  entityid: 0,
  ctc: {
    ctcid: null,
    maxMale: null,
    maxFemale: null,
    maxTeams: null,
    maxAthletes: null,
    maxAgeGroup: null,
    uniqueEvents: null,
    singleAgeGroup: null,
  },
  compRules: [
    {
      id: 212,
      agid: 0,
      options: {
        maxCompEvents: 2,
        maxCompField: 0,
        maxCompTrack: 0,
        maxExcludedEvents: 0,
        displayMaxDayFields: false,
      },
    },
  ],
  id: 368,
  indivEvents: true,
  hasSecureEvents: false,
  teamEvents: false,
  clubCompInfo: {},
  compAgeGroups: [
    {
      id: 3,
      name: "Under 9",
      fromDate: "2014-09-01",
      toDate: "2017-08-31",
    },
    {
      id: 1,
      name: "Under 11",
      fromDate: "2012-09-01",
      toDate: "2014-08-31",
    },
    {
      id: 2,
      name: "Under 13",
      fromDate: "2010-09-01",
      toDate: "2012-08-31",
    },
    {
      id: 209,
      name: "Under 13",
      fromDate: "2010-09-01",
      toDate: "2011-10-01",
    },
    {
      id: 9,
      name: "Under 15",
      fromDate: "2008-09-01",
      toDate: "2010-08-31",
    },
    {
      id: 8,
      name: "Under 17",
      fromDate: "2006-09-01",
      toDate: "2008-08-31",
    },
    {
      id: 11,
      name: "Under 20",
      fromDate: "2004-01-01",
      toDate: "2006-08-31",
    },
    {
      id: 12,
      name: "Under 23",
      fromDate: "2001-01-01",
      toDate: "2003-12-31",
    },
    {
      id: 28,
      name: "Senior",
      fromDate: "1989-01-01",
      toDate: "2003-12-31",
    },
    {
      id: 14,
      name: "Masters",
      fromDate: "1922-06-01",
      toDate: "1988-12-31",
    },
  ],
  humanReadableDate: "31st May 23",
} as any as ICompetition;

export const userInfoRules: IUserInfo = {
  // e4s_pagessize: 25,
  // e4s_schedInfoState: 0,
  e4s_credit: 3.5,
  orgs: [],
  areas: [],
  clubs: [],
  user: {
    id: 19828,
    // user_login: "nick wall",
    // user_nicename: "nick-wall",
    user_email: "<EMAIL>",
    display_name: "nick wall",
    role: "USER",
    google_email: "",
    impersonating: false,
    // e4sCredit: [3.5],
    permissions: [],
    version: {
      current: "v1",
      toggle: true,
    },
  },
  // wp_role: "subscriber",
  security: {
    permissions: [],
    permLevels: {
      builder: [],
    },
  },
};
