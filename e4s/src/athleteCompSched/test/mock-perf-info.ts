import { IPerfInfo } from "../pb/v3/edit-pb-v3-models";

export const mockPerfInfo: IPerfInfo = {
  perf: 0,
  limits: {
    min: 1,
    max: 10,
  },
  uom: [
    {
      pattern: "0.99",
      text: "metres",
      short: "m",
      uomType: "D",
    },
  ],
  ageGroup: "Under 17",
  curAgeGroup: "Under 17",
  athleteId: 130941,
  eventId: 22,
  eventName: "Long Jump",
  eventType: "D",
  info: "",
  pbAchieved: "",
  pb: 0,
  sbAchieved: "",
  sb: 0,
  perfText: "!#!0",
  pbText: "!#!0",
  sbText: "!#!0",
};
