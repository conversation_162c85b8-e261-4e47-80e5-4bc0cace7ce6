<template>
  <div class="event-card" :class="getClass">
    <!--FIRST LINE large only-->
    <div class="row hide-on-med-and-down">
      <div class="col l4">
        <div class="event-card-first-line">
          <!--          <div-->
          <!--            class="event-card-checkbox"-->
          <!--            @click="userSelecting"-->
          <!--            v-html="getCheckBox(compEvent)"-->
          <!--          ></div>-->
          <div
            class="event-card-checkbox"
            @click="userSelecting"
            v-html="getCheckBoxHtml"
          ></div>

          <div class="event-card-date-time e4s-force-inline-block">
            <span class="event-card-date" v-text="getEventDate"></span>
            <span class="event-card-date-sep">@</span>
            <span class="event-card-time" v-text="getEventTime"></span>
          </div>
        </div>
      </div>

      <div class="col l5">
        <div class="event-card-name">
          <div class="e4s-flex-column">
            <div class="e4s-flex-row e4s-gap--standard">
              <span v-text="getCompEventName"></span>
              <i
                v-if="
                  athleteCompSchedService &&
                  athleteCompSchedService.isTeamEvent(compEvent)
                "
                class="tiny material-icons"
              >
                group
              </i>
            </div>
            <div
              v-text="compEvent.ageGroupName"
              v-if="compEvent.ceoptions.ageGroupOverlap"
            ></div>
          </div>
        </div>
      </div>

      <div class="col l3">
        <span class="right">
          <span
            v-if="rowOptions.showPrice"
            class="event-price"
            v-text="getPrice"
          ></span>
          <span>Entries:</span>
          <span v-text="getEntryCountDisplay"></span>
        </span>
      </div>
    </div>
    <!--/FIRST LINE large only-->

    <!--SECOND LINE for large only -->
    <div class="row hide-on-med-and-down">
      <div class="col l8">
        <!--                <button-->
        <!--                    v-if="getIsEventPaidFor"-->
        <!--                    class="e4s-button e4s-button&#45;&#45;green"-->
        <!--                    v-on:click.prevent="showCompEventActions">-->
        <!--                    <span>Options</span>-->
        <!--                </button>-->
        <div
          class="e4s-force-inline-block e4s-bold"
          v-if="getAreActionsAvailable"
        >
          <!--                    <a href="#" @click.prevent="showCompEventActions">Options</a>-->
          <button
            class="e4s-button e4s-button--small e4s-button--green"
            v-on:click="showCompEventActions"
          >
            Options
          </button>
        </div>
        <div
          class="event-card-message e4s-force-inline-block"
          v-text="compEvent.ruleMessage"
        ></div>
      </div>

      <div class="col l4">
        <div class="e4s-flex-row" v-if="showPbInput || doAnyEventsRequirePb">
          <div class="e4s-flex-column e4s-flex-row--end" v-if="!isMultiEvent">
            <div class="e4s-flex-column">
              <span class="e4s-flex-row--end">Estimated Performance</span>
            </div>
            <div
              class="
                e4s-flex-row
                e4s-gap--small
                e4s-justify-flex-row-vert-center
                e4s-flex-row--end
              "
            >
              <span
                class="e4s-info-text--warn"
                v-text="showPbInput ? '(Required)' : '(Not Required)'"
              ></span>
              <div @click="editPb" class="e4s-flex-row--end" v-if="showPbInput">
                <!--                <FieldTextV2-->
                <!--                  ref="pbInput"-->
                <!--                  :value="getPbDisplayValue"-->
                <!--                  class="event-card&#45;&#45;pb-input"-->
                <!--                  @input="handlePbInput"-->
                <!--                  @onClick="handlePbInputClick"-->
                <!--                />-->

                <button
                  class="
                    browser-default
                    e4s-input-field e4s-input-field--primary
                    event-card--pb-input
                  "
                  v-text="getPbDisplayValue"
                ></button>
              </div>
            </div>

            <!--            <PbDisplay-->
            <!--              :comp-event="compEvent"-->
            <!--              :pb-service="pbService"-->
            <!--              v-on:editPb="editPb"-->
            <!--            ></PbDisplay>-->
          </div>

          <div v-if="isMultiEvent">
            <span class="event-card-pb-label">SP</span>
            <span v-text="pbUserEntry"></span>
            <div
              class="event-card-pbuom-input e4s-force-inline-block"
              v-text="pbUom"
            ></div>
          </div>
        </div>
      </div>
    </div>
    <!--/SECOND LINE for large only-->

    <!--FIRST LINE Med and below only-->
    <div class="row hide-on-large-only">
      <div class="col s9 m9">
        <div class="event-card-first-line">
          <div
            class="event-card-checkbox"
            @click="userSelecting"
            v-html="getCheckBoxHtml"
          ></div>
          <div class="event-card-name e4s-force-inline-block">
            <div class="e4s-flex-row e4s-gap--standard">
              <span v-text="getCompEventName"></span>
              <span
                v-text="compEvent.ageGroupName"
                v-if="compEvent.ceoptions.ageGroupOverlap"
              ></span>
              <i
                v-if="
                  athleteCompSchedService &&
                  athleteCompSchedService.isTeamEvent(compEvent)
                "
                class="tiny material-icons"
                >group</i
              >
            </div>
          </div>
        </div>
      </div>

      <div class="col s3 m3">
        <span class="right">
          <span
            v-if="rowOptions.showPrice"
            class="event-price"
            v-text="getPrice"
          ></span>
          <!--<br>Entries:-->
          <span
            class="hide-on-small-and-down"
            v-text="'Entries: ' + getEntryCountDisplay"
            v-if="showPbInput"
          ></span>
        </span>
      </div>
    </div>
    <!--/FIRST LINE Med and below only-->

    <!--SECOND LINE for Med and below only-->
    <div class="row hide-on-large-only">
      <div class="col s6 m6">
        <div class="event-card-date-time e4s-force-inline-block">
          <span class="event-card-date" v-text="getEventDate"></span>
          <span class="event-card-date-sep">@</span>
          <span class="event-card-time" v-text="getEventTime"></span>
        </div>
      </div>

      <div class="col s6 m6">
        <div class="e4s-flex-row" v-if="showPbInput || doAnyEventsRequirePb">
          <div v-if="!isMultiEvent" class="e4s-flex-column e4s-flex-row--end">
            <span class="e4s-flex-row--end">Estimated Performance</span>
            <div
              class="
                e4s-flex-row
                e4s-gap--small
                e4s-justify-flex-row-vert-center
                e4s-flex-row--end
              "
            >
              <span
                class="e4s-info-text--warn"
                v-text="showPbInput ? '(Required)' : '(Not Required)'"
              ></span>
              <div @click="editPb" class="e4s-flex-row--end">
                <!--                <FieldTextV2-->
                <!--                  ref="pbInput2"-->
                <!--                  v-if="showPbInput"-->
                <!--                  :value="getPbDisplayValue"-->
                <!--                  class="event-card&#45;&#45;pb-input"-->
                <!--                  @onClick="handlePbInputClick2"-->
                <!--                />-->
                <button
                  class="
                    browser-default
                    e4s-input-field e4s-input-field--primary
                    event-card--pb-input
                  "
                  v-text="getPbDisplayValue"
                ></button>
              </div>
            </div>

            <!--            <PbDisplay-->
            <!--              :comp-event="compEvent"-->
            <!--              :pb-service="pbService"-->
            <!--              v-on:editPb="editPb"-->
            <!--            ></PbDisplay>-->
          </div>

          <div v-if="isMultiEvent">
            <span class="event-card-pb-label">SP</span>
            <span v-text="pbUserEntry"></span>
            <div
              class="event-card-pbuom-input e4s-force-inline-block"
              v-text="pbUom"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="row hide-on-large-only"
      v-if="getAreActionsAvailable || compEvent.ruleMessage.length > 0"
    >
      <div class="col s12 m12">
        <div
          class="e4s-force-inline-block e4s-bold"
          v-if="getAreActionsAvailable"
        >
          <!--                    <a href="#" @click.prevent="showCompEventActions">Options</a>-->
          <button
            class="e4s-button e4s-button--small e4s-button--green"
            v-on:click="showCompEventActions"
          >
            Options
          </button>
        </div>
        <div
          class="event-card-message e4s-force-inline-block"
          v-text="compEvent.ruleMessage"
        ></div>
      </div>
    </div>
    <!--/SECOND LINE for Med and below only-->

    <!--FOOTER STUFF-->
    <div class="row" v-if="hasHelpText || isMultiEvent">
      <div class="col s6 m6 l6">
        <span v-if="hasHelpText">
          <a
            v-if="!displayMoreInfoComponent"
            href="#"
            @click.prevent="showMoreInfo"
            >More Info...</a
          >
          <a v-if="displayCloseMoreInfo" href="#" @click.prevent="hideMoreInfo"
            >Close Info...</a
          >
        </span>
      </div>

      <div class="col s6 m6 l6">
        <span v-if="isMultiEvent" class="right">
          <a
            v-if="!displayMultiPbComponent"
            href="#"
            @click.prevent="showMultiPbGrid"
            >Edit SPs...</a
          >
        </span>
      </div>
    </div>

    <div
      class="e4s-flex-row e4s-gap--standard"
      style="margin-top: 5px"
      v-if="getHasClubCompInfoCompetition"
    >
      <span v-text="getCompClubAthletesDescription"></span>
    </div>

    <div class="help-text-wrapper">
      <div
        class="help-text"
        v-if="displayMoreInfoComponent && hasEventHelpText"
      >
        <div
          class="help-text-content"
          v-html="compEvent.eoptions.helpText"
        ></div>
      </div>
      <div
        class="help-text"
        v-if="displayMoreInfoComponent && hasCompEventHelpText"
      >
        <div
          class="help-text-content"
          v-html="compEvent.ceoptions.helpText"
        ></div>
      </div>
    </div>
    <div class="event-card-multi-pb-more" v-if="isMultiEvent">
      multi-pb-card-grid goes here
      <!--      <multi-pb-card-grid-->
      <!--        :event="compEvent"-->
      <!--        v-on:closeMultiPb="hideMultiPbGrid"-->
      <!--        v-if="displayMultiPbComponent"-->
      <!--      >-->
      <!--      </multi-pb-card-grid>-->
    </div>
    <!--/FOOTER STUFF-->
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import { IConfigApp } from "../config/config-app-models";
import { CONFIG_STORE_CONST, IConfigStoreState } from "../config/config-store";
import { USER_MESSAGE_LEVEL } from "../user-message/user-message-models";
import { messageDispatchHelper } from "../user-message/user-message-store";
import type {
  IAthleteCompSchedResponse,
  IAthleteCompSchedRuleEvent,
  IRowOptions,
} from "./athletecompsched-models";
import { IEventActions } from "./athletecompsched-models";
import { AthleteCompSchedService } from "./athletecompsched-service";
import { PBService } from "./pb-service";
import {
  ATH_COMP_SCHED_STORE_CONST,
  IAthCompSchedStoreState,
} from "./store/athleteCompSched-store";
import { ENTRY_STORE_CONST, IEntryStoreState } from "../entry/entry-store";
import { ICompetitionInfo } from "../competition/competition-models";
import { CompetitionService } from "../competition/competiton-service";
import {
  IClubCompInfo,
  IClubCompInfoEntryIndiv,
} from "../entry/v2/schools/clubCompInfo-models";
import * as ClubCompInfoService from "../entry/v2/schools/clubCompInfoService";
import FieldTextV2 from "../common/ui/layoutV2/fields/field-text-v2.vue";
import { getPerfForDisplay } from "./pb/v3/pb-service-v3";

@Component({
  name: "event-card",
  components: { FieldTextV2 },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
    }),
    ...mapState(ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME, {
      eventsServerResponse: (state: any) =>
        state.eventsServerResponse,
    }),
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      selectedAthlete: (state: any) =>
        state.entryForm.selectedAthlete,
      selectedCompetition: (state: any) =>
        state.entryForm.selectedCompetition,
      clubCompInfo: (state: any) => state.entryForm.clubCompInfo,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapGetters({
      hasBuilderPermissionForComp:
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_GETTERS_HAS_BUILDER_PERM_FOR_COMP,
    }),
  },
})
export default class EventCard extends Vue {
  public isAdmin: boolean = false;
  public eventsServerResponse: IAthleteCompSchedResponse =
    {} as IAthleteCompSchedResponse;
  public selectedCompetition: ICompetitionInfo = {} as ICompetitionInfo;
  public hasBuilderPermissionForComp: boolean = false;
  public readonly configApp: IConfigApp = {} as IConfigApp;
  public readonly clubCompInfo: IClubCompInfo = {} as IClubCompInfo;

  @Prop({
    default: () => {
      return {} as IAthleteCompSchedRuleEvent;
    },
  })
  public readonly compEventProp: IAthleteCompSchedRuleEvent;
  @Prop({
    default: () => {
      return [] as IAthleteCompSchedRuleEvent[];
    },
  })
  public readonly compEventsProp: IAthleteCompSchedRuleEvent[];

  @Prop({
    default: false,
  })
  public readonly doAnyEventsRequirePb: boolean;

  // @Prop() public readonly compEventProp: IAthleteCompSchedRuleEvent;
  // @Prop() public readonly compEventsProp: IAthleteCompSchedRuleEvent[];

  public compEvent: IAthleteCompSchedRuleEvent =
    {} as IAthleteCompSchedRuleEvent;
  public compEvents: IAthleteCompSchedRuleEvent[] = [];

  public pbService: PBService | null = null;
  public athleteCompSchedService: AthleteCompSchedService | null = null;
  public competitionService: CompetitionService | null = null;

  public pbUserEntry: string = "";
  public pbUom: string = "";
  public pbEditMessage: string = "";

  public displayMultiPbComponent = false;
  public displayingMoreInfoComponent = false;
  public pbInput = null;
  public pbInput2 = null;

  public rowOptions: IRowOptions = {} as IRowOptions;

  public mounted() {
    // Initialize services
    this.athleteCompSchedService = new AthleteCompSchedService();
    this.pbService = new PBService();
    this.competitionService = new CompetitionService();

    // Initialize properties
    this.compEvent = R.clone(this.compEventProp);
    this.compEvents = R.clone(this.compEventsProp);
    this.rowOptions = this.athleteCompSchedService.factoryRowOptions();

    this.formatPb(this.compEvent);
    this.rowOptions = this.athleteCompSchedService.getRowOptions(
      this.compEvent,
      this.eventsServerResponse
    );
    this.displayingMoreInfoComponent =
      this.athleteCompSchedService.getRowOptions(
        this.compEvent,
        this.eventsServerResponse
      ).autoExpandHelpText;
  }

  @Watch("compEventProp")
  public onCompEventChanged(newValue: IAthleteCompSchedRuleEvent) {
    this.compEvent = R.clone(newValue);
    this.formatPb(newValue);
  }

  @Watch("compEventsProp")
  public onCompEventsChanged(newValue: IAthleteCompSchedRuleEvent[]) {
    this.compEvents = R.clone(newValue);
  }

  public userSelecting() {
    if (
      this.athleteCompSchedService &&
      this.athleteCompSchedService.isEventOverSubscribed(this.compEvent)
    ) {
    }

    // if (this.getIsCheckBoxDisabledByPb) {
    //   messageDispatchHelper(
    //     "Estimated Performance: this event requires a performance to be entered",
    //     USER_MESSAGE_LEVEL.WARN.toString()
    //   );
    //   return;
    // }

    if (this.compEvent.userEventAction) {
      messageDispatchHelper(
        "Event Switch: this is the same event that is being switched, " +
          "select another event.",
        USER_MESSAGE_LEVEL.WARN.toString()
      );
      return;
    }
    this.$emit("onEventSelected", this.compEvent);
  }

  public get getIsPbStateDisablingEntry() {
    return (
      this.athleteCompSchedService &&
      this.athleteCompSchedService.isPbRequiredForEvent(this.compEvent) &&
      !this.athleteCompSchedService.doesEventHavePb(this.compEvent)
    );
  }

  public getCheckBox(compEvent: IAthleteCompSchedRuleEvent) {
    if (compEvent.paid === 1) {
      return "<input type='checkbox' checked='checked' disabled='disabled' /><span></span>";
    }

    if (compEvent.order.productId > 0) {
      return "<input type='checkbox' checked='checked'/><span></span>";
    }

    if (compEvent.ruleIsDisabledBy) {
      return "<input type='checkbox' disabled='disabled' /><span></span>";
    }

    return "<input type='checkbox' /><span></span>";

    // if (compEvent.userEventAction) {
    //     return "<input type='checkbox' disabled='disabled' /><span></span>";
    // }
    // if (compEvent.userSelected) {
    //     return "<input type='checkbox' checked='checked' /><span></span>";
    // }
    // if (compEvent.ruleIsDisabledBy) {
    //     return "<input type='checkbox' disabled='disabled' /><span></span>";
    // }
    // if (compEvent.ruleIsDisabledBy === false && compEvent.userSelected === false) {
    //     return "<input type='checkbox' /><span></span>";
    // }
    // return "";
  }

  public get getIsCheckBoxDisabledByPb() {
    const isPbRequiredForEvent =
      this.athleteCompSchedService &&
      this.athleteCompSchedService.isPbRequiredForEvent(this.compEvent);
    const doesEventHavePb =
      this.athleteCompSchedService &&
      this.athleteCompSchedService.doesEventHavePb(this.compEvent);

    console.log(
      "event-card.getIsCheckBoxDisabledByPb() " +
        this.compEvent.eventGroup +
        ", isPbRequiredForEvent: " +
        isPbRequiredForEvent +
        " doesEventHavePb: " +
        doesEventHavePb
    );

    if (isPbRequiredForEvent && !doesEventHavePb) {
      return true;
    }
    return false;
    // return (
    //   this.athleteCompSchedService.isPbRequiredForEvent(this.compEvent) &&
    //   !this.athleteCompSchedService.doesEventHavePb(this.compEvent)
    // );
  }

  public get getCheckBoxHtml() {
    if (this.compEvent.paid === 1) {
      return "<input type='checkbox' checked='checked' disabled='disabled' /><span></span>";
    }

    if (this.compEvent.order.productId > 0) {
      return "<input type='checkbox' checked='checked'/><span></span>";
    }

    if (this.compEvent.ruleIsDisabledBy) {
      return "<input type='checkbox' disabled='disabled' /><span></span>";
    }

    if (this.getIsCheckBoxDisabledByPb) {
      return "<input type='checkbox' disabled='disabled' /><span></span>";
    }

    return "<input type='checkbox' /><span></span>";

    // if (compEvent.userEventAction) {
    //     return "<input type='checkbox' disabled='disabled' /><span></span>";
    // }
    // if (compEvent.userSelected) {
    //     return "<input type='checkbox' checked='checked' /><span></span>";
    // }
    // if (compEvent.ruleIsDisabledBy) {
    //     return "<input type='checkbox' disabled='disabled' /><span></span>";
    // }
    // if (compEvent.ruleIsDisabledBy === false && compEvent.userSelected === false) {
    //     return "<input type='checkbox' /><span></span>";
    // }
    // return "";
  }

  public get getEntryCountDisplay() {
    return (
      this.compEvent.entrycnt +
      (this.compEvent.maxathletes > 0 ? "/" + this.compEvent.maxathletes : "")
    );
  }

  public get getEventDate() {
    return this.athleteCompSchedService
      ? this.athleteCompSchedService.getEventDate(this.compEvent)
      : "";
  }

  public get getEventTime() {
    return this.athleteCompSchedService
      ? this.athleteCompSchedService.getEventTime(this.compEvent)
      : "";
  }

  public get showPbInput() {
    return this.athleteCompSchedService
      ? this.athleteCompSchedService.showPbInput(this.compEvent)
      : false;
  }

  public get isMultiEvent() {
    return this.compEvent.multiid && this.compEvent.multiid > 0;
  }

  public showMultiPbGrid() {
    this.displayMultiPbComponent = true;
  }

  public hideMultiPbGrid() {
    this.displayMultiPbComponent = false;
  }

  public showMoreInfo() {
    this.displayingMoreInfoComponent = true;
  }

  public hideMoreInfo() {
    this.displayingMoreInfoComponent = false;
  }

  public get displayMoreInfoComponent() {
    return (
      this.displayingMoreInfoComponent &&
      (this.hasEventHelpText || this.hasCompEventHelpText)
    );
  }

  public get displayCloseMoreInfo() {
    const autoExpandHelpText = this.athleteCompSchedService
      ? this.athleteCompSchedService.getRowOptions(
          this.compEvent,
          this.eventsServerResponse
        ).autoExpandHelpText
      : false;
    return this.displayMoreInfoComponent && !autoExpandHelpText;
  }

  public get hasHelpText() {
    return this.hasEventHelpText || this.hasCompEventHelpText;
  }

  public get hasEventHelpText() {
    return this.compEvent.eoptions && this.compEvent.eoptions.helpText
      ? true
      : false;
  }

  public get hasCompEventHelpText() {
    return this.compEvent.ceoptions && this.compEvent.ceoptions.helpText
      ? true
      : false;
  }

  public get getPrice() {
    //  "€"
    return this.compEvent.price && this.compEvent.price.curPrice
      ? this.configApp.currency + this.compEvent.price.curPrice.toFixed(2)
      : "";
  }

  public formatPb(compEvent: IAthleteCompSchedRuleEvent) {
    this.pbUserEntry = "";
    this.pbUom = "";

    if (R.isNil(compEvent.pb)) {
      return;
    }

    if (
      this.athleteCompSchedService &&
      this.athleteCompSchedService.hasSecondarySpend(compEvent)
    ) {
      return;
    }

    // const pbValue = this.getPbDisplayValue(compEvent);
    const pbText = compEvent.perfInfo.pbText;
    this.pbUserEntry = pbText + "";

    let pbUom: string = "";
    if (compEvent.perfInfo.uom.length > 0 && compEvent.maxathletes > -1) {
      pbUom = compEvent.perfInfo.uom[0].short;
    }
    if (compEvent.multiid && compEvent.multiid > 0) {
      pbUom = compEvent.perfInfo.uom[0].short;
    }
    this.pbUom = pbUom;
  }

  public onPbChanged() {}

  public switchEvent() {
    const event: IAthleteCompSchedRuleEvent = this.compEvent;
    this.$store.dispatch(
      ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_SWITCH_EVENT_SET,
      { event }
    );
  }

  public switchEventCancel() {
    const event: IAthleteCompSchedRuleEvent = this.compEvent;
    this.$store.dispatch(
      ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_SWITCH_EVENT_SET_CANCEL,
      { event }
    );
  }

  public get getAreActionsAvailable(): boolean {
    if (
      this.athleteCompSchedService &&
      this.athleteCompSchedService.isScheduleOnly(this.compEvent)
    ) {
      return false;
    }

    //  If event not in cart or paid for, then no actions available.
    if (this.compEvent.paid <= 0 && this.compEvent.order.productId <= 0) {
      return false;
    }

    let isClosed = false;
    if (this.competitionService) {
      isClosed = this.competitionService.isClosed(
        this.selectedCompetition.entriesClose
      );
    }

    return (
      this.hasBuilderPermissionForComp ||
      (this.getIsEventPaidFor && !isClosed)
    );
  }

  public get getIsEventPaidFor() {
    return this.compEvent.paid === 1;
  }

  public get getCanSwitchEventCancel() {
    return (
      this.compEvent.userEventAction &&
      this.compEvent.userEventAction.id === IEventActions.SWITCH_EVENT.id
    );
  }

  public get getCanCancelEvent() {
    if (this.compEvent.paid <= 0) {
      return false;
    }
    const userMatches =
      this.compEvent.user.userId === this.configApp.userInfo.user.id;
    return this.hasBuilderPermissionForComp || userMatches;
  }

  public get getCompEventName() {
    return this.athleteCompSchedService
      ? this.athleteCompSchedService.getCompEventName(this.compEvent)
      : "";
  }

  public get getClass() {
    if (this.compEvent.paid === 1 || this.compEvent.order.productId > 0) {
      return "event-selected";
    }
    return "";
  }

  public get getHasClubCompInfoCompetition(): boolean {
    return ClubCompInfoService.hasClubCompInfoCompetition(this.clubCompInfo);
  }

  public get getCompClubAthletes(): string[] {
    const match: IClubCompInfoEntryIndiv | null =
      ClubCompInfoService.getEntryMatchByEventGroupId(
        this.compEvent.maxgroup.toString(),
        this.clubCompInfo
      ) as IClubCompInfoEntryIndiv;
    if (match as IClubCompInfoEntryIndiv) {
      return ClubCompInfoService.getEntriesDescriptionIndiv(match.athletes);
    }
    return [];
  }

  public get getCompClubAthletesDescription(): string {
    if (this.getCompClubAthletes.length > 0) {
      return "Athletes: " + this.getCompClubAthletes.join(", ");
    }
    return "";
  }

  public get getPbDisplayValue(): string {
    return getPerfForDisplay(this.compEvent.perfInfo);
  }

  public cancelEvent() {
    this.$emit("cancelE4SEvent", this.compEvent);
  }

  public editPb() {
    console.log("event-card.editPb() compEvent: " + this.compEvent);
    this.$emit("onEditPb", this.compEvent);
  }

  public showCompEventActions() {
    this.$emit("showCompEventActions", this.compEvent);
  }
}
</script>

<style scoped>
.event-card {
  padding: 0.25em;
}

.event-selected {
  background-color: #e1eefd;
}

.event-card--pb-input {
  width: 100px;
}
</style>
