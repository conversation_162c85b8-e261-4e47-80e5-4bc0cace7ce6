<template>
    <e4s-modal v-if="getShowSwitchModal"
               :header-message="'Switch Event'"
               :body-message="getSwitchMessage"
               :button-primary-text="'Switch Event'"
               :isLoading="isLoading"
               v-on:closeSecondary="switchEventCancel"
               v-on:closePrimary="switchEvent">
    </e4s-modal>
</template>

<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";
    import type { IAthCompSchedStoreState } from "./store/athleteCompSched-store";
    import { ATH_COMP_SCHED_STORE_CONST } from "./store/athleteCompSched-store";
    import { mapState } from "vuex";
    import E4sModal from "../common/ui/e4s-modal.vue";
    import { IEventActions } from "./athletecompsched-models";
    import { AthleteCompSchedData } from "./athletecompsched-data";
    import type { IServerGenericResponse } from "../common/common-models";
    import { messageDispatchHelper } from "../user-message/user-message-store";
    import { USER_MESSAGE_LEVEL } from "../user-message/user-message-models";

    @Component({
        name: "event-switch-modal",
        components: {
            "e4s-modal": E4sModal
        },
        computed: {
            ...mapState(
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME,
                {
                    athCompSchedStoreState: (state: any) => state
                }
            )
        }
    })
    export default class EventSwitchModal extends Vue {
        public athCompSchedStoreState: IAthCompSchedStoreState = {} as IAthCompSchedStoreState;

        public isLoading: boolean = false;

        public get getSwitchMessage() {
            const eventSource = this.athCompSchedStoreState.eventSource;
            const eventDest = this.athCompSchedStoreState.eventDest;
            if (this.athCompSchedStoreState.eventAction.id === IEventActions.SWITCH_EVENT.id) {
                return "Are you sure you want to switch event from " + eventSource.Name + " to " + eventDest.Name + "?";
            }
            return "Action not designed!";
        }

        public get getShowSwitchModal() {
            return this.athCompSchedStoreState.eventDest &&
                this.athCompSchedStoreState.eventDest.ceid &&
                this.athCompSchedStoreState.eventDest.ceid > 0;
        }

        public switchEvent() {
            console.log("switchEvent");
            const athleteCompSchedData: AthleteCompSchedData = new AthleteCompSchedData();
            this.isLoading = true;
            athleteCompSchedData.switchEvent(this.athCompSchedStoreState.eventSource.entryId, this.athCompSchedStoreState.eventDest.ceid)
                .then( (response: IServerGenericResponse) => {
                    if (response.errNo > 0) {
                        messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
                        return;
                    }
                    messageDispatchHelper("Event Switched", USER_MESSAGE_LEVEL.INFO.toString());
                    this.$store.commit(
                        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
                        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_MUTATIONS_SWITCH_EVENT_CANCEL
                    );
                    this.$store.dispatch(
                        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
                        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_INITIALISE_EVENTS
                    );
                    return;
                })
                .catch((error) => {
                    messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
                    return;
                })
                .finally(() => {
                    this.isLoading = false;
                });
        }

        public switchEventCancel() {
            console.log("switchEventCancel");
            this.$store.commit(
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_MUTATIONS_SWITCH_EVENT_SET_DEST,
                {}
            );
        }
    }
</script>
