<template>
  <div>
    <span v-if="!ageInfo.vetAgeGroup || !ageInfo.vetAgeGroup.Name">
      <span>Age Group:</span>
      <span v-text="ageInfo.ageGroup"></span>
    </span>

      <!-- <span>Current Age:</span> No need to show??
      <span v-text="ageInfo.currentAge"></span> -->

    <span v-if="ageInfo.vetAgeGroup && ageInfo.vetAgeGroup.Name">
      <span>Age Group:</span>
      <span v-text="ageInfo.vetAgeGroup.Name"></span>
    </span>
  </div>
</template>

<script lang="ts">

import Vue from "vue";
import Component from "vue-class-component";
import {Prop} from "vue-property-decorator";
import type { IAgeInfo } from "@/athlete/athlete-models";

@Component({
})
export default class AgeInfo extends Vue {
    @Prop({ default: () => ({ ageGroup: "", currentAge: 0, vetAgeGroup: "" }) }) public ageInfo: IAgeInfo;
}
</script>