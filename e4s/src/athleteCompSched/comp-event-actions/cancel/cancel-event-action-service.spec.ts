import { mockCancelEvent200m } from "./mock/cancelEventMock200m";
import { IAthleteCompSchedRuleEvent } from "../../athletecompsched-models";
import * as CancelEventActionService from "./cancel-event-action-service";
import { mockCompEventActionsResult } from "./mock/cancelMockCancelResult";
import { ICompEventActionsResult } from "../comp-event-actions-controller";
import { simpleClone } from "../../../common/common-service-utils";
import { ICancelEvent } from "../../../builder/builder-models";
import { IConfigOptionsPrice } from "../../../config/config-app-models";

const mock200m: IAthleteCompSchedRuleEvent = mockCancelEvent200m;
const compEventActionsResult: ICompEventActionsResult =
  mockCompEventActionsResult;

const mockConfigOptionsPrice: IConfigOptionsPrice = {
  addCost: 0.35,
  minFee: 0.5,
  minCost: 5,
  percCharged: 5,
  nationaladdCost: 0,
  nationalminCost: 12,
};

describe("comp-event-action-service", () => {
  test("getHasZeroPrice", () => {
    expect(mock200m.order.wcLineValue).toBe(6);
    expect(CancelEventActionService.getHasZeroPrice(mock200m)).toBe(false);
  });

  test("calculateFee", () => {
    expect(
      CancelEventActionService.calculateFee(6, mockConfigOptionsPrice)
    ).toBe(0.5);

    expect(
      CancelEventActionService.calculateFee(10, mockConfigOptionsPrice)
    ).toBe(0.5);

    expect(
      CancelEventActionService.calculateFee(15, mockConfigOptionsPrice)
    ).toBe(0.75);

    expect(
      CancelEventActionService.calculateFee(20, mockConfigOptionsPrice)
    ).toBe(1);
  });

  test("getRefundMessage", () => {
    expect(
      CancelEventActionService.getRefundMessage(
        compEventActionsResult,
        mockConfigOptionsPrice,
        "£"
      )
    ).toBe(
      "This will refund the purchase value £6.00 minus transaction cost £0.50. Refund value: £5.50"
    );

    expect(
      CancelEventActionService.getRefundMessage(
        compEventActionsResult,
        mockConfigOptionsPrice,
        "$"
      )
    ).toBe(
      "This will refund the purchase value $6.00 minus transaction cost $0.50. Refund value: $5.50"
    );
  });

  test("getCreditMessage", () => {
    expect(
      CancelEventActionService.getCreditMessage(compEventActionsResult)
    ).toBe(
      "Credit the total amount paid £6.00. This can be redeemed against any other competition entries."
    );
    expect(
      CancelEventActionService.getCreditMessage(compEventActionsResult, "$")
    ).toBe(
      "Credit the total amount paid $6.00. This can be redeemed against any other competition entries."
    );
  });

  test("canUserRefundOrCredit", () => {
    const eventActionsResult = simpleClone(compEventActionsResult);
    const cancelEvent: ICancelEvent =
      eventActionsResult.competitionSummaryPublic.options.cancelEvent;

    //  Price above zero but not allowed
    cancelEvent.credit.allow = false;
    cancelEvent.refund.allow = false;

    expect(eventActionsResult.compEvent.order.wcLineValue).toBe(6);

    expect(
      CancelEventActionService.canUserRefundOrCredit(eventActionsResult)
    ).toBe(false);

    //  Price above zero and allowed credit
    cancelEvent.credit.allow = true;
    cancelEvent.refund.allow = false;

    expect(eventActionsResult.compEvent.order.wcLineValue).toBe(6);

    expect(
      CancelEventActionService.canUserRefundOrCredit(eventActionsResult)
    ).toBe(true);

    //  Price above zero and allowed refund
    cancelEvent.credit.allow = false;
    cancelEvent.refund.allow = true;

    expect(eventActionsResult.compEvent.order.wcLineValue).toBe(6);

    expect(
      CancelEventActionService.canUserRefundOrCredit(eventActionsResult)
    ).toBe(true);

    //  Price above zero and allowed refund
    cancelEvent.credit.allow = true;
    cancelEvent.refund.allow = true;

    expect(eventActionsResult.compEvent.order.wcLineValue).toBe(6);

    expect(
      CancelEventActionService.canUserRefundOrCredit(eventActionsResult)
    ).toBe(true);

    //  Allowed but price is zero
    cancelEvent.credit.allow = true;
    cancelEvent.refund.allow = true;
    eventActionsResult.compEvent.order.wcLineValue = 0;

    expect(eventActionsResult.compEvent.order.wcLineValue).toBe(0);

    expect(
      CancelEventActionService.canUserRefundOrCredit(eventActionsResult)
    ).toBe(false);
  });

  test("getCancelType", () => {
    const eventActionsResult = simpleClone(compEventActionsResult);
    const cancelEvent: ICancelEvent =
      eventActionsResult.competitionSummaryPublic.options.cancelEvent;

    //  comp does not allow
    cancelEvent.credit.allow = false;
    cancelEvent.refund.allow = false;
    eventActionsResult.compEvent.order.wcLineValue = 0;
    expect(eventActionsResult.compEvent.order.wcLineValue).toBe(0);
    expect(CancelEventActionService.getCancelType(eventActionsResult)).toBe(
      "Remove Only"
    );

    //  still not allowed as zero price
    eventActionsResult.compEvent.order.wcLineValue = 6;
    expect(eventActionsResult.compEvent.order.wcLineValue).toBe(6);
    expect(CancelEventActionService.getCancelType(eventActionsResult)).toBe(
      "Remove Only"
    );

    //  allowed but zero price
    cancelEvent.credit.allow = true;
    cancelEvent.refund.allow = false;
    eventActionsResult.compEvent.order.wcLineValue = 0;
    expect(eventActionsResult.compEvent.order.wcLineValue).toBe(0);
    expect(CancelEventActionService.getCancelType(eventActionsResult)).toBe(
      "Remove Only"
    );

    //  Credit
    cancelEvent.credit.allow = true;
    cancelEvent.refund.allow = false;
    eventActionsResult.compEvent.order.wcLineValue = 6;
    expect(eventActionsResult.compEvent.order.wcLineValue).toBe(6);
    expect(CancelEventActionService.getCancelType(eventActionsResult)).toBe(
      "Credit"
    );

    //  Credit
    cancelEvent.credit.allow = false;
    cancelEvent.refund.allow = true;
    eventActionsResult.compEvent.order.wcLineValue = 6;
    expect(eventActionsResult.compEvent.order.wcLineValue).toBe(6);
    expect(CancelEventActionService.getCancelType(eventActionsResult)).toBe(
      "Refund"
    );

    //  Both
    cancelEvent.credit.allow = true;
    cancelEvent.refund.allow = true;
    eventActionsResult.compEvent.order.wcLineValue = 6;
    expect(eventActionsResult.compEvent.order.wcLineValue).toBe(6);
    expect(CancelEventActionService.getCancelType(eventActionsResult)).toBe("");
  });
});
