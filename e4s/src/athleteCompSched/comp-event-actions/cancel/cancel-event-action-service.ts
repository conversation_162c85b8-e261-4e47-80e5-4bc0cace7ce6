import { IAthleteCompSched } from "../../athletecompsched-models";
import { ICompEventActionsResult } from "../comp-event-actions-controller";
import * as CommonServiceUtils from "../../../common/common-service-utils";
import { IConfigOptionsPrice } from "../../../config/config-app-models";
import { IBaseNameValue } from "../../../common/common-models";

export type CancelType = "Credit" | "Refund" | "Remove Only";

export function getHasZeroPrice(athleteCompSched: IAthleteCompSched): boolean {
  return athleteCompSched.order.wcLineValue === 0;
}

/**
 * Badly named property, this controls whether the user can cancel the entry
 * @param compEventActionsResult
 */
export function getCanUserCancelEntry(
  compEventActionsResult: ICompEventActionsResult
): boolean {
  return compEventActionsResult.competitionSummaryPublic.options.cancelEvent
    .credit.allow;
}

/**
 * @deprecated
 * @param compEventActionsResult
 */
export function canUserRefund(
  compEventActionsResult: ICompEventActionsResult
): boolean {
  return compEventActionsResult.competitionSummaryPublic.options.cancelEvent
    .refund.allow;
}

/**
 * @deprecated
 * @param compEventActionsResult
 */
export function canUserCredit(
  compEventActionsResult: ICompEventActionsResult
): boolean {
  return compEventActionsResult.competitionSummaryPublic.options.cancelEvent
    .credit.allow;
}

/**
 * @deprecated
 * @param compEventActionsResult
 */
export function canUserRefundOrCredit(
  compEventActionsResult: ICompEventActionsResult
): boolean {
  if (getHasZeroPrice(compEventActionsResult.compEvent)) {
    return false;
  }

  return (
    canUserCredit(compEventActionsResult) ||
    canUserRefund(compEventActionsResult)
  );
}

export function calculateFee(
  price: number,
  configOptions: IConfigOptionsPrice
) {
  if (price === 0) {
    return 0;
  }
  let priceResult: number =
    price > configOptions.minCost ? price * 0.05 : configOptions.minFee;
  if (priceResult < configOptions.minFee) {
    priceResult = configOptions.minFee;
  }
  return Number(priceResult.toFixed(2));
}

export function getRefundValue(
  compEventActionsResult: ICompEventActionsResult,
  configOptions: IConfigOptionsPrice
): number {
  const userPaid = compEventActionsResult.compEvent.order.wcLineValue;
  const curFee = calculateFee(userPaid, configOptions);
  return userPaid - curFee;
}

export function getRefundMessage(
  compEventActionsResult: ICompEventActionsResult,
  configOptions: IConfigOptionsPrice,
  currency: string = "£"
): string {
  const userPaid = compEventActionsResult.compEvent.order.wcLineValue;
  const curFee = calculateFee(userPaid, configOptions);
  return (
    "This will refund the purchase value " +
    CommonServiceUtils.getAmountAsCurrency(userPaid, currency) +
    " minus transaction cost " +
    CommonServiceUtils.getAmountAsCurrency(curFee, currency) +
    ". Refund value: " +
    CommonServiceUtils.getAmountAsCurrency(userPaid - curFee, currency)
  );
}

export function getCreditMessage(
  compEventActionsResult: ICompEventActionsResult,
  currency: string = "£"
): string {
  const userPaid = compEventActionsResult.compEvent.order.wcLineValue;
  return (
    "Credit the total amount paid " +
    CommonServiceUtils.getAmountAsCurrency(userPaid, currency) +
    ". This can be redeemed against any other competition entries."
  );
}

export function getCancelTypes(
  compEventActionsResult: ICompEventActionsResult
): IBaseNameValue<CancelType>[] {
  const cancelTypes: Partial<Record<CancelType, IBaseNameValue<CancelType>>> =
    {};

  if (getHasZeroPrice(compEventActionsResult.compEvent)) {
    return [{ name: "Remove Only", value: "Remove Only" }];
  }

  const cancelEvent =
    compEventActionsResult.competitionSummaryPublic.options.cancelEvent;

  //  Neither allowed
  if (!cancelEvent.refund.allow && !cancelEvent.credit.allow) {
    cancelTypes["Remove Only"] = { name: "Remove Only", value: "Remove Only" };
  }

  if (cancelEvent.refund.allow) {
    cancelTypes.Refund = { name: "Refund", value: "Refund" };
  }

  if (cancelEvent.credit.allow) {
    cancelTypes.Credit = { name: "Credit", value: "Credit" };
  }

  return Object.values(cancelTypes) as IBaseNameValue<CancelType>[];
}

export function getCancelType(
  compEventActionsResult: ICompEventActionsResult
): CancelType | "" {
  if (getHasZeroPrice(compEventActionsResult.compEvent)) {
    return "Remove Only";
  }

  const cancelEvent =
    compEventActionsResult.competitionSummaryPublic.options.cancelEvent;

  //  Neither allowed
  if (!cancelEvent.refund.allow && !cancelEvent.credit.allow) {
    return "Remove Only";
  }

  //  both allowed
  if (cancelEvent.refund.allow && cancelEvent.credit.allow) {
    return "";
  }

  //  one allowed
  if (cancelEvent.refund.allow) {
    return "Refund";
  } else if (cancelEvent.credit.allow) {
    return "Credit";
  } else {
    return "Remove Only";
  }
}
