import { IAthleteCompSchedRuleEvent } from "../../../athletecompsched-models";

export const mockCancelEvent200m: IAthleteCompSchedRuleEvent = {
  ceid: 59549,
  ageGroupId: 2,
  ageGroupName: "Under 13",
  description: "Std Event",
  startdate: "2023-09-27T19:45:00+01:00",
  IsOpen: 1,
  tf: "T",
  uomType: "T",
  price: {
    id: 822,
    priceName: "",
    stdPrice: 6,
    curPrice: 6,
    curFee: 0.65,
    salePrice: 6,
    saleDate: "+00:00",
  },
  eventid: 6,
  multiid: 0,
  Name: "200m",
  eventGroup: "200m",
  egOptions: {
    maxAthletes: 0,
    maxInHeat: 8,
    excludeFromCntRule: false,
    combinedId: 0,
    entriesFrom: 0,
    registration: {
      unregisteredAthletes: true,
      registeredAthletes: true,
    },
    display: {
      helpText: "",
      autoExpandHelpText: false,
      showPB: false,
      showPrice: false,
      showEntryCount: false,
      hideOnDisable: false,
    },
    heatInfo: {
      heatDurationMins: 0,
      useLanes: "A",
      unique: false,
      mandatoryPB: true,
    },
    seed: {
      gender: false,
      age: false,
      type: "O",
      firstLane: 1,
      laneCount: 8,
      waiting: false,
      seeded: false,
      qualifyToEg: {
        id: 0,
        compId: 0,
        name: "",
        eventNo: 0,
        eventGroupId: 0,
        eventGroupName: "",
      },
    },
    eventTeam: {
      isTeamEvent: false,
      min: 0,
      max: 0,
      minTargetAgeGroupCount: 0,
      maxOtherAgeGroupCount: 0,
      maxEventTeams: 0,
      teamPositionLabel: "",
      teamSubstituteLabel: "",
      singleClub: "",
      formType: "",
      teamNameFormat: "",
    },
    cardInfo: {
      reportInfo: "",
      trialInfo: "",
    },
    security: {
      clubs: "",
      counties: "",
      regions: {
        reportInfo: "",
        trialInfo: "",
      },
    },
    athleteSecurity: {
      clubs: [],
      counties: [],
      regions: [],
    },
    availability: {
      availableFrom: "",
      availableFromStatus: -1,
      availableTo: "",
      availableToStatus: -1,
    },
    checkIn: {
      enabled: false,
      from: -1,
      to: -1,
      seedOnEntries: false,
      checkInMins: 60,
    },
  },
  split: 0,
  split2: null,
  maxgroup: 10368,
  maxathletes: 0,
  ceoptions: {
    min: 20,
    max: 60,
    helpText: "",
    registeredAthletes: true,
    unregistered: false,
    registered: true,
    isTeamEvent: false,
    wind: "E",
    excludeFromCntRule: false,
    unique: [],
    eventTeam: {
      min: 0,
      max: 0,
      teamPositionLabel: "Leg",
      singleClub: true,
      teamSubstituteLabel: "Substitute",
      formType: "",
      maxTeamsForAthlete: 0,
      disableTeamNameEdit: true,
      mustBeIndivEntered: false,
      currCount: 0,
    },
    rowOptions: {
      autoExpandHelpText: false,
      showPB: true,
      showPrice: false,
      showEntryCount: true,
    },
    maxInHeat: 8,
    heatInfo: {
      useLanes: "A",
      heatDurationMins: 0,
    },
    xiText: "",
    xeText: "",
    xbText: "",
    xrText: "",
    warningMessage: "",
    mandatoryPB: false,
    trialInfo: "",
    reportInfo: "",
    ageGroups: [],
    singleAge: false,
    security: {},
    athleteSecurity: {},
    checkIn: {
      from: -1,
      to: -1,
      seedOnEntries: false,
      checkInMins: 60,
    },
    uniqueEventGroups: [],
    unregisteredAthletes: true,
  },
  eoptions: {
    min: 20,
    max: 60,
    helpText: "",
    registeredAthletes: true,
    unregistered: false,
    registered: true,
    isTeamEvent: false,
    wind: "E",
    excludeFromCntRule: false,
    unique: [],
    eventTeam: {
      min: 0,
      max: 0,
      mustBeIndivEntered: false,
      minTargetAgeGroupCount: 0,
      maxOtherAgeGroupCount: 0,
      teamPositionLabel: "",
      maxEventTeams: 0,
      currCount: 0,
      singleClub: false,
      teamNameFormat:
        "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
      teamSubstituteLabel: "athlete",
      showForm: false,
      formType: "DEFAULT",
      price: "",
    },
    rowOptions: {
      autoExpandHelpText: false,
      showPB: true,
      showPrice: false,
      showEntryCount: true,
    },
    maxInHeat: 8,
    heatInfo: {
      useLanes: "A",
    },
  },
  eventGroupId: 10368,
  entered: true,
  entryId: 185462,
  paid: 1,
  athleteid: 189520,
  entryInfo: {
    unpaidCount: 6,
    paidCount: 23,
    totalCount: 29,
    entryPosition: 5,
    entryCreated: "2023-08-22T20:46:54+01:00",
    entryPositionDate: "2023-08-22T20:46:54+01:00",
    maxAthletes: 0,
  },
  entrycnt: 23,
  compName: "DASH Sprint Fest",
  user: {
    userId: 35678,
    userName: "Jackjack12",
    userEmail: "<EMAIL>",
  },
  order: {
    orderId: 234772,
    productId: 234770,
    e4sLineValue: 185462,
    wcLineValue: 6,
    isRefund: false,
    refundValue: 6,
    isCredit: false,
    creditValue: 6,
    discountId: 0,
    dateOrdered: "2023-08-22",
  },
  teamBibNo: "",
  pbInfo: {
    pb: 30,
    pbText: 30,
  },
  clubId: 1666,
  clubName: "Tipton Harriers",
  firstName: "Jack",
  surName: "PARTRIDGE",
  uom: [
    {
      pattern: "s.SS",
      text: "seconds",
      short: "s",
      uomType: "T",
    },
  ],
} as any as IAthleteCompSchedRuleEvent;
