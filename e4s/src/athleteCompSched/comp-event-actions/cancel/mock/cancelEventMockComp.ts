import { ICompetitionSummaryPublic } from "../../../../competition/competition-models";
import { <PERSON><PERSON>thlet<PERSON> } from "../../../../athlete/athlete-models";
import { IUserApplication } from "../../../../config/config-app-models";

export const mockCancelEventMockComp: ICompetitionSummaryPublic = {
  ID: 518,
  active: true,
  IndoorOutdoor: "",
  link: "/wp-json/view/233076/1692047488",
  yearFactor: 0,
  areaid: 0,
  options: {
    isClubComp: false,
    sourceId: 0,
    anonymize: false,
    standards: [],
    allowExpiredRegistration: true,
    useTeamBibs: false,
    bacs: {
      enabled: false,
      msg: "",
    },
    scoreboard: {
      image: "/results/blank.jpg",
    },
    seqEventNo: false,
    disabled: false,
    stripeMandatory: false,
    resultsAvailable: false,
    homeInfo: "",
    shortCode: "dashsprint23",
    priority: {
      required: false,
      code: "",
      dateTime: "",
      message: "",
    },
    bibNos: 62,
    bibSort1: "created",
    bibSort2: "firstname",
    bibSort3: "dob",
    heatOrder: "s",
    stadium: "",
    adjustEventNo: 0,
    athleteType: "A",
    tickets: {
      enabled: false,
    },
    pf: {
      pfTargetDirectory: "",
      type: "",
    },
    contact: {
      email: "<EMAIL>",
      id: 76,
      tel: "",
      userName: "E4S Admin",
      visible: false,
    },
    cardInfo: {
      enabled: true,
      availableFrom: "",
      callRoom: false,
    },
    subscription: {
      enabled: false,
      timeCloses: "",
      organiserMessage: "",
      e4sMessage: "",
      refunded: "2023-08-14T17:19:30+01:00",
      process: true,
      processRefundTime: "",
    },
    card: {
      header: {
        field: "",
        track: "",
      },
      footer: {
        field: "",
        track: "",
      },
    },
    checkIn: {
      enabled: false,
      checkInDateTimeOpens: "",
      defaultFrom: 180,
      defaultTo: 60,
      qrCode: true,
      text: "Welcome to the Check-in service for our competition. To help us in the registration process, we would appreciate if you could let us know which events you are taking part in today. This will help reduce the number of athletes and the time you spend at the registration area picking up your bib number.\r\nWe have provided a number of methods to do this, please choose one of the methods below.",
      terms: "",
      useTerms: false,
      seedOnEntries: false,
    },
    school: false,
    orgFreeEntry: false,
    autoPayFreeEntry: false,
    cheques: {
      allow: false,
      ends: "",
    },
    allowAdd: {
      unregistered: true,
      registered: true,
    },
    timetable: "Provisional",
    helpText: {
      schedule: "",
      teams: "",
      cart: "",
    },
    showTeamAthletes: true,
    singleAge: false,
    showAthleteAgeInEntries: false,
    report: {
      summary: true,
      athletes: true,
      ttathletes: true,
      ttentries: true,
      individual_entries: true,
      teams: true,
      subscriptions: true,
      orders: true,
      events: true,
    },
    athleteSecurity: {
      areas: [],
      clubs: [],
      onlyClubsUpTo: "",
    },
    ui: {
      enterButtonText: "Enter",
      entryDefaultPanel: "SCHEDULE",
      ticketComp: 0,
      ticketCompButtonText: "Buy Tickets",
      sectionsToHide: {
        SCHEDULE: false,
        ATHLETES: false,
        SHOP: true,
        TEAMS: true,
      },
    },
    athleteQrData: false,
    nonGuests: [],
    disabledReason: "",
    paymentCode: "",
    laneCount: 8,
    compLimits: {
      athletes: 0,
      entries: 0,
      teams: 0,
    },
    stopReport: false,
    cancelEvent: {
      hrsBeforeClose: 48,
      refund: {
        allow: false,
        type: "E4S_FEES",
      },
      credit: {
        allow: true,
      },
    },
    autoEntries: {
      selectedTargetComp: {
        id: 0,
        name: "",
        timeTronicsUrl: "",
      },
      targetable: {
        allowedSources: [],
        enabled: false,
      },
    },
    level: 1,
    pfTargetDirectory: "",
    dates: ["2023-09-27"],
    clubComp: false,
    categoryId: 0,
  },
  teamid: 0,
  lastentrymod: "2023-09-19 04:32:24",
  information: "",
  r4s: 0,
  waitingrefunded: 1,
  areaname: "All",
  today: "2023-09-19",
  systemtime: "2023-09-19 04:33:04",
  compDate: "2023-09-27",
  daysToComp: 8,
  daysToClose: 7,
  location: {
    id: 129,
    name: "The Dell",
    address1: "Bryce Road",
    address2: "",
    town: "Brierley Hill ",
    postcode: "DY5 4NE",
    county: "",
    map: "https://www.google.com/maps?q=DY5 4NE",
    directions: "",
    website: "",
  },
  loccontact: "",
  logo: "/resources/default_logo.gif",
  ctcid: null,
  maxathletes: null,
  maxteams: null,
  maxmale: null,
  maxfemale: null,
  maxagegroup: null,
  uniqueevents: null,
  singleagegroup: null,
  ctc: {
    ctcid: null,
    maxathletes: null,
    maxteams: null,
    maxmale: null,
    maxfemale: null,
    maxagegroup: null,
    uniqueevents: null,
    singleagegroup: null,
  },
  compOrgId: 121,
  compName: "DASH Sprint Fest",
  entityid: null,
  opendate: "2023-08-14T08:00:00+01:00",
  closedate: "2023-09-26T23:59:00+01:00",
  club: "Dudley and Stourbridge",
  dates: ["2023-09-27"],
  saleenddate: null,
  entries: {
    eventCount: 28,
    teamEventCount: 0,
    indivEventCount: 28,
    indiv: 52,
    waitingCount: 0,
    uniqueIndivAthletes: 37,
    athletes: 37,
  },
  access: "",
  permissions: {
    adminMenu: true,
    builder: true,
    check: true,
    report: true,
  },
  reportId: "",
  reportAccess: true,
  status: {
    id: 0,
    compid: 518,
    description: "No Status",
    status: "NO_STATUS",
    invoicelink: "",
    reference: "",
    notes: "",
    value: 0,
    code: 9010,
    wfid: 0,
    previd: 0,
    prevdescription: "",
    nextdescription: "",
    nextid: 0,
  },
  organisers: [
    {
      permId: 214,
      orgId: 121,
      compId: 0,
      user: {
        id: 35551,
        userEmail: "<EMAIL>",
        displayName: "Rhe Hinds-Harris",
      },
      role: {
        id: 6,
        name: "admin",
      },
    },
  ],
  compId: 518,
  e4sNotes: "",
  newsFlash: "",
  termsConditions: "",
  emailText: "",
  compRules: [],
  eventTypes: {
    indivEvents: true,
    teamEvents: false,
    tickets: false,
  },
  clubCompInfo: {},
} as any as ICompetitionSummaryPublic;

export const cancelEventMockAthlete: IAthlete = {
  id: 189520,
  firstName: "Jack",
  surName: "PARTRIDGE",
  aocode: "EA",
  URN: 4092861,
  pof10id: null,
  dob: "2012-08-02",
  gender: "M",
  classification: 0,
  email: "<EMAIL>",
  type: "A",
  options: {
    urnChecked: "JackPartridge2012-08-02",
    noEntryReason: "",
    emergency: {
      name: "Jon partridge ",
      tel: 7731426641,
      relationship: "",
    },
  },
  activeEndDate: "2023-09-27",
  lastChecked: "2023-09-17 18:33:26",
  image:
    "https://www.thepowerof10.info/athletes/profilepic.aspx?athleteid=unknown",
  modified: "2023-09-17 18:33:26",
  club: "Tipton Harriers",
  clubId: 1666,
  club2Id: 0,
  schoolId: 0,
  club2: "",
  school: "",
  events: [
    {
      compid: 518,
      compname: "DASH Sprint Fest",
      compdate: "2023-09-27T00:00:00+01:00",
      eventid: 1,
      eventname: "75m",
      eventtime: "2023-09-27T18:30:00+01:00",
      orderno: 234772,
      price: 6,
      paid: 1,
      entryid: 185461,
      userid: 35678,
      teamBibNo: "",
      options: {
        allowAreas: false,
        otd: false,
        forceBasket: false,
        waitingInfo: {
          originalPos: 0,
          wentOn: "",
          cameOff: "",
        },
        resultsKey: "",
        trackPB: false,
        checkIn: {
          from: null,
          to: null,
        },
        autoEntries: {
          targetEntry: {
            id: 0,
            paid: 0,
            orderId: 0,
          },
          targetEventGroup: {
            id: 0,
            name: "",
          },
        },
        lastStatus: "completed",
      },
      username: "jackjack12",
      email: "<EMAIL>",
    },
    {
      compid: 518,
      compname: "DASH Sprint Fest",
      compdate: "2023-09-27T00:00:00+01:00",
      eventid: 6,
      eventname: "200m",
      eventtime: "2023-09-27T19:45:00+01:00",
      orderno: 234772,
      price: 6,
      paid: 1,
      entryid: 185462,
      userid: 35678,
      teamBibNo: "",
      options: {
        allowAreas: false,
        otd: false,
        forceBasket: false,
        waitingInfo: {
          originalPos: 0,
          wentOn: "",
          cameOff: "",
        },
        resultsKey: "",
        trackPB: false,
        checkIn: {
          from: null,
          to: null,
        },
        autoEntries: {
          targetEntry: {
            id: 0,
            paid: 0,
            orderId: 0,
          },
          targetEventGroup: {
            id: 0,
            name: "",
          },
        },
        lastStatus: "completed",
      },
      username: "jackjack12",
      email: "<EMAIL>",
    },
  ],
  userAthletes: [
    {
      id: 35678,
      email: "<EMAIL>",
      userName: "jackjack12",
    },
  ],
  pbInfo: [
    {
      eventid: 6,
      pb: 36,
      pbid: 107752,
      pbText: 36,
      pof10pb: 0,
      sb: 0,
      trackSb: false,
      eventName: "200m",
      options: {
        maxInHeat: 8,
        min: 20,
        max: 60,
        wind: "E",
      },
      min: 20,
      max: 60,
      uomInfo: {
        id: 1,
        type: "T",
        options: [
          {
            pattern: "s.SS",
            text: "seconds",
            short: "s",
          },
        ],
      },
    },
    {
      eventid: 1,
      pb: 11.2,
      pbid: 107751,
      pbText: 11.2,
      pof10pb: 0,
      sb: 0,
      trackSb: false,
      eventName: "75m",
      options: {
        maxInHeat: 8,
        min: 6.6,
        max: 20,
      },
      min: 6.6,
      max: 20,
      uomInfo: {
        id: 1,
        type: "T",
        options: [
          {
            pattern: "s.SS",
            text: "seconds",
            short: "s",
          },
        ],
      },
    },
    {
      eventid: 25,
      pb: 1.23,
      pbid: 107730,
      pbText: 1.23,
      pof10pb: 0,
      sb: 0,
      trackSb: false,
      eventName: "High Jump",
      options: {
        min: 0.05,
        max: 2.5,
        cardType: "H",
      },
      min: 0,
      max: 0,
      uomInfo: {
        id: 13,
        type: "H",
        options: [
          {
            pattern: 0.99,
            text: "metres",
            short: "mt",
          },
        ],
      },
    },
    {
      eventid: 23,
      pb: 4.16,
      pbid: 107731,
      pbText: 4.16,
      pof10pb: 0,
      sb: 0,
      trackSb: false,
      eventName: "Long Jump",
      options: {
        min: 1,
        max: 10,
        wind: "A",
        unique: [
          {
            e: 52,
          },
        ],
      },
      min: 1,
      max: 10,
      uomInfo: {
        id: 12,
        type: "D",
        options: [
          {
            pattern: 0.99,
            text: "metres",
            short: "mt",
          },
        ],
      },
    },
  ],
  clubInfo: {
    "1666": [
      {
        id: 2745,
        userName: "bryanm",
        displayName: "bryanm",
        email: "<EMAIL>",
      },
    ],
  },
} as any as IAthlete;

export const mockUserApplication: IUserApplication = {
  id: 1,
  user_login: "E4SAdmin",
  user_nicename: "e4sadmin",
  user_email: "<EMAIL>",
  display_name: "E4S Admin",
  role: "E4SUSER",
  google_email: "",
  impersonating: false,
  e4sCredit: [1701.25],
  permissions: [
    {
      id: 5,
      userid: 1,
      role: {
        id: 0,
        name: "All",
      },
      comp: {
        id: 0,
        name: "",
      },
      org: {
        id: 0,
        name: "All",
      },
      permLevels: [],
    },
  ],
  version: {
    current: "v1",
    toggle: false,
  },
} as any as IUserApplication;
