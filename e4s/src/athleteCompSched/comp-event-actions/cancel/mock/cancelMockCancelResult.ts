import { ICompEventActionsResult } from "../../comp-event-actions-controller";
import { mockCancelEvent200m } from "./cancelEventMock200m";

import {
  cancelEventMockAthlete,
  mockCancelEventMockComp,
  mockUserApplication,
} from "./cancelEventMockComp";

export const mockCompEventActionsResult: ICompEventActionsResult = {
  competitionSummaryPublic: mockCancelEventMockComp,
  compEvent: mockCancelEvent200m,
  athlete: cancelEventMockAthlete,
  compEvents: [mockCancelEvent200m],
  compRule: null,
  userApplication: mockUserApplication,
};
