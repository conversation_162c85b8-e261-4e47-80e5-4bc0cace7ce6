<template>
    <div>

        <div class="row">

            <div class="col s12 m12 l12">


                <div class="comp-event-actions--action-section">

                    <div class="row">
                        <div class="col s12 m12 l12">
                            <div class="e4s-form-sub-header left">
                                <i class="material-icons e4s-force-img-vert-align-middle e4s-force-img--pad-right">highlight_off</i>Cancel Event
                            </div>
                        </div>
                    </div>

                    <div class="e4s-section-padding-separator"></div>

                    <div class="row">
                        <div class="col s12 m12 l12">
                            <span class="active">
                                Cancellation reason.
                            </span>
                            <TextareaExpand v-model="refundEvent.reason"></TextareaExpand>
                        </div>
                    </div>

                </div>

            </div>

<!--        </div>-->


<!--        <div class="comp-event-actions&#45;&#45;action-section">-->
            <div class="row">
                <div class="col s12 m12 l12">
                    <label v-if="configApp.theme === 'gbr'">
                        <input type="radio"
                               id="comp-event-cancel"
                               class="browser-default"
                               value="Refund"
                               v-model="cancelType"
                               v-on:change="cancelTypeChanged">
                        <span class="comp-event-actions--credit-cancel-radio-label e4s-form-sub-header">
                            Refund
                        </span>
                    </label>

                    <label>
                        <input type="radio"
                               id="comp-event-credit"
                               class="browser-default"
                               value="Credit"
                               v-model="cancelType"
                               v-on:change="cancelTypeChanged">
                        <span class="comp-event-actions--credit-cancel-radio-label e4s-form-sub-header">
                            Credit
                        </span>
                    </label>

                    <label>
                        <input type="radio"
                               id="comp-event-remove-only"
                               class="browser-default"
                               value="Remove Only"
                               v-model="cancelType"
                               v-on:change="cancelTypeChanged">
                        <span class="comp-event-actions--credit-cancel-radio-label e4s-form-sub-header">
                            Remove Only
                        </span>
                    </label>
                </div>
            </div>

            <div class="row" v-if="cancelType!=='Remove Only'">

                <div class="col s6 m6 l6">
                    <label>
                        <input id="refundEvent--test"
                               class="e4s-checkbox"
                               type="checkbox"
                               v-model="refundEvent.test" />
                        <span class="e4s-form-sub-header">Test Mode: <span v-text="refundEvent.test ? 'ON' : 'OFF'"></span></span>
                    </label>
                </div>

                <div class="col s6 m6 l6">
                    <label>
                        <input id="refundEvent--remove-entry"
                               class="e4s-checkbox"
                               type="checkbox"
                               v-model="refundEvent.removeEntry" />
                        <span class="e4s-form-sub-header">Remove Entry</span>
                    </label>
                </div>

                <div class="col s6 m6 l6">
                    <label>
                        <input id="refundEvent--remove-email"
                               class="e4s-checkbox"
                               type="checkbox"
                               v-model="refundEvent.email" />
                        <span class="e4s-form-sub-header">Email</span>
                    </label>

                </div>

                <div class="col s6 m6 l6" v-if="cancelType === 'Refund'">
                    <label>
                        <input id="refundEvent--refund-stripe-fee"
                               class="e4s-checkbox"
                               type="checkbox"
                               v-model="refundEvent.refundStripeFee" />
                        <span class="e4s-form-sub-header">Refund Stripe Fee</span>
                    </label>
                </div>

                <div class="col s6 m6 l6" v-if="cancelType === 'Refund'">
                    <label>
                        <input id="refundEvent--refund-e4s-fee"
                               class="e4s-checkbox"
                               type="checkbox"
                               v-model="refundEvent.refundE4SFee" />
                        <span class="e4s-form-sub-header">Refund E4S Fee</span>
                    </label>
                </div>

                <div class="col s12 m6 l6">
                    <span class="e4s-form-sub-header">Value:</span>
                    <input id="refundEvent--value"
                           class="e4s-input e4s-input-auto-width"
                           type="number"
                           v-model.number="refundEvent.value" />
                </div>

            </div>

        </div>



        <div class="e4s-section-padding-separator"></div>

        <div class="row">
            <div class="col s12 m12 l12">

                <div slot="buttons">
                    <div class="left">
                        <button
                            class="e4s-button e4s-button--green e4s-button--10-wide"
                            :disabled="getIsDisableButtons"
                            v-on:click="proceed"
                            v-if="askConf"
                        >
                            Confirm
                        </button>
                    </div>


                    <div class="right">
                        <LoadingSpinner v-if="isLoading"></LoadingSpinner>
                        <button class="e4s-button e4s-button--red e4s-button--10-wide"
                                v-on:click.stop="cancel">
                            <span>Back</span>
                        </button>
                        <button
                            class="e4s-button e4s-button--green e4s-button--10-wide"
                            :disabled="getIsDisableButtons"
                            v-on:click="askForConf"
                            v-if="!askConf"
                        >
                            Proceed
                        </button>
                    </div>
                </div>

            </div>
        </div>

        <div class="e4s-section-padding-separator"></div>

    </div>

</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import {ICompEventActionsResult} from "../comp-event-actions-controller"

import {CommonService} from "../../../common/common-service"
import {mapState} from "vuex"
import {CONFIG_STORE_CONST, IConfigStoreState} from "../../../config/config-store"
import {IConfigApp} from "../../../config/config-app-models"
import {PaymentsData} from "../../../admin/payments/payments.data"
import {handleResponseMessages} from "../../../common/handle-http-reponse"
import {IRefundEvent} from "../../../admin/payments/payments.models"
import TextareaExpand from "../../../common/ui/field/textarea/textarea-expand.vue"
import {AthleteCompSchedData} from "../../athletecompsched-data"

@Component({
    name: "comp-event-action-cred-ref-admin",
    components: {TextareaExpand},
    computed: {
        ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
            configApp: (state: any) => state.configApp
        })
    }
})
export default class CompEventActionCredRefAdmin extends Vue {
    public readonly configApp!: IConfigApp;

    @Prop({
        required: true
    })
    public readonly compEventActionsResult!: ICompEventActionsResult;

    public commonService: CommonService = new CommonService();

    public cancelType: "Credit" | "Refund" | "Remove Only" | "" = "";
    // public reason = "";
    // public testMode = true;
    public askConf = false;
    public isLoading = false;
    public removeOnly =  false

    public refundEvent: IRefundEvent = {
        compid: this.compEventActionsResult.competitionSummaryPublic.compId,
        orderid: this.compEventActionsResult.compEvent.order.orderId,
        reason: "",
        productid: this.compEventActionsResult.compEvent.order.productId,
        test: false,
        removeEntry: true,
        email: true,
        value: 0,
        credit: true,
        price: 0,
        refundStripeFee: false,
        refundE4SFee: false,
        text: ""
    };

    public get getRefundValue(): string {
        return this.commonService.getAmountAsCurrency(
            this.compEventActionsResult.compEvent.order.refundValue,
            this.configApp.currency
        );
    }

    public get getCreditValue(): string {
        return this.commonService.getAmountAsCurrency(
            this.compEventActionsResult.compEvent.order.creditValue,
            this.configApp.currency
        );
    }

    public get getIsDisableButtons(): boolean {
        return this.isLoading;
    }

    public proceed() {

        if (this.cancelType === "Remove Only") {
            this.onlyRemoveUserFromEvent();
            return;
        }

        this.isLoading = true;
        this.refundEvent.credit = this.cancelType === "Credit";
        const prom = new PaymentsData().refundEvent(this.refundEvent);
        const successMessage = (this.refundEvent.credit ? "Credit" : "Refund" ) + " processed.";
        handleResponseMessages(prom, successMessage);
        prom
        .then( (response) => {
            if (response.errNo === 0) {
                this.$emit("submitted");
            }
        })
        .finally( () => {
            this.isLoading = false;
            this.askConf = false;
        })
    }

    public onlyRemoveUserFromEvent() {
        this.isLoading = true;
        const prom = new AthleteCompSchedData().cancelEvent(this.compEventActionsResult.compEvent.entryId, this.refundEvent.reason);
        const successMessage = "Cancel processed.";
        handleResponseMessages(prom, successMessage);
        prom
            .then((response) => {
                if (response.errNo === 0) {
                    // this.$emit("close");
                    this.$emit("submitted");
                }
            })
            .finally(() => {
                this.isLoading = false;
                this.askConf = false;
            })
    }

    public askForConf() {
        this.askConf = true;
    }

    public cancelTypeChanged() {
        this.askConf = false;

        if (this.cancelType === "Credit") {
            this.refundEvent.refundE4SFee = false;
            this.refundEvent.refundStripeFee = false;
        }

    }

    public cancel() {
        this.$emit("close");
    }
}
</script>

<style>

</style>
