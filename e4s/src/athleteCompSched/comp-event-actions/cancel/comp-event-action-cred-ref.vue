<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <div class="e4s-form-sub-header left">
          <i
            class="
              material-icons
              e4s-force-img-vert-align-middle
              e4s-force-img--pad-right
            "
            >highlight_off</i
          >Cancel Event
        </div>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        <span class="active">
          Please let us know why you are having to cancel.
        </span>
        <TextareaExpand v-model="reason"></TextareaExpand>
      </div>
    </div>

    <div class="row">
      <div class="col s12 m6 l6">
        <div class="e4s-section--padding">
          <div class="row">
            <div class="col s12 m12 l12">
              <div class="left">
                <i
                  class="
                    material-icons
                    e4s-force-img-vert-align-middle
                    e4s-force-img--pad-right
                  "
                >
                  call_split </i
                ><span class="e4s-form-sub-header">Refund Event</span>
              </div>

              <div class="right">
                <span
                  class="e4s-form-sub-header"
                  v-text="
                    commonService.getAmountAsCurrency(
                      compEventActionsResult.compEvent.order.refundValue,
                      configApp.currency
                    )
                  "
                ></span>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col s12 m12 l12">
              <span v-text="getRefundMessage"></span>
            </div>
          </div>
        </div>
      </div>

      <div class="col s12 m6 l6">
        <div class="e4s-section--padding">
          <div class="row">
            <div class="col s12 m12 l12">
              <div class="left">
                <i
                  class="
                    material-icons
                    e4s-force-img-vert-align-middle
                    e4s-force-img--pad-right
                  "
                >
                  card_giftcard
                </i>
                <span class="e4s-form-sub-header">Credit Event</span>
              </div>
              <div class="right">
                <span
                  class="e4s-form-sub-header"
                  v-text="
                    commonService.getAmountAsCurrency(
                      compEventActionsResult.compEvent.order.creditValue,
                      configApp.currency
                    )
                  "
                ></span>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col s12 m12 l12">
              <div>
                Credit the total amount paid to user's E4S account. This can be
                redeemed against any other competition entries.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="comp-event-actions--action-section">
      <div class="row">
        <div class="col s12 m12 l12">
          <span class="e4s-form-sub-header">Select Option:</span>
          <div class="right">
            <label v-if="configApp.theme === 'gbr'">
              <input
                type="radio"
                id="comp-event-cancel"
                class="browser-default"
                value="Refund"
                v-model="cancelType"
                v-on:change="cancelTypeChanged"
              />
              <span
                class="
                  comp-event-actions--credit-cancel-radio-label
                  e4s-form-sub-header
                "
              >
                Refund <span v-text="getRefundValue"></span>
              </span>
            </label>

            <label>
              <input
                type="radio"
                id="comp-event-credit"
                class="browser-default"
                value="Credit"
                v-model="cancelType"
                v-on:change="cancelTypeChanged"
              />
              <span
                class="
                  comp-event-actions--credit-cancel-radio-label
                  e4s-form-sub-header
                "
              >
                Credit <span v-text="getCreditValue"></span>
              </span>
            </label>

            <label>
              <input
                type="radio"
                id="comp-event-remove-only"
                class="browser-default"
                value="Remove Only"
                v-model="cancelType"
                v-on:change="cancelTypeChanged"
              />
              <span
                class="
                  comp-event-actions--credit-cancel-radio-label
                  e4s-form-sub-header
                "
              >
                Remove Only
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        <div slot="buttons">
          <div class="left">
            <button
              class="e4s-button e4s-button--green e4s-button--10-wide"
              :disabled="getIsDisableButtons"
              v-on:click="proceed"
              v-if="askConf"
            >
              Confirm
            </button>
          </div>

          <div class="right">
            <LoadingSpinner v-if="isLoading"></LoadingSpinner>
            <button
              class="e4s-button e4s-button--red e4s-button--10-wide"
              v-on:click.stop="cancel"
            >
              <span>Back</span>
            </button>
            <button
              class="e4s-button e4s-button--green e4s-button--10-wide"
              :disabled="getIsDisableButtons"
              v-on:click="askForConf"
              v-if="!askConf"
            >
              Proceed
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>
  </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { ICompEventActionsResult } from "../comp-event-actions-controller";

import { CommonService } from "../../../common/common-service";
import { mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import { IConfigApp } from "../../../config/config-app-models";
import { PaymentsData } from "../../../admin/payments/payments.data";
import { handleResponseMessages } from "../../../common/handle-http-reponse";
import { IRefundEvent } from "../../../admin/payments/payments.models";
import TextareaExpand from "../../../common/ui/field/textarea/textarea-expand.vue";
import { AthleteCompSchedData } from "../../athletecompsched-data";
import { ConfigService } from "../../../config/config-service";

@Component({
  name: "comp-event-action-cred-ref",
  components: { TextareaExpand },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
    }),
  },
})
export default class CompEventActionCredRef extends Vue {
  public readonly configApp!: IConfigApp;

  @Prop({
    required: true,
  })
  public readonly compEventActionsResult!: ICompEventActionsResult;

  public commonService: CommonService = new CommonService();
  public configService: ConfigService = new ConfigService();

  public cancelType: "Credit" | "Refund" | "Remove Only" | "" = "";
  public reason = "";
  public testMode = false;
  public askConf = false;
  public isLoading = false;

  public get getHasBuilderPermissionForComp(): boolean {
    return this.configService.hasBuilderPermissionForComp(
      this.configApp.userInfo,
      this.compEventActionsResult.competitionSummaryPublic.compOrgId,
      this.compEventActionsResult.competitionSummaryPublic.compId
    );
  }

  public get getRefundValue(): string {
    return this.commonService.getAmountAsCurrency(
      this.compEventActionsResult.compEvent.order.refundValue,
      this.configApp.currency
    );
  }

  public get getCreditValue(): string {
    return this.commonService.getAmountAsCurrency(
      this.compEventActionsResult.compEvent.order.creditValue,
      this.configApp.currency
    );
  }

  public get getIsDisableButtons(): boolean {
    return this.isLoading || this.cancelType === "";
  }

  public get getRefundMessage() {
    if (
      this.compEventActionsResult.competitionSummaryPublic.options.cancelEvent
        .refund.type === "E4S_FEES"
    ) {
      return "This will refund the purchase value minus transaction costs.";
    }
    let message = "This will refund the full purchase value.";
    if (this.getHasBuilderPermissionForComp) {
      message += " (Fees will be covered by organiser)";
    }
    return message;
  }

  public proceed() {
    if (this.cancelType === "Remove Only") {
      this.onlyRemoveUserFromEvent();
      return;
    }

    const isCredit = this.cancelType === "Credit";
    const refundEvent: IRefundEvent = {
      compid: this.compEventActionsResult.competitionSummaryPublic.compId,
      orderid: this.compEventActionsResult.compEvent.order.orderId,
      reason: this.reason,
      productid: this.compEventActionsResult.compEvent.order.productId,
      test: this.testMode,
      removeEntry: true,
      email: !this.testMode,
      value: isCredit
        ? this.compEventActionsResult.compEvent.order.creditValue
        : this.compEventActionsResult.compEvent.order.refundValue,
      credit: isCredit,
      price: 0,
      refundStripeFee: false,
      refundE4SFee: false,
      text: "",
    };
    this.isLoading = true;
    const prom = new PaymentsData().refundEvent(refundEvent);
    const successMessage = this.cancelType + " processed.";
    handleResponseMessages(prom, successMessage);
    prom
      .then((response) => {
        if (response.errNo === 0) {
          this.$emit("submitted");
        }
      })
      .finally(() => {
        this.isLoading = false;
        this.askConf = false;
      });
  }

  public onlyRemoveUserFromEvent() {
    this.isLoading = true;
    const prom = new AthleteCompSchedData().cancelEvent(
      this.compEventActionsResult.compEvent.entryId,
      this.reason
    );
    const successMessage = "Cancel processed.";
    handleResponseMessages(prom, successMessage);
    prom
      .then((response) => {
        if (response.errNo === 0) {
          this.$emit("submitted");
        }
      })
      .finally(() => {
        this.isLoading = false;
        this.askConf = false;
      });
  }

  public askForConf() {
    this.askConf = true;
  }

  public cancelTypeChanged() {
    this.askConf = false;
  }

  public cancel() {
    this.$emit("close");
  }
}
</script>

<style></style>
