<template>
    <div>

        <div class="row">
            <div class="col s12 m12 l12">
                <div class="e4s-form-sub-header left">
                    <i class="material-icons e4s-force-img-vert-align-middle e4s-force-img--pad-right">local_atm</i>
                    Toggle Paid (Currently set to <span v-text="compEventActionsResult.compEvent.paid ? 'PAID' : 'NOT PAID'"></span>)
                </div>
            </div>
        </div>

        <div class="e4s-section-padding-separator"></div>

        <div class="row">
            <div class="col s12 m12 l12">
                <span v-text="getToggleSubmitDialogMessage"></span>
            </div>
            <div class="col s12 m12 l12">
                <label class="active"
                       for="reason">
                    Reason (Required)
                </label>
                <input
                    id="reason"
                    name="reason"
                    class="e4s-input"
                    v-model="paidMessage"
                    placeholder="">
            </div>
        </div>

        <ButtonsCancelOkConfirm
            :is-loading="isLoading"
            v-on:cancel="close"
            v-on:ok="setPaid"
        ></ButtonsCancelOkConfirm>
    </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import {ICompEventActionsResult} from "../comp-event-actions-controller"
import ButtonsCancelOkConfirm from "../../../common/ui/buttons/buttons-cancel-ok-confirm.vue"
import {IServerGenericResponse} from "../../../common/common-models"
import {AthleteCompSchedData} from "../../athletecompsched-data"
import {handleResponseMessages} from "../../../common/handle-http-reponse"
import {messageDispatchHelper} from "../../../user-message/user-message-store"
import {USER_MESSAGE_LEVEL} from "../../../user-message/user-message-models"


@Component({
    name: "comp-event-action-set-paid",
    components: {ButtonsCancelOkConfirm}
})
export default class CompEventActionSetPaid extends Vue {
    @Prop({
        required: true
    })
    public readonly compEventActionsResult!: ICompEventActionsResult;

    public paidMessage: string  = "";
    public isLoading = false;

    public get getToggleSubmitDialogMessage(): string {
        return "Please enter a reason why toggling paid status of: " + this.compEventActionsResult.compEvent.Name;
    }

    public setPaid() {

        this.isLoading = true;
        const setPaidTo = this.compEventActionsResult.compEvent.paid ? 0 : 1;
        const prom = new AthleteCompSchedData().setPaid(
            [this.compEventActionsResult.compEvent.order.productId],
            setPaidTo,
            this.paidMessage
        );
        handleResponseMessages(prom);
        prom
            .then((resp: IServerGenericResponse) => {
                if (resp.errNo === 0) {
                    messageDispatchHelper("Saved.", USER_MESSAGE_LEVEL.INFO.toString());
                    this.$emit("submitted");
                }
            })
            .finally(() => {
                this.isLoading = false;
            });
    }

    public close() {
        this.$emit("close");
    }
}
</script>
