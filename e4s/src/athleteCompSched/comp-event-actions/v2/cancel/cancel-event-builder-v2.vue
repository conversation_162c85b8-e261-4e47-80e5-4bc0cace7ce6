<template>
  <div>
    <FormGenericFieldGridV2>
      <template slot="content">
        <div></div>
      </template>
    </FormGenericFieldGridV2>

    <p>Please enter a reason why cancelling (required).</p>
    <InputRestrictLength
      slot="content"
      :use-text-area="true"
      :max-length="600"
      :text-area-class="
        'browser-default e4s-input-field e4s-input-field--primary ask-organiser--text-area' +
        (errorMessages.reason.length > 0 ? ' e4s-input-field--error' : '')
      "
      v-on:isMaxLength="isMaxLength = $event"
      v-on:onChanged="state.reason = $event"
    >
    </InputRestrictLength>
    <FormGenericInputErrorMessageV2 :error-message="errorMessages.reason" />

    <InfoSectionV2 info-type="info">
      <FormGenericSectionTitleV2
        slot="default"
        title-size="500"
        section-title="Refund Event"
        :section-overview="getRefundMessage"
      />
    </InfoSectionV2>

    <div class="e4s-vertical-spacer--large"></div>

    <InfoSectionV2 info-type="info">
      <FormGenericSectionTitleV2
        slot="default"
        title-size="500"
        section-title="Credit Event"
        section-overview="Credit the total amount paid to user's E4S account. This can be redeemed against any other competition entries."
      />
    </InfoSectionV2>

    <div class="e4s-vertical-spacer--large"></div>

    <div class="e4s-flex-column">
      <div
        class="
          e4s-flex-row e4s-flex-nowrap
          e4s-input--container
          e4s-justify-flex-space-around
        "
      >
        <label>
          <input
            type="radio"
            class="browser-default e4s-input-field e4s-input-field--primary"
            value="Refund"
            v-model="state.cancelType"
          />
          <span v-text="'Refund ' + getRefundValue"></span>
        </label>
        <label>
          <input
            type="radio"
            class="browser-default e4s-input-field e4s-input-field--primary"
            value="Credit"
            v-model="state.cancelType"
          />
          <span v-text="'Credit ' + getCreditValue"></span>
        </label>
        <label>
          <input
            type="radio"
            class="browser-default e4s-input-field e4s-input-field--primary"
            value="Remove Only"
            v-model="state.cancelType"
          />
          <span>Remove Only</span>
        </label>
      </div>

      <FormGenericInputErrorMessageV2
        :error-message="errorMessages.cancelType"
      />
    </div>

    <div class="e4s-vertical-spacer--large"></div>

    <ButtonGroupSectionV2>
      <ButtonGenericV2
        slot="buttons-left"
        button-type="tertiary"
        v-on:click="cancel"
        text="Cancel"
      />

      <div slot="buttons-right">
        <ButtonGenericV2
          button-type="primary"
          v-on:click="askForConfirmation"
          text="Submit"
          v-if="!state.showConfirm"
        />
        <ButtonGenericV2
          button-type="primary"
          class="e4s-button--destructive"
          v-on:click="submit"
          text="Confirm"
          v-if="state.showConfirm"
        />
      </div>
    </ButtonGroupSectionV2>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  SetupContext,
} from "vue";
import InputRestrictLength from "../../../../common/ui/field/input-restrict-length/input-restrict-length.vue";
import FormGenericInputErrorMessageV2 from "../../../../common/ui/layoutV2/form/form-generic-input-error-message-v2.vue";
import FormGenericSectionTitleV2 from "../../../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import {
  ICancelEventBuilderOutputPayload,
  ICancelEventBuilderState,
  ICancelEventUserInputParams,
} from "./cancel-event-models-v2";
import FormGenericFieldGridV2 from "../../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import InfoSectionV2 from "../../../../common/ui/layoutV2/info-section-v2.vue";
import { getAmountAsCurrency } from "../../../../common/common-service-utils";
import { useConfigStore } from "../../../../config/useConfigStore";
import ButtonGroupSectionV2 from "../../../../common/ui/layoutV2/buttons/button-group-section-v2.vue";

export default defineComponent({
  name: "cancel-event-builder-v2",
  components: {
    ButtonGroupSectionV2,
    InfoSectionV2,
    ButtonGenericV2,
    FormGenericFieldGridV2,
    FormGenericSectionTitleV2,
    FormGenericInputErrorMessageV2,
    InputRestrictLength,
  },
  props: {
    cancelEventInputParams: {
      type: Object as PropType<ICancelEventUserInputParams>,
      required: true,
    },
  },
  setup(
    props: { cancelEventInputParams: ICancelEventUserInputParams },
    context: SetupContext
  ) {
    const configStore = useConfigStore();

    const errorMessages = reactive({
      reason: "",
      cancelType: "",
    });

    const state = reactive<ICancelEventBuilderState>({
      showConfirm: false,
      reason: "",
      cancelType: "",
    });

    function doValidation(): boolean {
      errorMessages.reason = "";
      errorMessages.cancelType = "";

      let isValid = true;

      if (state.reason.length === 0) {
        errorMessages.reason = "Please enter a reason.";
        isValid = false;
      }

      if (state.cancelType.length === 0) {
        errorMessages.cancelType = "Please select a type.";
        isValid = false;
      }
      return isValid;
    }

    function askForConfirmation() {
      if (!doValidation()) {
        return;
      }

      state.showConfirm = true;
    }

    const getRefundValue = computed(() => {
      return getAmountAsCurrency(
        props.cancelEventInputParams.compEvent.order.refundValue,
        configStore.configApp.currency
      );
    });

    const getRefundMessage = computed(() => {
      if (
        props.cancelEventInputParams.competitionSummaryPublic.options
          .cancelEvent.refund.type === "E4S_FEES"
      ) {
        return "This will refund the purchase value minus transaction costs.";
      }
      let message = "This will refund the full purchase value.";
      if (props.cancelEventInputParams.hasBuilderPermissionForComp) {
        message += " (Fees will be covered by organiser)";
      }
      return message;
    });

    const getCreditValue = computed(() => {
      return getAmountAsCurrency(
        props.cancelEventInputParams.compEvent.order.creditValue,
        configStore.configApp.currency
      );
    });

    function cancel() {
      context.emit("cancel");
    }

    function submit() {
      if (!doValidation()) {
        return;
      }

      const cancelEventBuilderOutputPayload: ICancelEventBuilderOutputPayload =
        {
          compEvent: props.cancelEventInputParams.compEvent,
          reason: state.reason,
          cancelType: state.cancelType,
        };

      context.emit("submit", cancelEventBuilderOutputPayload);
    }

    return {
      state,
      errorMessages,
      askForConfirmation,
      cancel,
      submit,
      getRefundValue,
      getCreditValue,
      getRefundMessage,
    };
  },
});
</script>
