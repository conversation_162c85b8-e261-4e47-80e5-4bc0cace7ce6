import {IAthleteCompSchedRuleEvent} from "../../../athletecompsched-models"
import {ICompetitionSummaryPublic} from "../../../../competition/competition-models"

export interface ICancelEventState {
  isLoading: boolean;
}

export interface ICancelEventUserInputParams {
  isAdmin: boolean;
  hasBuilderPermissionForComp: boolean;
  compEvent: IAthleteCompSchedRuleEvent;
  competitionSummaryPublic: ICompetitionSummaryPublic;
}

export interface ICancelEventUserState {
  showConfirm: boolean;
  reason: string;
}

export interface ICancelEventUserOutputPayload {
  compEvent: IAthleteCompSchedRuleEvent;
  reason: string;
}


export interface ICancelEventBuilderState {
  showConfirm: boolean;
  reason: string;
  cancelType: "Credit" | "Refund" | "Remove Only" | "";
}

export interface ICancelEventBuilderOutputPayload {
  compEvent: IAthleteCompSchedRuleEvent;
  reason: string;
  cancelType: "Credit" | "Refund" | "Remove Only" | "";
}
