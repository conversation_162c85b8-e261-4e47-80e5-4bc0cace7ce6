<template>
  <CardGenericV2 class="e4s-card--well e4s-gap--standard">
    <template slot="all">
      <FormGenericSectionTitleV2
        title-size="400"
        :section-title="eventTitle"
        section-overview="Cancel Event"
        :show-cancel-button="false"
        v-on:cancel="cancel"
      />

      <CancelEventUserV2
        v-if="!useAdminCancel"
        :cancel-event-input-params="cancelEventInputParams"
        v-on:submit="doCancelEvent"
        v-on:cancel="cancel"
      />

      <CancelEventBuilderV2
        v-if="useAdminCancel"
        :cancel-event-input-params="cancelEventInputParams"
        v-on:submit="doCreditRefund"
        v-on:cancel="cancel"
      />

      <LoadingSpinnerV2 v-if="state.isLoading"/>
    </template>
  </CardGenericV2>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  SetupContext,
} from "vue";
import {
  ICancelEventUserInputParams,
  ICancelEventUserOutputPayload,
  ICancelEventState,
  ICancelEventBuilderOutputPayload,
} from "./cancel-event-models-v2";
import CancelEventUserV2 from "./cancel-event-user-v2.vue";
import FormGenericSectionTitleV2 from "../../../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import { AthleteCompSchedData } from "../../../athletecompsched-data";
import { messageDispatchHelper } from "../../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../../user-message/user-message-models";
import CancelEventBuilderV2 from "./cancel-event-builder-v2.vue";
import { IRefundEvent } from "../../../../admin/payments/payments.models";
import { PaymentsData } from "../../../../admin/payments/payments.data";
import { handleResponseMessages } from "../../../../common/handle-http-reponse";
import CompEventActionCredRefAdmin from "../../cancel/comp-event-action-cred-ref-admin.vue";
import CardGenericV2 from "../../../../common/ui/layoutV2/card-generic-v2.vue"
import LoadingSpinnerV2 from "../../../../common/ui/loading-spinner-v2.vue"

export default defineComponent({
  name: "cancel-event-v2",
  components: {
    LoadingSpinnerV2,
    CardGenericV2,
    CompEventActionCredRefAdmin,
    CancelEventBuilderV2,
    FormGenericSectionTitleV2,
    CancelEventUserV2,
  },
  props: {
    eventTitle: {
      type: String,
      required: true
    },
    cancelEventInputParams: {
      type: Object as PropType<ICancelEventUserInputParams>,
      required: true,
    },
    isAdmin: {
      type: Boolean,
      default: false,
    },
    hasBuilderPermission: {
      type: Boolean,
      default: false,
    },
  },
  setup(
    props: {
      eventTitle: string;
      cancelEventInputParams: ICancelEventUserInputParams;
      isAdmin: boolean;
      hasBuilderPermission: boolean;
    },
    context: SetupContext
  ) {
    const athleteCompSchedData = new AthleteCompSchedData();

    const state = reactive<ICancelEventState>({
      isLoading: false,
    });

    /*
    watch(
      () => props.athlete,
      (newValue: IAthlete) => {
        athleteForm.init(newValue);
      },
      {
        immediate: true,
      }
    );
     */

    function doCancelEvent(
      cancelEventOutputPayload: ICancelEventUserOutputPayload
    ) {
      state.isLoading = true;
      athleteCompSchedData
        .cancelEvent(
          cancelEventOutputPayload.compEvent.entryId,
          cancelEventOutputPayload.reason
        )
        .then((response) => {
          if (response.errNo > 0) {
            messageDispatchHelper(
              response.error,
              USER_MESSAGE_LEVEL.ERROR.toString()
            );
            return;
          }

          submitted();
        })
        .catch((error) => {
          messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
          cancel();
          return;
        })
        .finally(() => {
          state.isLoading = false;
        });
    }

    function doCreditRefund(
      cancelEventBuilderOutputPayload: ICancelEventBuilderOutputPayload
    ) {
      if (cancelEventBuilderOutputPayload.cancelType === "Remove Only") {
        doCancelEvent({
          compEvent: cancelEventBuilderOutputPayload.compEvent,
          reason: cancelEventBuilderOutputPayload.reason,
        });
        return;
      }

      const testMode = false;
      const isCredit = cancelEventBuilderOutputPayload.cancelType === "Credit";
      const compEvent = props.cancelEventInputParams.compEvent;
      const refundEvent: IRefundEvent = {
        compid: props.cancelEventInputParams.competitionSummaryPublic.compId,
        orderid: compEvent.order.orderId,
        reason: cancelEventBuilderOutputPayload.reason,
        productid: compEvent.order.productId,
        test: testMode,
        removeEntry: true,
        email: !testMode,
        value: isCredit
          ? compEvent.order.creditValue
          : compEvent.order.refundValue,
        credit: isCredit,
        price: 0,
        refundStripeFee: false,
        refundE4SFee: false,
        text: "",
      };
      state.isLoading = true;
      const prom = new PaymentsData().refundEvent(refundEvent);
      const successMessage =
        cancelEventBuilderOutputPayload.cancelType + " processed.";
      handleResponseMessages(prom, successMessage);
      prom
        .then((response) => {
          if (response.errNo === 0) {
            submitted();
          }
        })
        .catch((error) => {
          messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
          cancel();
          return;
        })
        .finally(() => {
          state.isLoading = false;
        });
    }

    const useAdminCancel = computed(() => {
      return props.isAdmin || props.hasBuilderPermission;
    });

    function cancel() {
      context.emit("cancel");
    }

    function submit() {
      context.emit("submit");
    }

    function submitted() {
      context.emit("submitted", props.cancelEventInputParams.compEvent);
    }

    return {
      state,
      useAdminCancel,
      doCancelEvent,
      doCreditRefund,
      cancel,
      submit,
    };
  },
});
</script>
