import { ICompEventActionsResult } from "../comp-event-actions-controller";
import * as CommonServiceUtils from "../../../common/common-service-utils";
import * as CompetitonServiceV2 from "../../../competition/v2/competiton-service-v2";
import { format, parse } from "date-fns";
import { ConfigService } from "../../../config/config-service";
import { IConfigStoreState } from "../../../config/config-store";
import { CompetitionService } from "../../../competition/competiton-service";
import { AthleteService } from "../../../athlete/athlete-service";
import { AthleteCompSchedService } from "../../athletecompsched-service";
import { ICancelEventUserInputParams } from "./cancel/cancel-event-models-v2";
import { eventDateTimeDisplay } from "../../../common/common-service-utils";
import { IAthleteCompSchedRuleEvent } from "../../athletecompsched-models";

const configService = new ConfigService();
const competitionService = new CompetitionService();
const athleteService = new AthleteService();
const athleteCompSchedService = new AthleteCompSchedService();

export interface ICompEventActionsAccess {
  result: boolean;
  message: string;
  level: "" | "info" | "warn" | "error";
}

export type showSectionType =
  | "FORM"
  | "CONTACT_ORGANISER"
  | "SWITCH"
  | "CANCEL"
  | "ADD_CART"
  | "SET_PAID";

export interface ICompEventActionsStatePerms {
  isAdmin: boolean;
  hasBuilderPermission: boolean;
  belongsToUser: boolean;
}

export interface ICompEventActionsState {
  showSection: showSectionType;
  compEventActionsResult: ICompEventActionsResult;
  perms: ICompEventActionsStatePerms;
  access: {
    switch: ICompEventActionsAccess;
    cancel: ICompEventActionsAccess;
    addCart: ICompEventActionsAccess;
    setPaid: ICompEventActionsAccess;
  };
  compFlags: {
    isEntryOpen: boolean;
    name: string;
  };
  eventFlags: {
    name: string;
    isPaidFor: boolean;
    currencyValue: string;
    eventTime: string;
    orderInfoDisplay: string;
  };
  cancelEvent: {
    input: ICancelEventUserInputParams;
  };
}

export function factoryCompEventActionsResult(): ICompEventActionsResult {
  return {
    competitionSummaryPublic: competitionService.factorySummaryPublic(),
    athlete: athleteService.factoryGetAthlete(),
    compEvent: athleteCompSchedService.factoryAthleteCompSchedRuleEvent(),
    compEvents: [],
    compRule: null,
    userApplication: new ConfigService().factoryUserApplication(),
  };
}

export function factoryCompEventActionsState(): ICompEventActionsState {
  return {
    compEventActionsResult: factoryCompEventActionsResult(),
    perms: {
      isAdmin: false,
      hasBuilderPermission: false,
      belongsToUser: false,
    },
    access: {
      addCart: factoryCompEventActionsAccess(),
      cancel: factoryCompEventActionsAccess(),
      setPaid: factoryCompEventActionsAccess(),
      switch: factoryCompEventActionsAccess(),
    },
    compFlags: {
      isEntryOpen: false,
      name: "",
    },
    eventFlags: {
      currencyValue: "",
      eventTime: "",
      isPaidFor: false,
      name: "",
      orderInfoDisplay: "",
    },
    showSection: "FORM",
    cancelEvent: {
      input: {
        isAdmin: false,
        hasBuilderPermissionForComp: false,
        compEvent: athleteCompSchedService.factoryAthleteCompSchedRuleEvent(),
        competitionSummaryPublic: competitionService.factorySummaryPublic(),
      },
    },
  };
}

export function factoryCompEventActionsAccess(): ICompEventActionsAccess {
  return {
    result: false,
    message: "",
    level: "",
  };
}

export function init(
  compEventActionsResult: Readonly<ICompEventActionsResult>,
  configStoreState: Readonly<IConfigStoreState>,
  permsOverride?: ICompEventActionsStatePerms
): ICompEventActionsState {
  const compEventActionsState: ICompEventActionsState =
    factoryCompEventActionsState();

  compEventActionsState.compEventActionsResult = CommonServiceUtils.simpleClone(
    compEventActionsResult
  );
  compEventActionsState.showSection = "FORM";

  compEventActionsState.compFlags.name = getCompAndDateName(
    compEventActionsResult
  );
  compEventActionsState.compFlags.isEntryOpen = CompetitonServiceV2.isEntryOpen(
    compEventActionsResult.competitionSummaryPublic
  );

  //  <eventFlags>
  compEventActionsState.eventFlags.name = getEventName(
    compEventActionsResult.compEvent
  );
  compEventActionsState.eventFlags.isPaidFor = isEventPaidFor(
    compEventActionsResult
  );
  compEventActionsState.eventFlags.currencyValue =
    CommonServiceUtils.getAmountAsCurrency(
      compEventActionsResult.compEvent.order.wcLineValue,
      configStoreState.configApp.currency
    );
  compEventActionsState.eventFlags.eventTime =
    CommonServiceUtils.eventTimeDisplay(
      compEventActionsResult.compEvent.startdate
    );
  compEventActionsState.eventFlags.orderInfoDisplay = getOrderDetailsDisplay(
    compEventActionsResult
  );
  //  </eventFlags>

  //  <Perms>

  if (permsOverride) {
    compEventActionsState.perms = permsOverride;
  } else {
    compEventActionsState.perms.isAdmin = configService.isUserAdmin(
      configStoreState.configApp
    );

    compEventActionsState.perms.hasBuilderPermission =
      configService.hasBuilderPermissionForComp(
        configStoreState.configApp.userInfo,
        compEventActionsResult.competitionSummaryPublic.compOrgId,
        compEventActionsResult.competitionSummaryPublic.compId
      );

    compEventActionsState.perms.belongsToUser = belongsToUser(
      compEventActionsResult,
      configStoreState
    );
  }
  //  </Perms>

  //  <Access>
  compEventActionsState.access.switch = standardCompEventActionsAccess(
    compEventActionsResult,
    configStoreState,
    configService.isUserAdmin(configStoreState.configApp)
  );

  compEventActionsState.access.cancel = cancelCompEventActionsAccess(
    compEventActionsResult,
    configStoreState,
    configService.isUserAdmin(configStoreState.configApp)
  );

  compEventActionsState.access.addCart = standardCompEventActionsAccess(
    compEventActionsResult,
    configStoreState,
    configService.isUserAdmin(configStoreState.configApp)
  );

  compEventActionsState.access.setPaid = standardCompEventActionsAccess(
    compEventActionsResult,
    configStoreState,
    configService.isUserAdmin(configStoreState.configApp)
  );
  if (!compEventActionsState.perms.isAdmin && compEventActionsState.perms.hasBuilderPermission) {
    if (compEventActionsState.compEventActionsResult.compEvent.paid > 0) {
      compEventActionsState.access.setPaid.result = false;
      compEventActionsState.access.setPaid.level = "info";
      compEventActionsState.access.setPaid.message = "Already set as paid.  Use cancel to remove entry.";
    }
  }

  //  </Access>

  return compEventActionsState;
}

export function getCompAndDateName(
  compEventActionsResult: ICompEventActionsResult
): string {
  return (
    compEventActionsResult.competitionSummaryPublic.compName +
    " - " +
    CommonServiceUtils.eventDateDisplay(
      compEventActionsResult.competitionSummaryPublic.dates[0]
    )
  );
}

export function getEventName(
  athleteCompSchedRuleEvent: IAthleteCompSchedRuleEvent
): string {
  const compEvent = athleteCompSchedRuleEvent;
  const hasUpScaling = athleteCompSchedRuleEvent.ceoptions.ageGroups.length > 0;
  return (
    compEvent.eventGroup +
    (hasUpScaling && compEvent.ageGroup && compEvent.ageGroup.shortName
      ? ": " + compEvent.ageGroup.shortName
      : "")
  );
}

export function getEventTitle(
  athleteCompSchedRuleEvent: IAthleteCompSchedRuleEvent
): string {
  const compEvent = athleteCompSchedRuleEvent;
  const hasUpScaling = athleteCompSchedRuleEvent.ceoptions.ageGroups.length > 0;
  return (
    compEvent.eventGroup +
    (hasUpScaling && compEvent.ageGroup && compEvent.ageGroup.shortName
      ? ": " + compEvent.ageGroup.shortName
      : "") +
    ": " +
    eventDateTimeDisplay(compEvent.startdate)
  );
}

export function getOrderDetailsDisplay(
  compEventActionsResult: ICompEventActionsResult
): string {
  const order = compEventActionsResult.compEvent.order;
  const orderId = order.orderId;
  const date =
    order.dateOrdered.length === 0
      ? "NA"
      : format(parse(order.dateOrdered), "Do MMM YY");
  const prodId = order.productId;
  return (
    orderId +
    " / " +
    date +
    " / " +
    prodId +
    " / " +
    compEventActionsResult.compEvent.entryId
  );
}

function genericCompEventActionsAccess(
  compEventActionsResult: Readonly<ICompEventActionsResult>,
  configStoreState: Readonly<IConfigStoreState>,
  isAdmin: boolean
): ICompEventActionsAccess | null {
  const compEventActionsAccess = factoryCompEventActionsAccess();

  if (isAdmin) {
    //  can do anything..
    compEventActionsAccess.result = true;
    compEventActionsAccess.message = "";
    compEventActionsAccess.level = "info";
    return compEventActionsAccess;
  }

  const hasBuilderPermission = configService.hasBuilderPermissionForComp(
    configStoreState.configApp.userInfo,
    compEventActionsResult.competitionSummaryPublic.compOrgId,
    compEventActionsResult.competitionSummaryPublic.compId
  );
  if (hasBuilderPermission) {
    compEventActionsAccess.result = true;
    compEventActionsAccess.message = "";
    compEventActionsAccess.level = "info";
    return compEventActionsAccess;
  }

  if (
    !CompetitonServiceV2.isEntryOpen(
      compEventActionsResult.competitionSummaryPublic
    )
  ) {
    compEventActionsAccess.result = false;
    compEventActionsAccess.message = "Entries now closed.";
    compEventActionsAccess.level = "error";
    return compEventActionsAccess;
  }

  if (!isEventPaidFor(compEventActionsResult)) {
    //  you can't do anything to it.
    compEventActionsAccess.result = false;
    compEventActionsAccess.message = "Not yet paid.";
    compEventActionsAccess.level = "error";
    return compEventActionsAccess;
  }

  if (!belongsToUser(compEventActionsResult, configStoreState)) {
    //  you can't do anything to it.
    compEventActionsAccess.result = false;
    compEventActionsAccess.message = "Not assigned to you.";
    compEventActionsAccess.level = "error";
    return compEventActionsAccess;
  }

  return null;
}

function genericCompEventActionsAccessSuccess(): ICompEventActionsAccess {
  return {
    level: "info",
    message: "",
    result: true,
  };
}

export function standardCompEventActionsAccess(
  compEventActionsResult: Readonly<ICompEventActionsResult>,
  configStoreState: Readonly<IConfigStoreState>,
  isAdmin: boolean
): ICompEventActionsAccess {
  const genericAccess = genericCompEventActionsAccess(
    compEventActionsResult,
    configStoreState,
    isAdmin
  );
  if (genericAccess) {
    return genericAccess;
  }

  //  User is OK to perform function
  return genericCompEventActionsAccessSuccess();
}

export function cancelCompEventActionsAccess(
  compEventActionsResult: Readonly<ICompEventActionsResult>,
  configStoreState: Readonly<IConfigStoreState>,
  isAdmin: boolean
): ICompEventActionsAccess {
  const genericAccess = genericCompEventActionsAccess(
    compEventActionsResult,
    configStoreState,
    isAdmin
  );
  if (genericAccess) {
    return genericAccess;
  }

  const compEventActionsAccess = factoryCompEventActionsAccess();

  const isCancelStillAvailable = competitionService.isCancelStillAvailable(
    compEventActionsResult.competitionSummaryPublic
  );
  if (!isCancelStillAvailable) {
    const cancelNotAvailableAfter = format(
      competitionService.cancelsNotPermittedAfter(
        compEventActionsResult.competitionSummaryPublic
      ),
      "ddd Do MMM HH:mm"
    );

    compEventActionsAccess.result = false;
    compEventActionsAccess.message =
      "Cancel is not available past " + cancelNotAvailableAfter;
    compEventActionsAccess.level = "error";
    return compEventActionsAccess;
  }

  //  User is OK to perform function
  return genericCompEventActionsAccessSuccess();
}

export function belongsToUser(
  compEventActionsResult: Readonly<ICompEventActionsResult>,
  configStoreState: Readonly<IConfigStoreState>
): boolean {
  return (
    compEventActionsResult.compEvent.user.userId ===
    configStoreState.configApp.userInfo.user.id
  );
}

export function isEventPaidFor(
  compEventActionsResult: Readonly<ICompEventActionsResult>
): boolean {
  return compEventActionsResult.compEvent.paid === 1;
}

// export function hasBeenCancelled(  compEventActionsResult: Readonly<ICompEventActionsResult>): boolean {
//   return compEventActionsResult.compEvent.entryId === 0;
// }
