<template>
  <CardGenericV2 class="e4s-card--well e4s-gap--standard">
    <template slot="all">
      <LoadingSpinnerV2 v-if="state.isLoading" />

      <AthleteGridRowContentV2
        :athlete="entriesOptionsAthleteScheduleInput.athlete"
      />

      <div class="e4s-header--400">Switch from the following event:</div>

      <AthleteScheduleGridRowReadV2
        class="e4s-card--bold-neutral"
        :athlete-comp-sched-rule-event="compEvent"
        :entries-options-athlete-schedule-input="
          entriesOptionsAthleteScheduleInput
        "
        :competition-summary-public="competitionSummaryPublic"
        :athlete-comp-sched-service="athleteCompSchedService"
      />

      <div
        class="e4s-flex-column e4s-gap--small"
        v-if="!state.switchEventShowConfirm"
      >
        <div class="e4s-vertical-spacer--large"></div>

        <div class="e4s-flex-row e4s-justify-flex-space-between">
          <div class="e4s-header--400">Select an event to switch to:</div>

          <div class="e4s-flex-row e4s-gap--standard">
            <FieldCheckboxV2
              form-label="Show all events"
              :value="state.showAllEvents"
              :show-label="false"
              v-on:input="startFilter"
            />
            <span>Show all events</span>
          </div>
        </div>

        <AthleteScheduleGridRowV2
          v-for="athleteCompSchedRuleEvent in state.compEventsInternal"
          :athlete-comp-sched-rule-event="athleteCompSchedRuleEvent"
          :entries-options-athlete-schedule-input="
            entriesOptionsAthleteScheduleInput
          "
          :competition-summary-public="competitionSummaryPublic"
          :athlete-comp-sched-service="athleteCompSchedService"
          :key="athleteCompSchedRuleEvent.ceid"
          :is-check-box-disabled="
            isCompEventDisabled(athleteCompSchedRuleEvent)
          "
          :is-admin="configController.isAdmin.value"
          :has-builder-permission="hasBuilderPermissionForComp"
          :does-any-event-have-start-time="false"
        >
          <ButtonGenericV2
            slot="enter-button"
            button-type="secondary"
            text="Select"
            @click="onEventSelected(athleteCompSchedRuleEvent)"
          />
        </AthleteScheduleGridRowV2>
      </div>

      <div
        class="e4s-flex-column e4s-gap--standard"
        v-if="state.switchEventShowConfirm"
      >
        <div class="e4s-vertical-spacer--large"></div>

        <div class="e4s-header--400">Confirm you would like to switch to:</div>

        <AthleteScheduleGridRowReadV2
          class="e4s-card--bold-neutral"
          :athlete-comp-sched-rule-event="state.switchCompEvent"
          :entries-options-athlete-schedule-input="
            entriesOptionsAthleteScheduleInput
          "
          :competition-summary-public="competitionSummaryPublic"
          :athlete-comp-sched-service="athleteCompSchedService"
        />

        <div class="e4s-vertical-spacer--large"></div>

        <div class="e4s-flex-row e4s-justify-flex-space-between">
          <ButtonGenericV2
            button-type="tertiary"
            text="Cancel"
            @click="reset"
          />
          <ButtonGenericV2
            button-type="primary"
            text="Confirm"
            @click="switchEventConfirmed"
          />
        </div>
      </div>
    </template>
  </CardGenericV2>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  reactive,
  SetupContext,
  watch,
} from "vue";
import { IAthleteCompSchedRuleEvent } from "../../../athletecompsched-models";
import { simpleClone } from "../../../../common/common-service-utils";
import { AthleteCompSchedService } from "../../../athletecompsched-service";
import { AthleteCompSchedData } from "../../../athletecompsched-data";
import { IServerGenericResponse } from "../../../../common/common-models";
import { messageDispatchHelper } from "../../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../../user-message/user-message-models";
import { ISwitchEventStateV2 } from "./swicth-event-models-v2";
import AthleteScheduleGridRowV2 from "../../../../entry/v2/athlete-schedule/grid/AthleteScheduleGridRowV2.vue";
import { ICompetitionSummaryPublic } from "../../../../competition/competition-models";
import { useConfigController } from "../../../../config/useConfigStore";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import InfoSectionV2 from "../../../../common/ui/layoutV2/info-section-v2.vue";
import FormGenericSectionTitleV2 from "../../../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import { ICompEventActionsResult } from "../../comp-event-actions-controller";
import { IEntriesOptionsAthleteScheduleInput } from "../../../../entry/v2/athlete-schedule/entries-options-athlete-schedule-v2.vue";
import CardGenericV2 from "../../../../common/ui/layoutV2/card-generic-v2.vue";
import LoadingSpinnerV2 from "../../../../common/ui/loading-spinner-v2.vue";
import AthleteScheduleGridRowReadV2 from "../../../../entry/v2/athlete-schedule/grid/AthleteScheduleGridRowReadV2.vue";
import FieldCheckboxV2 from "../../../../common/ui/layoutV2/fields/field-checkbox-v2.vue";
import AthleteGridRowContentV2 from "../../../../athlete/v2/AthleteGridRowContentV2.vue";

export default defineComponent({
  name: "switch-event-v2",
  components: {
    AthleteGridRowContentV2,
    FieldCheckboxV2,
    AthleteScheduleGridRowReadV2,
    LoadingSpinnerV2,
    CardGenericV2,
    FormGenericSectionTitleV2,
    InfoSectionV2,
    ButtonGenericV2,
    AthleteScheduleGridRowV2,
  },
  props: {
    eventTitle: {
      type: String,
      required: true,
    },
    entriesOptionsAthleteScheduleInput: {
      type: Object as PropType<IEntriesOptionsAthleteScheduleInput>,
      required: true,
    },
    compEventActionsResult: {
      type: Object as PropType<ICompEventActionsResult>,
      required: true,
    },
    competitionSummaryPublic: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      required: true,
    },
    compEvent: {
      type: Object as PropType<IAthleteCompSchedRuleEvent>,
      required: true,
    },
    compEvents: {
      type: Array as PropType<IAthleteCompSchedRuleEvent[]>,
      default: () => {
        return [];
      },
    },
    hasBuilderPermissionForComp: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
  },
  setup(
    props: {
      eventTitle: string;
      entriesOptionsAthleteScheduleInput: IEntriesOptionsAthleteScheduleInput;
      competitionSummaryPublic: ICompetitionSummaryPublic;
      compEvent: IAthleteCompSchedRuleEvent;
      compEvents: IAthleteCompSchedRuleEvent[];
      hasBuilderPermissionForComp: boolean;
    },
    context: SetupContext
  ) {
    const athleteCompSchedService = new AthleteCompSchedService();

    const state = reactive<ISwitchEventStateV2>({
      compEventsInternal: [],
      switchCompEvent:
        athleteCompSchedService.factoryAthleteCompSchedRuleEvent(),
      switchEventShowConfirm: false,
      isLoading: false,
      showAllEvents: false,
    });

    const configController = useConfigController();

    watch(
      () => props.compEvents,
      (newValue: IAthleteCompSchedRuleEvent[]) => {
        reProcess(newValue);
      },
      {
        immediate: true,
      }
    );

    function startFilter(showAll: boolean) {
      state.showAllEvents = showAll;
      reProcess(props.compEvents);
    }

    function reProcess(compEvents: IAthleteCompSchedRuleEvent[]) {
      state.compEventsInternal = compEvents.filter((compEvent) => {
        return !isCompEventDisabled(compEvent);
      });
    }

    function isCompEventDisabled(
      compEvent: IAthleteCompSchedRuleEvent
    ): boolean {
      //  Event we are switching from
      if (compEvent.ceid === props.compEvent.ceid) {
        return true;
      }

      //  another event already selected
      if (compEvent.order.productId > 0) {
        return true;
      }

      //  can select any other event
      // if (props.hasBuilderPermissionForComp) {
      //   return false;
      // }

      if (compEvent.userEventAction) {
        return true;
      }
      if (compEvent.userSelected) {
        return true;
      }

      if (!state.showAllEvents) {
        if (compEvent.ruleIsDisabledBy) {
          return true;
        }
      }

      if (compEvent.paid > 0) {
        return true;
      }

      return false;
    }

    function onEventSelected(
      athleteCompSchedRuleEvent: IAthleteCompSchedRuleEvent
    ) {
      state.switchCompEvent = simpleClone(athleteCompSchedRuleEvent);
      state.switchEventShowConfirm = true;
    }

    function switchEventConfirmed() {
      const eventTarget: IAthleteCompSchedRuleEvent = state.switchCompEvent;
      const athleteCompSchedData: AthleteCompSchedData =
        new AthleteCompSchedData();
      state.isLoading = true;
      athleteCompSchedData
        .switchEvent(props.compEvent.entryId, eventTarget.ceid)
        .then((response: IServerGenericResponse) => {
          if (response.errNo > 0) {
            messageDispatchHelper(
              response.error,
              USER_MESSAGE_LEVEL.ERROR.toString()
            );
            return;
          }
          context.emit("submitted");
          return;
        })
        .catch((error) => {
          messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
          return;
        })
        .finally(() => {
          state.isLoading = false;
        });
    }

    function reset() {
      state.switchCompEvent =
        athleteCompSchedService.factoryAthleteCompSchedRuleEvent();
      state.switchEventShowConfirm = false;
    }

    function cancel() {
      context.emit("cancel");
    }

    return {
      state,
      configController,
      cancel,
      reset,
      isCompEventDisabled,
      switchEventConfirmed,
      athleteCompSchedService,
      onEventSelected,
      startFilter,
    };
  },
});
</script>
