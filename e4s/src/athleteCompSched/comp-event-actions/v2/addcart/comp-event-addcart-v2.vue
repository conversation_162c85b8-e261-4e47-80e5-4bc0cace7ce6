<template>
  <CardGenericV2 class="e4s-card--well e4s-gap--standard">
    <template slot="all">
      <LoadingSpinnerV2 v-if="state.isLoading"/>

      <FormGenericSectionTitleV2
        title-size="400"
        :section-title="eventTitle"
        section-overview="Add to Users Cart"
        :show-cancel-button="false"
        v-on:cancel="cancel"
      />

      <p>Select a user, this event will be added to their cart.</p>

      <UserTypeAheadV2 v-on:input="state.userSummary = $event"/>
      <FormGenericInputErrorMessageV2 :error-message="errorMessages.userSummary" />

      <ButtonGroupSectionV2>
        <ButtonGenericV2
          slot="buttons-left"
          button-type="tertiary"
          v-on:click="cancel"
          text="Cancel"
        />

        <div slot="buttons-right">
          <ButtonGenericV2
            button-type="primary"
            v-on:click="askForConfirmation"
            text="Submit"
            v-if="!state.showConfirm"
          />
          <ButtonGenericV2
            button-type="primary"
            class="e4s-button--destructive"
            v-on:click="submit"
            text="Confirm"
            v-if="state.showConfirm"
          />
        </div>
      </ButtonGroupSectionV2>
    </template>
  </CardGenericV2>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { reactive } from "vue";
import type { PropType } from "vue";
import FormGenericSectionTitleV2 from "../../../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import FormGenericInputErrorMessageV2 from "../../../../common/ui/layoutV2/form/form-generic-input-error-message-v2.vue";
import InputRestrictLength from "../../../../common/ui/field/input-restrict-length/input-restrict-length.vue";
import ButtonGroupSectionV2 from "../../../../common/ui/layoutV2/buttons/button-group-section-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { AthleteCompSchedData } from "../../../athletecompsched-data";
import { messageDispatchHelper } from "../../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../../user-message/user-message-models";
import type {
  IAddCartEventInputParams,
  IAddCartEventState,
} from "./comp-event-addcart-models";
import { UserService } from "../../../../admin/user/user-service";
import UserTypeAheadV2 from "../../../../admin/user/v2/user-type-ahead-v2.vue"
import CardGenericV2 from "../../../../common/ui/layoutV2/card-generic-v2.vue"
import LoadingSpinnerV2 from "../../../../common/ui/loading-spinner-v2.vue"

@Component({
  name: "comp-event-addcart-v2",
  components: {
    LoadingSpinnerV2,
    CardGenericV2,
    UserTypeAheadV2,
    ButtonGenericV2,
    ButtonGroupSectionV2,
    InputRestrictLength,
    FormGenericInputErrorMessageV2,
    FormGenericSectionTitleV2,
  },
  props: {
    eventTitle: {
      type: String,
      required: true
    },
    addCartEventInputParams: {
      type: Object as PropType<IAddCartEventInputParams>,
      default: () => ({} as IAddCartEventInputParams),
    },
  },
})
export default class CompEventAddcartV2 extends Vue {
  @Prop({ type: String, required: true })
  public readonly eventTitle!: string;

  @Prop({ type: Object as PropType<IAddCartEventInputParams>, default: () => ({}) })
  public readonly addCartEventInputParams!: IAddCartEventInputParams;

  public userService = new UserService();

  public state = reactive<IAddCartEventState>({
    showConfirm: false,
    userSummary: this.userService.factoryUserSummary(),
    isLoading: false,
  });

  public errorMessages = reactive({
    userSummary: "",
  });

  public doValidation(): boolean {
    this.errorMessages.userSummary = "";

    if (this.state.userSummary.id === 0) {
      this.errorMessages.userSummary = "Please select a user.";
      return false;
    }
    return true;
  }

  public askForConfirmation() {
    if (!this.doValidation()) {
      return;
    }

    this.state.showConfirm = true;
  }

  public cancel() {
    this.$emit("cancel");
  }

  public submit() {
    if (!this.doValidation()) {
      return;
    }
    this.state.isLoading = true;

    new AthleteCompSchedData()
      .addEntryToUsersCart(
        this.addCartEventInputParams.compEvent.order.productId,
        this.state.userSummary.id
      )
      .then((response) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }

        this.$emit("submitted", this.addCartEventInputParams.compEvent);
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return {};
      })
      .finally(() => {
        this.state.isLoading = false;
      });
  }
}
</script>
