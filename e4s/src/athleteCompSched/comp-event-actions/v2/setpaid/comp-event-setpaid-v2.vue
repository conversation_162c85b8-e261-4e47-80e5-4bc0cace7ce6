<template>
  <CardGenericV2 class="e4s-card--well e4s-gap--standard">
    <template slot="all">
      <LoadingSpinnerV2 v-if="state.isLoading"/>
      <FormGenericSectionTitleV2
        title-size="400"
        :section-title="eventTitle"
        :section-overview="
        'Toggle Event Paid Status of: ' +
        setPaidEventInputParams.compEvent.eventGroup +
        ' to ' +
        (setPaidEventInputParams.compEvent.paid ? 'NOT PAID' : 'PAID')
      "
        :show-cancel-button="false"
        v-on:cancel="cancel"
      />

      <p>Please enter a reason (required).</p>
      <InputRestrictLength
        slot="content"
        :use-text-area="true"
        :max-length="600"
        text-area-class="browser-default e4s-input-field e4s-input-field--primary ask-organiser--text-area"
        v-on:isMaxLength="isMaxLength = $event"
        v-on:onChanged="state.reason = $event"
      >
      </InputRestrictLength>
      <FormGenericInputErrorMessageV2 :error-message="errorMessages.reason" />

      <ButtonGroupSectionV2>
        <ButtonGenericV2
          slot="buttons-left"
          button-type="tertiary"
          v-on:click="cancel"
          text="Cancel"
        />

        <div slot="buttons-right">
          <ButtonGenericV2
            button-type="primary"
            v-on:click="askForConfirmation"
            text="Submit"
            v-if="!state.showConfirm"
          />
          <ButtonGenericV2
            button-type="primary"
            class="e4s-button--destructive"
            v-on:click="submit"
            text="Confirm"
            v-if="state.showConfirm"
          />
        </div>
      </ButtonGroupSectionV2>
    </template>
  </CardGenericV2>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { reactive } from "vue";
import type { PropType } from "vue";
import FormGenericSectionTitleV2 from "../../../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import type {
  ISetPaidEventInputParams,
  ISetPaidEventState,
  ISetPaidEventUserOutputPayload,
} from "./comp-event-setpaid-models";
import FormGenericInputErrorMessageV2 from "../../../../common/ui/layoutV2/form/form-generic-input-error-message-v2.vue";
import InputRestrictLength from "../../../../common/ui/field/input-restrict-length/input-restrict-length.vue";
import ButtonGroupSectionV2 from "../../../../common/ui/layoutV2/buttons/button-group-section-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { AthleteCompSchedData } from "../../../athletecompsched-data";
import { handleResponseMessages } from "../../../../common/handle-http-reponse";
import type { IServerGenericResponse } from "../../../../common/common-models";
import { messageDispatchHelper } from "../../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../../user-message/user-message-models";
import LoadingSpinnerV2 from "../../../../common/ui/loading-spinner-v2.vue"
import CardGenericV2 from "../../../../common/ui/layoutV2/card-generic-v2.vue"

@Component({
  name: "comp-event-setpaid-v2",
  components: {
    CardGenericV2,
    LoadingSpinnerV2,
    ButtonGenericV2,
    ButtonGroupSectionV2,
    InputRestrictLength,
    FormGenericInputErrorMessageV2,
    FormGenericSectionTitleV2,
  },
  props: {
    eventTitle: {
      type: String,
      required: true,
    },
    setPaidEventInputParams: {
      type: Object as PropType<ISetPaidEventInputParams>,
      default: () => ({} as ISetPaidEventInputParams),
    },
  },
})
export default class CompEventSetpaidV2 extends Vue {
  @Prop({ type: String, required: true })
  public readonly eventTitle!: string;

  @Prop({ type: Object as PropType<ISetPaidEventInputParams>, default: () => ({}) })
  public readonly setPaidEventInputParams!: ISetPaidEventInputParams;

  public state = reactive<ISetPaidEventState>({
    showConfirm: false,
    reason: "",
    isLoading: false,
  });

  public errorMessages = reactive({
    reason: "",
  });

  public isMaxLength = false;

  public doValidation(): boolean {
    this.errorMessages.reason = "";

    if (this.state.reason.length === 0) {
      this.errorMessages.reason = "Please enter a reason.";
      return false;
    }
    return true;
  }

  public askForConfirmation() {
    if (!this.doValidation()) {
      return;
    }

    this.state.showConfirm = true;
  }

  public cancel() {
    this.$emit("cancel");
  }

  public submit() {
    if (!this.doValidation()) {
      return;
    }

    this.state.isLoading = true;

    const compEvent = this.setPaidEventInputParams.compEvent;
    const setPaidTo = compEvent.paid ? 0 : 1;
    const prom = new AthleteCompSchedData().setPaid(
      [compEvent.order.productId],
      setPaidTo,
      this.state.reason
    );
    handleResponseMessages(prom);
    prom
      .then((resp: IServerGenericResponse) => {
        if (resp.errNo === 0) {
          messageDispatchHelper("Saved.", USER_MESSAGE_LEVEL.INFO.toString());
          const setPaidEventUserOutputPayload: ISetPaidEventUserOutputPayload =
            {
              compEvent: this.setPaidEventInputParams.compEvent,
              reason: this.state.reason,
            };

          this.$emit("submitted", setPaidEventUserOutputPayload);
        }
      })
      .finally(() => {
        this.state.isLoading = false;
      });
  }
}
</script>
