import {IAthleteCompSchedRuleEvent} from "../../../athletecompsched-models"
import {ICompetitionSummaryPublic} from "../../../../competition/competition-models"

export interface ISetPaidEventState {
  showConfirm: boolean;
  reason: string;
  isLoading: boolean;
}

export interface ISetPaidEventInputParams {
  isAdmin: boolean;
  hasBuilderPermissionForComp: boolean;
  compEvent: IAthleteCompSchedRuleEvent;
  competitionSummaryPublic: ICompetitionSummaryPublic;
}

export interface ISetPaidEventUserOutputPayload {
  compEvent: IAthleteCompSchedRuleEvent;
  reason: string;
}
