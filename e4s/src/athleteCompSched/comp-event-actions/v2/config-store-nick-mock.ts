import { IConfigStoreState } from "../../../config/config-store";

export const nickConfig: IConfigStoreState = {
  configApp: {
    id: 2,
    systemName: "UK Entry System",
    public: 0,
    theme: "GBR",
    logo: "/resources/e4s_logo.png",
    cardlogo: "/resources/default_logo.gif",
    currency: "£",
    support: "https://support.entry4sports.com",
    userguide: "",
    defaultao: {
      id: 2,
      code: "EA",
      description: "United Kingdom",
      prefix: "uk_",
      urnformat: "(^[0-9]{7}$)",
      urnmessage: "Please enter 7 digits",
      icon: "",
    },
    vetDisplay: "vet",
    userDaysPast: 10,
    adminDaysPast: 30,
    payments: "entry",
    options: {
      allowAreas: false,
      allowClubs: true,
      minFee: 0.5,
      minCost: 5,
      informActive: "<EMAIL>",
      paymentCodeMandatory: false,
      stripeMandatory: false,
      useStripeConnect: false,
      waitingListDays: 4,
      homePage: {
        message: "",
        defaultFilters: {
          fromDate: "",
          toDate: "",
          type: "ALL",
          freeTextSearch: "",
          event: {
            id: 0,
            name: "",
          },
          compOrg: {
            id: 0,
            name: "",
          },
          location: {
            id: 0,
            name: "",
          },
          organiser: {
            id: 0,
            name: "",
          },
        },
      },
      maintenance: false,
      adminEmail: "entry4sports.com",
      nationalminCost: 12,
      nationaladdCost: 0,
      addCost: 0.35,
      percCharged: 5,
      message: "",
      homeMessage: "",
      health: {
        paymentStatus: {
          lastRun: "2022-08-31T09:17:14+01:00",
          every: 86400,
        },
        entriesClosingCheck: {
          lastRun: "2022-08-31T13:39:12+01:00",
          every: 86400,
        },
        healthMonitor: {
          lastRun: "2022-08-31T16:16:19+01:00",
          every: 360,
        },
        orderCheck: {
          lastRun: "2022-08-31T16:17:22+01:00",
          every: 360,
        },
        entryCheck: {
          lastRun: "2022-08-31T16:18:33+01:00",
          every: 120,
        },
        emailCheck: {
          lastRun: "2022-08-31T16:19:35+01:00",
          every: 360,
        },
        waitingList: {
          lastRun: "2022-08-31T16:19:35+01:00",
          every: 900,
        },
      },
    },
    env: "prod",
    features: {},
    maintenance: false,
    aos: [
      {
        id: 1,
        code: "IRL",
        description: "Athletics Ireland",
        prefix: "ire_",
        urnformat: "(^[0-9]{6}$)",
        urnmessage: "Please enter 6 digits",
        icon: "",
      },
      {
        id: 2,
        code: "EA",
        description: "United Kingdom",
        prefix: "uk_",
        urnformat: "(^[0-9]{7}$)",
        urnmessage: "Please enter 7 digits",
        icon: "",
      },
      {
        id: 3,
        code: "ANI",
        description: "Athletics Northern Ireland",
        prefix: "",
        urnformat: "(^[0-9]{6}$)",
        urnmessage: "Please enter 6 digits",
        icon: "",
      },
    ],
    userInfo: {
      e4s_credit: 6.15,
      e4s_pagessize: 25,
      e4s_schedInfoState: 0,
      orgs: [],
      areas: [],
      clubs: [],
      user: {
        id: 19828,
        user_login: "nick wall",
        user_nicename: "nick-wall",
        user_email: "<EMAIL>",
        display_name: "nick wall",
        role: "USER",
        google_email: "",
        impersonating: false,
        e4sCredit: [6.15],
        permissions: [],
      },
      wp_role: "subscriber",
      security: {
        permissions: [],
        permLevels: {
          "": [],
        },
      },
    },
    userId: 19828,
    checkIn: {
      shortUrl: "http://e4scheck.in/{compid}",
      defaultText:
        "Welcome to the Check-in service for our competition. To help us in the registration process, we would appreciate if you could let us know which events you are taking part in today. This will help reduce the number of athletes and the time you spend at the registration area picking up your bib number.\r\nWe have provided a number of methods to do this, please choose one of the methods below.",
    },
    role: "USER",
    menus: [],
    classifications: [],
    logout: "https://entry4sports.co.uk/wp-json/e4s/v5/logout",
    help: [
      {
        id: 1,
        preload: true,
        key: "showentries",
        title: "Entry4Sports Home Page",
        data: "https://support.entry4sports.com/the-home-page",
        type: "U",
      },
      {
        id: 5,
        preload: true,
        key: "builder",
        title: "Competition Builder",
        data: "https://support.entry4sports.com/builder",
        type: "U",
      },
      {
        id: 11,
        preload: true,
        key: "athletes",
        title: "Athlete Help",
        data: "Testing<b>HTML</b> help for Nick<br>\r\nThis is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br> THIS IS THE END",
        type: "H",
      },
    ],
    messages: [],
  },
} as any as IConfigStoreState;
