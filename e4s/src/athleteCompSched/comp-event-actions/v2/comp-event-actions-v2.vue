<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <div
      class="e4s-flex-column e4s-gap--standard"
      v-if="compEventActions.state.showSection === 'FORM'"
    >
      <!--      <div class="e4s-flex-row e4s-justify-flex-end">-->
      <!--        <ButtonGenericBackV2 v-on:click="cancel" />-->
      <!--      </div>-->

      <!--      <CardGenericV2 class="e4s-card&#45;&#45;well e4s-gap&#45;&#45;standard">-->
      <!--        <template slot="all">-->
      <div class="e4s-flex-row e4s-justify-flex-space-between">
        <AthleteGridRowContentV2
          :show-athlete-profile-link="false"
          :athlete="compEventActions.state.compEventActionsResult.athlete"
        >
          <PrimaryLink slot="back-button" link-text="Back" @onClick="cancel" />
          <div slot="view-athlete-button"></div>
        </AthleteGridRowContentV2>

        <!--            <PillV2-->
        <!--              slot="right-content"-->
        <!--              style="min-width: 170px;max-height: 20px;"-->
        <!--              :pill-type="compEventActions.getPillType.value"-->
        <!--              :text="compEventActions.getPillMessage.value"-->
        <!--            />-->
      </div>

      <!--          put a light grey line here-->
      <hr class="dat-e4s-hr dat-e4s-hr--slightly-lighter dat-e4s-hr-only" />

      <FormGenericSectionTitleV2
        :section-title="compEventActions.eventTitle.value"
        title-size="400"
        :show-cancel-button="true"
        v-on:cancel="cancel"
      >
        <span slot="right-content"></span>
      </FormGenericSectionTitleV2>

      <div class="e4s-flex-row e4s-justify-flex-space-between">
        <div>
          <p class="e4s-subheader--300">Value</p>
          <p
            class="e4s-body--100"
            v-text="compEventActions.state.eventFlags.currencyValue"
          ></p>
        </div>

        <div>
          <p class="e4s-subheader--300">
            Order (No# / Date
            <template v-if="configController.isAdmin.value"
              >/ Prod# / Entry#</template
            >)
          </p>

          <div class="e4s-flex-row e4s-gap--standard">
            <template v-if="configController.isAdmin.value">
              <a
                class="e4s-hyperlink--100 e4s-hyperlink--primary"
                target="_order"
                :href="
                  '/wp-admin/post.php?post=' +
                  compEventActions.state.compEventActionsResult.compEvent
                    .order +
                  '&action=edit'
                "
              >
                <span
                  v-text="
                    compEventActions.state.compEventActionsResult.compEvent
                      .order.orderId
                  "
                ></span>
              </a>
            </template>

            <template v-if="!configController.isAdmin.value">
              <span
                v-text="
                  compEventActions.state.compEventActionsResult.compEvent.order
                    .orderId
                "
              ></span>
            </template>

            <span v-text="compEventActions.getOrderTime.value"></span>
            <template v-if="configController.isAdmin.value">
              <span
                v-text="
                  compEventActions.state.compEventActionsResult.compEvent.order
                    .productId
                "
              ></span>
              <span
                v-text="
                  compEventActions.state.compEventActionsResult.compEvent
                    .entryId
                "
              ></span>
            </template>
          </div>
        </div>
      </div>

      <div
        class="
          dat-e4s-form-input-control--grid
          dat-e4s-form-input-control--grid-2-col
          e4s-full-width
        "
      >
        <!--          <FormGenericFieldGridV2>-->
        <!--            <template slot="content">-->
        <!--Switch Event-->
        <CardGenericV2>
          <div slot="all" class="e4s-flex-column e4s-gap--standard">
            <div class="e4s-flex-row e4s-justify-flex-space-between">
              <h2 class="e4s-header--500">Switch Event</h2>
              <ButtonGenericV2
                class="e4s-button--75"
                button-type="primary"
                text="Switch"
                :disabled="!compEventActions.state.access.switch.result"
                v-on:click="compEventActions.setShowSection('SWITCH')"
              />
            </div>

            <p class="e4s-subheader--general e4s-subheader--500">
              Any events that are available to switch to will be shown. Once
              switched the athlete will have been automatically removed from the
              original event.
            </p>

            <InfoSectionV2
              :info-type="compEventActions.state.access.switch.level"
              v-if="compEventActions.state.access.switch.message.length > 0"
            >
              <div slot="default">
                <p v-text="compEventActions.state.access.switch.message"></p>
              </div>
            </InfoSectionV2>
          </div>
        </CardGenericV2>
        <!--/Switch Event-->

        <!--Cancel Event-->
        <CardGenericV2>
          <div slot="all" class="e4s-flex-column e4s-gap--standard">
            <div class="e4s-flex-row e4s-justify-flex-space-between">
              <h2 class="e4s-header--500">Cancel Entry</h2>
              <ButtonGenericV2
                class="e4s-button--75"
                button-type="primary"
                text="Cancel"
                :disabled="!compEventActions.state.access.cancel.result"
                v-on:click="compEventActions.setShowSection('CANCEL')"
              />
            </div>

            <p class="e4s-subheader--general e4s-subheader--500">
              This will cancel the entry and remove the athlete from
              <span
                v-text="
                  compEventActions.state.compEventActionsResult.compEvent
                    .eventGroup
                "
              ></span
              >. This will free up a space for another athlete to enter.
            </p>

            <InfoSectionV2
              :info-type="compEventActions.state.access.switch.level"
              v-if="compEventActions.state.access.cancel.message.length > 0"
            >
              <div slot="default">
                <p v-text="compEventActions.state.access.cancel.message"></p>
              </div>
            </InfoSectionV2>
          </div>
        </CardGenericV2>
        <!--/Cancel Event-->

        <!--Add Cart Event-->
        <CardGenericV2
          v-if="
            compEventActions.state.perms.hasBuilderPermission ||
            compEventActions.state.perms.isAdmin
          "
        >
          <div slot="all" class="e4s-flex-column e4s-gap--standard">
            <div class="e4s-flex-row e4s-justify-flex-space-between">
              <h2 class="e4s-header--500">Add To A User's Cart</h2>
              <ButtonGenericV2
                class="e4s-button--75"
                button-type="primary"
                text="Add"
                :disabled="!compEventActions.state.access.addCart.result"
                v-on:click="compEventActions.setShowSection('ADD_CART')"
              />
            </div>

            <p class="e4s-subheader--general e4s-subheader--500">
              Add or move an entry to a specific users cart. E.g. competition
              entries are closed, but want an athlete entered and still want
              them to pay. Add an athlete as an organiser, then assign the entry
              to a given user using this function.
            </p>

            <InfoSectionV2
              :info-type="compEventActions.state.access.addCart.level"
              v-if="compEventActions.state.access.addCart.message.length > 0"
            >
              <div slot="default">
                <p v-text="compEventActions.state.access.addCart.message"></p>
              </div>
            </InfoSectionV2>
          </div>
        </CardGenericV2>
        <!--/Add Cart Event-->

        <!--Set Paid Event-->
        <CardGenericV2
          v-if="
            compEventActions.state.perms.hasBuilderPermission ||
            compEventActions.state.perms.isAdmin
          "
        >
          <div slot="all" class="e4s-flex-column e4s-gap--standard">
            <div class="e4s-flex-row e4s-justify-flex-space-between">
              <h2 class="e4s-header--500">Paid Status</h2>
              <ButtonGenericV2
                button-type="primary"
                :text="
                  compEventActions.state.eventFlags.isPaidFor
                    ? 'Set not paid'
                    : 'Set Paid'
                "
                class="e4s-align-self-flex-end"
                :disabled="!compEventActions.state.access.setPaid.result"
                v-on:click="compEventActions.setShowSection('SET_PAID')"
              />
            </div>

            <p class="e4s-subheader--general e4s-subheader--500">
              Toggle the paid status.
            </p>

            <InfoSectionV2
              info-type="error"
              v-if="
                compEventActions.state.eventFlags.isPaidFor ||
                compEventActions.state.access.setPaid.message.length > 0
              "
            >
              <div slot="default">Already set as paid.</div>
            </InfoSectionV2>
          </div>
        </CardGenericV2>
        <!--/Set Paid Event-->
        <!--            </template>-->
        <!--          </FormGenericFieldGridV2>-->
      </div>
      <!--        </template>-->
      <!--      </CardGenericV2>-->
    </div>

    <div
      class="e4s-flex-column e4s-gap--standard"
      v-if="compEventActions.state.showSection !== 'FORM'"
    >
      <div class="e4s-flex-row e4s-justify-flex-end">
        <ButtonGenericBackV2
          v-on:click="compEventActions.state.showSection = 'FORM'"
        />
      </div>

      <SwitchEventV2
        v-if="compEventActions.state.showSection === 'SWITCH'"
        :event-title="compEventActions.eventTitle.value"
        :entries-options-athlete-schedule-input="
          entriesOptionsAthleteScheduleInput
        "
        :comp-event-actions-result="compEventActionsResult"
        :competition-summary-public="
          compEventActions.state.compEventActionsResult.competitionSummaryPublic
        "
        :comp-event="compEventActions.state.compEventActionsResult.compEvent"
        :comp-events="compEventActions.state.compEventActionsResult.compEvents"
        :has-builder-permission-for-comp="
          compEventActions.state.perms.hasBuilderPermission
        "
        v-on:cancel="compEventActions.state.showSection = 'FORM'"
        v-on:submitted="submitted"
      />

      <CancelEventV2
        v-if="compEventActions.state.showSection === 'CANCEL'"
        :event-title="compEventActions.eventTitle.value"
        :cancel-event-input-params="compEventActions.state.cancelEvent.input"
        :is-admin="configController.isAdmin.value"
        v-on:cancel="compEventActions.state.showSection = 'FORM'"
        v-on:submitted="submitted"
      />

      <CompEventSetpaidV2
        v-if="compEventActions.state.showSection === 'SET_PAID'"
        :event-title="compEventActions.eventTitle.value"
        :set-paid-event-input-params="compEventActions.getSetPaidInput.value"
        v-on:cancel="compEventActions.state.showSection = 'FORM'"
        v-on:submitted="submitted"
      />

      <CompEventAddcartV2
        v-if="compEventActions.state.showSection === 'ADD_CART'"
        :event-title="compEventActions.eventTitle.value"
        :add-cart-event-input-params="compEventActions.getAddCartInput.value"
        v-on:cancel="compEventActions.state.showSection = 'FORM'"
        v-on:submitted="submitted"
      />

      <AskOrganiserFormV2
        v-if="compEventActions.state.showSection === 'CONTACT_ORGANISER'"
        :competition-summary-public="
          compEventActions.state.compEventActionsResult.competitionSummaryPublic
        "
        v-on:cancel="compEventActions.state.showSection === 'FORM'"
        v-on:submitted="submitted"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  SetupContext,
  watch,
} from "vue";
import { ICompEventActionsResult } from "../comp-event-actions-controller";
import FormGenericFieldGridV2 from "../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import { useCompEventActions } from "./useCompEventActions";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import AskOrganiserFormV2 from "../../../competition/askorganiser/ask-organiser-form-v2.vue";
import FormGenericSectionTitleV2 from "../../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import InfoSectionV2 from "../../../common/ui/layoutV2/info-section-v2.vue";
import CardGenericV2 from "../../../common/ui/layoutV2/card-generic-v2.vue";
import SwitchEventV2 from "./switch/switch-event-v2.vue";
import CancelEventV2 from "./cancel/cancel-event-v2.vue";
import CompEventSetpaidV2 from "./setpaid/comp-event-setpaid-v2.vue";
import CompEventAddcartV2 from "./addcart/comp-event-addcart-v2.vue";
import { IEntriesOptionsAthleteScheduleInput } from "../../../entry/v2/athlete-schedule/entries-options-athlete-schedule-v2.vue";
import PillV2 from "../../../common/ui/layoutV2/pills/pill-v2.vue";
import { useConfigController } from "../../../config/useConfigStore";
import ButtonGenericBackV2 from "../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import { ICompEventActionsStatePerms } from "./comp-event-actions-service-v2";
import AthleteGridRowContentV2 from "../../../athlete/v2/AthleteGridRowContentV2.vue";
import PrimaryLink from "../../../common/ui/layoutV2/href/PrimaryLink.vue";

// const compEventActionsController = new CompEventActionsController();

export default defineComponent({
  name: "comp-event-actions-v2",
  components: {
    PrimaryLink,
    AthleteGridRowContentV2,
    ButtonGenericBackV2,
    PillV2,
    CompEventAddcartV2,
    CompEventSetpaidV2,
    CancelEventV2,
    SwitchEventV2,
    CardGenericV2,
    InfoSectionV2,
    FormGenericSectionTitleV2,
    AskOrganiserFormV2,
    ButtonGenericV2,
    FormGenericFieldGridV2,
  },
  props: {
    compEventActionsResult: {
      type: Object as PropType<ICompEventActionsResult>,
      // default: () => {
      //   return compEventActionsController.factoryCompEventActionsResult();
      // },
      required: true,
    },
    entriesOptionsAthleteScheduleInput: {
      type: Object as PropType<IEntriesOptionsAthleteScheduleInput>,
      required: true,
    },
  },
  setup(
    props: {
      compEventActionsResult: ICompEventActionsResult;
      entriesOptionsAthleteScheduleInput: IEntriesOptionsAthleteScheduleInput;
    },
    context: SetupContext
  ) {
    const compEventActions = useCompEventActions();
    const configController = useConfigController();

    watch(
      () => props.compEventActionsResult,
      (newValue: ICompEventActionsResult) => {
        compEventActions.init(newValue);
      },
      {
        immediate: true,
      }
    );

    watch(
      () => configController.isAdmin.value,
      (newValue: boolean) => {
        // compEventActions.state.perms.isAdmin = configController.isAdmin.value;
        // compEventActions.state.perms.hasBuilderPermission = configController.isAdmin.value;
        const compEventActionsStatePerms: ICompEventActionsStatePerms = {
          isAdmin: newValue,
          hasBuilderPermission: newValue,
          belongsToUser: false,
        };
        compEventActions.init(
          props.compEventActionsResult,
          compEventActionsStatePerms
        );
      }
    );

    function cancel() {
      context.emit("cancel");
    }

    /**
     * whatever is bound to this might need to reload schedule.
     */
    function submitted() {
      context.emit("submitted");
    }

    return { compEventActions, configController, cancel, submitted };
  },
});
</script>
