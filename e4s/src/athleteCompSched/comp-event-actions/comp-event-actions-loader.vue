<template>
    <div>
        <div class="row" v-if="isLoading">
            <div class="col s12 m12 l12">
                Loading...<LoadingSpinner></LoadingSpinner>
            </div>
        </div>

        <CompEventActions
            v-if="isDataLoaded"
            :comp-event-actions-result="compEventActionsResult"
            v-on:close="close"
        ></CompEventActions>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import CompEventActions from "./comp-event-actions.vue"
import {mapState} from "vuex"
import {CONFIG_STORE_CONST, IConfigStoreState} from "../../config/config-store"
import type {IConfigApp} from "../../config/config-app-models";
import { CompEventActionsController } from "./comp-event-actions-controller";
import type { ICompEventActionsResult } from "./comp-event-actions-controller";

@Component({
    name: "comp-event-actions-loader",
    components: {CompEventActions},
    computed: {
        ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
            configApp: (state: any) => state.configApp
        })
    }
})
export default class CompEventActionsLoader extends Vue {
    public readonly configApp!: IConfigApp;

    public compId = 0;
    public athleteId = 0;
    public clubId = 0;
    public entryId = 0;

    public isLoading = true;
    public compEventActionsController: CompEventActionsController = new CompEventActionsController();
    public compEventActionsResult: ICompEventActionsResult = {} as ICompEventActionsResult;

    public xxx: string = "0";

    public created() {
        // Initialize compEventActionsResult now that compEventActionsController is available
        this.compEventActionsResult = this.compEventActionsController.factoryCompEventActionsResult();
        
        this.compId = isNaN(Number(this.$route.params.compId)) ? 0 : parseInt(this.$route.params.compId, 0);
        this.athleteId = isNaN(Number(this.$route.params.athleteId)) ? 0 : parseInt(this.$route.params.athleteId, 0);
        this.clubId = isNaN(Number(this.$route.params.clubId)) ? 0 : parseInt(this.$route.params.clubId, 0);
        this.entryId = isNaN(Number(this.$route.params.entryId)) ? 0 : parseInt(this.$route.params.entryId, 0);

        this.loadData();
    }

    public get isDataLoaded(): boolean {
        return this.compEventActionsResult.competitionSummaryPublic.compId > 0;
    }

    public loadData() {
        this.isLoading = true;
        this.compEventActionsController.loadData(
            this.compId,
            this.athleteId,
            this.clubId,
            this.entryId,
            this.configApp.userInfo.user)
            .then((response) => {
                this.compEventActionsResult = response;
                this.isLoading = false;
            })
    }

    public close() {
        window.location.href = window.document.referrer;
    }
}
</script>
