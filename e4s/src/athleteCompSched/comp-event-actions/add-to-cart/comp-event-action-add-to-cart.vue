<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <div class="e4s-form-sub-header left">
          <i
            class="
              material-icons
              e4s-force-img-vert-align-middle
              e4s-force-img--pad-right
            "
            >shopping_cart</i
          >Add To A User's Cart
        </div>
      </div>
    </div>

    <!--    <div class="row">-->
    <!--      <div class="col s12 m12 l12">-->
    <!--        userAthletes: {{ userAthletes }}-->
    <!--        <div></div>-->
    <!--        validationMessage: {{ validationMessage }}-->
    <!--        <div></div>-->
    <!--        userAthleteSelected: {{ userAthleteSelected }}-->
    <!--      </div>-->
    <!--    </div>-->

    <div class="row">
      <div class="col s12 m12 l12" v-if="showUserSearch">
        <!--        <div class="e4s-flex-column e4s-full-width e4s-gap&#45;&#45;standard">-->
        <!--          <label> Select User </label>-->
        <!--        </div>-->

        <FormGenericInputTemplateV2 form-label="Select User">
          <div slot="after-label" v-if="getUserNameCount > 0">
            <label>
              <input
                type="radio"
                class="browser-default e4s-input-field e4s-input-field--primary"
                value="SELECT"
                v-model="userSelectionType"
              />
              <span>Select From Currently Assigned</span>
            </label>
            <label>
              <input
                type="radio"
                class="browser-default e4s-input-field e4s-input-field--primary"
                value="SEARCH"
                v-model="userSelectionType"
              />
              <span>Search</span>
            </label>
          </div>

          <div slot="field">
            <UserTypeAhead
              v-on:onSelected="onUserSelected"
              v-if="userSelectionType === 'SEARCH'"
            />

            <FieldSelectV2
              :data-array="userAthletes"
              :value="userAthleteSelected"
              v-on:input="onUserSelected"
              v-if="userSelectionType === 'SELECT'"
            >
              <template slot-scope="{ obj }">
                {{ getUserDisplayName(obj) }}
              </template>
            </FieldSelectV2>
          </div>
        </FormGenericInputTemplateV2>
      </div>

      <div class="col s12 m12 l12" v-if="!showUserSearch">
        <label class="active" for="reason">
          Selected User
          <a href="#" v-on:click.prevent="showUserSearch = true">Search</a>
        </label>
        <div v-text="getUserName"></div>
      </div>

      <div class="col s12 m12 l12">
        <label class="active" for="reason"> Reason </label>
        <input id="reason" name="reason" v-model="reason" placeholder="" />
      </div>
    </div>

    <div class="row" v-if="validationMessage.length > 0">
      <div class="col s12 m12 l12">
        <span v-text="validationMessage" class="error-message"></span>
      </div>
    </div>

    <div slot="buttons">
      <div class="row">
        <div class="col s12 m12 l12">
          <ButtonsCancelOkConfirm
            :is-loading="isLoading"
            v-on:cancel="close"
            v-on:ok="addToCart"
          ></ButtonsCancelOkConfirm>
        </div>
      </div>
    </div>
    <LoadingSpinnerV2 v-if="isLoading" />
  </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { AthleteCompSchedData } from "../../athletecompsched-data";
import { messageDispatchHelper } from "../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../user-message/user-message-models";
import ButtonsCancelOkConfirm from "../../../common/ui/buttons/buttons-cancel-ok-confirm.vue";
import UserTypeAhead from "../../../admin/user/user-type-ahead.vue";
import { IUserSummary } from "../../../admin/user/user-models";
import { UserService } from "../../../admin/user/user-service";
import { ICompEventActionsResult } from "../comp-event-actions-controller";
import { IAthleteUser } from "../../../athlete/athlete-models";
import { AthleteDataService } from "../../../athlete/athlete-data-service";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import FieldSelectV2 from "../../../common/ui/layoutV2/fields/field-select-v2.vue";
import FormGenericInputTemplateV2 from "../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";

@Component({
  name: "comp-event-action-add-to-cart",
  components: {
    FormGenericInputTemplateV2,
    FieldSelectV2,
    LoadingSpinnerV2,
    UserTypeAhead,
    ButtonsCancelOkConfirm,
  },
})
export default class CompEventActionAddToCart extends Vue {
  @Prop({
    required: true,
  })
  public readonly compEventActionsResult!: ICompEventActionsResult;

  public isLoading: boolean = false;
  public reason: string = "";
  public validationMessage = "";
  public userService = new UserService();
  public userSummary: IUserSummary | null = null;
  public showUserSearch = true;
  public userAthletes: IAthleteUser[] = [];
  public userAthleteSelected: IAthleteUser = {
    id: 0,
    email: "",
    userName: "",
  };
  public userSelectionType: "SELECT" | "SEARCH" = "SELECT";

  public created() {
    // Initialize userSummary now that userService is available
    this.userSummary = this.userService.factoryUserSummary();
    
    if (this.compEventActionsResult.athlete.userAthletes) {
      this.setUserAthletes(this.compEventActionsResult.athlete.userAthletes);
      // this.userAthletes = this.compEventActionsResult.athlete.userAthletes;
      //
      // if (this.userAthletes.length === 1) {
      //   this.onUserSelected(this.userAthletes[0]);
      // }
    } else {
      this.isLoading = true;
      const prom = new AthleteDataService();

      prom
        .read(this.compEventActionsResult.athlete.id)
        .then((resp) => {
          if (resp.errNo === 0) {
            let userAthletes = resp.data.userAthletes;

            userAthletes =
              resp.data.userAthletes && resp.data.userAthletes.length > 0
                ? resp.data.userAthletes
                : [];

            this.setUserAthletes(userAthletes);

            // this.userSelectionType =
            //   this.userAthletes.length > 0 ? "SELECT" : "SEARCH";

            // this.userAthletes = userAthletes;
            //
            // if (userAthletes.length === 1) {
            //   this.userAthleteSelected = userAthletes[0];
            //   this.onUserSelected(userAthletes[0]);
            // }
          }
        })
        .finally(() => {
          this.isLoading = false;
        });
    }
  }

  /**
   * Sets the user athletes list after filtering based on specific criteria and updates related properties.
   *
   * @param {IAthleteUser[]} userAthletesInput - The list of athlete users to be filtered and set.
   * @return {void} - Does not return a value.
   */
  public setUserAthletes(userAthletesInput: IAthleteUser[]) {
    let userAthletes = userAthletesInput.filter((userAthlete) => {
      return userAthlete.id > 0;
    });

    // userAthletes.unshift({
    //   id: 0,
    //   email: "",
    //   userName: "Select User",
    // });

    this.userAthletes = userAthletes;

    this.userSelectionType = this.userAthletes.length > 0 ? "SELECT" : "SEARCH";

    if (userAthletes.length === 1) {
      this.userAthleteSelected = userAthletes[0];
      this.onUserSelected(userAthletes[0]);
    }
  }

  public onUserSelected(userSummary: IUserSummary | IAthleteUser) {
    if (!(userSummary as IUserSummary).displayName) {
      const athleteUser: IAthleteUser = userSummary as IAthleteUser;
      const userSummaryTemp: IUserSummary = {
        displayName: athleteUser.userName,
        email: athleteUser.email,
        id: athleteUser.id,
        login: "",
        name: "",
        niceName: "",
      };
      this.userSummary = userSummaryTemp;
      this.userAthleteSelected = athleteUser;
    } else {
      this.userSummary = userSummary as IUserSummary;
    }

    this.validationMessage = "";
    this.showUserSearch = false;
  }

  public addToCart() {
    this.isLoading = true;

    this.validationMessage = "";

    if (!this.userSummary || this.userSummary.id === 0) {
      this.validationMessage = "Please select a user.";
      this.isLoading = false;
      return;
    }

    new AthleteCompSchedData()
      .addEntryToUsersCart(
        this.compEventActionsResult.compEvent.order.productId,
        this.userSummary.id
      )
      .then((response) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }

        this.$emit("submitted", this.compEventActionsResult.compEvent);
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return {};
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public get getUserName() {
    if (!this.userSummary) {
      return "No user selected";
    }
    return (
      "[" +
      this.userSummary.id +
      "] " +
      this.userSummary.displayName +
      " : " +
      this.userSummary.email +
      " : " +
      this.userSummary.login
    );
  }

  public getUserDisplayName(athleteUser: IAthleteUser): string {
    return (
      athleteUser.id +
      " " +
      [athleteUser.userName, athleteUser.email]
        .filter((value) => {
          return value && value.length > 0;
        })
        .join(", ")
    );
  }

  public get getUserNameCount() {
    if (!this.userAthletes) {
      return 0;
    }
    return this.userAthletes.length;
  }

  public close() {
    this.$emit("close");
  }
}
</script>
