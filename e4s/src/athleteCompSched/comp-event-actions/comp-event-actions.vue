<template>
  <div>
    <FormHeader title="Event Actions" v-on:close="close">
      <i
        slot="icon"
        class="
          material-icons
          e4s-force-img-vert-align-middle
          e4s-force-img--pad-right
        "
        >event_note</i
      >
    </FormHeader>

    <div class="row">
      <div class="input-field col s12 m6 l6">
        <label class="active" for="comp-athlete"> Athlete </label>
        <span
          class="e4s-form-sub-header"
          v-text="getAthlete"
          id="comp-athlete"
        ></span>
      </div>

      <div class="input-field col s12 m6 l6">
        <label class="active" for="comp-name"> Competition </label>
        <span
          class="e4s-form-sub-header"
          v-text="getCompName"
          id="comp-name"
        ></span>
      </div>

      <div class="input-field col s12 m6 l6">
        <label class="active" for="comp-event-name"> Event Name </label>
        <span
          class="e4s-form-sub-header"
          id="comp-event-name"
          v-text="
            compEventActionsResultInternal && compEventActionsResultInternal.compEvent && athleteCompSchedService ? athleteCompSchedService.getCompEventName(
              compEventActionsResultInternal.compEvent
            ) : ''
          "
        ></span>
      </div>

      <div class="input-field col s12 m6 l6">
        <label class="active" for="comp-event-time"> Event Time </label>
        <span
          class="e4s-form-sub-header"
          v-text="getEventDateTime"
          id="comp-event-time"
        ></span>
      </div>

      <div class="input-field col s12 m6 l6">
        <label class="active" for="comp-event-paid"> Event Status </label>
        <span
          class="e4s-form-sub-header"
          v-text="getOrderStatus"
          id="comp-event-paid"
        ></span>
        <span
          v-if="!getBelongsToUser"
          style="margin-left: 8px"
          class="e4s-form-sub-header e4s-info-text--error"
          v-text="'(' + getOrderPaidBy + ')'"
        ></span>
      </div>

      <div class="input-field col s12 m6 l6">
        <label class="active" for="comp-event-value"> Value </label>
        <span
          class="e4s-form-sub-header"
          v-text="
            commonService && compEventActionsResult && compEventActionsResult.compEvent && compEventActionsResult.compEvent.order ? commonService.getAmountAsCurrency(
              compEventActionsResult.compEvent.order.wcLineValue || 0,
              configApp.currency
            ) : ''
          "
          id="comp-event-value"
        ></span>
      </div>

      <div class="input-field col s12 m6 l6">
        <label class="active" for="comp-event-status">
          Competition Entry Status
        </label>
        <span
          class="e4s-form-sub-header"
          :class="getIsEntryOpen ? '' : 'red-text'"
          v-text="getIsEntryOpen ? 'OPEN' : 'CLOSED'"
          id="comp-event-status"
        ></span>
      </div>

      <div
        class="input-field col s12 m6 l6"
        v-if="getHasBuilderPermissionForComp || isAdmin"
      >
        <label class="active" for="comp-event-paid"> Competition Access </label>
        <span class="e4s-form-sub-header"
          >You have
          <span v-text="isAdmin ? 'Admin' : 'Builder'"></span> access.</span
        >
      </div>

      <div class="col s12 m6 l6">
        <!--        <button-->
        <!--          class="e4s-button e4s-button&#45;&#45;green e4s-button&#45;&#45;contact-organiser"-->
        <!--          v-on:click="contactOrganiser"-->
        <!--        >-->
        <!--          Contact Organiser-->
        <!--        </button>-->
        <ButtonGenericV2
          text="Contact Organiser"
          @click="contactOrganiser"
          class="e4s-button--auto"
        />
      </div>

      <div class="input-field col s12 m6 l6" v-if="isAdmin">
        <label class="active" for="comp-event-order-details">
          Order Info (Order# / Date / Prod# / Entry#)
        </label>
        <span
          class="e4s-form-sub-header"
          v-text="getOrderDetails"
          id="comp-event-order-details"
        ></span>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <div v-if="showSection === sections.MAIN">
      <div v-if="!getAreActionsAvailable">
        <div class="row">
          <div class="col s12 m12 l12">
            <div v-if="!getIsEntryOpen">
              The competition is not open for entries.
            </div>
          </div>
        </div>
      </div>

      <div v-if="getAreActionsAvailable">
        <div class="row">
          <!--<switch>-->
          <div class="col s12 m6 l6" v-if="getIsEventPaidFor">
            <div class="comp-event-actions--action-section">
              <div class="row">
                <div class="col s12 m12 l12">
                  <div class="e4s-form-sub-header left">
                    <i
                      class="
                        material-icons
                        e4s-force-img-vert-align-middle
                        e4s-force-img--pad-right
                      "
                      >sync</i
                    >Switch Event
                  </div>
                  <!--                  <button-->
                  <!--                    class="-->
                  <!--                      e4s-button e4s-button&#45;&#45;green e4s-button&#45;&#45;10-wide-->
                  <!--                      right-->
                  <!--                    "-->
                  <!--                    :disabled="!getCanSwitchEvent"-->
                  <!--                    v-on:click="switchEvent"-->
                  <!--                  >-->
                  <!--                    Switch Event-->
                  <!--                  </button>-->
                  <ButtonGenericV2
                    :disabled="!getCanSwitchEvent"
                    text="Switch Event"
                    class="
                      e4s-button--auto
                      comp-event-actions--button-110
                      right
                    "
                    @click="switchEvent"
                  />
                </div>
              </div>

              <div class="e4s-section-padding-separator"></div>

              <div class="row">
                <div class="col s12 m12 l12">
                  <div>
                    Any events that are available to switch to will be shown.
                    Once switched the athlete will have been automatically
                    removed from the original event.
                  </div>
                </div>
              </div>

              <div v-if="!getCanSwitchEvent">
                <div class="e4s-section-padding-separator"></div>

                <div class="row">
                  <div class="col s12 m12 l12">
                    <div class="e4s-bold e4s-info-text--error">
                      Switch is not available, please contact the organiser.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--</switch>-->

          <!--<cancel>-->
          <div class="col s12 m6 l6" v-if="getIsEventPaidFor">
            <div class="comp-event-actions--action-section">
              <div class="row">
                <div class="col s12 m12 l12">
                  <div class="e4s-form-sub-header left">
                    <i
                      class="
                        material-icons
                        e4s-force-img-vert-align-middle
                        e4s-force-img--pad-right
                      "
                      >highlight_off</i
                    >Cancel Entry
                  </div>
                  <!--                  <button-->
                  <!--                    class="-->
                  <!--                      e4s-button e4s-button&#45;&#45;green e4s-button&#45;&#45;10-wide-->
                  <!--                      right-->
                  <!--                    "-->
                  <!--                    :disabled="!getCanCancelEvent"-->
                  <!--                    v-on:click="cancelEvent"-->
                  <!--                  >-->
                  <!--                    Cancel Entry-->
                  <!--                  </button>-->
                  <ButtonGenericV2
                    :disabled="!getCanCancelEvent"
                    text="Cancel Entry"
                    class="
                      e4s-button--auto
                      comp-event-actions--button-110
                      right
                    "
                    @click="cancelEvent"
                  />
                </div>
              </div>

              <div class="e4s-section-padding-separator"></div>

              <div class="row">
                <div class="col s12 m12 l12">
                  <span v-text="getCancelUserMessage"></span>
                </div>
              </div>

              <div v-if="!getIsCancelStillAvailable">
                <div class="e4s-section-padding-separator"></div>

                <div class="row">
                  <div class="col s12 m12 l12">
                    <div class="e4s-bold e4s-info-text--error">
                      Cancel is not available after
                      <span v-text="getCancelsNotPermittedAfter"></span>. Please
                      contact the organiser.
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="!getBelongsToUser">
                <div class="e4s-section-padding-separator"></div>

                <div class="row">
                  <div class="col s12 m12 l12">
                    <div class="e4s-bold e4s-info-text--error">
                      Cancel is not available as paid for by a different user
                      <span
                        style="padding-left: 4px"
                        v-text="getOrderPaidBy"
                      ></span
                      >. Please contact the organiser.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--</cancel>-->

          <!--<set-paid>-->
          <div class="col s12 m6 l6" v-if="getHasBuilderPermissionForComp">
            <div class="comp-event-actions--action-section">
              <div class="row">
                <div class="col s12 m12 l12">
                  <div class="e4s-form-sub-header left">
                    <i
                      class="
                        material-icons
                        e4s-force-img-vert-align-middle
                        e4s-force-img--pad-right
                      "
                      >local_atm</i
                    >Set Paid
                  </div>
                  <!--                  <button-->
                  <!--                    class="-->
                  <!--                      e4s-button e4s-button&#45;&#45;green e4s-button&#45;&#45;10-wide-->
                  <!--                      right-->
                  <!--                    "-->
                  <!--                    v-on:click="showSetPaid"-->
                  <!--                  >-->
                  <!--                    <span-->
                  <!--                      v-text="-->
                  <!--                        compEventActionsResultInternal.compEvent.paid-->
                  <!--                          ? 'Set Not Paid'-->
                  <!--                          : 'Set Paid'-->
                  <!--                      "-->
                  <!--                    ></span>-->
                  <!--                  </button>-->
                  <ButtonGenericV2
                    :disabled="!getCanCancelEvent"
                    :text="
                      compEventActionsResultInternal && compEventActionsResultInternal.compEvent && compEventActionsResultInternal.compEvent.paid
                        ? 'Set Not Paid'
                        : 'Set Paid'
                    "
                    class="
                      e4s-button--auto
                      comp-event-actions--button-110
                      right
                    "
                    @click="showSetPaid"
                  />
                </div>
              </div>

              <div class="e4s-section-padding-separator"></div>

              <div class="row">
                <div class="col s12 m12 l12">
                  <div>Toggle paid status.</div>
                </div>
              </div>

              <div v-if="compEventActionsResultInternal && compEventActionsResultInternal.compEvent && compEventActionsResultInternal.compEvent.paid">
                <div class="e4s-section-padding-separator"></div>

                <div class="row">
                  <div class="col s12 m12 l12">
                    <div class="e4s-bold e4s-info-text--error">
                      Already set as paid
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--</set-paid>-->

          <!--<add-to-users-cart>-->
          <div class="col s12 m6 l6" v-if="getHasBuilderPermissionForComp">
            <div class="comp-event-actions--action-section">
              <div class="row">
                <div class="col s12 m12 l12">
                  <div class="e4s-form-sub-header left">
                    <i
                      class="
                        material-icons
                        e4s-force-img-vert-align-middle
                        e4s-force-img--pad-right
                      "
                      >shopping_cart</i
                    >Add To A User's Cart
                  </div>
                  <!--                  v-if="!compEventActionsResultInternal.compEvent.paid"-->
                  <!--                  <button-->
                  <!--                    class="-->
                  <!--                      e4s-button e4s-button&#45;&#45;green e4s-button&#45;&#45;10-wide-->
                  <!--                      right-->
                  <!--                    "-->
                  <!--                    v-on:click="showAddToUsersCart"-->
                  <!--                  >-->
                  <!--                    Add Cart-->
                  <!--                  </button>-->
                  <ButtonGenericV2
                    :disabled="!getCanCancelEvent"
                    text="Add Cart"
                    class="
                      e4s-button--auto
                      comp-event-actions--button-110
                      right
                    "
                    @click="showAddToUsersCart"
                  />
                </div>
              </div>

              <div class="e4s-section-padding-separator"></div>

              <div class="row">
                <div class="col s12 m12 l12">
                  <div>
                    Add an entry to a specific users cart. E.g. competition
                    entries are closed, but want an athlete entered and still
                    want them to pay. Add an athlete as an organiser, then
                    assign the entry to a given user using this function.
                  </div>
                </div>
              </div>

              <div v-if="compEventActionsResultInternal && compEventActionsResultInternal.compEvent && compEventActionsResultInternal.compEvent.paid">
                <div class="e4s-section-padding-separator"></div>

                <div class="row">
                  <div class="col s12 m12 l12">
                    <div class="e4s-bold e4s-info-text--error">
                      Already set as paid. If you add to a user cart, it will
                      set as not paid.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--</add-to-users-cart>-->
        </div>
      </div>
    </div>

    <div class="comp-event-actions--action-section">
      <div v-if="showSection === sections.SWITCH">
        <!--                getHasBuilderPermissionForComp-->
        <CompEventActionSwitch
          :comp-event="compEventActionsResultInternal && compEventActionsResultInternal.compEvent ? compEventActionsResultInternal.compEvent : null"
          :comp-events="compEventActionsResultInternal && compEventActionsResultInternal.compEvents ? compEventActionsResultInternal.compEvents : []"
          :has-builder-permission-for-comp="getHasBuilderPermissionForComp"
          v-on:cancel="switchCancel"
          v-on:submitted="genericSubmittedAndClose"
        ></CompEventActionSwitch>
      </div>

      <div v-if="showSection === sections.CANCEL">
        <CompEventActionCredRefAdmin
          v-if="isAdmin"
          :comp-event-actions-result="compEventActionsResultInternal"
          v-on:submitted="genericSubmittedAndClose"
          v-on:close="showSection = sections.MAIN"
        ></CompEventActionCredRefAdmin>

        <CompEventActionCredRef
          v-if="!isAdmin && getHasBuilderPermissionForComp"
          :comp-event-actions-result="compEventActionsResultInternal"
          v-on:submitted="genericSubmittedAndClose"
          v-on:close="showSection = sections.MAIN"
        ></CompEventActionCredRef>

        <!--        -->
        <CompEventActionCancel
          v-if="!(isAdmin || getHasBuilderPermissionForComp)"
          :comp-event-actions-result="compEventActionsResultInternal"
          v-on:submitted="genericSubmittedAndClose"
          v-on:close="showSection = sections.MAIN"
        ></CompEventActionCancel>
      </div>

      <div v-if="showSection === sections.SET_PAID">
        <CompEventActionSetPaid
          :comp-event-actions-result="compEventActionsResultInternal"
          v-on:close="showSection = sections.MAIN"
          v-on:submitted="genericSubmittedAndClose"
        ></CompEventActionSetPaid>
      </div>

      <div v-if="showSection === sections.ADD_TO_USERS_CART">
        <CompEventActionAddToCart
          :comp-event-actions-result="compEventActionsResultInternal"
          v-on:close="showSection = sections.MAIN"
          v-on:submitted="genericSubmittedAndClose"
        >
        </CompEventActionAddToCart>
      </div>
    </div>

    <AskOrganiserModal
      v-if="showContactOrganiser"
      :show-contact-organiser="true"
      :selected-competition="
        compEventActionsResultInternal && compEventActionsResultInternal.competitionSummaryPublic ? compEventActionsResultInternal.competitionSummaryPublic : null
      "
      v-on:onClose="showContactOrganiser = false"
    >
    </AskOrganiserModal>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import { CONFIG_STORE_CONST } from "../../config/config-store";
import type { IConfigApp } from "../../config/config-app-models";
import CloseBack from "../../common/ui/close-back.vue";
import EventCard from "../event-card.vue";
import CompEventActionSwitch from "./switch/comp-event-action-switch.vue";
import { CompEventActionsController } from "./comp-event-actions-controller";
import type { ICompEventActionsResult } from "./comp-event-actions-controller";
import { CompetitionService } from "../../competition/competiton-service";
import FormHeader from "../../common/ui/form/header/form-header.vue";
import { format, parse } from "date-fns";
import { AthleteCompSchedService } from "../athletecompsched-service";
import { VueComponent } from "../../typings/vue-ts-component";
import CompEventActionCredRef from "./cancel/comp-event-action-cred-ref.vue";
import { CommonService } from "../../common/common-service";
import { ConfigService } from "../../config/config-service";
import CompEventActionSetPaid from "./set-paid/comp-event-action-set-paid.vue";
import CompEventActionCancel from "./cancel/comp-event-action-cancel.vue";
import CompEventActionAddToCart from "./add-to-cart/comp-event-action-add-to-cart.vue";
import { PaidTypes } from "../../common/common-models";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";

interface ICompEventActionsProps {
  canSwitchEvent: boolean;
  compEventActionsResult: ICompEventActionsResult;
}

@Component({
  name: "comp-event-actions",
  components: {
    ButtonGenericV2,
    CompEventActionAddToCart,
    CompEventActionCancel,
    CompEventActionSetPaid,
    AskOrganiserModal: () => {
      return import(
        /* webpackChunkName: "ask-organiser-modal" */
        "../../competition/askorganiser/ask-organiser-modal.vue"
      );
    },
    CompEventActionCredRef,
    CompEventActionCredRefAdmin: () => {
      return import("./cancel/comp-event-action-cred-ref-admin.vue");
    },
    FormHeader,
    CompEventActionSwitch,
    EventCard,
    CloseBack,
  },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class CompEventActions extends VueComponent<ICompEventActionsProps> {
  public readonly configApp!: IConfigApp;
  public readonly isAdmin!: boolean;

  @Prop({
    default: false,
  })
  public readonly canSwitchEvent!: boolean;

  @Prop({
    required: true,
  })
  public readonly compEventActionsResult!: ICompEventActionsResult;

  public competitionService: CompetitionService | null = null;
  public athleteCompSchedService: AthleteCompSchedService | null = null;
  public commonService: CommonService | null = null;
  public configService: ConfigService | null = null;

  public sections = {
    MAIN: "MAIN",
    SWITCH: "SWITCH",
    CANCEL: "CANCEL",
    CREDIT: "CREDIT",
    SET_PAID: "SET_PAID",
    ADD_TO_USERS_CART: "ADD_TO_USERS_CART",
  };
  public showSection: string = "MAIN";
  public isLoading = false;
  public PREFIX = Math.random().toString(36).substring(2);

  public e4sCreditInput: number = 0;
  public showContactOrganiser = false;

  public compEventActionsResultInternal: ICompEventActionsResult | null = null;
  public compEventActionsController: CompEventActionsController | null = null;

  public requiresParentReloadOnClose = false;

  public created() {
    // Initialize services now that they're available
    this.competitionService = new CompetitionService();
    this.athleteCompSchedService = new AthleteCompSchedService();
    this.commonService = new CommonService();
    this.configService = new ConfigService();
    this.compEventActionsController = new CompEventActionsController();
    
    this.showSection = this.sections.MAIN;
    this.init(this.compEventActionsResult);
  }

  public init(compEventActionsResult: ICompEventActionsResult) {
    this.showSection = this.sections.MAIN;

    const compEventActionsResultInternal = R.clone(compEventActionsResult);

    //  TODO
    // compEventActionsResultInternal.compEvent.order.wcLineValue = 10;

    this.compEventActionsResultInternal = compEventActionsResultInternal;
    if (this.compEventActionsResultInternal && this.compEventActionsResultInternal.compEvent.price) {
      this.e4sCreditInput = this.compEventActionsResultInternal.compEvent.price.curPrice;
    }
    
    // Initialize factory method
    if (!this.compEventActionsResultInternal) {
      this.compEventActionsResultInternal = this.compEventActionsController!.factoryCompEventActionsResult();
    }
  }

  public get getHasBuilderPermissionForComp(): boolean {
    if (!this.configService || !this.compEventActionsResultInternal || !this.compEventActionsResultInternal.competitionSummaryPublic) {
      return false;
    }
    return this.configService.hasBuilderPermissionForComp(
      this.configApp.userInfo,
      this.compEventActionsResultInternal.competitionSummaryPublic.compOrgId,
      this.compEventActionsResultInternal.competitionSummaryPublic.compId
    );
  }

  public get getAreActionsAvailable() {
    return (
      this.getHasBuilderPermissionForComp ||
      (this.getIsEventPaidFor && this.getIsEntryOpen)
    );
  }

  public get getIsEntryOpen() {
    if (!this.competitionService || !this.compEventActionsResultInternal || !this.compEventActionsResultInternal.competitionSummaryPublic) {
      return false;
    }
    return this.competitionService.isEntryOpen(
      this.compEventActionsResultInternal.competitionSummaryPublic
    );
  }

  public get getIsEventPaidFor() {
    if (!this.compEventActionsResultInternal || !this.compEventActionsResultInternal.compEvent) {
      return false;
    }
    return this.compEventActionsResultInternal.compEvent.paid === 1;
  }

  public get getCanSwitchEvent() {
    if (this.getHasBuilderPermissionForComp) {
      return true;
    }
    return (
      this.getIsEventPaidFor && this.getBelongsToUser && this.getIsEntryOpen
    );
  }

  public get getBelongsToUser() {
    if (!this.compEventActionsResultInternal || !this.compEventActionsResultInternal.compEvent || !this.compEventActionsResultInternal.compEvent.user) {
      return false;
    }
    return (
      this.compEventActionsResultInternal.compEvent.user.userId ===
      this.configApp.userInfo.user.id
    );
  }

  public get getCanCancelEvent() {
    if (this.getHasBuilderPermissionForComp) {
      return true;
    }
    if (!this.getIsEventPaidFor) {
      return false;
    }
    if (!this.getIsCancelStillAvailable) {
      return false;
    }
    return this.getBelongsToUser;
  }

  public get getIsCancelStillAvailable() {
    if (!this.competitionService || !this.compEventActionsResultInternal || !this.compEventActionsResultInternal.competitionSummaryPublic) {
      return false;
    }
    return this.competitionService.isCancelStillAvailable(
      this.compEventActionsResultInternal.competitionSummaryPublic
    );
  }

  public get getCancelsNotPermittedAfter(): string {
    if (!this.competitionService || !this.compEventActionsResultInternal || !this.compEventActionsResultInternal.competitionSummaryPublic) {
      return "";
    }
    return format(
      this.competitionService.cancelsNotPermittedAfter(
        this.compEventActionsResultInternal.competitionSummaryPublic
      ),
      "ddd Do MMM HH:mm"
    );
  }

  public cancelEvent() {
    this.showSection = this.sections.CANCEL;
  }

  public switchEvent() {
    this.showSection = this.sections.SWITCH;
  }

  public get getCompName() {
    if (!this.competitionService || !this.compEventActionsResultInternal || !this.compEventActionsResultInternal.competitionSummaryPublic) {
      return "";
    }
    return this.competitionService.getCompetitionTitle(
      this.compEventActionsResultInternal.competitionSummaryPublic
    );
  }

  public get getEventDateTime() {
    if (!this.compEventActionsResultInternal || !this.compEventActionsResultInternal.compEvent) {
      return "";
    }
    const date = format(
      parse(this.compEventActionsResultInternal.compEvent.startdate),
      "Do MMM YY"
    );
    let startTime = format(
      parse(this.compEventActionsResultInternal.compEvent.startdate),
      "HH:mm"
    );
    startTime = startTime === "00:00" ? "TBC" : startTime;
    return date + "@" + startTime;
  }

  public get getAthlete() {
    if (!this.compEventActionsResultInternal || !this.compEventActionsResultInternal.athlete) {
      return "";
    }
    const athlete = this.compEventActionsResultInternal.athlete;
    const club = this.compEventActionsResultInternal.compEvent?.club || "";
    return (
      athlete.firstName +
      " " +
      athlete.surName +
      (club && club.length > 0 ? " (" + club + ")" : "")
    );
  }

  public get getOrderDetails() {
    if (!this.compEventActionsResultInternal || !this.compEventActionsResultInternal.compEvent || !this.compEventActionsResultInternal.compEvent.order) {
      return "";
    }
    const order = this.compEventActionsResultInternal.compEvent.order;
    const orderId = order.orderId;
    const date =
      order.dateOrdered.length === 0
        ? "NA"
        : format(parse(order.dateOrdered), "Do MMM YY");
    const prodId = order.productId;
    return (
      orderId +
      " / " +
      date +
      " / " +
      prodId +
      " / " +
      this.compEventActionsResultInternal.compEvent.entryId
    );
  }

  public get getOrderStatus() {
    if (!this.compEventActionsResultInternal || !this.compEventActionsResultInternal.compEvent) {
      return "";
    }
    return PaidTypes[this.compEventActionsResultInternal.compEvent.paid];
  }

  public get getOrderPaidBy(): string | "" {
    if (!this.compEventActionsResultInternal || !this.compEventActionsResultInternal.compEvent || !this.compEventActionsResultInternal.compEvent.user) {
      return "";
    }
    return this.compEventActionsResultInternal.compEvent.user.userName;
  }

  public switchCancel() {
    this.showSection = this.sections.MAIN;
  }

  public genericSubmittedAndClose() {
    this.showSection = this.sections.MAIN;
    this.requiresParentReloadOnClose = true;
    this.close();
  }

  public creditEvent() {
    this.showSection = this.sections.CREDIT;
  }

  public showSetPaid() {
    this.showSection = this.sections.SET_PAID;
  }

  public showAddToUsersCart() {
    this.showSection = this.sections.ADD_TO_USERS_CART;
  }

  public close() {
    this.showSection = this.sections.MAIN;
    console.log("CompEventActions close");
    this.$emit("close", this.requiresParentReloadOnClose);
  }

  public contactOrganiser() {
    this.showContactOrganiser = true;
  }

  public get getCancelUserMessage() {
    if (!this.compEventActionsResultInternal || !this.compEventActionsResultInternal.compEvent) {
      return "";
    }
    let message =
      "This will cancel the entry and remove the athlete from " +
      (this.compEventActionsResultInternal.compEvent.Name || "") +
      ". This will free up a space for another athlete to enter.";
    if (!(this.isAdmin || this.getHasBuilderPermissionForComp)) {
      message += " No refunds are made with this option.";
    }
    return message;
  }
}
</script>

<style scoped>
.comp-event-actions--button-110 {
  width: 110px;
}
</style>
