<template>
    <div>
        <div class="row">
            <div class="col s12 m12 l12">
                <div class="e4s-form-sub-header left">
                    <i class="material-icons e4s-force-img-vert-align-middle e4s-force-img--pad-right">sync</i>
                    Switch Event: Select the event to switch to
                </div>
            </div>
        </div>

        <div class="e4s-section-padding-separator"></div>

<!--        <div class="row">-->
<!--            <div class="col s6 m4 l3">-->
<!--                <span class="e4s-form-sub-header">Select the event to switch to.</span>-->
<!--            </div>-->
<!--        </div>-->

<!--        <div class="e4s-section-padding-separator"></div>-->

        <div
            v-for="cEvent in compEventsInternal"
            :key="cEvent.ceid"
            :id="cEvent.ceid">

            <CompEventActionsRow
                :class="cEvent.ceid === compEvent.ceid ? 'e4s-card-selected' : ''"
                :comp-event="cEvent"
                :has-builder-permission-for-comp="hasBuilderPermissionForComp"
                :is-disabled="isCompEventDisabled(cEvent)"
                v-on:onEventSelected="onEventSelectedSwitch"
            >
            </CompEventActionsRow>
            <div class="e4s-card-standard-sep"></div>

        </div>

        <div class="e4s-section-padding-separator"></div>

        <div class="row">
            <div class="col s12 m12 l12">
                <button
                    :disabled="isLoading"
                    class="btn waves-effect waves red"
                    v-on:click="cancel"
                >
                    Cancel
                </button>

                <div class="right" v-if="switchEventShowConfirm">
                    Are you sure you would like to switch event?
                    <button
                        :disabled="isLoading"
                        class="btn waves-effect waves green"
                        v-on:click="switchEventConfirmed"
                    >
                        Confirm
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue"
import Component from "vue-class-component"
import {Prop, Watch} from "vue-property-decorator"
import {IAthleteCompSchedRuleEvent, RULE_TYPE} from "../../athletecompsched-models"
import {AthleteCompSchedService} from "../../athletecompsched-service"
import * as R from "ramda"
import {AthleteCompSchedData} from "../../athletecompsched-data"
import {IServerGenericResponse} from "../../../common/common-models"
import {messageDispatchHelper} from "../../../user-message/user-message-store"
import {USER_MESSAGE_LEVEL} from "../../../user-message/user-message-models"
import CompEventActionsRow from "./comp-event-actions-row.vue"

const athleteCompSchedService: AthleteCompSchedService = new AthleteCompSchedService();

@Component({
    name: "comp-event-action-switch",
    components: {CompEventActionsRow}
})
export default class CompEventActionSwitch extends Vue {
    @Prop({
        default: () => {
            return athleteCompSchedService.factoryAthleteCompSchedRuleEvent();
        }
    })
    public readonly compEvent!: IAthleteCompSchedRuleEvent;

    @Prop({
        default: () => {
            return [];
        }
    })
    public readonly compEvents!: IAthleteCompSchedRuleEvent[];

    @Prop({
        default: false
    })
    public readonly hasBuilderPermissionForComp!: boolean;

    public compEventsInternal: IAthleteCompSchedRuleEvent[] = [];
    public switchEventShowConfirm = false;
    public athleteCompSchedService = athleteCompSchedService;
    public switchCompEvent: IAthleteCompSchedRuleEvent | null = null;
    public isLoading = false;

    public created() {
        // Initialize switchCompEvent now that athleteCompSchedService is available
        this.switchCompEvent = this.athleteCompSchedService.factoryAthleteCompSchedRuleEvent();
        // this.compEventsInternal = R.clone(this.compEvents);
        this.reProcess();
    }

    @Watch("compEvents")
    public onCompEventsChanged(newValue: IAthleteCompSchedRuleEvent[]) {
        // this.compEventsInternal = R.clone(newValue);
        this.reProcess();
    }

    public reProcess() {

        let compEvents = R.clone(this.compEvents)
        .map( (compEvent) => {

            if (compEvent.ceid === this.compEvent.ceid) {
                compEvent.userSelected = true;
                compEvent.ruleMessage = "Event to switch from";
                compEvent.ruleIsDisabledBy = true;

                // return compEvent;
            }
            // else {
            //     if (this.hasBuilderPermissionForComp) {
            //         compEvent.userSelected = false;
            //         compEvent.ruleMessage = "";
            //         compEvent.ruleIsDisabledBy = false;
            //     }
            // }

            return compEvent;
        })

        // compEvents = this.athleteCompSchedService.processEventsNew()

        this.compEventsInternal = compEvents;
    }

    public isCompEventDisabled(compEvent: IAthleteCompSchedRuleEvent): boolean {

        //  Event we are switching from
        if (compEvent.ceid === this.compEvent.ceid) {
            return true;
        }

        //  another event already selected
        if (compEvent.order.productId > 0) {
            return true;
        }

        //  can select any other event
        if (this.hasBuilderPermissionForComp) {
            return false;
        }

        if (compEvent.userEventAction) {
            return true;
        }
        if (compEvent.userSelected) {
            return true;
        }
        if (compEvent.ruleIsDisabledBy && compEvent.ruleType!== RULE_TYPE.COMP) {
            return true;
        }
        // if (compEvent.ruleIsDisabledBy === false && compEvent.userSelected === false) {
        //     return "<input type='checkbox' /><span></span>";
        // }
        return false;
    }

    public onEventSelectedSwitch(cEvent: IAthleteCompSchedRuleEvent) {
        console.log("CompEventActionSwitch.onEventSelectedSwitch()...");
        if (cEvent.ceid === this.compEvent.ceid) {
            //  Selected same event as currently selected.
            console.log("CompEventActionSwitch.onEventSelectedSwitch()...Selected same event as currently selected");
            return;
        }

        this.compEventsInternal = this.compEventsInternal
            .map( (compEventMap) => {
                if (cEvent.ceid === this.compEvent.ceid) {
                    //  Selected same event as currently selected.
                    return compEventMap;
                }
                if (cEvent.ceid === compEventMap.ceid) {
                    //  User has selected this one.
                    compEventMap.userSelected = true;
                    return cEvent;
                } else {
                    //  set to false
                    compEventMap.userSelected = false;
                    return compEventMap;
                }
            });

        console.log("CompEventActionSwitch.onEventSelectedSwitch()...show confirm");
        this.switchCompEvent = R.clone(cEvent);
        this.switchEventShowConfirm = true;

    }

    public switchEventConfirmed() {
        if (!this.switchCompEvent) {
            console.warn('CompEventActionSwitch: switchCompEvent not available for confirmation');
            return;
        }

        const eventTarget: IAthleteCompSchedRuleEvent = this.switchCompEvent;
        const athleteCompSchedData: AthleteCompSchedData = new AthleteCompSchedData();
        this.isLoading = true;
        athleteCompSchedData.switchEvent(this.compEvent.entryId, eventTarget.ceid)
            .then( (response: IServerGenericResponse) => {
                if (response.errNo > 0) {
                    messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
                    return;
                }
                // messageDispatchHelper("Event Switched", USER_MESSAGE_LEVEL.INFO.toString());
                // this.reloadUserSchedule();
                // this.showSection = this.sections.MAIN;
                // this.close();
                this.$emit("submitted");
                return;
            })
            .catch((error) => {
                messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
                return;
            })
            .finally(() => {
                this.isLoading = false;
            });

    }

    public cancel() {
        this.$emit("cancel");
    }
}
</script>
