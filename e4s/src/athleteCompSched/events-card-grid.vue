<template>
  <div>
    <!--Schedule Mask-->
    <!--    <E4sModal v-show="getIsLoading">-->
    <!--      <div slot="header"></div>-->

    <!--      <div slot="body">-->
    <!--        Loading...-->
    <!--        <loading-spinner></loading-spinner>-->
    <!--      </div>-->

    <!--      <div slot="buttons"></div>-->
    <!--    </E4sModal>-->
    <LoadingSpinnerV2
      v-if="getIsLoading"
      loading-message="Loading athlete selection..."
    />
    <!--/Schedule Mask-->
    <sched-info
      v-if="showSchedInfo"
      class="e4s-card-v1"
      v-bind:schedInfo="getSchedInfo"
    >
      <div slot="extra">
        <div class="row" v-if="anySecondaryAthleteItems">
          <div class="col s12 m12 l12">
            <span class="e4s-bold"
              >Some items in the
              <a href="#" v-on:click.prevent="openShop">Shop</a> are only
              available when an eligible athlete is selected.</span
            >
          </div>
        </div>

        <!--        <button-->
        <!--          class="btn waves-effect waves green"-->
        <!--          v-on:click="showContactOrganiser = true"-->
        <!--        >-->
        <!--          Contact Organiser-->
        <!--        </button>-->
        <ButtonGenericV2
          class="e4s-button--auto"
          text="Contact Organiser"
          @click="showContactOrganiser = true"
        />
      </div>
    </sched-info>

    <div class="sched-info" v-if="showNoAthlete">
      <div>
        <i class="material-icons e4s-icon">info_outline</i>
        <div class="e4s-force-inline-block event-card-title">
          Please select athlete
        </div>
      </div>

      <div>Event schedules will only display when an athlete is selected.</div>
      <div class="e4s-card-standard-sep"></div>

      <div v-if="getContactInfo.length > 0">
        <span class="e4s-bold">Organiser Contact: </span
        ><span v-text="getContactInfo"></span>
      </div>

      <div class="e4s-flex-row">
        <ButtonGenericV2
          class="e4s-flex-row--end e4s-button--auto"
          text="Contact Organiser"
          @click="showContactOrganiser = true"
        />
      </div>

      <!--      <button-->
      <!--        class="btn waves-effect waves green"-->
      <!--        v-on:click="showContactOrganiser = true"-->
      <!--      >-->
      <!--        Contact Organiser-->
      <!--      </button>-->
    </div>

    <div v-if="showNoIndivEventsForAthlete" class="no-events-for-athlete">
      <SchedInfoDetail
        v-if="getHasAthleteRegExpired"
        class="events-card-grid--sched-info"
        :sched-info-detail="getRegExpiredSchedInfoDetail"
      />

      <SchedInfoDetail
        v-if="getUnRegAthleteInRegOnlyComp"
        class="events-card-grid--sched-info"
        :sched-info-detail="{
          title: 'Registered Athletes Only',
          body: '<div>Competition only open to Active Registered athletes.</div>',
        }"
      />

      <!--      <UserTeamAccess-->
      <!--        :in-team-section="false"-->
      <!--        :user-info="userInfo"-->
      <!--        :competition="selectedCompetition"-->
      <!--        :user-entity="userEntity"-->
      <!--        :event-team-headers="eventTeamHeaders"-->
      <!--        v-on:submitted="selfServiceSubmitted"-->
      <!--      />-->

      <div v-if="!getHasAthleteRegExpired">
        <div
          v-if="selectedCompetition.options.athleteSecurity.clubs.length > 0"
        >
          <CompRestricted
            :athlete-security="selectedCompetition.options.athleteSecurity"
            :athlete="selectedAthlete"
          ></CompRestricted>
          <div class="e4s-section-padding-separator"></div>
        </div>

        <p>
          There are no individual events for
          <span
            v-text="selectedAthlete.firstName + ' ' + selectedAthlete.surName"
          ></span>
          at this competition. Please contact the
          <a href="#" v-on:click.prevent="showContactOrganiser = true"
            >Competition organiser</a
          >
          if there is an issue with the available events.
        </p>

        <p
          v-if="getHasNoEntryReason"
          v-text="selectedAthlete.options.noEntryReason"
        ></p>

        <div v-if="!getHasNoEntryReason">
          <p>
            Please check the schedule for eligible genders/age groups.
            <a target="_blank" :href="getScheduleUrl">Open Schedule</a>
          </p>

          <p v-if="selectedCompetition.teamEvents">
            If entering a team event, please use the Team Events section.
          </p>
        </div>
      </div>
    </div>

    <div class="row" v-if="getDoAnyEventsRequirePb">
      <div class="col s12 m12 l12">
        <InfoSectionV2 style="margin: 8px 0">
          <span>
            The organiser requires a performance to be entered before you can
            enter to assist with seeding. Once entered, the system will allow
            entry
          </span>
        </InfoSectionV2>
        <!--        <InfoSectionV2 info-type="info">-->
        <!--          <span slot="default">        The organiser requires a performance to be entered before you can enter to assist with seeding.-->
        <!--        Once entered, the system will allow entry</span>-->
        <!--        </InfoSectionV2>-->
      </div>
    </div>

    <div
      v-for="compEvent in compEventsInternal"
      :key="compEvent.ceid"
      :id="compEvent.ceid.toString()"
    >
      <div v-if="!hideThisEvent(compEvent)">
        <event-card
          :comp-event-prop="compEvent"
          :comp-events-prop="compEventsInternal"
          :do-any-events-require-pb="getDoAnyEventsRequirePb"
          v-on:onEventSelected="onEventSelected"
          v-on:cancelE4SEvent="cancelE4SEvent"
          v-on:onEditPb="onEditPb"
          v-on:showCompEventActions="doCompEventActions"
        >
        </event-card>
        <div class="e4s-card-standard-sep"></div>
      </div>
    </div>

    <E4sEventCancel
      v-if="showE4sEventCancelDialog"
      :comp-event-prop="e4sEventToCancel"
      v-on:onSubmitted="cancelE4sSubmitted"
      v-on:cancelDialog="cancelE4sCancelDialog"
    >
    </E4sEventCancel>

    <E4sModal
      css-class="e4s-modal-container--full-size"
      :is-full-size-form="true"
      v-show="showContactOrganiser"
    >
      <div slot="header"></div>

      <StandardForm title="Ask Organiser" slot="body">
        <AskOrganiserForm
          style="padding: 1rem"
          slot="form-content"
          :comp-id="selectedCompetition.id"
          :competition="selectedCompetition"
          v-on:onClose="showContactOrganiser = false"
        >
        </AskOrganiserForm>
      </StandardForm>

      <div slot="buttons"></div>
    </E4sModal>

    <ModalV2
      :is-full-screen="$mq === VUE_MQ_SIZES.MOBILE.name"
      v-if="isEditingPb"
      :always-show-header-blank="true"
    >
      <EditPbV3
        slot="body"
        style="margin: 8px"
        :edit-pb-v3-input-prop="editPbV3InputProp"
        :edit-pb-options="editPbV3Options"
        v-on:cancel="isEditingPb = false"
        v-on:submitRoute="pbSaved"
      />
    </ModalV2>

    <E4sModal
      css-class="e4s-modal-container--full-size"
      :is-full-size-form="true"
      v-if="actionsCompEventShow"
    >
      <div slot="header"></div>

      <StandardForm title="Event Actions" slot="body">
        <CompEventActionsWrapperSchedule
          style="padding: 1rem"
          slot="form-content"
          :comp-id="selectedCompetition.id"
          :comp-event="compEventEdit"
          :compEvents="compEventsInternal"
          :athlete="selectedAthlete"
          v-on:close="compEventActionsClose"
          v-on:contactOrganiser="contactOrganiserFromActions"
        ></CompEventActionsWrapperSchedule>
      </StandardForm>

      <div slot="buttons"></div>
    </E4sModal>

    <E4sModalSimple
      css-class="e4s-modal-container--full-size"
      v-if="showOverSubscribed"
    >
      <StandardForm title="Over Subscribed" slot="body">
        <div slot="form-content">
          <SubscriptionMessage
            :comp-event="compEventEdit"
            :selected-competition="selectedCompetition"
            :selected-athlete="selectedAthlete"
            :config-app="configApp"
            v-on:continue="subscriptionContinue"
            v-on:cancel="subscriptionCancel"
          ></SubscriptionMessage>
          <div slot="buttons"></div>
        </div>
      </StandardForm>
    </E4sModalSimple>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Watch } from "vue-property-decorator";
import type {
  IAthlete,
  IAthletePb,
  IAthleteSummary,
} from "../athlete/athlete-models";
import type { IObjectKey } from "../common/common-models";
import type { IEntryStoreState } from "../entry/entry-store";
import { ENTRY_STORE_CONST } from "../entry/entry-store";
import type {
  IUserMessage,
} from "../user-message/user-message-models";
import { USER_MESSAGE_STORE_CONST } from "../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../user-message/user-message-models";
import type {
  IAthleteCompSchedResponse,
  IAthleteCompSchedRuleEvent,
  ISchedInfo,
  ISchedInfoDetail,
} from "./athletecompsched-models";
import { AthleteCompSchedService } from "./athletecompsched-service";
import EventCard from "./event-card.vue";
import { PBService } from "./pb-service";
import SchedInfo from "./sched-info/sched-info.vue";
import {
  ATH_COMP_SCHED_STORE_CONST,
  IAthCompSchedStoreState,
} from "./store/athleteCompSched-store";
import { AthleteService } from "../athlete/athlete-service";
import { CompetitionService } from "../competition/competiton-service";
import type { ICompetitionInfo } from "../competition/competition-models";
import type { IContact } from "../builder/builder-models";
import { CONFIG } from "../common/config";
import E4sEventCancel from "./e4s-event-cancel/e4s-event-cancel.vue";
import E4sModal from "../common/ui/e4s-modal.vue";
import CompRestricted from "../competition/restricted/comp-restricted.vue";
import { AthleteController } from "../athlete/athlete-controller";
import { VUE_MQ_SIZES } from "..";
import { mapGetters, mapState } from "vuex";
import { SECONDARY_CUST_STORE_CONST } from "../secondary/cust/secondary-cust-store";
import type { ISecondaryCustStoreState } from "../secondary/cust/secondary-cust-store";
import E4sModalSimple from "../common/ui/modal/e4s-modal-simple.vue";
import SubscriptionMessage from "./subscription/subscription-message.vue";
import { CONFIG_STORE_CONST } from "../config/config-store";
import type { IConfigStoreState } from "../config/config-store";
import type { IConfigApp, IEntity, IUserInfo } from "../config/config-app-models";
import {
  CompEventActionsController,
  ICompEventActionsResult,
} from "./comp-event-actions/comp-event-actions-controller";
import CompEventActionsWrapper from "./comp-event-actions/comp-event-actions-wrapper.vue";
import CompEventActionsWrapperSchedule from "./comp-event-actions/comp-event-actions-wrapper-schedule.vue";
import SchedInfoDetail from "./sched-info/sched-info-detail.vue";
import { format, parse } from "date-fns";
import AthleteRegistrationAlert from "./AthleteRegistrationAlert.vue";
import ButtonE4s from "../common/ui/buttons/button-e4s.vue";
import EditPbV3 from "./pb/v3/EditPbV3.vue";
import type {
  EditPbV3InputProp,
  EditPbV3Options,
  IPbEditV3RouteOutput,
} from "./pb/v3/edit-pb-v3-models";
import ModalV2 from "../common/ui/layoutV2/modal/modal-v2.vue";
import UserTeamAccess from "./comp-event-teams/user-team-access/user-team-access.vue";
import {
  COMP_EVENT_TEAMS_STORE_CONST,
} from "./comp-event-teams/comp-event-store";
import type { ICompEventTeamsStoreState } from "./comp-event-teams/comp-event-store";
import type { IEventTeamHeader } from "./comp-event-teams/event-teams-models";
import { simpleClone } from "../common/common-service-utils";
import {
  factoryEditPbV3InputProp,
  factoryEditPbV3Options,
} from "./pb/v3/pb-service-v3";
import InfoSectionV2 from "../common/ui/layoutV2/info-section-v2.vue";
import LoadingSpinnerV2 from "../common/ui/loading-spinner-v2.vue";
import ButtonGenericV2 from "../common/ui/layoutV2/buttons/button-generic-v2.vue";


@Component({
  name: "events-card-grid",
  components: {
    ButtonGenericV2,
    LoadingSpinnerV2,
    InfoSectionV2,
    UserTeamAccess,
    ModalV2,
    EditPbV3,
    ButtonE4s,
    AthleteRegistrationAlert,
    SchedInfoDetail,
    CompEventActionsWrapperSchedule,
    CompEventActionsWrapper,
    SubscriptionMessage,
    E4sModalSimple,
    CompEventActions: () => {
      return import("./comp-event-actions/comp-event-actions.vue");
    },
    CompRestricted,
    StandardForm: () => {
      return import("../common/ui/standard-form/standard-form.vue");
    },
    AskOrganiserForm: () => {
      return import("../competition/askorganiser/ask-organiser-form.vue");
    },
    E4sModal,
    E4sEventCancel,
    "event-card": EventCard,
    "sched-info": SchedInfo,
  },
  computed: {
    ...mapGetters({
      anySecondaryAthleteItems:
        SECONDARY_CUST_STORE_CONST.SECONDARY_CUST_CONST_MODULE_NAME +
        "/" +
        SECONDARY_CUST_STORE_CONST.SECONDARY_CUST_GETTER_ANY_ATHLETE_ITEMS,
    }),
    ...mapState(SECONDARY_CUST_STORE_CONST.SECONDARY_CUST_CONST_MODULE_NAME, {
      secondaryCustStoreState: (state: any) => state,
    }),
    ...mapState(ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME, {
      eventsSelected: (state: any) => state.eventsSelected,
      eventsProcessed: (state: any) =>
        state.eventsProcessed,
      isLoading: (state: any) => state.eventsLoading,
      eventsServerResponse: (state: any) =>
        state.eventsServerResponse,
    }),
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      selectedAthlete: (state: any) =>
        state.entryForm.selectedAthlete,
      selectedCompetition: (state: any) =>
        state.entryForm.selectedCompetition,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
      userInfo: (state: any) => state.configApp.userInfo,
      userEntity: (state: any) => state.userEntity,
    }),
    ...mapState(
      COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME,
      {
        eventTeamHeaders: (state: any) =>
          state.eventTeamHeaders,
        eventTeamHeadersLoading: (state: any) =>
          state.eventTeamHeadersLoading,
      }
    ),
  },
})
export default class EventsCardGrid extends Vue {
  public readonly secondaryCustStoreState!: ISecondaryCustStoreState;
  public readonly anySecondaryAthleteItems!: boolean;
  public readonly isLoading!: boolean;
  public readonly eventsSelected!: IAthleteCompSchedRuleEvent[];
  public readonly eventsProcessed!: IAthleteCompSchedRuleEvent[];
  public readonly selectedAthlete!: IAthlete;
  public readonly selectedCompetition!: ICompetitionInfo;
  public readonly eventsServerResponse!: IAthleteCompSchedResponse;
  public readonly configApp!: IConfigApp;
  public readonly userInfo: IUserInfo;
  public readonly userEntity: IEntity;

  public readonly eventTeamHeaders: IEventTeamHeader[];
  public readonly eventTeamHeadersLoading: boolean;

  public $mq: any;
  public VUE_MQ_SIZES = VUE_MQ_SIZES;

  public selectedEvent: IAthleteCompSchedRuleEvent = {} as IAthleteCompSchedRuleEvent;

  public pbService: PBService | null = null;
  public athleteCompSchedService: AthleteCompSchedService | null = null;
  public athleteController: AthleteController | null = null;
  public competitionService: CompetitionService | null = null;

  public pbUserEntry: string = "";
  public pbEditMessage: string = "";
  public eventHelperText: string = "";
  public isEditingPb: boolean = false;
  public compEventPb: IAthleteCompSchedRuleEvent = {} as IAthleteCompSchedRuleEvent;
  public editPbV3InputProp: EditPbV3InputProp = {} as EditPbV3InputProp;
  public editPbV3Options: EditPbV3Options = {} as EditPbV3Options;

  public athletePb: IAthletePb = {} as IAthletePb;

  public selectedAsObjectByCeid: IObjectKey = {};

  public showE4sEventCancelDialog: boolean = false;
  public e4sEventToCancel: IAthleteCompSchedRuleEvent = {} as IAthleteCompSchedRuleEvent;

  public showContactOrganiser: boolean = false;

  public showOverSubscribed = false;
  public compEventEdit: IAthleteCompSchedRuleEvent = {} as IAthleteCompSchedRuleEvent;

  public compEventsInternal: IAthleteCompSchedRuleEvent[] = [];
  public isLoadingInternal: boolean = false;
  public edited: boolean = false;

  public actionsCompEventShow = false;
  public compEventActionsController: CompEventActionsController | null = null;
  public compEventActionsResult: ICompEventActionsResult = {} as ICompEventActionsResult;

  public hasAthleteRegExpiredForCompetition = false;
  public registrationExpiredUserConfirmed = false;

  public created() {
    // Initialize services
    const athleteService = new AthleteService();
    this.competitionService = new CompetitionService();
    this.pbService = new PBService();
    this.athleteCompSchedService = new AthleteCompSchedService();
    this.athleteController = new AthleteController();
    this.compEventActionsController = new CompEventActionsController();
    
    // Initialize properties
    this.selectedEvent = {} as IAthleteCompSchedRuleEvent;
    this.compEventPb = this.athleteCompSchedService.factoryAthleteCompSchedRuleEvent();
    this.editPbV3InputProp = factoryEditPbV3InputProp();
    this.editPbV3Options = factoryEditPbV3Options();
    this.athletePb = athleteService.factoryAthletePb();
    this.e4sEventToCancel = this.athleteCompSchedService.factoryAthleteCompSchedRuleEvent();
    this.compEventEdit = this.athleteCompSchedService.factoryAthleteCompSchedRuleEvent();
    this.compEventActionsResult = this.compEventActionsController.factoryCompEventActionsResult();
    
    this.init();
  }

  public init() {
    this.compEventsInternal = R.clone(this.eventsProcessed);
  }

  @Watch("isLoading")
  public onIsLoadingChanged(newValue: boolean) {
    console.log("..............EventsCardGrid.isLoading: " + newValue);
    if (newValue) {
      this.showLoadingOverlay();
    } else {
      this.hideOverlay();
    }
  }

  @Watch("compEvents")
  public onEventsChanged(
    newValue: IAthleteCompSchedRuleEvent[],
    oldValue: IAthleteCompSchedRuleEvent[]
  ) {
    console.log(
      "..............EventsCardGrid.compEvents watch: " + newValue.length
    );
    this.selectedAsObjectByCeid =
      this.athleteCompSchedService ? this.athleteCompSchedService.getSelectedAsObjectByCeid(newValue) : {};
  }

  @Watch("eventsProcessed")
  public onEventsProcessedChanged(
    newValue: IAthleteCompSchedRuleEvent[],
    oldValue: IAthleteCompSchedRuleEvent[]
  ) {
    console.log(
      "..............EventsCardGrid.eventsProcessed watch: " + newValue.length
    );
    this.compEventsInternal = R.clone(newValue);
    this.selectedAsObjectByCeid =
      this.athleteCompSchedService ? this.athleteCompSchedService.getSelectedAsObjectByCeid(newValue) : {};
  }

  @Watch("selectedAthlete")
  public onSelectedAthleteChanged(athleteNew: IAthlete, athleteOld: IAthlete) {
    console.log(
      "EventsCardGrid. selectedAthlete watch",
      athleteNew,
      athleteOld
    );
    if (athleteNew.id === athleteOld.id) {
      console.log("selectedAthleteChanged...same athlete...no need to reload.");
      return;
    }
    this.compEventsInternal = [];
    this.registrationExpiredUserConfirmed = false;

    console.warn(
      "selectedAthleteChanged...this.selectedCompetition.",
      this.selectedCompetition
    );

    console.warn(
      "selectedAthleteChanged...this.selectedCompetition.options.",
      this.selectedCompetition.options
    );

    if (!this.selectedCompetition.options) {
      console.warn(
        "selectedAthleteChanged...this.selectedCompetition.options is null...exiting."
      );
      return;
    }

    if (!this.selectedCompetition.options.allowExpiredRegistration) {
      //  If do NOT allow expired, then follow normal procedure.
      this.hasAthleteRegExpiredForCompetition = false;
    }
    this.hasAthleteRegExpiredForCompetition =
      this.athleteCompSchedService ? this.athleteCompSchedService.hasAthleteRegExpiredForCompetition(
        this.selectedAthlete,
        this.selectedCompetition
      ) : false;
    this.getAthleteCompSched(this.selectedCompetition.id, athleteNew.id);
  }

  public get getIsLoading() {
    return this.isLoading || this.isLoadingInternal;
  }

  public getAthleteCompSched(competitionId: number, athleteId: number) {
    if (
      competitionId === undefined ||
      competitionId === 0 ||
      athleteId === undefined ||
      athleteId === 0
    ) {
      return;
    }
    if (this.edited) {
      //  TODO reloading, you will lose any current changes.
      //  e.g. this.userMessage()...which will allow user to continue, set edited = false;
    }
    this.$store.dispatch(
      ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_INITIALISE_EVENTS
    );
  }

  public showLoadingOverlay() {
    console.log("showLoadingOverlay");
  }

  public hideOverlay() {
    console.log("hideOverlay");
  }

  public rowSelected(event: any) {
    console.log("CARD CLICKED", event);
    // this.$emit("onSelectedEventGrid", event);
  }

  public onEventSelected(compEvent: IAthleteCompSchedRuleEvent) {
    console.log("CELL CLICKED");

    this.edited = true; //  If user browses away, detect here.
    this.compEventEdit = R.clone(compEvent);

    const isEventAlreadySelected = compEvent.userSelected;

    if (this.athleteCompSchedService && !this.athleteCompSchedService.isEventAvailableForClicking(compEvent)) {
      console.log(
        "onEventSelected()" +
          compEvent.ceid +
          " - " +
          compEvent.eventid +
          "...CAN'T"
      );
      return;
    }
    console.log(
      "onEventSelected()" +
        compEvent.ceid +
        " - " +
        compEvent.eventid +
        "...CAN...userSelected: " +
        compEvent.userSelected
    );

    if (
      this.selectedCompetition.options.subscription?.enabled &&
      !isEventAlreadySelected &&
      this.athleteCompSchedService && this.athleteCompSchedService.isEventOverSubscribed(compEvent)
    ) {
      //  We can do this from schedule sent up.  If any changes to schedule
      //  happen while user "sits" at their screen, response will indicate this.
      this.showOverSubscribed = true;
      return;
    }

    this.proceedWithSelection(compEvent);

    // this.$emit("onSelectedEventGrid", event);
  }

  public get getHasNoEntryReason() {
    return this.selectedAthlete.options.noEntryReason.length > 0;
  }

  public onPbSelected(event: IAthleteCompSchedRuleEvent) {
    console.log("CELL CLICKED");

    if (this.athleteCompSchedService && this.athleteCompSchedService.hasSecondarySpend(event)) {
      return;
    }

    this.$store.dispatch(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_ACTIONS_GET_ATHLETE_MULTI_PB,
      {
        event,
      }
    );

    if (event.multiid && event.multiid > 0) {
      this.$store.commit(
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
          "/" +
          ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_ATHLETE_MULTI_PB_MODAL_SHOW,
        true
      );
    }
  }

  public onRowClicked(event: IAthleteCompSchedRuleEvent) {
    console.log("ROW CLICKED");
    //  const event: IAthleteCompSchedRuleEvent = rowClickedEvent.data;
    this.$store.commit(
      ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_MUTATIONS_CURRENT_SELECTED_EVENT,
      event
    );
    this.eventHelperText = "";
    if (event.eoptions && event.eoptions.helpText) {
      this.eventHelperText = event.eoptions.helpText + " Event price";
      this.$store.commit(
        USER_MESSAGE_STORE_CONST.USER_MESSAGE_CONST_MODULE_NAME +
          "/" +
          USER_MESSAGE_STORE_CONST.USER_MESSAGE_MUTATIONS_ADD_MESSAGE,
        {
          level: USER_MESSAGE_LEVEL.INFO,
          message: event.eoptions.helpText + " Event price",
        } as IUserMessage
      );
    }
  }

  public hideThisEvent(event: IAthleteCompSchedRuleEvent): boolean {
    return this.athleteCompSchedService ? this.athleteCompSchedService.hideThisEvent(
      this.selectedAsObjectByCeid,
      event
    ) : false;
  }

  public get getSchedInfo(): ISchedInfo {
    const schedInfo = {
      ...(this.athleteCompSchedService ? this.athleteCompSchedService.factorySchedInfo() : {}),
      ...R.clone(this.eventsServerResponse.schedInfo),
    };

    if (this.selectedAthlete.id > 0) {
      schedInfo.title =
        "Schedule information for: " +
        this.selectedAthlete.firstName +
        " " +
        this.selectedAthlete.surName;
    }

    const selectedCompetition = this.selectedCompetition;

    /*
    // if (
    //   selectedCompetition.options.allowAdd.registered &&
    //   !selectedCompetition.options.allowAdd.unregistered
    // ) {
    if (this.getHasAthleteRegExpired) {
      const regExpiry = this.selectedAthlete.activeEndDate;
      // const dateOfComp = selectedCompetition.date;

      // if (isAfter(parse(dateOfComp), parse(regExpiry))) {
      const regExpiryDetail: ISchedInfoDetail =
        this.athleteCompSchedService.factorySchedInfoDetail();
      regExpiryDetail.title = "Athlete registration expired: " + regExpiry;
      regExpiryDetail.body =
        "This competition only allows entry with a valid registration. Please ensure " +
        "athlete registration is renewed with athletics organisation.";

      schedInfo.schedInfoDetails.push(regExpiryDetail);
    }
    */

    if (
      this.athleteCompSchedService && this.athleteCompSchedService.areAnySubscriptionEvents(
        this.eventsProcessed
      )
    ) {
      const subscriptionDetailContact: ISchedInfoDetail =
        this.athleteCompSchedService ? this.athleteCompSchedService.factorySchedInfoDetail() : { title: "", body: "" };
      subscriptionDetailContact.title = "Max Athlete Entry:";
      subscriptionDetailContact.body =
        "Some events have max entry limits, these must be paid for within " +
        this.configApp.options.cartTimeLimit +
        "mins to guarantee place.";
      schedInfo.schedInfoDetails.push(subscriptionDetailContact);
    }

    if (this.competitionService && this.competitionService.hasContactInfo(selectedCompetition)) {
      const contact: IContact = selectedCompetition.options.contact;
      const schedInfoDetailContact: ISchedInfoDetail =
        this.athleteCompSchedService ? this.athleteCompSchedService.factorySchedInfoDetail() : { title: "", body: "" };

      const getContactInfo = (cont: IContact) => {
        const info: string[] = [];
        if (cont.userName && cont.userName.length > 0) {
          info.push(cont.userName);
        }

        if (cont.visible) {
          if (cont.tel && cont.tel.length > 0) {
            info.push(cont.tel);
          }

          if (cont.email && cont.email.length > 0) {
            info.push(cont.email);
          }
        }

        return info.join(", ");
      };

      schedInfoDetailContact.title = "Organiser Contact:";
      schedInfoDetailContact.body = getContactInfo(contact);

      schedInfo.schedInfoDetails.unshift(schedInfoDetailContact);
    }

    return schedInfo;
  }

  public get getContactInfo() {
    const selectedCompetition = this.selectedCompetition;
    if (this.competitionService && this.competitionService.hasContactInfo(selectedCompetition)) {
      const contact: IContact = selectedCompetition.options.contact;

      const getContactInfo = (cont: IContact) => {
        const info: string[] = [];
        if (cont.userName && cont.userName.length > 0) {
          info.push(cont.userName);
        }

        if (cont.visible) {
          if (cont.tel && cont.tel.length > 0) {
            info.push(cont.tel);
          }

          if (cont.email && cont.email.length > 0) {
            info.push(cont.email);
          }
        }

        return info.join(", ");
      };
      return getContactInfo(contact);
    }
    return "";
  }

  public get getHasAthleteRegExpired(): boolean {
    return this.athleteCompSchedService ? this.athleteCompSchedService.hasAthleteRegExpiredForCompetition(
      this.selectedAthlete,
      this.selectedCompetition
    ) : false;
  }

  public get getRegExpiredSchedInfoDetail(): ISchedInfoDetail {
    const athlete = this.selectedAthlete;

    if (athlete.id === 0) {
      return { title: "", body: "" };
    }

    if (this.getHasAthleteRegExpired) {
      const eaRegLink =
        "<a target='_blank' href='https://ea-registration-check.myathletics.uk/'>England Athletics Registration Check</a>";

      let registrationMessage: string[] = [];

      if (this.selectedAthlete.aocode === "EA") {
        registrationMessage.push("<div class='e4s-flex-column e4s-gap--standard'>")
        registrationMessage.push("<div>" + eaRegLink + "</div>");
        registrationMessage.push("<div>If on using the EA Reg checker " + this.selectedAthlete.firstName + " "  +
          this.selectedAthlete.surName + " shows as unregistered, " +
          "please contact EA to get resolved</div>");

        registrationMessage.push("<div>If the EA site says "+ this.selectedAthlete.firstName + " "  +
          this.selectedAthlete.surName +" is registered, first try refreshing the page and if our " +
          "system still shows you as un-registered, please get in <NAME_EMAIL></div>");
        registrationMessage.push("</div>")
      }

      return {
        title:
          "The registration for " +
          athlete.firstName +
          " " +
          athlete.surName +
          " expired on " +
          format(parse(this.selectedAthlete.activeEndDate), "Do MMM YYYY") +
          ".",
        body:
          "<div class='e4s-flex-column e4s-gap--standard'>" +
          "<div>This competition only allows entry with a valid registration. " +
          "Please ensure " + this.selectedAthlete.firstName + " "  +
          this.selectedAthlete.surName + " registration is renewed with your athletics organisation.</div>" +
          (this.selectedAthlete.aocode === "EA"
            ? '<hr class="dat-e4s-hr-only" /><div>Click here for ' +
              registrationMessage.join("") +
              "</div>"
            : "") +
          "</div>",
      };
    }

    return {
      title: "",
      body: "",
    };
  }

  public get getUnRegAthleteInRegOnlyComp(): boolean {
    return this.athleteCompSchedService ? this.athleteCompSchedService.isUnRegAthleteInRegOnlyComp(
      this.selectedAthlete,
      this.selectedCompetition
    ) : false;
  }

  public get showSchedInfo() {
    return (
      this.eventsServerResponse.schedInfo &&
      this.eventsServerResponse.schedInfo.title &&
      this.eventsServerResponse.schedInfo.title.length > 0
    );
  }

  public get showNoAthlete() {
    return !(
      this.selectedAthlete &&
      this.selectedAthlete.id &&
      this.selectedAthlete.id > 0
    );
  }

  public get showNoIndivEventsForAthlete() {
    return (
      !this.isLoading &&
      this.compEventsInternal.length === 0 &&
      !this.showNoAthlete
    );
  }

  public get getScheduleUrl() {
    return (
      CONFIG.E4S_HOST +
      "/entry/v5/competition/schedule.php?compid=" +
      this.selectedCompetition.id
    );
  }

  public cancelE4SEvent(e4sEvent: IAthleteCompSchedRuleEvent) {
    this.showE4sEventCancelDialog = true;
    this.e4sEventToCancel = R.clone(e4sEvent);
  }

  public cancelE4sCancelDialog() {
    this.showE4sEventCancelDialog = false;
    this.e4sEventToCancel =
      this.athleteCompSchedService ? this.athleteCompSchedService.factoryAthleteCompSchedRuleEvent() : {} as IAthleteCompSchedRuleEvent;
  }

  public cancelE4sSubmitted(e4sEvent: IAthleteCompSchedRuleEvent) {
    this.cancelE4sCancelDialog();

    //  reload schedule for user
    this.$store.dispatch(
      ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_INITIALISE_EVENTS
    );
  }

  // public editPb() {
  //   this.isEditingPb = true;
  // }

  public onEditPb(compEvent: IAthleteCompSchedRuleEvent) {
    console.log("events-card-grid onEditPb()", compEvent);
    this.compEventPb = simpleClone(compEvent);

    this.editPbV3InputProp = {
      athleteMini: {
        id: this.selectedAthlete.id,
        firstName: this.selectedAthlete.firstName,
        surName: this.selectedAthlete.surName,
        URN: this.selectedAthlete.URN,
        aocode: this.selectedAthlete.aocode,
      },
      athleteCompSchedMini: {
        split: compEvent.split,
        entryId: compEvent.entryId,
      },
      performance: compEvent.perfInfo,
    };

    this.editPbV3Options = {
      doSubmit: compEvent.order.productId > 0,
    };

    // let athletePb: IAthletePb | null = athleteService.getAthletePbForEventId(
    //   this.selectedAthlete,
    //   compEvent.eventid
    // );
    // if (athletePb && athletePb.eventid > 0) {
    //   this.athletePb = R.clone(athletePb);
    // } else {
    //   athletePb = athleteService.factoryAthletePb();
    //   athletePb.eventName = compEvent.Name;
    //   athletePb.eventid = compEvent.eventid;
    //   athletePb.min = compEvent.eoptions.min;
    //   athletePb.max = compEvent.eoptions.max;
    //   athletePb.uomInfo.options = compEvent.uom;
    //   //  @ts-ignore
    //   athletePb.uomInfo.type = compEvent.tf;
    //
    //   this.athletePb = athletePb;
    // }
    this.isEditingPb = true;
    this.isEditingPb = true;
  }

  // public get getEditPbV3InputProp(): EditPbV3InputProp {
  //   const compEvent: IAthleteCompSchedRuleEvent = this.compEventPb;
  //
  //   return {
  //     athleteMini: {
  //       id: this.selectedAthlete.id,
  //       firstName: this.selectedAthlete.firstName,
  //       surName: this.selectedAthlete.surName,
  //       URN: this.selectedAthlete.URN,
  //       aocode: this.selectedAthlete.aocode,
  //     },
  //     athleteCompSchedMini: {
  //       split: compEvent.split,
  //       entryId: compEvent.entryId,
  //     },
  //     performance: compEvent.perfInfo,
  //   };
  // }

  public get getDoAnyEventsRequirePb(): boolean {
    return this.compEventsInternal.reduce<boolean>((accum, evt) => {
      if (evt.ceoptions.mandatoryPB) {
        accum = true;
      }
      return accum;
    }, false);
  }

  public pbSaved(pbEditV3RouteOutput: IPbEditV3RouteOutput) {
    console.log("pbSaved()", pbEditV3RouteOutput);
    this.$store.commit(
      ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_MUTATIONS_SET_PB,
      { compEvent: this.compEventPb, perfInfo: pbEditV3RouteOutput.perfInfo }
    );
    this.isEditingPb = false;
  }

  public pbSavedOrig() {
    this.athleteController &&
    this.athleteController
      .loadAthleteById(this.selectedAthlete.id)
      .then((athlete: IAthleteSummary) => {
        this.$store.commit(
          ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
            "/" +
            ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_SET_SELECTED_ATHLETE,
          athlete
        );

        this.$store.commit(
          ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
            "/" +
            ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_MUTATIONS_PROCESS_EVENTS,
          []
        );

        this.$store.dispatch(
          ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
            "/" +
            ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_INITIALISE_EVENTS
        );
      });
  }

  public get getShowAthleteItemsAvailable() {
    return this.anySecondaryAthleteItems && this.compEventsInternal.length > 0;
  }

  public doCompEventActions(compEvent: IAthleteCompSchedRuleEvent) {
    // this.isLoadingInternal = true;
    // const promComp = new CompetitionData().getCompById(this.selectedCompetition.id);
    // handleResponseMessages(promComp);
    // promComp
    //     .then((response) => {
    //         if (response.errNo === 0) {
    //
    //             this.compEventActionsResult = {
    //                 competitionSummaryPublic: response.data,
    //                 athlete: this.selectedAthlete,
    //                 compEvent: compEvent,
    //                 compEvents: this.compEventsInternal
    //             };
    //             this.actionsCompEventShow = true;
    //         }
    //     })
    //     .finally(() => {
    //         this.isLoadingInternal = false;
    //     })
    this.compEventEdit = R.clone(compEvent);
    this.actionsCompEventShow = true;
  }

  public compEventActionsClose(requiresParentReloadOnClose: boolean) {
    this.actionsCompEventShow = false;
    if (requiresParentReloadOnClose) {
      this.$store.dispatch(
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
          "/" +
          ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_INITIALISE_EVENTS
      );
    }
  }

  public openShop() {
    this.$emit("openShop");
  }

  public proceedWithSelection(
    compEvent: IAthleteCompSchedRuleEvent
  ): Promise<void> {
    this.edited = true; //  If user browses away, detect here.
    const isEventAlreadySelected = compEvent.order.productId > 0;

    if (isEventAlreadySelected) {
      this.isLoadingInternal = true;
      return this.$store
        .dispatch(
          ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
            "/" +
            ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_REMOVE_FROM_SELECTED_NEW,
          {
            compEvent: compEvent,
          }
        )
        .finally(() => {
          this.isLoadingInternal = false;
        });
    } else {
      this.isLoadingInternal = true;
      return this.$store
        .dispatch(
          ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
            "/" +
            ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_SELECTED,
          {
            event: compEvent,
          }
        )
        .then(() => {
          //  Until other parts of local state get moved to.
          return this.recalc(compEvent);
        })
        .finally(() => {
          this.isLoadingInternal = false;
        });
    }
  }

  public recalc(compEvent: null | IAthleteCompSchedRuleEvent): Promise<void> {
    console.log(
      "recalc()" + (compEvent ? compEvent.userSelected : "undefined")
    );
    return this.$store.dispatch(
      ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_PROCESS_EVENTS,
      {
        compEvent,
      }
    );
  }

  public selfServiceSubmitted() {
    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_ATHLETE_TRIGGER_REFRESH
    );
  }

  public subscriptionContinue() {
    this.proceedWithSelection(this.compEventEdit);
    this.showOverSubscribed = false;
  }

  public subscriptionCancel() {
    this.showOverSubscribed = false;
  }

  public contactOrganiserFromActions() {
    this.actionsCompEventShow = false;
    this.showContactOrganiser = true;
  }

  // public get getHasRegistrationExpired() {
  //   return athleteService.hasRegistrationExpired(this.a)
  // }
}
</script>

<style scoped>
.no-events-for-athlete {
  font-size: 1.5em;
  margin-top: 2em;
}
.events-card-grid--sched-info {
  margin-bottom: 1em;
}
</style>
