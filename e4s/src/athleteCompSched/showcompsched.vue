<template>
    <div>
        compsched---><iframe :src="url"></iframe>
    </div>
</template>


<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop, Watch} from "vue-property-decorator";
    import {CONFIG} from "../common/config";
    import type {ICompetition} from "../competition/competition-models";

    @Component({})
    export default class ShowCompSched extends Vue {

        @Prop({
            default: () => {
                return {} as ICompetition;
            },
        })
        public readonly competition: ICompetition;

        public url: string = "";

        @Watch("competition")
        public onCompetitionChanged(newValue: ICompetition, oldValue: ICompetition) {
            if (newValue && newValue.id) {
                this.url = CONFIG.E4S_HOST + "/entry/php/getSchedule.php?compid=" + newValue.id;
            } else {
                this.url = "";
            }
        }
    }
</script>