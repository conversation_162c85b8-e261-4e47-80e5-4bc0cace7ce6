import { ICompSummary } from "./competition-models";
import { IBuilderOptions } from "../builder/builder-models";

export const compSummaryDataMock: ICompSummary = {
  // ID: 413,
  active: false,
  IndoorOutdoor: "",
  link: "",
  yearFactor: 0,
  areaid: 0,
  options: {
    isClubComp: false,
    // sourceId: 0,
    // anonymize: false,
    // hideScheduledOnly: false,
    // standards: [],
    allowExpiredRegistration: true,
    // useTeamBibs: false,
    // bacs: { enabled: false, msg: "" },
    // scoreboard: { image: "/results/blank.jpg" },
    // seqEventNo: true,
    disabled: false,
    // stripeMandatory: false,
    homeInfo: "",
    shortCode: "",
    priority: { required: false, code: "", dateTime: "", message: "" },
    bibNos: "1",
    bibSort1: "surname",
    bibSort2: "firstname",
    bibSort3: "dob",
    heatOrder: "s",
    stadium: "",
    // adjustEventNo: 0,
    // athleteType: "A",
    // tickets: { enabled: false },
    // pf: { pfTargetDirectory: "", type: "" },
    contact: {
      email: "<EMAIL>",
      id: 145,
      tel: "",
      userName: "xdvcsdff",
      visible: true,
    },
    cardInfo: {
      enabled: true,
      availableFrom: "",
    },
    subscription: {
      enabled: false,
      timeCloses: "",
      organiserMessage: "",
      e4sMessage: "",
      refunded: "",
      process: true,
      processRefundTime: "",
    },
    cloneInfo: { fromId: 411, saved: true },
    // card: {
    //   header: { field: "", track: "" },
    //   footer: { field: "", track: "" },
    // },
    checkIn: {
      enabled: false,
      checkInDateTimeOpens: "",
      defaultFrom: 180,
      defaultTo: 60,
      qrCode: true,
      text: "",
      terms: "",
      useTerms: false,
      seedOnEntries: false,
    },
    school: false,
    // orgFreeEntry: false,
    // autoPayFreeEntry: false,
    cheques: { allow: false, ends: "" },
    allowAdd: { unregistered: false, registered: true },
    timetable: "Provisional",
    helpText: { schedule: "", teams: "", cart: "" },
    showTeamAthletes: true,
    singleAge: false,
    // showAthleteAgeInEntries: false,
    // report: {
    //   summary: true,
    //   athletes: true,
    //   ttathletes: true,
    //   ttentries: true,
    //   individual_entries: true,
    //   teams: true,
    //   subscriptions: true,
    //   orders: true,
    //   events: true,
    // },
    athleteSecurity: { areas: [], clubs: [], onlyClubsUpTo: "" },
    ui: {
      enterButtonText: "Enter",
      entryDefaultPanel: "SCHEDULE",
      ticketComp: 0,
      ticketCompButtonText: "Buy Tickets",
      sectionsToHide: {
        SCHEDULE: false,
        ATHLETES: false,
        SHOP: true,
        TEAMS: true,
      },
      ticketCompBase: { id: 0, name: "" },
    },
    athleteQrData: false,
    // nonGuests: [],
    disabledReason: "",
    paymentCode: "",
    laneCount: 8,
    compLimits: { athletes: 0, entries: 0, teams: 0 },
    stopReport: false,
    cancelEvent: {
      hrsBeforeClose: 48,
      refund: { allow: true, type: "E4S_FEES" },
      credit: { allow: true },
    },
    resultsAvailable: false,
    autoEntries: {
      selectedTargetComp: { id: 0, name: "", timeTronicsUrl: "" },
      targetable: { allowedSources: [], enabled: false },
    },
    level: "",
    pfTargetDirectory: "",
    dates: ["2024-02-29"],
    clubComp: false,
    pbMandatory: true,
    // categoryId: 0,
  } as any as IBuilderOptions,
  teamid: null,
  lastentrymod: "2000-01-01 00:00:00",
  information: "",
  r4s: null,
  waitingrefunded: null,
  resultsAvailable: 0,
  areaname: "All",
  today: "2024-03-05",
  systemtime: "2024-03-05 05:00:05",
  compDate: "2024-02-29",
  daysToComp: -76,
  daysToClose: -78,
  location: {
    id: 20,
    name: "Pingles S'tadium 2",
    address1: "The Pingles Stadium",
    address2: "",
    town: "Nuneaton",
    postcode: "CV11 4LX",
    county: "Warwickshire",
    map: "https://www.google.com/maps?q=CV11 4LX",
    directions: "",
    website: "https://www.thepinglesstadium.com/",
  },
  loccontact: "02476 344 429",
  logo: "/resources/nuneaton_opens_logo.jpg",
  ctcid: null,
  maxathletes: null,
  maxteams: null,
  maxmale: null,
  maxfemale: null,
  maxagegroup: null,
  uniqueevents: null,
  singleagegroup: null,
  ctc: {
    ctcid: null,
    maxathletes: null,
    maxteams: null,
    maxmale: null,
    maxfemale: null,
    maxagegroup: null,
    uniqueevents: null,
    singleagegroup: null,
  },
  compOrgId: 54,
  compName: "Cloned 411 Cloned 410 test source for clone",
  entityid: null,
  opendate: "2024-02-18T16:12:00+00:00",
  closedate: "2024-02-27T00:00:00+00:00",
  club: "Nuneaton Opens",
  dates: ["2024-02-29"],
  saleenddate: null,
  entries: {
    eventCount: 40,
    teamEventCount: 0,
    indivEventCount: 40,
    indiv: 0,
    waitingCount: 0,
    uniqueIndivAthletes: 0,
    athletes: 0,
  },
  access: "",
  permissions: { adminMenu: true, builder: true, check: true, report: true },
  reportId: "",
  reportAccess: true,
  status: {
    id: 0,
    compid: 413,
    description: "No Status",
    status: "NO_STATUS",
    invoicelink: "",
    reference: "",
    notes: "",
    value: 0,
    code: 9010,
    wfid: 0,
    previd: 0,
    prevdescription: "",
    nextdescription: "",
    nextid: 0,
  },
  organisers: [
    {
      permId: 84,
      orgId: 54,
      compId: 0,
      user: {
        id: 6900,
        userEmail: "<EMAIL>",
        displayName: "Jared Wilson",
      },
      role: { id: 6, name: "admin" },
    },
    {
      permId: 112,
      orgId: 54,
      compId: 0,
      user: {
        id: 22162,
        userEmail: "<EMAIL>",
        displayName: "Pingles",
      },
      role: { id: 6, name: "admin" },
    },
    {
      permId: 124,
      orgId: 54,
      compId: 0,
      user: {
        id: 18243,
        userEmail: "<EMAIL>",
        displayName: "Andy Hulse",
      },
      role: { id: 6, name: "admin" },
    },
    {
      permId: 184,
      orgId: 54,
      compId: 0,
      user: {
        id: 19828,
        userEmail: "<EMAIL>",
        displayName: "nick wall",
      },
      role: { id: 9, name: "seeding" },
    },
    {
      permId: 186,
      orgId: 54,
      compId: 0,
      user: {
        id: 1,
        userEmail: "<EMAIL>",
        displayName: "E4S Admin",
      },
      role: { id: 6, name: "admin" },
    },
  ],
  compId: 413,
  compRules: null,
  eventTypes: { indivEvents: true, teamEvents: false, tickets: false },
  clubCompInfo: {},
  compAgeGroups: [
    { id: 1, name: "Under 11", fromDate: "2013-09-01", toDate: "2015-08-31" },
    { id: 2, name: "Under 13", fromDate: "2011-09-01", toDate: "2013-08-31" },
    { id: 3, name: "Under 9", fromDate: "2015-09-01", toDate: "2018-08-31" },
    { id: 8, name: "Under 17", fromDate: "2007-09-01", toDate: "2009-08-31" },
    { id: 9, name: "Under 15", fromDate: "2009-09-01", toDate: "2011-08-31" },
    { id: 11, name: "Under 20", fromDate: "2005-01-01", toDate: "2007-08-31" },
    { id: 12, name: "Under 23", fromDate: "2002-01-01", toDate: "2004-12-31" },
    { id: 14, name: "Masters", fromDate: "1923-03-02", toDate: "1989-12-31" },
    { id: 28, name: "Senior", fromDate: "1990-01-01", toDate: "2004-12-31" },
    { id: 209, name: "Under 13", fromDate: "2011-09-01", toDate: "2012-10-01" },
  ],
};
