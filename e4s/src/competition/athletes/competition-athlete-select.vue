<template>
    <div class="competition-athlete-select">

<!--        <div class="row">-->
<!--            <div class="col s12 m12 l12">-->
<!--                <div style="height: 1rem">-->
<!--                    Enter first few characters of athlete name: <LoadingSpinner v-if="true"></LoadingSpinner>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->

        <div style="height: 1rem;margin-bottom: 1rem;">
            Enter first few characters of athlete name: <LoadingSpinner v-if="isLoading" style="vertical-align: middle"></LoadingSpinner>
        </div>

        <div class="row">
            <div class="col s12 m12 l12">
                <InputDebounce class="e4s-input"
                               id="quick-search"
                               :default-value="defaultValue"
                               placeholder="Enter search..."
                               v-on:input="doSearch"
                ></InputDebounce>
            </div>
        </div>

        <div class="row">
            <div class="col s12 m12 l12">
                <div v-text="searchMessage"></div>
            </div>
        </div>

        <div class="club-crud-picker--results-section" v-show="showResults">
            <div v-for="result in results" :key="result.athleteId" class="club-crud-picker--result">
                <a href="#" v-on:click.prevent="selected(result.athleteId)">
                    <span v-text="result.gender"></span> -
                    <span v-text="result.firstName"></span> <span v-text="result.surName"></span>&nbsp;
                    (<span v-text="result.clubName"></span>)
                </a>
            </div>
        </div>

    </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import InputDebounce from "../../common/ui/field/input-debounce.vue";
import {handleResponseMessages} from "../../common/handle-http-reponse";
import * as R from "ramda";
import {CompetitionAthleteData} from "./competition-athlete-data";
import {ICompetitionAthleteSummary} from "./competition-athlete-models";
import {CommonService} from "../../common/common-service";

@Component({
    name: "competition-athlete-select",
    components: {
        InputDebounce
    }
})
export default class CompetitionAthleteSelect extends Vue {
    @Prop({default: 0})
    public readonly compId: number;

    @Prop({
        default: ""
    })
    public readonly defaultValue!: string;

    public commonService = new CommonService()
    public competitionAthleteData: CompetitionAthleteData = new CompetitionAthleteData();
    public isLoading = false;
    public results: ICompetitionAthleteSummary[] = [];
    public showResults = false;
    public searchMessage = "";

    public doSearch(searchTerm: string) {

        if (searchTerm.length === 0) {
            this.showResults = false;
            this.searchMessage = "";
            this.results = [];
            return;
        }

        this.isLoading = true;
        this.searchMessage = "";
        const prom = this.competitionAthleteData.search(this.compId, searchTerm, 20)
        handleResponseMessages(prom)
        prom
          .then((resp) => {
              if (resp.errNo === 0) {
                  this.results = this.commonService.convertObjectToArray(resp.data);
                  if (this.results.length === 0) {
                      this.searchMessage = "No results found";
                  }
                  this.showResults = true;
              }
          })
          .finally(()=> {
              this.isLoading = false;
          })
    }

    public selected(athleteId: number) {
        const athlete: ICompetitionAthleteSummary | null = this.commonService.findFirst( (athleteSummary)=> {
            return athleteSummary.athleteId === athleteId;
        }, this.results);

        if (athlete) {
            this.$emit("selected", R.clone(athlete));
        }
    }

    public cancel() {
        this.$emit("cancel");
    }

}
</script>
