import {
  IResultAthlete,
  IResultEvent,
  IResultHeat,
  IResultOverall,
} from "./results-models";
import { IValidationProp } from "../../validation/validation-models";
import { ValidationService } from "../../validation/validation-service";
import { CommonService } from "../../common/common-service";
import {
  IAutoEntryOptions,
  IR4sAthleteEntry,
  IR4sAthleteEntryAgeGroup,
  IR4sBaseEntry,
  IR4sCompSchedule,
  IR4sScheduledEvent,
  IR4sTeamEntry,
  R4SEventType,
} from "../scoreboard/rs4/rs4-scoreboard-models";
import { IEventSeed } from "../../athleteCompSched/athletecompsched-models";
import {
  isNumeric,
  simpleClone,
  sortArray,
} from "../../common/common-service-utils";

const commonService: CommonService = new CommonService();

export class ResultsService {
  public factoryResultEvent(): IResultEvent {
    return {
      comp: {
        id: 0,
        name: "",
        logo: "",
      },
      eventGroup: {
        id: 0,
        name: "",
        eventNo: 0,
        scheduledTime: "",
        wind: "",
        type: "T",
        seed: {
          gender: false,
          age: false,
          type: "O",
          qualifyToEg: {
            id: 0,
            name: "",
            eventNo: 0,
            isMultiEvent: false,
          },
          laneCount: 8,
          firstLane: 1,
          waiting: false,
        },
      },
      heats: [],
    };
  }

  public factoryEventSeed(): IEventSeed {
    return {
      gender: false,
      age: false,
      type: "O",
      qualifyToEg: {
        id: 0,
        name: "",
        eventNo: 0,
        isMultiEvent: false,
      },
      laneCount: 8,
      firstLane: 1,
      waiting: false,
    };
  }

  public factoryResultHeat(): IResultHeat {
    return {
      id: 0,
      scheduledTime: "",
      actualTime: "",
      heatNo: 0,
      description: "",
      image: "",
      results: [],
    };
  }

  public factoryResultAthlete(): IResultAthlete {
    return {
      id: 0,
      laneNo: 0,
      position: 0,
      bibNo: 0,
      bibText: "",
      gender: "",
      athlete: "",
      athleteId: 0,
      clubName: "",
      wind: "",
      score: 0,
      scoreValue: "",
      scoreText: "",
      urn: "",
      qualify: "",
      eaAward: 0,
      ageGroup: "",
    };
  }

  public factoryR4sScheduledEvent(): IR4sScheduledEvent {
    return {
      id: 0,
      eventNo: 0,
      eventName: "",
      name: "",
      type: "T",
      typeNo: "T1",
      isTeamEvent: false,
      startDate: "",
      counts: {
        entries: 0,
        maxAthletes: 0,
        maxInHeat: 0,
      },
      wind: "",
      results: false,
      waiting: false,
      seed: this.factoryEventSeed(),
      entries: [],
      teamEntries: [],
      resultsPossible: true,
    };
  }

  public factoryR4sBaseEntry(): IR4sBaseEntry {
    return {
      entryId: 0,
      ageGroup: this.factoryR4sAthleteEntryAgeGroup(),
      gender: "",
      entryOptions: {
        autoEntries: this.factoryAutoEntryOptions(),
      },
      user: {
        id: 0,
        name: "",
        email: "",
      },
    };
  }

  public factoryR4sAthleteEntry(): IR4sAthleteEntry {
    return {
      ...this.factoryR4sBaseEntry(),
      athleteId: 0,
      clubname: "",
      club: {
        id: 0,
        name: "",
      },
      firstname: "",
      gender: "",
      qualify: "",
      surname: "",
      urn: "",
      paid: false,
      egId: 0,
      perf: {
        perf: 0,
        perfText: "",
      },
      editAccess: false,
    };
  }

  public factoryR4sTeamEntry(): IR4sTeamEntry {
    return {
      ...this.factoryR4sBaseEntry(),
      athletes: [],
      entity: {
        entityName: "",
        id: 0,
        teamEntity: "",
      },
      teamName: "",
    };
  }

  public factoryAutoEntryOptions(): IAutoEntryOptions {
    return {
      targetEntry: {
        id: 0,
        paid: 0,
        orderId: 0,
      },
      targetEventGroup: {
        id: 0,
        name: "",
      },
    };
  }

  public factoryR4sAthleteEntryAgeGroup(): IR4sAthleteEntryAgeGroup {
    return {
      competitionAge: 0,
      currentAge: 0,
      name: "",
      shortName: "",
    };
  }

  public resultHeatsToSubmit(results: IResultAthlete[]): IResultAthlete[] {
    return results.filter((result) => {
      if (commonService.isOnlyNumbers(result.bibNo.toString())) {
        return !commonService.isEmpty(result.bibNo) && result.bibNo > 0;
      } else {
        return result.bibNo.toString() !== "0";
      }
    });
  }

  public validateHeat(
    resultHeat: IResultHeat
  ): Record<string, IValidationProp> {
    const validationService: ValidationService = new ValidationService();
    let validationState: Record<string, IValidationProp> = {};

    // const results = resultHeat.results.filter( (result) => {
    //   return !commonService.isEmpty(result.bibNo) && result.bibNo > 0;
    // });

    const results = this.resultHeatsToSubmit(resultHeat.results);

    if (resultHeat.heatNo === 0) {
      validationState = validationService.addMessage(
        "heatNumber",
        "Required.",
        validationState
      );
    }

    const bibsCountMap: Record<string, number> = results.reduce(
      (accum, result) => {
        if (!accum[result.bibNo]) {
          accum[result.bibNo] = 0;
        }
        accum[result.bibNo]++;
        return accum;
      },
      {} as Record<string, number>
    );

    const bibNumbers = Object.keys(bibsCountMap).filter((bibNo) => {
      return bibsCountMap[bibNo] > 1;
    });
    if (bibNumbers.length > 0) {
      validationState = validationService.addValidationProp(
        {
          title: "Bib no",
          propPath: "bibNo",
          messages: ["duplicated: " + bibNumbers.join(", ")],
        },
        validationState
      );
    }

    const allAthleteNames = results.reduce((accum, result) => {
      if (commonService.isEmpty(result.athlete)) {
        accum = false;
      }
      return accum;
    }, true as boolean);

    if (!allAthleteNames) {
      validationState = validationService.addValidationProp(
        {
          title: "Athlete Names",
          propPath: "athleteName",
          messages: ["Required."],
        },
        validationState
      );
    }

    const allAthleteScores = results.reduce((accum, result) => {
      if (
        commonService.isEmpty(result.score) &&
        commonService.isEmpty(result.scoreValue)
      ) {
        console.log("empty score", simpleClone(result));
        accum = false;
      }
      return accum;
    }, true as boolean);

    if (!allAthleteScores) {
      validationState = validationService.addValidationProp(
        {
          title: "Athlete Scores",
          propPath: "athleteScore",
          messages: ["Required."],
        },
        validationState
      );
    }

    const lanesCountMap: Record<string, number> = results.reduce(
      (accum, result) => {
        if (result.laneNo > 0) {
          const laneNo = result.laneNo.toString();
          if (!accum[laneNo]) {
            accum[laneNo] = 0;
          }
          accum[laneNo]++;
        }
        return accum;
      },
      {} as Record<string, number>
    );

    const laneNumbers = Object.keys(lanesCountMap).filter((laneNo) => {
      return lanesCountMap[laneNo] > 1;
    });
    if (laneNumbers.length > 0) {
      validationState = validationService.addValidationProp(
        {
          title: "Lane no",
          propPath: "laneNo",
          messages: ["duplicated: " + laneNumbers.join(", ")],
        },
        validationState
      );
    }

    return validationState;
  }

  /**
   * put current position zero at bottom
   * @param entries
   */
  public sortEntries(
    resultAthletes: IResultAthlete[],
    eventType?: R4SEventType
  ): IResultAthlete[] {
    const splitIntoWithAndWithOutScore = resultAthletes.reduce(
      (accum, result) => {
        if (result.position > 0) {
          accum.withScore.push(result);
        } else {
          accum.noScore.push(result);
        }

        return accum;
      },
      {
        withScore: [] as IResultAthlete[],
        noScore: [] as IResultAthlete[],
      }
    );
    return [
      ...splitIntoWithAndWithOutScore.withScore.sort(function (a, b) {
        //  Track: lowest time is better, Field: highest score is better..
        // return eventType === 'T' ?
        //     b.results.currentPosition - a.results.currentPosition :
        //     a.results.currentPosition - b.results.currentPosition;
        return a.position - b.position;
      }),
      ...splitIntoWithAndWithOutScore.noScore,
    ];
  }

  /**
   * put current position zero at bottom
   * @param entries
   */
  public sortResults(
    resultAthletes: IResultAthlete[],
    eventType: R4SEventType = "T"
  ): IResultAthlete[] {
    const splitIntoWithAndWithOutScore = resultAthletes.reduce(
      (accum, result) => {
        const resultInternal = simpleClone(result);
        const hasScore =
          resultInternal.scoreValue && isNumeric(resultInternal.scoreValue);

        //  @ts-ignore
        resultInternal.hasScore = hasScore;

        //  @ts-ignore
        resultInternal.x = "1";

        if (hasScore) {
          const realValue = Number(resultInternal.scoreValue);
          resultInternal.score = realValue;
          accum.withScore.push(resultInternal);
        } else {
          accum.noScore.push(resultInternal);
        }

        return accum;
      },
      {
        withScore: [] as IResultAthlete[],
        noScore: [] as IResultAthlete[],
      }
    );

    const withScoresSorted = sortArray(
      "score",
      splitIntoWithAndWithOutScore.withScore,
      eventType === "T" ? "ASC" : "DESC"
    ).map((ath, index) => {
      ath.position = index + 1;
      return ath;
    });

    return [...withScoresSorted, ...splitIntoWithAndWithOutScore.noScore];
  }

  public getBibNo(result: IResultAthlete): string {
    return result.bibText && result.bibText.length > 0
      ? result.bibText
      : result.bibNo.toString();
  }

  public getHeatName(resultHeat: IResultHeat): string {
    return resultHeat.description && resultHeat.description.length > 0
      ? resultHeat.description
      : "Heat " + resultHeat.heatNo;
  }

  public createResultOverallsFromResultHeat(
    resultHeat: IResultHeat
  ): IResultOverall[] {
    const res = resultHeat.results.map((resultAthlete) => {
      return {
        ...resultAthlete,
        heatNo: resultHeat.heatNo,
        heatName: this.getHeatName(resultHeat),
      };
    });
    return res;
  }

  public createResultOverallsFromResultEvent(
    resultEvent: IResultEvent
  ): IResultOverall[] {
    const res = resultEvent.heats.reduce((accum, resultHeat) => {
      const resultOveralls =
        this.createResultOverallsFromResultHeat(resultHeat);
      accum = accum.concat(resultOveralls);
      return accum;
    }, [] as IResultOverall[]);
    return res;
  }

  public canQualificationsBeDone(resultEvent: IResultEvent): boolean {
    return resultEvent.eventGroup?.seed?.qualifyToEg.eventNo > 0;
  }

  public canQualifyToAnotherComp(compSchedule: IR4sCompSchedule): boolean {
    return compSchedule.autoEntries.selectedTargetComp.id > 0;
  }

  public isTrackEvent(eventType: R4SEventType): boolean {
    return eventType === "T";
  }
}
