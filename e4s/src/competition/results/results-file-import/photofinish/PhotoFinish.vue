<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <div class="e4s-flex-row e4s-justify-flex-space-between e4s-gap--large">
      <!--      <h1 class="e4s-header&#45;&#45;400">Photo Finish Updates</h1>-->

      <p style="text-align: justify">
        Use this section to upload or edit data inputted to the photo finish
        system, e.g. Lynx, TimeTronics. Any changes made on Entry4Sports score
        cards will result in a message being displayed here. Any ad-hoc changes
        can be made here and sent to the Photo Finish system.
      </p>
    </div>

    <div class="e4s-flex-row e4s-gap--standard e4s-flex-center">
      <ButtonGenericV2
        style="width: auto"
        text="Select Source Folder"
        v-on:click="photoFinishController.controller.pickTargetDirectory()"
      />

      <span
        class="e4s-header--400"
        v-text="photoFinishController.state.targetDirectoryName"
      ></span>

      <!--    A select for system option: 1, 2  -->
      <div class="e4s-flex-row e4s-gap--standard e4s-flex-center">
        <label>
          <input
            type="radio"
            class="browser-default e4s-input-field e4s-input-field--primary"
            value="1"
            v-model="photoFinishController.state.system"
          />
          <span>System 1</span>
        </label>
        <label>
          <input
            type="radio"
            class="browser-default e4s-input-field e4s-input-field--primary"
            value="2"
            v-model="photoFinishController.state.system"
          />
          <span>System 2</span>
        </label>
      </div>

      <div
        class="e4s-flex-row e4s-gap--standard e4s-flex-row--end e4s-header--400"
      >
        Socket:
        <span
          v-text="photoFinishController.socketConfig.currentSocketState"
          :class="
            photoFinishController.socketConfig.currentSocketState === 'OPEN'
              ? 'photo-finish-input--socket-open'
              : ''
          "
        ></span>
      </div>
    </div>

    <div v-if="photoFinishController.state.targetDirectoryName.length > 0">
      <div class="e4s-flex-column e4s-gap--standard">
        <FormGenericInputTextV2
          form-label="Manual File Name"
          v-model="photoFinishController.state.manualPhotoFinishPayload.file"
        />

        <FormGenericInputTemplateV2 form-label="Manual File Data">
          <textarea
            slot="field"
            style="min-height: 150px; width: 100%"
            v-model="photoFinishController.state.manualPhotoFinishPayload.data"
          ></textarea>
        </FormGenericInputTemplateV2>

        <div class="e4s-flex-row e4s-gap--standard">
          <ButtonGenericV2
            text="Submit"
            slot="field"
            v-on:click="photoFinishController.controller.manualFileSubmit()"
          />

          <ButtonGenericV2
            text="Cancel"
            button-type="tertiary"
            slot="field"
            v-on:click="
              photoFinishController.controller.cancelManualFileSubmit()
            "
          />
        </div>
      </div>
    </div>

    <!--Entries loop-->
    <div>
      <div
        class="e4s-flex-row e4s-gap--standard e4s-repeatable-grid--top"
        v-for="commonFile in getCommonFiles"
        :key="commonFile.name"
      >
        <div style="min-width: 80px" v-text="'edited'"></div>
        <div>
          <a
            href="#"
            v-on:click.prevent="
              photoFinishController.controller.editCommonFile(commonFile)
            "
            v-text="commonFile.name"
          ></a>
        </div>
        <div
          style="min-width: 80px"
          v-text="
            photoFinishController.controller.getSimpleTime(
              commonFile.lastModifiedDate
            )
          "
        ></div>
        <div
          style="min-width: 80px"
          v-text="commonFile.webkitRelativePath"
        ></div>
      </div>
    </div>
    <!--/Entries Loop-->

    <LoadingSpinnerV2 v-if="photoFinishController.state.isLoading" />
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onBeforeUnmount,
  PropType,
  SetupContext,
} from "vue";
import {
  IPhotoFinishControllerInput,
  usePhotoFinishController,
} from "./usePhotoFinish";
import { IBuilderCompetition } from "../../../../builder/builder-models";
import FormGenericInputTemplateV2 from "../../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import LoadingSpinnerV2 from "../../../../common/ui/loading-spinner-v2.vue";
import FieldTextV2 from "../../../../common/ui/layoutV2/fields/field-text-v2.vue";
import FormGenericInputTextV2 from "../../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { CONFIG } from "../../../../common/config";
import { sortArray } from "../../../../common/common-service-utils";

export default defineComponent({
  name: "PhotoFinish",
  components: {
    ButtonGenericV2,
    FormGenericInputTextV2,
    FieldTextV2,
    LoadingSpinnerV2,
    FormGenericInputTemplateV2,
  },
  props: {
    builderCompetition: {
      type: Object as PropType<IBuilderCompetition>,
      required: true,
    },
  },
  setup(
    props: { builderCompetition: IBuilderCompetition },
    context: SetupContext
  ) {
    const photoFinishControllerInput: IPhotoFinishControllerInput = {
      domain: CONFIG.E4S_HOST,
      builderCompetition: props.builderCompetition,
      system: "1",
    };

    const photoFinishController = usePhotoFinishController();
    photoFinishController.controller.init(photoFinishControllerInput);

    const getCommonFiles = computed(() => {
      return sortArray(
        "isoDateTime",
        photoFinishController.state.commonFiles,
        "DESC"
      );
    });

    onBeforeUnmount(() => {
      photoFinishController.destroy();
    });

    return { photoFinishController, getCommonFiles };
  },
});
</script>

<style>
.photo-finish-input--socket-error {
  color: var(--e4s-input-field--error__text-color);
}
.photo-finish-input--socket-open {
  color: var(--green-700);
}
</style>
