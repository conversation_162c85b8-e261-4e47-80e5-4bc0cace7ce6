import { IServerResponse } from "../../../../common/common-models";
import https from "../../../../common/https";

export interface IPhotoFinishDataResponseData {
  body: string;
  filename: string;
}

export function getPhotoFinishData(
  compId: number,
  fileName: string,
  system: string | number
): Promise<IServerResponse<IPhotoFinishDataResponseData>> {
  return https.get(
    "/v5/photofinish/" + compId + "/" + fileName + "?system=" + system
  ) as any as Promise<IServerResponse<any>>;
}

export function setPhotoFinishResponse(
  compId: number,
  fileName: string
): Promise<IServerResponse<unknown>> {
  return https.post("/v5/photofinish/response", {
    compId,
    fileName,
  }) as any as Promise<IServerResponse<any>>;
}
