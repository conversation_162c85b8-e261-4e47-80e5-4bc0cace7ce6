import {
  IPhotoFinishControllerState,
  IPhotoFinishSocketMessage,
  IPhotoFinishSocketMessagePayload,
} from "./usePhotoFinish";
import { BuilderService } from "../../../../builder/builder-service";

export function factoryPhotoFinishControllerState(): IPhotoFinishControllerState {
  return {
    isLoading: false,
    builderCompetition: new BuilderService().factoryGetBuilder({}),
    domain: "",
    system: "1",
    targetDirectoryName: "",
    targetDirectoryHandle: null,
    backupsDirectoryName: "backups",
    backupsDirectoryHandle: null,
    latestPhotoFinishPayload: {
      file: "",
      data: "",
    },
    manualPhotoFinishPayload: {
      file: "",
      data: "",
    },
    commonFiles: [],
  };
}

export function isSocketMessageOk(
  message: IPhotoFinishSocketMessage<IPhotoFinishSocketMessagePayload>,
  matchData: { compId: number; domain: string; system: string | number }
): boolean {
  if (message.action !== "pf_file") {
    return false;
  }
  if (message.comp && message.domain) {
    const matchDomain = matchData.domain.replace("https://", "");

    // does payload have a system?
    if (message.payload.data.system) {
      const systemMatch = matchData.system.toString();
      const systemPayload = message.payload.data.system.toString();

      console.log(
        "isSocketMessageOk() systemMatch: " +
          systemMatch +
          ", systemPayload: " +
          systemPayload
      );

      if (systemMatch !== systemPayload) {
        console.log(
          "isSocketMessageOk() systemMatch: " +
            systemMatch +
            ", systemPayload: " +
            systemPayload +
            " do not match"
        );
        return false;
      }
      console.log(
        "isSocketMessageOk() systemMatch: " +
          systemMatch +
          ", systemPayload: " +
          systemPayload +
          " do match"
      );
    }

    // return message.comp.id === matchData.compId;

    const doDomainsMap = matchDomain === message.domain;
    console.log(
      "isSocketMessageOk() doDomainsMap: " +
        doDomainsMap +
        ", matchDomain: " +
        matchDomain +
        ", message.domain: " +
        message.domain
    );

    const doCompIdsMatch = message.comp.id === matchData.compId;
    console.log(
      "isSocketMessageOk() doCompIdsMatch: " +
        doCompIdsMatch +
        ", matchData.compId: " +
        matchData.compId +
        ", message.comp.id: " +
        message.comp.id
    );

    // TODO turn domain match back on.
    return doCompIdsMatch && doDomainsMap;
  }
  return false;
}
