<template>
  <div
    class="e4s-flex-column e4s-gap--large"
    style="padding: var(--e4s-gap--large)"
  >
    <h1 class="e4s-header--400">
      <div class="e4s-flex-row e4s-justify-flex-space-between">
        <div>Photo Finish</div>

        <div class="e4s-flex-row e4s-gap--standard e4s-flex-wrap">
          <CompetitionGoToSummary
            :comp-id="builderCompetition.id"
            :link-text="builderCompetition.id.toString()"
          />
          <div v-text="builderCompetition.name"></div>
          <div>-</div>
          <div v-text="getFirstCompDate"></div>
        </div>
      </div>
    </h1>

    <div class="e4s-flex-column e4s-gap--large">
      <SectionLinksWrapper>
        <div class="e4s-flex-row e4s-gap--standard" slot="default">
          <SectionLinkSimple
            style="min-width: 50px"
            link-id="Config"
            :is-active="showSection === 'CONFIG'"
            v-on:selected="showSection = 'CONFIG'"
          >
            <span slot="default" class="e4s-header--400">Config</span>
          </SectionLinkSimple>

          <SectionLinkSimple
            style="min-width: 50px"
            link-id="Files"
            :is-active="showSection === 'FILES'"
            v-on:selected="showSection = 'FILES'"
          >
            <span slot="default" class="e4s-header--400">Input Files</span>
          </SectionLinkSimple>

          <SectionLinkSimple
            style="min-width: 50px"
            link-id="Results"
            :is-active="showSection === 'RESULTS'"
            v-on:selected="showSection = 'RESULTS'"
          >
            <span slot="default" class="e4s-header--400">Output files</span>
          </SectionLinkSimple>

          <SectionLinkSimple
            style="min-width: 50px"
            link-id="Results"
            :is-active="showSection === 'VIDEO'"
            v-on:selected="showSection = 'VIDEO'"
          >
            <span slot="default" class="e4s-header--400">Video files</span>
          </SectionLinkSimple>
        </div>
      </SectionLinksWrapper>

      <div v-show="showSection === 'CONFIG'">
        <div class="e4s-flex-row e4s-gap--large">
          <label>
            <input
              type="radio"
              class="browser-default e4s-input-field e4s-input-field--primary"
              value="TimeTronics"
              v-model="photoFinishSystem"
            />
            <span>TimeTronics</span>
          </label>
          <label>
            <input
              type="radio"
              class="browser-default e4s-input-field e4s-input-field--primary"
              value="Lynx"
              v-model="photoFinishSystem"
            />
            <span>Lynx</span>
          </label>

          <!--          <h1>Nick Test</h1>-->
          <!--          <button @click="nickTestController.controller.addTestName()">-->
          <!--            Add-->
          <!--          </button>-->
          <!--          <span v-text="nickTestController.state.isoTimeStamp"></span>-->

          <!--          <div-->
          <!--            v-for="person in nickTestController.state.userNames"-->
          <!--            :key="person"-->
          <!--          >-->
          <!--            =====-->
          <!--            <div>id: {{ person + person + "-" + person }}</div>-->
          <!--          </div>-->
        </div>
      </div>

      <PhotoFinish
        v-show="showSection === 'FILES'"
        :builder-competition="builderCompetition"
      />

      <ResultsImportFileOutput
        v-show="showSection === 'RESULTS'"
        :builder-competition="builderCompetition"
        :show-title="false"
        :file-types-to-monitor="['txt', 'png', 'lif', 'jpg']"
      />

      <ResultsImportVideoFilesOutput
        v-show="showSection === 'VIDEO'"
        :builder-competition="builderCompetition"
        :show-title="false"
        :file-types-to-monitor="['avi', 'mp4', 'mov', 'wmv', 'mkv']"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
} from "vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import FieldTextV2 from "../../../common/ui/layoutV2/fields/field-text-v2.vue";
import FieldNumberV2 from "../../../common/ui/layoutV2/fields/field-number-v2.vue";
import FormGenericInputTemplateV2 from "../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import { IBuilderCompetition } from "../../../builder/builder-models";
import FormGenericInputTextV2 from "../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import PhotoFinish from "./photofinish/PhotoFinish.vue";
import SectionLinkSimple from "../../../common/ui/layoutV2/tabs/section-link-simple.vue";
import ResultsImportFileOutput from "./ResultsImportFileOutput.vue";
import {
  ResultsFileImportControllerStateSections,
  ResultsFileImportControllerSystem,
} from "./useResultsFileImport";
import { eventDateDisplay } from "../../../common/common-service-utils";
import SectionLinksWrapper from "../../../common/ui/layoutV2/tabs/section-links-wrapper.vue";
import ResultsImportVideoFilesOutput from "./video-files/ResultsImportVideoFilesOutput.vue";
import CompetitionGoToSummary from "../../ui/competition-go-to-summary.vue";
import { useNickTestController } from "../../../test/nicktest-controller";

export default defineComponent({
  name: "ResultsImportFile",
  components: {
    CompetitionGoToSummary,
    ResultsImportVideoFilesOutput,
    SectionLinksWrapper,
    ResultsImportFileOutput,
    SectionLinkSimple,
    PhotoFinish,
    FormGenericInputTextV2,
    FormGenericInputTemplateV2,
    FieldNumberV2,
    FieldTextV2,
    ButtonGenericV2,
  },
  props: {
    builderCompetition: {
      type: Object as PropType<IBuilderCompetition>,
      required: true,
    },
  },
  setup(
    props: { builderCompetition: IBuilderCompetition },
    context: SetupContext
  ) {
    const photoFinishSystem =
      ref<ResultsFileImportControllerSystem>("TimeTronics");
    const showSection = ref<ResultsFileImportControllerStateSections>("CONFIG");

    const getFirstCompDate = computed(() => {
      return eventDateDisplay(props.builderCompetition.options.dates[0]);
    });

    const nickTestController = useNickTestController();

    return {
      photoFinishSystem,
      showSection,
      getFirstCompDate,
      nickTestController,
    };
  },
});
</script>
