<template>
  <div style="margin: 8px 4px 4px 4px">
    <LoadingSpinnerV2 v-if="isLoading" />
    <ResultsImportFile
      :builder-competition="builderCompetition"
      v-if="builderCompetition.id > 0"
    />
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import ResultsImportFile from "./ResultsImportFile.vue";
import { IBuilderCompetition } from "../../../builder/builder-models";
import { BuilderService } from "../../../builder/builder-service";
import { BuilderData } from "../../../builder/builder-data";
import { handleResponseMessages } from "../../../common/handle-http-reponse";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";

@Component({
  name: "ResultsImportFileRoute",
  components: {
    LoadingSpinnerV2,
    ResultsImportFile,
  },
})
export default class ResultsImportFileRoute extends Vue {
  public compId: number = 0;
  public isLoading = false;

  public builderCompetition: IBuilderCompetition =
    new BuilderService().factoryGetBuilder({});

  public created() {
    this.compId = isNaN(Number(this.$route.params.id))
      ? 0
      : parseInt(this.$route.params.id, 0);

    if (this.compId > 0) {
      this.isLoading = true;
      const prom = new BuilderData().read(this.compId);
      handleResponseMessages(prom);
      prom
        .then((res) => {
          if (res.errNo === 0) {
            this.builderCompetition = res.data;
          }
        })
        .finally(() => {
          this.isLoading = false;
        });
    }
  }
}
</script>
