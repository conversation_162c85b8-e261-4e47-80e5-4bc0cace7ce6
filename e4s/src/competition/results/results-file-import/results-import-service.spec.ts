import * as ResultsImportService from "./results-import-service";

describe("ResultsImportService", () => {
  test("getProcessedFileName", () => {
    expect(
      ResultsImportService.getProcessedFileName(
        "some-file-name.txt",
        new Date(2023, 6, 28, 9, 15, 30)
      )
    ).toBe("some-file-name_ee44ss_2023-07-28T09-15-30-01-00.txt");

    expect(
      ResultsImportService.getProcessedFileName(
        "some-file-name_ee44ss_2023-07-28T09-15-30-01-00.txt",
        new Date(2023, 6, 28, 10, 15, 30)
      )
    ).toBe("some-file-name_ee44ss_2023-07-28T10-15-30-01-00.txt");

    expect(
      ResultsImportService.getOriginalFileName(
        "some-file-name_ee44ss_2023-07-28T09-15-30-01-00.txt"
      )
    ).toBe("some-file-name.txt");

    expect(
      ResultsImportService.getOriginalFileName(
        "some-file-name_ee44ss_2023-07-28T09-15-30-01-00.txt",
        "_edit"
      )
    ).toBe("some-file-name_edit.txt");

    expect(
      ResultsImportService.getOriginalFileName("some-file-name.txt", "")
    ).toBe("some-file-name.txt");
  });

  test("isFileTypeOk", () => {
    expect(ResultsImportService.isFileTypeOk("some-file-name.txt", "")).toBe(
      true
    );

    expect(ResultsImportService.isFileTypeOk("some-file-name.txt", "png")).toBe(
      false
    );

    expect(
      ResultsImportService.isFileTypeOk("some-file-name.txt", "png,txt")
    ).toBe(true);

    expect(ResultsImportService.isFileTypeOk("some-file-name.txt", "txt")).toBe(
      true
    );

    expect(ResultsImportService.isFileTypeOk("some-file-name.txt", "*")).toBe(
      true
    );
  });

  test("getFileName", () => {
    expect(ResultsImportService.getFileName("some-file-name.txt")).toBe(
      "some-file-name"
    );

    expect(ResultsImportService.getFileName("some-file-name.spec.txt")).toBe(
      "some-file-name.spec"
    );

    expect(
      ResultsImportService.getFileName(
        "https://www.blah.com/some-path/some-file-name.spec.txt"
      )
    ).toBe("https://www.blah.com/some-path/some-file-name.spec");
  });

  test("doesFileExistInArray", () => {
    let fileNames = [
      "some-file-name_ee44ss_2023-07-28T09-15-30-01-00.txt",
      "some-file-name_ee44ss_2023-07-28T10-15-30-01-00.txt",
      "some-file-name_ee44ss_2023-07-28T11-15-30-01-00.txt",
    ];

    expect(
      ResultsImportService.doesFileExistInArray(
        fileNames,
        "some-file-name_ee44ss_2023-07-28T10-15-30-01-00.txt"
      )
    ).toBe(true);

    expect(
      ResultsImportService.doesFileExistInArray(
        fileNames,
        "some-file-name_ee44ss_2023-07-28T12-15-30-01-00.txt"
      )
    ).toBe(false);

    expect(
      ResultsImportService.doesFileExistInArray(
        fileNames,
        "some-file-name.txt",
        true
      )
    ).toBe(true);

    fileNames = [
      "f105-Free_Test_Data_copy_ee44ss_2023-08-15T17-32-12-01-00.avi",
      "f105-Free_Test_Data_copy_ee44ss_2023-08-15T17-32-16-01-00.avi",
      "f105-Free_Test_Data_copy_ee44ss_2023-08-15T17-32-21-01-00.avi",
      "f105-Free_Test_Data_copy_ee44ss_2023-08-15T17-32-25-01-00.avi",
      "f105-Free_Test_Data_copy_ee44ss_2023-08-15T17-32-29-01-00.avi",
      "f105-Free_Test_Data_copy_ee44ss_2023-08-15T17-32-34-01-00.avi",
      "f105-Free_Test_Data_copy_ee44ss_2023-08-15T17-32-38-01-00.avi",
      "f105-Free_Test_Data_copy_ee44ss_2023-08-15T17-32-42-01-00.avi",
    ];

    expect(
      ResultsImportService.doesFileExistInArray(
        fileNames,
        ResultsImportService.getOriginalFileName(
          "f105-Free_Test_Data_copy_ee44ss_2023-08-15T17-32-47-01-00.avi"
        ),
        true
      )
    ).toBe(true);
  });
});
