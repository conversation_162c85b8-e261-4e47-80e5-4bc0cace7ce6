import { computed, reactive, readonly } from "vue";
import * as ResultsImportService from "./results-import-service";
import { ILogging, useLogging } from "../../../logging/useLogging";
import { IBuilderCompetition } from "../../../builder/builder-models";
import {
  convertToIsoDateTimeWithOffset,
  eventDateDisplay,
} from "../../../common/common-service-utils";
import https from "../../../common/https";
import {
  IFileUploadResponse,
  IsoDateTime,
} from "../../../common/common-models";
import { messageDispatchHelper } from "../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../user-message/user-message-models";
import { format, parse } from "date-fns";
import { VideoFileTypeToOutput } from "../../scoreboard/scoreboard-output/display/v3/video/video-models-v3";
import * as CommonFileApiWrapper from "../../../common/common-file-api-wrapper";

export const resultsFileImportStatusMap = {
  STOPPED: "Stopped",
  POLLING: "Polling",
} as const;

type resultsFileImportStatus = typeof resultsFileImportStatusMap;
type resultsFileImportStatusType = keyof resultsFileImportStatus;
// type resultsFileImportStatusValue =  resultsFileImportStatus[keyof resultsFileImportStatus];

export type ResultsFileImportControllerSystem = "TimeTronics" | "Lnyx";

export type FileTypeTimeTronicsResultFileExtension = "txt";
export type FileTypeLynxResultFileExtension = "lif";
export type FileTypePhotoFinishResultFileExtension =
  | FileTypeTimeTronicsResultFileExtension
  | FileTypeLynxResultFileExtension;

export type ResultsFileImportControllerStateSections =
  | "CONFIG"
  | "RESULTS"
  | "FILES"
  | "VIDEO";

export interface IVideoParams {
  process: boolean;
  startClip: number;
  duration: number;
  reverse: boolean;
  output: VideoFileTypeToOutput;
  speed: number;
  text: string;
  text2: string;
}

export interface IResultsFileImportControllerConfig {
  stateOptions: Partial<IResultsFileImportControllerState>;
}

export interface IResultsFileImportControllerState {
  builderCompetition: IBuilderCompetition;
  isLoading: boolean;
  sourceDirectoryName: string;
  sourceDirectoryHandle: any;
  copyBeforeProcessing: boolean; //  E.g. Video files.  User has file open in another program so the "source" file is locked.
  copyBeforeProcessingDirectoryName: string;
  copyBeforeProcessingDirectoryHandle: any;
  moveProcessed: boolean;
  targetDirectoryName: string;
  targetDirectoryHandle: any;
  pollEveryMilliSecs: number;
  pollLastTime: IsoDateTime;
  isProcessing: boolean;
  processAllFilesCounter: number;
  endPoint: string;
  keepProcessedLog: boolean;
  status: resultsFileImportStatusType;
  pollingTimeOut: number;
  currentFiles: string[];
  payloadFileLabel: string;
  fileTypesAllowed: string[];
  fileTypesToMonitor: string[];
  editData: string;
  editFileName: string;
  editFileNameManual: string;
  editDataEntry: any | null;
  showSection: ResultsFileImportControllerStateSections; //  TODO @deprecated ?
  videoParams: IVideoParams;
  sendVideoParams: boolean;
  processedFilesRecord: Record<string, any>;
}
export type ResultsFileImportSubmitStatus = "SUCCESS" | "ERROR";

export interface IResultsFileImportLoggingMessage extends ILogging {
  status: ResultsFileImportSubmitStatus;
  serverMessage: string;
  isoDateTime: IsoDateTime;
  targetFileName: string;
}

export interface IResultsFileImportControllerInit {
  builderCompetition: IBuilderCompetition;
  endPoint: string;
  fileTypesToMonitor: string[];
}

export function factoryResultsFileImportController(
  config?: IResultsFileImportControllerConfig
) {
  const state: IResultsFileImportControllerState =
    ResultsImportService.factoryResultsFileImportControllerState();

  if (config && config.stateOptions) {
    Object.assign(state, config.stateOptions);
  }

  const logging = useLogging({ useGlobalState: false });
  logging.setMaxSize(100);

  //  This is to get round TS stuff... we need to update TS, etc....for another day.
  const windowInternal: any = window;

  function init(
    resultsFileImportControllerInit: IResultsFileImportControllerInit
  ) {
    state.builderCompetition =
      resultsFileImportControllerInit.builderCompetition;
    state.endPoint = resultsFileImportControllerInit.endPoint;
    state.fileTypesAllowed = resultsFileImportControllerInit.fileTypesToMonitor;

    //  Initially set to the same as the allowed so user does not have to check them.
    state.fileTypesToMonitor =
      resultsFileImportControllerInit.fileTypesToMonitor;
  }

  function startPolling() {
    state.processAllFilesCounter++;
    state.status = "POLLING";
    console.warn(
      "startPolling...START...processAllFilesCounter: " +
        state.processAllFilesCounter
    );
    state.pollingTimeOut = window.setTimeout(() => {
      processAllFilesInDirectory().then(() => {
        startPolling();
      });
    }, state.pollEveryMilliSecs);
  }

  function stopPolling() {
    state.status = "STOPPED";
    window.clearTimeout(state.pollingTimeOut);
  }

  function setPollingTime(timeInMilliSecs: number) {
    if (timeInMilliSecs < 1000) {
      state.pollEveryMilliSecs = 1000;
      return;
    }
    state.pollEveryMilliSecs = timeInMilliSecs;
  }

  /*
  function processFilesX(): Promise<void> {
    console.warn(
      "processFiles...START...processAllFilesCounter: " +
        state.processAllFilesCounter
    );
    return new Promise((resolve) => {
      state.isProcessing = true;
      return processAllFilesInDirectory()
        .then(() => {
          console.log(".....processFiles()...then");
        })
        .catch((error) => {
          console.log(".....Promise.all()...catch", error);
        })
        .finally(() => {
          console.log(".....Promise.all()...finally");
          state.isProcessing = false;
          state.pollLastTime = convertToIsoDateTimeWithOffset(new Date());
          console.warn(
            "processFiles...START...processAllFilesCounter: " +
              state.processAllFilesCounter +
              "....FINISH."
          );
          resolve();
        });
    });
  }
  */

  function pickDirectory(): Promise<void> {
    return windowInternal.showDirectoryPicker().then((res: any) => {
      state.sourceDirectoryHandle = res;

      /**
       * Getting full path is not possibel at the moment.
       * See: https://github.com/WICG/file-system-access/issues/282
       * as an example. Summary: security risk.
       */
      state.sourceDirectoryName = res.name;
      console.log(res, "res");

      return verifyTargetDirectory();
    });
  }

  async function verifyTargetDirectory() {
    state.targetDirectoryHandle =
      await state.sourceDirectoryHandle.getDirectoryHandle(
        state.targetDirectoryName,
        {
          create: true,
        }
      );
    if (state.copyBeforeProcessing) {
      state.copyBeforeProcessingDirectoryHandle =
        await state.sourceDirectoryHandle.getDirectoryHandle(
          state.copyBeforeProcessingDirectoryName,
          {
            create: true,
          }
        );
    }
  }

  async function listAllFilesInDirectory() {
    state.currentFiles = [];
    console.log("listAllFilesInDirectory...start...");
    for await (const entry of state.sourceDirectoryHandle.values()) {
      console.log("file : " + entry.name);
      if (entry.kind !== "file") {
        continue;
      }
      state.currentFiles.push(entry.name);
    }
  }

  async function doesFileExistInDirectory(
    directoryHandle: any,
    fileName: string,
    isOriginalFileName = false
  ): Promise<boolean> {
    console.log("doesFileExistInDirectory...start...");
    const fileNames = await CommonFileApiWrapper.getFilesNamesInDirectory(
      directoryHandle
    );
    const doesFileExistInTargetDirectory =
      ResultsImportService.doesFileExistInArray(
        fileNames,
        ResultsImportService.getOriginalFileName(fileName),
        isOriginalFileName
      );

    return doesFileExistInTargetDirectory;
  }

  async function editDocument(fileName: string) {
    const entry = await selectFileInDirectory(fileName);
    if (entry) {
      const file = await entry.getFile();
      const contents = await file.text();
      state.editDataEntry = entry;
      state.editFileName = file.name;
      state.editFileNameManual = ResultsImportService.getOriginalFileName(
        state.editFileName,
        "_edit"
      );
      state.editData = contents;
      console.log("processFileEntry...contents : " + contents);
    }
  }

  function editCancel() {
    state.editData = "";
    state.editFileName = "";
    state.editFileNameManual = "";
    state.editDataEntry = null;
  }

  async function saveDocument() {
    if (state.editDataEntry) {
      // const fileHandleOriginal = state.editDataEntry;
      // const file = await entry.getFile();

      // const newFileName = fileHandle.name;
      // const newFileName = ResultsImportService.getOriginalFileName(
      //   state.editFileName,
      //   "_edit"
      // );

      const newFileName = state.editFileNameManual;
      const fileHandle = await state.targetDirectoryHandle.getFileHandle(
        newFileName,
        { create: true }
      );

      const writable = await fileHandle.createWritable();
      // Write the contents of the file to the stream.
      await writable.write(state.editData);
      // Close the file and write the contents to disk.
      await writable.close();

      await fileHandle.move(state.sourceDirectoryHandle, newFileName);

      editCancel();
    }
  }

  async function selectFileInDirectory(fileName: string): Promise<any | null> {
    //  const newFileHandle = await newDirectoryHandle.getFileHandle('My Notes.txt', { create: true });
    state.currentFiles = [];
    console.log("selectFileInDirectory...start...");
    let response: any | null = null;
    for await (const entry of state.targetDirectoryHandle.values()) {
      console.log("file : " + entry.name);
      if (entry.kind !== "file") {
        continue;
      }
      if (entry.name === fileName) {
        response = entry;
      }
    }
    return response;
  }

  async function processAllFilesInDirectory() {
    const randomString = Math.random().toString(36).substring(2, 8);
    console.warn(
      randomString +
        ": processAllFilesInDirectory...START...processAllFilesCounter: " +
        state.processAllFilesCounter
    );
    for await (const entry of state.sourceDirectoryHandle.values()) {
      console.warn(
        randomString +
          ": processAllFilesInDirectory...FILE : " +
          entry.kind +
          " = " +
          entry.name
      );
      if (entry.kind !== "file") {
        continue;
      }
      //  TODO really crap hack, this promise loop is NOT working as expected.
      // if (state.processedFilesRecord[entry.name]) {
      //   console.error(
      //     randomString +
      //       ": processAllFilesInDirectory...filea already looped: " +
      //       entry.name
      //   );
      //   continue;
      // }
      if (state.copyBeforeProcessing) {
        await processFileEntryHandlesLockedFiles(entry);
      } else {
        await processFileEntry(entry);
      }

      // (await state.copyBeforeProcessing)
      //   ? processFileEntryHandlesLockedFiles(entry)
      //   : processFileEntry(entry);
    }
    console.warn(
      randomString +
        ": processAllFilesInDirectory...FINISH...processAllFilesCounter: " +
        state.processAllFilesCounter
    );
  }

  async function processFileEntry(entry: any): Promise<void> {
    const randomString = Math.random().toString(36).substring(2, 8);
    const fileName: string = entry.name;
    console.warn(
      randomString +
        ": processFileEntry...START...processAllFilesCounter: " +
        state.processAllFilesCounter
    );

    const path = await state.sourceDirectoryHandle.resolve(entry);
    console.log(randomString + ": processFileEntry...path : " + path);

    const file = await entry.getFile();

    const isFileTypeOk = ResultsImportService.isFileTypeOk(
      file.name,
      state.fileTypesToMonitor
    );

    console.log(
      randomString + ": processFileEntry...fileNameToProcess" + fileName
    );
    const processedFileName: string =
      ResultsImportService.getProcessedFileName(fileName);

    const submitResultLog: IResultsFileImportLoggingMessage =
      (await isFileTypeOk)
        ? await doUploadForm(file)
        : await Promise.resolve({
            id: 999,
            message:
              "File type not allowed: " +
              ResultsImportService.getFileNameType(file.name) +
              " - " +
              file.name,
            status: "SUCCESS",
            serverMessage: "",
            isoDateTime: new Date().toISOString(),
            targetFileName: file.name,
          });
    console.log(
      randomString +
        ": processFileEntry...serverMessage : " +
        submitResultLog.serverMessage
    );

    if (isFileTypeOk) {
      console.log(
        randomString + ": processFileEntry...newFileName : " + processedFileName
      );
      await entry.move(state.targetDirectoryHandle, processedFileName);

      submitResultLog.targetFileName = processedFileName;
      logging.addMessage(submitResultLog);
    }
    console.warn(
      randomString +
        ": processFileEntry...FINISH...processAllFilesCounter: " +
        state.processAllFilesCounter
    );
  }

  async function processFileEntryHandlesLockedFiles(entry: any): Promise<void> {
    //  a const that is 6 random characters.
    const randomString = Math.random().toString(36).substring(2, 8);

    state.processedFilesRecord[entry.name] = entry.name;

    const fileName: string = entry.name;
    const fileNameParts: string[] =
      ResultsImportService.getFileNameParts(fileName);
    console.warn(
      randomString +
        ": processFileEntryHandlesLockedFiles...fileName : " +
        fileName +
        "....START...processAllFilesCounter: " +
        state.processAllFilesCounter
    );

    const isFileTypeOk = ResultsImportService.isFileTypeOk(
      fileName,
      state.fileTypesToMonitor
    );

    if (!isFileTypeOk) {
      return Promise.resolve();
    }

    const file = await entry.getFile();
    const fileNameToProcess =
      fileNameParts[0] + "_copy_" + file.lastModified + "." + fileNameParts[1];

    console.log(
      randomString +
        ": processFileEntryHandlesLockedFiles...fileNameToProcess" +
        fileNameToProcess
    );
    const processedFileName: string =
      ResultsImportService.getProcessedFileName(fileNameToProcess);

    const isFileAlreadyProcessed = await doesFileExistInDirectory(
      state.targetDirectoryHandle,
      processedFileName,
      true
    );
    console.log(
      randomString +
        ": processFileEntryHandlesLockedFiles...isFileAlreadyProcessed" +
        isFileAlreadyProcessed
    );

    if (isFileAlreadyProcessed) {
      console.log(
        randomString +
          ": processFileEntryHandlesLockedFiles...isFileAlreadyProcessed: true...returning"
      );
      return Promise.resolve();
    }

    console.log(
      randomString + ": processFileEntryHandlesLockedFiles...copy file..."
    );
    const copiedFileHandle = await CommonFileApiWrapper.copyFile(
      state.sourceDirectoryHandle,
      state.copyBeforeProcessingDirectoryHandle,
      fileName,
      fileNameToProcess
    );
    console.log(
      randomString + ": processFileEntryHandlesLockedFiles...copied file..."
    );

    console.log(
      randomString + ": processFileEntryHandlesLockedFiles...get file again..."
    );
    const sourceEntry =
      await state.copyBeforeProcessingDirectoryHandle.getFileHandle(
        copiedFileHandle.name
      );
    const sourceEntryFile = await sourceEntry.getFile();
    console.log(
      randomString +
        ": processFileEntryHandlesLockedFiles...get file again...got"
    );

    console.log(
      randomString + ": processFileEntryHandlesLockedFiles...upload..."
    );
    const submitResultLog: IResultsFileImportLoggingMessage =
      await doUploadForm(sourceEntryFile);
    console.log(
      randomString + ": processFileEntryHandlesLockedFiles...upload...done."
    );

    console.log(
      randomString +
        ": processFileEntryHandlesLockedFiles...serverMessage : " +
        submitResultLog.serverMessage
    );

    const fileNameToRemove = sourceEntryFile.name;
    console.log(
      randomString +
        ": processFileEntryHandlesLockedFiles...removeEntry...sourceEntryFile.name: " +
        fileNameToRemove
    );
    //  WE do not want to copy the file as just take sup disk space...
    // await sourceEntry.move(state.targetDirectoryHandle, processedFileName);
    await state.copyBeforeProcessingDirectoryHandle.removeEntry(
      fileNameToRemove
    );
    console.log(
      randomString +
        randomString +
        ": processFileEntryHandlesLockedFiles...removeEntry...processedFileName: " +
        fileNameToRemove +
        "...done."
    );

    console.log(
      randomString +
        ": processFileEntryHandlesLockedFiles...make dummy empty file..."
    );
    //  ...so make an "empty" file, so we can still check if already processed.
    await state.targetDirectoryHandle.getFileHandle(processedFileName, {
      create: true,
    });
    console.log(
      randomString +
        ": processFileEntryHandlesLockedFiles...make dummy empty file...done"
    );

    submitResultLog.targetFileName = processedFileName;
    logging.addMessage(submitResultLog);
    console.warn(
      randomString +
        ": processFileEntryHandlesLockedFiles...fileName : " +
        fileName +
        "....FINISH.processAllFilesCounter: " +
        state.processAllFilesCounter
    );
    console.log(randomString + ": processFileEntryHandlesLockedFiles...FINISH");
  }

  // function submitFile(file: any): Promise<IResultsFileImportLoggingMessage> {
  //   console.warn(
  //     "submitFile...fileName : " +
  //       file.name +
  //       "....FINISH.processAllFilesCounter: " +
  //       state.processAllFilesCounter
  //   );
  //   return doUploadForm(file);
  // }

  function doUploadForm(file: any): Promise<IResultsFileImportLoggingMessage> {
    const randomString = Math.random().toString(36).substring(2, 8);

    console.warn(
      randomString +
        ": doUploadForm...fileName : " +
        file.name +
        "....FINISH.processAllFilesCounter: " +
        state.processAllFilesCounter
    );
    const memberData = new FormData();
    memberData.append(state.payloadFileLabel, file);

    // let endPoint = state.endPoint;
    if (state.sendVideoParams) {
      // endPoint = endPoint + "?" + convertObjectToUrlParams(state.videoParams);
      memberData.append("videoparams", JSON.stringify(state.videoParams));
    }

    const req = https.post(state.endPoint, memberData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }) as any as Promise<IFileUploadResponse>;

    const log: IResultsFileImportLoggingMessage = {
      id: new Date().getTime(),
      status: "SUCCESS",
      message: file.name,
      serverMessage: "",
      isoDateTime: convertToIsoDateTimeWithOffset(new Date()),
      targetFileName: "",
    };

    return req
      .then((response: IFileUploadResponse) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );

          log.status = "ERROR";
          log.serverMessage = response.errNo + " - " + response.error;

          // logging.addMessage(log);

          return log;
        }
        return log;
      })
      .catch((error) => {
        log.status = "ERROR";
        log.serverMessage = error.message;
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return log;
      })
      .finally(() => {
        console.warn(
          randomString +
            ": doUploadForm...fileName : " +
            file.name +
            "....FINISH.processAllFilesCounter: " +
            state.processAllFilesCounter +
            "....FINISH."
        );
      });
  }

  /**
   * Turn "2023-06-02T17:51:01+01:00" into "17:51:01"
   * @param iso
   */
  function loggingIsoToDisplay(iso: IsoDateTime): string {
    return iso.split("T")[1].slice(0, 8);
  }

  function destroy() {
    stopPolling();
  }

  return {
    state,
    init,
    pickDirectory,
    listAllFilesInDirectory,
    setPollingTime,
    startPolling,
    stopPolling,
    logging,
    loggingIsoToDisplay,
    selectFileInDirectory,
    editDocument,
    editCancel,
    saveDocument,
    destroy,
  };
}

export function useResultsFileImportController(
  config?: IResultsFileImportControllerConfig
) {
  const controller = factoryResultsFileImportController(config);

  const state = reactive(controller.state);
  const stateCopy = readonly(controller.state);

  const isSourceFolderSelected = computed(() => {
    return state.sourceDirectoryName.length > 0;
  });

  const getFirstCompDate = computed(() => {
    return eventDateDisplay(state.builderCompetition.options.dates[0]);
  });

  const getLastPollTime = computed(() => {
    if (state.pollLastTime === "") {
      return "";
    }
    return format(parse(state.pollLastTime), "HH:mm:ss");
  });

  return {
    controller,
    state,
    stateCopy,
    isSourceFolderSelected,
    getFirstCompDate,
    getLastPollTime,
  };
}
