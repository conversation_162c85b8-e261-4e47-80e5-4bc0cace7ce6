<template>
    <div>
        <div class="row">
            <div class="col s12 m12 l12">
                <div v-if="isLoading">
                    <div class="e4s-section-padding-separator"></div>
                    <LoadingSpinner></LoadingSpinner>
                </div>

                <ResultEvent
                  v-if="resultEvent.eventGroup.id > 0"
                  :result-event="resultEvent"
                  :r4s-comp-schedule="r4sCompSchedule"
                ></ResultEvent>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import {ResultData} from "./result-data";
import {IResultEvent} from "./results-models";
import {ResultsService} from "./results-service";
import ResultEvent from "./result-event.vue";
import {Rs4Service} from "../scoreboard/rs4/rs4-service";
import {ScoreboardData} from "../scoreboard/scoreboard-data";
import {handleResponseMessages} from "../../common/handle-http-reponse";
import {IR4sCompSchedule} from "../scoreboard/rs4/rs4-scoreboard-models";

@Component({
    name: "result-route",
    components: {ResultEvent}
})
export default class ResultRoute extends Vue {

    public compId: number = 0
    public eventGroupId: number = 0
    public isLoading = false;
    public resultsService: ResultsService = new ResultsService();
    public rs4Service: Rs4Service = new Rs4Service();

    public resultEvent: IResultEvent = this.resultsService.factoryResultEvent();
    public r4sCompSchedule: IR4sCompSchedule = this.rs4Service.factoryR4sCompSchedule();

    public editMode = false;

    public created() {
        this.compId = isNaN(Number(this.$route.params.compId)) ? 0 : parseInt(this.$route.params.compId, 0)
        this.eventGroupId = isNaN(Number(this.$route.params.eventGroupId)) ? 0 : parseInt(this.$route.params.eventGroupId, 0);
        this.isLoading = true;

        const promResultEvent = new ResultData().getResultEvent(this.eventGroupId);
        handleResponseMessages(promResultEvent);
        promResultEvent
        .then((resp) => {
            if (resp.errNo === 0) {
                this.resultEvent = resp.data;
            }
        });

        const promSchedule = new ScoreboardData().getCompSchedule(this.compId);
        handleResponseMessages(promSchedule);
        promSchedule
          .then((resp) => {
              if (resp.errNo === 0) {
                  this.r4sCompSchedule = resp.data;
              }
          });

        Promise.all([promResultEvent, promSchedule])
        .finally(() => {
            this.isLoading = false;
        })
    }

}
</script>
