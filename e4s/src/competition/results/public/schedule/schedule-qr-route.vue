<template>
  <div class="row">
    <div class="col s12 m12 l12">
      <LoadingSpinner v-if="isLoading"></LoadingSpinner>
      <ButtonPrintV2 class="e4s-do-not-print" />
      <ScheduleQr
        :competition-summary-public="competitionSummaryPublic"
        v-if="competitionSummaryPublic.compId > 0"
      ></ScheduleQr>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import ScheduleQr from "./schedule-qr.vue";
import { CompetitionData } from "../../../competition-data";
import { handleResponseMessages } from "../../../../common/handle-http-reponse";
import { ICompetitionSummaryPublic } from "../../../competition-models";
import { CompetitionService } from "../../../competiton-service";
import ButtonPrintV2 from "../../../../common/ui/layoutV2/buttons/button-print-v2.vue";

@Component({
  name: "schedule-qr-route",
  components: {
    ButtonPrintV2,
    ScheduleQr,
  },
})
export default class ScheduleQrRoute extends Vue {
  public competitionSummaryPublic: ICompetitionSummaryPublic =
    new CompetitionService().factorySummaryPublic();
  public isLoading = false;

  public created() {
    const compId: number = isNaN(Number(this.$route.params.compId))
      ? 0
      : parseInt(this.$route.params.compId, 0);

    this.isLoading = true;
    const promComp = new CompetitionData().getCompById(compId);
    handleResponseMessages(promComp);
    promComp
      .then((response) => {
        if (response.errNo === 0) {
          this.competitionSummaryPublic = response.data;
        }
      })
      .finally(() => {
        this.isLoading = false;
      });
  }
}
</script>
