<template>
  <div class="e4s-flex-column e4s-full-width">
    <div class="e4s-flex-row e4s-justify-flex-space-between">
      <span v-text="getEventName" class="e4s-header--500"></span>
      <ButtonGenericBackV2 v-on:click="onCancel"/>
    </div>

    <div class="e4s-flex-column e4s-full-width">
      <table class="result-entries--table">
        <tr class="e4s-header--500">
          <th class="">Name</th>
          <th class="result-entries--narrow result-entries--centre">Gender</th>
          <th class="result-entries--narrow result-entries--centre">Age</th>
          <th class="">Association</th>
        </tr>

        <tr
          v-for="athleteEntry in athleteEntries"
          :key="athleteEntry.athleteId"
        >
          <td>
            <span
              v-text="athleteEntry.firstname + ' ' + athleteEntry.surname"
            ></span>
            <PowerOfTenLinkV2 :urn="athleteEntry.urn || ''" />
          </td>
          <td v-text="athleteEntry.gender" class="result-entries--centre"></td>
          <td
            v-text="athleteEntry.ageGroup.shortName"
            class="result-entries--centre"
          ></td>
          <td>
            <span v-text="athleteEntry.clubname"></span>
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script lang="ts">
import {computed, defineComponent, PropType, SetupContext} from "vue"
import {IR4sAthleteEntry} from "../../../../scoreboard/rs4/rs4-scoreboard-models"
import ButtonGenericBackV2 from "../../../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue"
import PowerOfTenLinkV2 from "../../../../../common/ui/layoutV2/PowerOfTenLinkV2.vue"
import {IAthleteCompSchedRuleEvent} from "../../../../../athleteCompSched/athletecompsched-models"
import {AthleteCompSchedService} from "../../../../../athleteCompSched/athletecompsched-service"

const athleteCompSchedService = new AthleteCompSchedService();

export default defineComponent({
  name: "result-entries-simple",
  components: {PowerOfTenLinkV2, ButtonGenericBackV2},
  props: {
    athleteEntries: {
      type: Array as PropType<IR4sAthleteEntry[]>
    },
    athleteCompSchedRuleEvent: {
      type: Object as PropType<IAthleteCompSchedRuleEvent>,
      default: function() {
        return athleteCompSchedService.factoryAthleteCompSchedRuleEvent();
      }
    }
  },
  setup(props: any, context: SetupContext) {

    /*
    watch(
      () => props.athlete,
      (newValue: IAthlete) => {
        athleteForm.init(newValue);
      },
      {
        immediate: true,
      }
    );
     */

    const getEventName = computed(()=>{
      return props.athleteCompSchedRuleEvent.eventGroup
        ;
    })

    function onCancel() {
      context.emit("onCancel");
    }

    return {getEventName, onCancel};
  },
});
</script>
