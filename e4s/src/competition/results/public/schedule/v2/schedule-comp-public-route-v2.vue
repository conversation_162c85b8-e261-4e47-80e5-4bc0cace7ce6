<template>
  <div class="e4s-content-wrapper">
    <LoadingSpinnerV2 v-if="scheduleCompController.getState.value.isLoading"/>
    <CardGenericV2>
      <ScheduleCompPublicV2
        slot="all"
        v-if="scheduleCompController.getState.value.isReady"
        :r4s-comp-schedule="scheduleCompController.getState.value.compSchedule"
      />
    </CardGenericV2>
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "vue";
import { useRoute } from "../../../../../router/migrateRouterVue3";
import LoadingSpinnerV2 from "../../../../../common/ui/loading-spinner-v2.vue";
import { useScheduleCompController } from "./useScheduleCompController";
import ScheduleCompPublicV2 from "./schedule-comp-public-v2.vue"
import CardGenericV2 from "../../../../../common/ui/layoutV2/card-generic-v2.vue"

export default defineComponent({
  name: "schedule-comp-public-route-v2",
  components: {CardGenericV2, ScheduleCompPublicV2, LoadingSpinnerV2 },
  props: {},
  setup(props: any, context: SetupContext) {
    const route = useRoute();

    const scheduleCompController = useScheduleCompController();
    const compId = isNaN(Number(route.params.id)) ? 0 : Number(route.params.id);
    if (compId) {
      scheduleCompController.getSchedule(compId);
    }

    return {scheduleCompController};
  },
});
</script>
