<template>
  <div class="e4s-flex-column e4s-full-width e4s-gap--large">
    <div class="e4s-flex-row e4s-justify-flex-space-between">
      <div class="e4s-flex-row e4s-gap--standard e4s-header--400">
        <span v-text="getEventName"></span>
        <AdminIdV2 :obj="scheduleTableRow.scheduledEvent" />
      </div>

      <!--      <ButtonGenericV2 button-type="tertiary" text="Back" v-on:click="close" />-->
      <ButtonGenericBackV2
        button-type="tertiary"
        :text="backButtonText"
        v-on:click="close"
      />
    </div>

    <div class="e4s-flex-column e4s-full-width">
      <table class="result-entries--table">
        <tr class="e4s-header--500">
          <th class="">Name</th>
          <th class="result-entries--narrow result-entries--centre">Gender</th>
          <th class="result-entries--narrow result-entries--centre">Age</th>
          <th class="">Association</th>
        </tr>

        <tr
          v-for="athleteEntry in athleteEntries"
          :key="athleteEntry.athleteId"
          :class="getRowClass(athleteEntry)"
        >
          <td>
            <span
              v-text="athleteEntry.firstname + ' ' + athleteEntry.surname"
            ></span>
            <!--              <PowerOfTenLink :urn="athleteEntry.urn" />-->
            <PowerOfTenLinkV2 :urn="athleteEntry.urn" />
          </td>
          <td v-text="athleteEntry.gender" class="result-entries--centre"></td>
          <td
            v-text="athleteEntry.ageGroup.shortName"
            class="result-entries--centre"
          ></td>
          <td>
            <span v-text="athleteEntry.clubname"></span>
            <div
              v-if="getIsThisAFeederComp"
              v-text="
                athleteEntry.entryOptions.autoEntries.targetEventGroup.id +
                ': ' +
                athleteEntry.entryOptions.autoEntries.targetEventGroup.name
              "
            ></div>
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import { IConfigApp } from "../../../../../config/config-app-models";
import { ResultsService } from "../../../results-service";
import { Rs4Service } from "../../../../scoreboard/rs4/rs4-service";
import { ConfigService } from "../../../../../config/config-service";
import ScheduleEventGroupPicker from "../../entries/ScheduleEventGroupPicker.vue";
import FieldHelp from "../../../../../common/ui/field/field-help/field-help.vue";
import { ScoreboardService } from "../../../../scoreboard/scoreboard-service";
import {
  IR4sAthleteEntry,
  IR4sCompSchedule,
  IR4sScheduledEvent,
  IScheduleTableRow,
} from "../../../../scoreboard/rs4/rs4-scoreboard-models";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../../../config/config-store";
import { IAutoEntrySubmit } from "../../entries/auto-entries-models";
import { AutoEntriesData } from "../../entries/auto-entries-data";
import { format, parse } from "date-fns";
import PowerOfTenLinkV2 from "../../../../../common/ui/layoutV2/PowerOfTenLinkV2.vue";
import { CONFIG } from "../../../../../common/config";
import ButtonGenericV2 from "../../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import ButtonGenericBackV2 from "../../../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import AdminIdV2 from "../../../../../common/ui/layoutV2/admin-id-v2.vue";

const resultsService = new ResultsService();
const rs4Service: Rs4Service = new Rs4Service();
const scoreboardService: ScoreboardService = new ScoreboardService();
const configService = new ConfigService();

@Component({
  name: "result-entries",
  components: {
    AdminIdV2,
    ButtonGenericBackV2,
    ButtonGenericV2,
    PowerOfTenLinkV2,
    ScheduleEventGroupPicker,
    FieldHelp,
  },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
    }),
  },
})
export default class ResultEntries extends Vue {
  public readonly isAdmin: boolean;
  public readonly configApp: IConfigApp;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sCompSchedule();
    },
  })
  public readonly r4sCompSchedule!: IR4sCompSchedule;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sCompSchedule();
    },
  })
  public readonly r4sCompScheduleTarget!: IR4sCompSchedule;

  @Prop({
    default: () => {
      return scoreboardService.factoryScheduleTableRow();
    },
  })
  public readonly scheduleTableRow: IScheduleTableRow;

  @Prop({
    default: "Back",
  })
  public readonly backButtonText!: string;

  public athleteEntries: IR4sAthleteEntry[] = [];
  public isLoading = false;

  public rs4Service: Rs4Service = new Rs4Service();

  public scheduledEventSelected: IR4sScheduledEvent =
    resultsService.factoryR4sScheduledEvent();

  @Watch("scheduleTableRow", { immediate: true })
  public onScheduleTableRowChanged(
    newValue: IScheduleTableRow,
    oldValue: IScheduleTableRow
  ) {
    if (newValue.athleteEntries) {
      this.athleteEntries = R.clone(newValue.athleteEntries).map(
        (athleteEntry) => {
          if (!athleteEntry.entryOptions) {
            athleteEntry.entryOptions = {
              autoEntries: resultsService.factoryAutoEntryOptions(),
            };
          }
          if (!athleteEntry.entryOptions.autoEntries) {
            athleteEntry.entryOptions.autoEntries =
              resultsService.factoryAutoEntryOptions();
          }
          return athleteEntry;
        }
      );
    }
  }

  public getRowClass(
    athleteEntry: IR4sAthleteEntry
  ): "result-entries--big-q" | "result-entries--big-q-select-match" | "" {
    if (athleteEntry.entryOptions.autoEntries.targetEventGroup.id > 0) {
      if (
        athleteEntry.entryOptions.autoEntries.targetEventGroup.id ===
        this.scheduledEventSelected.id
      ) {
        return "result-entries--big-q-select-match";
      }

      return "result-entries--big-q";
    }
    return "";
  }

  public isBigQ(athleteEntry: IR4sAthleteEntry): boolean {
    return athleteEntry.qualify === "Q";
  }

  public isLittleQ(athleteEntry: IR4sAthleteEntry): boolean {
    return athleteEntry.qualify === "q";
  }

  public get getShowQualify(): boolean {
    if (!resultsService.canQualifyToAnotherComp(this.r4sCompSchedule)) {
      return false;
    }
    return this.isAdmin || this.getHasBuilderPermissionForComp;
  }

  public get getHasBuilderPermissionForComp(): boolean {
    return configService.hasBuilderPermissionForComp(
      this.configApp.userInfo,
      this.r4sCompSchedule.org.id,
      this.r4sCompSchedule.compId
    );
  }

  public setQ(athleteEntry: IR4sAthleteEntry) {
    this.athleteEntries = this.athleteEntries.map((athlete) => {
      if (athlete.athleteId === athleteEntry.athleteId) {
        athlete.entryOptions.autoEntries.targetEventGroup = {
          id: this.scheduledEventSelected.id,
          name: this.scheduledEventSelected.name,
        };
      }
      return athlete;
    });
  }

  public removeQ(athleteEntry: IR4sAthleteEntry) {
    this.athleteEntries = this.athleteEntries.map((athlete) => {
      if (athlete.athleteId === athleteEntry.athleteId) {
        athlete.entryOptions.autoEntries.targetEventGroup = {
          id: 0,
          name: "",
        };
      }
      return athlete;
    });
  }

  public isQualified(athleteEntry: IR4sAthleteEntry): boolean {
    return athleteEntry.qualify === "Q";
  }

  public get getCompDate() {
    return this.r4sCompSchedule.date.length > 0
      ? format(parse(this.r4sCompSchedule.date), "Do MMM YYYY")
      : "";
  }

  public get getLogoPath(): string {
    return CONFIG.E4S_HOST + this.r4sCompSchedule.logo;
  }

  public get getEventName() {
    if (this.scheduleTableRow.scheduledEvent) {
      // return commonService.getIdAndDescription(
      //   this.scheduleTableRow.scheduledEvent as IR4sScheduledEvent
      // );
      return (this.scheduleTableRow.scheduledEvent as IR4sScheduledEvent).name;
    }
    return "";
  }

  public onEventGroupSelected(r4sScheduledEvent: IR4sScheduledEvent) {
    this.scheduledEventSelected = R.clone(r4sScheduledEvent);
  }

  public get getIsEventGroupSelected(): boolean {
    return this.scheduledEventSelected.id > 0;
  }

  public get getIsThisAFeederComp() {
    return this.rs4Service.isFeederComp(this.r4sCompSchedule);
  }

  public close() {
    this.$emit("close");
  }

  public save() {
    //  TODO   move to centralised.

    const entries: IAutoEntrySubmit[] = this.athleteEntries.map(
      (athleteEntry) => {
        return {
          sourceEntryId: athleteEntry.entryId,
          targetEventGroupId:
            athleteEntry.entryOptions.autoEntries.targetEventGroup.id,
        };
      }
    );
    this.isLoading = true;
    new AutoEntriesData().submitEntries(entries).finally(() => {
      this.isLoading = false;
    });

    this.$emit("save");
  }
}
</script>
