import { IR4sCompSchedule } from "../../../../../scoreboard/rs4/rs4-scoreboard-models";

export const scheduleMockLive337: IR4sCompSchedule = {
  compId: 337,
  org: { id: 90, name: "Midland Masters Athletic Club" },
  name: "Midland Masters Championships 2023",
  date: "2023-06-11",
  location: "The Pingles, Nuneaton",
  club: "Midland Masters Athletic Club",
  logo: "/resources/midlandmasters.png",
  autoEntries: {
    selectedTargetComp: { id: 0, name: "", timeTronicsUrl: "" },
    targetable: { allowedSources: [], enabled: false },
  },
  schedule: [
    {
      id: 5970,
      results: true,
      eventNo: 1,
      name: "T1 : 2k Walk",
      typeNo: "T1",
      startDate: "2022-06-04T10:00:00",
      eventName: "2k Walk",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 8, maxAthletes: 20, maxInHeat: 20 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5987,
      results: true,
      eventNo: 4,
      name: "T4 : 80m Hurdles W40-75",
      typeNo: "T4",
      startDate: "2022-06-04T10:40:00",
      eventName: "80m Hurdles W40-75",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 8, maxAthletes: 9, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5939,
      results: true,
      eventNo: 3,
      name: "T3 : 100m Hurdles M50-65",
      typeNo: "T3",
      startDate: "2022-06-04T10:30:00",
      eventName: "100m Hurdles M50-65",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 4, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5989,
      results: true,
      eventNo: 50,
      name: "F1 : Discus Women & M70+",
      typeNo: "F1",
      startDate: "2022-06-04T09:15:00",
      eventName: "Discus Women & M70+",
      wind: "",
      type: "D",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 16, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5952,
      results: true,
      eventNo: 6,
      name: "T6 : 100m W70+",
      typeNo: "T6",
      startDate: "2022-06-04T10:55:00",
      eventName: "100m W70+",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 3, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5951,
      results: true,
      eventNo: 7,
      name: "T7 : 100m W60/65",
      typeNo: "T7",
      startDate: "2022-06-04T11:00:00",
      eventName: "100m W60/65",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 3, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5990,
      results: true,
      eventNo: 51,
      name: "F2 : Hammer M35-50",
      typeNo: "F2",
      startDate: "2022-06-04T10:30:00",
      eventName: "Hammer M35-50",
      wind: "",
      type: "D",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 7, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5950,
      results: true,
      eventNo: 8,
      name: "T8 : 100m W50/55",
      typeNo: "T8",
      startDate: "2022-06-04T11:05:00",
      eventName: "100m W50/55",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 8, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5949,
      results: true,
      eventNo: 9,
      name: "T9 : 100m W45",
      typeNo: "T9",
      startDate: "2022-06-04T11:10:00",
      eventName: "100m W45",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 2, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5948,
      results: true,
      eventNo: 10,
      name: "T10 : 100m W35/40",
      typeNo: "T10",
      startDate: "2022-06-04T11:15:00",
      eventName: "100m W35/40",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 6, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5947,
      results: true,
      eventNo: 11,
      name: "T11 : 100m M70+",
      typeNo: "T11",
      startDate: "2022-06-04T11:20:00",
      eventName: "100m M70+",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 6, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5946,
      results: true,
      eventNo: 12,
      name: "T12 : 100m M65",
      typeNo: "T12",
      startDate: "2022-06-04T11:25:00",
      eventName: "100m M65",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 5, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5945,
      results: true,
      eventNo: 13,
      name: "T13 : 100m M60",
      typeNo: "T13",
      startDate: "2022-06-04T11:30:00",
      eventName: "100m M60",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 6, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5944,
      results: true,
      eventNo: 14,
      name: "T14 : 100m M55",
      typeNo: "T14",
      startDate: "2022-06-04T11:35:00",
      eventName: "100m M55",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 3, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5943,
      results: true,
      eventNo: 15,
      name: "T15 : 100m M50",
      typeNo: "T15",
      startDate: "2022-06-04T11:40:00",
      eventName: "100m M50",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 5, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5942,
      results: true,
      eventNo: 16,
      name: "T16 : 100m M45",
      typeNo: "T16",
      startDate: "2022-06-04T11:45:00",
      eventName: "100m M45",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 3, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5941,
      results: true,
      eventNo: 17,
      name: "T17 : 100m M40",
      typeNo: "T17",
      startDate: "2022-06-04T11:50:00",
      eventName: "100m M40",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 4, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5940,
      results: true,
      eventNo: 18,
      name: "T18 : 100m M35",
      typeNo: "T18",
      startDate: "2022-06-04T11:55:00",
      eventName: "100m M35",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 5, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5998,
      results: true,
      eventNo: 52,
      name: "F3 : Pole Vault",
      typeNo: "F3",
      startDate: "2022-06-04T10:30:00",
      eventName: "Pole Vault",
      wind: "",
      type: "H",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 5, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5971,
      results: true,
      eventNo: 20,
      name: "T20 : 300m Hurdles M60/75 & W50/65",
      typeNo: "T20",
      startDate: "2022-06-04T12:10:00",
      eventName: "300m Hurdles M60/75 & W50/65",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 4, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5972,
      results: true,
      eventNo: 21,
      name: "T21 : 400m Hurdles M35-65 & W35-45",
      typeNo: "T21",
      startDate: "2022-06-04T12:20:00",
      eventName: "400m Hurdles M35-65 & W35-45",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 6, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5969,
      results: true,
      eventNo: 22,
      name: "T22 : 200m W70+",
      typeNo: "T22",
      startDate: "2022-06-04T12:30:00",
      eventName: "200m W70+",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 2, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5968,
      results: true,
      eventNo: 23,
      name: "T23 : 200m W60/65",
      typeNo: "T23",
      startDate: "2022-06-04T12:35:00",
      eventName: "200m W60/65",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 6, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5993,
      results: true,
      eventNo: 53,
      name: "F4 : High Jump",
      typeNo: "F4",
      startDate: "2022-06-04T10:30:00",
      eventName: "High Jump",
      wind: "",
      type: "H",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 11, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5967,
      results: true,
      eventNo: 24,
      name: "T24 : 200m W50/55",
      typeNo: "T24",
      startDate: "2022-06-04T12:40:00",
      eventName: "200m W50/55",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 8, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5966,
      results: true,
      eventNo: 25,
      name: "T25 : 200m W35-45",
      typeNo: "T25",
      startDate: "2022-06-04T12:45:00",
      eventName: "200m W35-45",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 7, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5964,
      results: true,
      eventNo: 27,
      name: "T27 : 200m M65/70",
      typeNo: "T27",
      startDate: "2022-06-04T12:55:00",
      eventName: "200m M65/70",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 8, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5958,
      results: true,
      eventNo: 28,
      name: "T28 : 200m M 60",
      typeNo: "T28",
      startDate: "2022-06-04T13:00:00",
      eventName: "200m M 60",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 5, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 6648,
      results: true,
      eventNo: 29,
      name: "T29 : 200m M50/55 & M80",
      typeNo: "T29",
      startDate: "2022-06-04T13:10:00",
      eventName: "200m M50/55 & M80",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      counts: { entries: 1, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5961,
      results: true,
      eventNo: 31,
      name: "T31 : 200m M45",
      typeNo: "T31",
      startDate: "2022-06-04T13:15:00",
      eventName: "200m M45",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 4, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5991,
      results: true,
      eventNo: 55,
      name: "F6 : Hammer M55-75",
      typeNo: "F6",
      startDate: "2022-06-04T11:30:00",
      eventName: "Hammer M55-75",
      wind: "",
      type: "D",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 13, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 6646,
      results: true,
      eventNo: 32,
      name: "T32 : 200m M35/40",
      typeNo: "T32",
      startDate: "2022-06-04T13:20:00",
      eventName: "200m M35/40",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      counts: { entries: 6, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 6001,
      results: true,
      eventNo: 54,
      name: "F5 : Shotput Women",
      typeNo: "F5",
      startDate: "2022-06-04T10:30:00",
      eventName: "Shotput Women",
      wind: "",
      type: "D",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 16, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5985,
      results: true,
      eventNo: 34,
      name: "T34 : 800m W50+",
      typeNo: "T34",
      startDate: "2022-06-04T13:35:00",
      eventName: "800m W50+",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 6, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5984,
      results: true,
      eventNo: 35,
      name: "T35 : 800m W35-45",
      typeNo: "T35",
      startDate: "2022-06-04T13:45:00",
      eventName: "800m W35-45",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 5, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5983,
      results: true,
      eventNo: 36,
      name: "T36 : 800m M60+",
      typeNo: "T36",
      startDate: "2022-06-04T13:55:00",
      eventName: "800m M60+",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 4, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5997,
      results: true,
      eventNo: 57,
      name: "F8 : Long Jump Women",
      typeNo: "F8",
      startDate: "2022-06-04T12:15:00",
      eventName: "Long Jump Women",
      wind: "A",
      type: "D",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 16, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5982,
      results: true,
      eventNo: 37,
      name: "T37 : 800m M50/55",
      typeNo: "T37",
      startDate: "2022-06-04T14:10:00",
      eventName: "800m M50/55",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 5, maxAthletes: 9, maxInHeat: 9 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5992,
      results: true,
      eventNo: 58,
      name: "F9 : Hammer Women & M80+",
      typeNo: "F9",
      startDate: "2022-06-04T12:45:00",
      eventName: "Hammer Women & M80+",
      wind: "",
      type: "D",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 11, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 6000,
      results: true,
      eventNo: 59,
      name: "F10 : Shotput M65+",
      typeNo: "F10",
      startDate: "2022-06-04T13:30:00",
      eventName: "Shotput M65+",
      wind: "",
      type: "D",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 11, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5981,
      results: true,
      eventNo: 38,
      name: "T38 : 800m M35-45",
      typeNo: "T38",
      startDate: "2022-06-04T14:20:00",
      eventName: "800m M35-45",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 9, maxAthletes: 9, maxInHeat: 9 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5999,
      results: true,
      eventNo: 56,
      name: "F7 : Shotput M35-60",
      typeNo: "F7",
      startDate: "2022-06-04T12:15:00",
      eventName: "Shotput M35-60",
      wind: "",
      type: "D",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 16, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5996,
      results: true,
      eventNo: 60,
      name: "F11 : Long Jump Men",
      typeNo: "F11",
      startDate: "2022-06-04T13:30:00",
      eventName: "Long Jump Men",
      wind: "A",
      type: "D",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 11, maxAthletes: 12, maxInHeat: 12 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5995,
      results: true,
      eventNo: 61,
      name: "F12 : Javelin Women & M70+",
      typeNo: "F12",
      startDate: "2022-06-04T14:00:00",
      eventName: "Javelin Women & M70+",
      wind: "",
      type: "D",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 17, maxAthletes: 17, maxInHeat: 17 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5979,
      results: true,
      eventNo: 40,
      name: "T40 : 5000m M35-55",
      typeNo: "T40",
      startDate: "2022-06-04T15:00:00",
      eventName: "5000m M35-55",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 10, maxAthletes: 18, maxInHeat: 18 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5978,
      results: true,
      eventNo: 41,
      name: "T41 : 400m W45-70",
      typeNo: "T41",
      startDate: "2022-06-04T15:30:00",
      eventName: "400m W45-70",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 8, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5980,
      results: true,
      eventNo: 39,
      name: "T39 : 5000m Women & M60+",
      typeNo: "T39",
      startDate: "2022-06-04T14:30:00",
      eventName: "5000m Women & M60+",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 13, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5976,
      results: true,
      eventNo: 43,
      name: "T43 : 400m M70+",
      typeNo: "T43",
      startDate: "2022-06-04T15:40:00",
      eventName: "400m M70+",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 4, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5974,
      results: true,
      eventNo: 44,
      name: "T44 : 400m M50/55",
      typeNo: "T44",
      startDate: "2022-06-04T15:45:00",
      eventName: "400m M50/55",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 6, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5973,
      results: true,
      eventNo: 46,
      name: "T46 : 400m M35-45",
      typeNo: "T46",
      startDate: "2022-06-04T15:55:00",
      eventName: "400m M35-45",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 9, maxAthletes: 10, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5954,
      results: true,
      eventNo: 47,
      name: "T47 : 1500m Female ",
      typeNo: "T47",
      startDate: "2022-06-04T16:05:00",
      eventName: "1500m Female ",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 9, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5975,
      results: true,
      eventNo: 45,
      name: "T45 : 400m M60/65",
      typeNo: "T45",
      startDate: "2022-06-04T15:50:00",
      eventName: "400m M60/65",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 3, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5956,
      results: true,
      eventNo: 48,
      name: "T48 : 1500m M50+",
      typeNo: "T48",
      startDate: "2022-06-04T16:20:00",
      eventName: "1500m M50+",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 8, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 6002,
      results: true,
      eventNo: 62,
      name: "F13 : Triple Jump",
      typeNo: "F13",
      startDate: "2022-06-04T14:30:00",
      eventName: "Triple Jump",
      wind: "A",
      type: "D",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 17, maxAthletes: 17, maxInHeat: 17 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5955,
      results: true,
      eventNo: 49,
      name: "T49 : 1500m M35-45",
      typeNo: "T49",
      startDate: "2022-06-04T16:35:00",
      eventName: "1500m M35-45",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 9, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5994,
      results: true,
      eventNo: 63,
      name: "F14 : Javelin M35-65",
      typeNo: "F14",
      startDate: "2022-06-04T15:15:00",
      eventName: "Javelin M35-65",
      wind: "",
      type: "D",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 14, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5988,
      results: true,
      eventNo: 64,
      name: "F15 : Discus M35-65",
      typeNo: "F15",
      startDate: "2022-06-04T16:30:00",
      eventName: "Discus M35-65",
      wind: "",
      type: "D",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 16, maxAthletes: 16, maxInHeat: 16 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5977,
      results: false,
      eventNo: 42,
      name: "T42 : 400m W35/40",
      typeNo: "T42",
      startDate: "2022-06-04T15:35:00",
      eventName: "400m W35/40",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      counts: { entries: 2, maxAthletes: 8, maxInHeat: 8 },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
  ],
} as any as IR4sCompSchedule;
