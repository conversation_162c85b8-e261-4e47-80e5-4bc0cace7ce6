import { IResultRow } from "../../../../scoreboard/rs4/rs4-scoreboard-models";
import { EventGroupIdString } from "../../../../../common/common-models";
import {
  ILatestScoresQuery,
  ILatestScoresQueryResponseType,
} from "../../../../scoreboard/scoreboard-data";
import { EventType } from "../../../../../athleteCompSched/athletecompsched-models";

export interface IResultRowsFromLatestScores {
  resultRows: IResultRow[];
  eventType: EventType;
  eventName: string;
}

export function createResultRowsFromLatestScores(
  eventGroup: EventGroupIdString,
  latestScoresQueryResponseType: ILatestScoresQueryResponseType
): IResultRowsFromLatestScores {
  const resultsForEventGroupId = latestScoresQueryResponseType[eventGroup];
  if (!resultsForEventGroupId) {
    return {
      resultRows: [],
      eventType: "T",
      eventName: "",
    };
  }

  const athleteResults = Object.values(resultsForEventGroupId);
  const resultRows = athleteResults.map((athleteResult) => {
    return createResultRowFromLatestScoresQuery(athleteResult);
  });

  return {
    resultRows,
    eventType: athleteResults[0].eventGroup.type,
    eventName: athleteResults[0].eventGroup.groupName,
  };
}

export function createResultRowFromLatestScoresQuery(
  latestScoresQuery: ILatestScoresQuery
): IResultRow {
  const resultRow: IResultRow = {
    bibNo: latestScoresQuery.entry.bibNo,
    athlete: {
      id: latestScoresQuery.athlete.id,
      name: latestScoresQuery.athlete.name,
      urn: latestScoresQuery.athlete.urn
        ? latestScoresQuery.athlete.urn.toString()
        : "",
      aocode: latestScoresQuery.athlete.aocode,
    },
    club: latestScoresQuery.athlete.club,
    result:
      latestScoresQuery.results.scoreText.length === 0
        ? "NA"
        : latestScoresQuery.results.scoreText,
    position: latestScoresQuery.results.currentPosition,
    heatPosition: latestScoresQuery.results.heatPosition,
    status: "unchecked",
  };
  return resultRow;
}
