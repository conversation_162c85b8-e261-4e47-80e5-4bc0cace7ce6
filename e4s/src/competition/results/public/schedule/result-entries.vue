<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <div class="e4s-flex-column">
          <div
            class="
              e4s-flex-row
              e4s-gap--standard
              e4s-justify-flex-row-vert-center
            "
          >
            <span v-text="getEventName" class="e4s-header--400"></span>
            <AdminIdV2 :obj="scheduleTableRow.scheduledEvent" />
            <ButtonGenericBackV2 v-on:click="close" class="e4s-flex-row--end" />
          </div>

          <div
            class="e4s-flex-row e4s-gap--standard"
            v-if="getIsThisAFeederComp"
          >
            <span>Target event in comp:</span>
            <span
              v-text="
                r4sCompScheduleTarget.compId + ': ' + r4sCompScheduleTarget.name
              "
            ></span>
            <ScheduleEventGroupPicker
              v-if="r4sCompScheduleTarget.compId > 0"
              class="right"
              :comp-schedule="r4sCompScheduleTarget"
              v-on:onSelected="onEventGroupSelected"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="e4s-vertical-spacer--large"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        <table class="result-entries--table">
          <tr class="result-entries--header">
            <th class="">Name</th>
            <th class="result-entries--narrow result-entries--centre">
              Gender
            </th>
            <th class="result-entries--narrow result-entries--centre">Age</th>
            <th class="">Club</th>
            <th class="result-entries--narrow result-entries--right">EP</th>
            <th class="" v-if="getShowQualify">
              <span class="right">
                Q
                <FieldHelp
                  title="Qualify"
                  help-key="result--qualify"
                  :get-from-server="true"
                ></FieldHelp>
              </span>
            </th>
          </tr>

          <tr
            v-for="athleteEntry in athleteEntries"
            :key="athleteEntry.athleteId"
            :class="getRowClass(athleteEntry)"
          >
            <td class="result-entries--cell">
              <span
                v-text="athleteEntry.firstname + ' ' + athleteEntry.surname"
              ></span>
              <!--              <PowerOfTenLink :urn="athleteEntry.urn" />-->
              <PowerOfTenLinkV2 :urn="athleteEntry.urn" />
            </td>
            <td
              v-text="athleteEntry.gender"
              class="result-entries--cell result-entries--centre"
            ></td>
            <td
              v-text="athleteEntry.ageGroup.shortName"
              class="result-entries--cell result-entries--centre"
            ></td>
            <td class="result-entries--cell">
              <span v-text="athleteEntry.clubname"></span>
              <div
                v-if="getIsThisAFeederComp"
                v-text="
                  athleteEntry.entryOptions.autoEntries.targetEventGroup.id +
                  ': ' +
                  athleteEntry.entryOptions.autoEntries.targetEventGroup.name
                "
              ></div>
            </td>

            <td class="result-entries--cell result-entries--right">
              <div class="e4s-flex-column">
                <!--                <PrimaryLink-->
                <!--                  v-if="athleteEntry.editAccess"-->
                <!--                  class="e4s-button&#45;&#45;slim"-->
                <!--                  @onClick="editOptions(athleteEntry)"-->
                <!--                  link-text="Options"-->
                <!--                />-->
                <ButtonGenericV2
                  v-if="athleteEntry.editAccess"
                  class="result-entries--options-button e4s-button--slim"
                  button-type="secondary"
                  text="Options"
                  @click="editOptions(athleteEntry)"
                />
                <div
                  class="
                    e4s-flex-row
                    e4s-gap--small
                    e4s-justify-flex-row-vert-center
                    e4s-flex-row--end
                  "
                  style="align-items: baseline"
                >
                  <PrimaryLink
                    v-if="athleteEntry.editAccess"
                    @onClick="editAthletePb(athleteEntry)"
                    link-text="Edit"
                  />
                  <span v-text="getAthletePerformance(athleteEntry)"></span>
                </div>
              </div>
            </td>
            <td class="result-entries--cell" v-if="getShowQualify">
              <!--              <button-->
              <!--                :disabled="!getIsEventGroupSelected"-->
              <!--                v-if="-->
              <!--                  athleteEntry.entryOptions.autoEntries.targetEventGroup.id ===-->
              <!--                  0-->
              <!--                "-->
              <!--                class="e4s-button e4s-button&#45;&#45;green e4s-button&#45;&#45;pad right"-->
              <!--                v-on:click="setQ(athleteEntry)"-->
              <!--              >-->
              <!--                Qualify-->
              <!--              </button>-->
              <!--              <button-->
              <!--                v-if="-->
              <!--                  canShowRemoveButton(athleteEntry)-->
              <!--                "-->
              <!--                class="e4s-button e4s-button&#45;&#45;green e4s-button&#45;&#45;pad right"-->
              <!--                v-on:click="removeQ(athleteEntry)"-->
              <!--              >-->
              <!--                Remove-->
              <!--              </button>-->
            </td>
          </tr>
        </table>
      </div>
    </div>

    <ModalV2
      :is-full-screen="$mq === VUE_MQ_SIZES.MOBILE.name"
      v-if="isEditingPb"
      :always-show-header-blank="true"
    >
      <AthletePbWrapper
        slot="body"
        style="margin: 8px"
        :entry-id="athleteEventEditing.entryId"
        @submitted="pbSaved"
        @cancel="isEditingPb = false"
      />
    </ModalV2>

    <ModalV2
      :is-full-screen="$mq === VUE_MQ_SIZES.MOBILE.name"
      v-if="isEditingOptions"
      :always-show-header-blank="true"
      :disable-scroll="$mq === VUE_MQ_SIZES.MOBILE.name"
    >
      <CompEventActionsWrapper
        slot="body"
        style="margin: 8px"
        :comp-id="r4sCompSchedule.compId"
        :athlete-id="athleteEventEditing.athleteId"
        :club-id="athleteEventEditing.club.id"
        :entry-id="athleteEventEditing.entryId"
        @close="editingOptionsClose"
      />
    </ModalV2>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {
  IR4sAthleteEntry,
  IR4sCompSchedule,
  IR4sScheduledEvent,
  IScheduleTableRow,
} from "../../../scoreboard/rs4/rs4-scoreboard-models";
import FieldHelp from "../../../../common/ui/field/field-help/field-help.vue";
import PowerOfTenLink from "../../../../common/ui/power-of-ten-link.vue";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../../config/config-store";
import { ScoreboardService } from "../../../scoreboard/scoreboard-service";
import { format, parse } from "date-fns";
import { CONFIG } from "../../../../common/config";
import { Rs4Service } from "../../../scoreboard/rs4/rs4-service";
import { ResultsService } from "../../results-service";
import { ConfigService } from "../../../../config/config-service";
import { IConfigApp } from "../../../../config/config-app-models";
import ScheduleEventGroupPicker from "../entries/ScheduleEventGroupPicker.vue";
import { IAutoEntrySubmit } from "../entries/auto-entries-models";
import { AutoEntriesData } from "../entries/auto-entries-data";
import PowerOfTenLinkV2 from "../../../../common/ui/layoutV2/PowerOfTenLinkV2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import ButtonGenericBackV2 from "../../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import AdminIdV2 from "../../../../common/ui/layoutV2/admin-id-v2.vue";
import ModalV2 from "../../../../common/ui/layoutV2/modal/modal-v2.vue";
import EditPbV3 from "../../../../athleteCompSched/pb/v3/EditPbV3.vue";
import { VUE_MQ_SIZES } from "../../../../index";
import AthletePbWrapper from "../../../../athleteCompSched/pb/v3/AthletePbWrapper.vue";
import PrimaryLink from "../../../../common/ui/layoutV2/href/PrimaryLink.vue";
import { simpleClone } from "../../../../common/common-service-utils";
import { IPbEditV3RouteOutput } from "../../../../athleteCompSched/pb/v3/edit-pb-v3-models";
import CompEventActionsWrapper from "../../../../athleteCompSched/comp-event-actions/comp-event-actions-wrapper.vue";

const resultsService = new ResultsService();
const rs4Service: Rs4Service = new Rs4Service();
const scoreboardService: ScoreboardService = new ScoreboardService();
const configService = new ConfigService();

@Component({
  name: "result-entries",
  components: {
    CompEventActionsWrapper,
    PrimaryLink,
    AthletePbWrapper,
    EditPbV3,
    ModalV2,
    AdminIdV2,
    ButtonGenericBackV2,
    ButtonGenericV2,
    PowerOfTenLinkV2,
    ScheduleEventGroupPicker,
    PowerOfTenLink,
    FieldHelp,
  },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
    }),
  },
})
export default class ResultEntries extends Vue {
  public readonly isAdmin: boolean;
  public readonly configApp: IConfigApp;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sCompSchedule();
    },
  })
  public readonly r4sCompSchedule!: IR4sCompSchedule;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sCompSchedule();
    },
  })
  public readonly r4sCompScheduleTarget!: IR4sCompSchedule;

  @Prop({
    default: () => {
      return scoreboardService.factoryScheduleTableRow();
    },
  })
  public readonly scheduleTableRow: IScheduleTableRow;

  @Prop({
    default: false,
  })
  public readonly hasResultsPermissionForComp: boolean;

  public $mq: any;
  public VUE_MQ_SIZES = VUE_MQ_SIZES;
  public isEditingPb = false;
  public athleteEventEditing: IR4sAthleteEntry =
    resultsService.factoryR4sAthleteEntry();
  public isEditingOptions = false;

  public athleteEntries: IR4sAthleteEntry[] = [];
  public isLoading = false;

  public rs4Service: Rs4Service = new Rs4Service();

  public scheduledEventSelected: IR4sScheduledEvent =
    resultsService.factoryR4sScheduledEvent();

  @Watch("scheduleTableRow", { immediate: true })
  public onScheduleTableRowChanged(
    newValue: IScheduleTableRow,
    _oldValue: IScheduleTableRow
  ) {
    if (newValue.athleteEntries) {
      this.athleteEntries = R.clone(newValue.athleteEntries).map(
        (athleteEntry) => {
          if (!athleteEntry.entryOptions) {
            athleteEntry.entryOptions = {
              autoEntries: resultsService.factoryAutoEntryOptions(),
            };
          }
          if (!athleteEntry.entryOptions.autoEntries) {
            athleteEntry.entryOptions.autoEntries =
              resultsService.factoryAutoEntryOptions();
          }
          return athleteEntry;
        }
      );
    }
  }

  public getRowClass(
    athleteEntry: IR4sAthleteEntry
  ): "result-entries--big-q" | "result-entries--big-q-select-match" | "" {
    if (athleteEntry.entryOptions.autoEntries.targetEventGroup.id > 0) {
      if (
        athleteEntry.entryOptions.autoEntries.targetEventGroup.id ===
        this.scheduledEventSelected.id
      ) {
        return "result-entries--big-q-select-match";
      }

      return "result-entries--big-q";
    }
    return "";
  }

  public isBigQ(athleteEntry: IR4sAthleteEntry): boolean {
    return athleteEntry.qualify === "Q";
  }

  public isLittleQ(athleteEntry: IR4sAthleteEntry): boolean {
    return athleteEntry.qualify === "q";
  }

  public get getShowQualify(): boolean {
    if (!resultsService.canQualifyToAnotherComp(this.r4sCompSchedule)) {
      return false;
    }
    return this.isAdmin || this.getHasBuilderPermissionForComp;
  }

  public get getHasBuilderPermissionForComp(): boolean {
    return configService.hasBuilderPermissionForComp(
      this.configApp.userInfo,
      this.r4sCompSchedule.org.id,
      this.r4sCompSchedule.compId
    );
  }

  public setQ(athleteEntry: IR4sAthleteEntry) {
    this.athleteEntries = this.athleteEntries.map((athlete) => {
      if (athlete.athleteId === athleteEntry.athleteId) {
        athlete.entryOptions.autoEntries.targetEventGroup = {
          id: this.scheduledEventSelected.id,
          name: this.scheduledEventSelected.name,
        };
      }
      return athlete;
    });
  }

  public removeQ(athleteEntry: IR4sAthleteEntry) {
    this.athleteEntries = this.athleteEntries.map((athlete) => {
      if (athlete.athleteId === athleteEntry.athleteId) {
        athlete.entryOptions.autoEntries.targetEventGroup = {
          id: 0,
          name: "",
        };
      }
      return athlete;
    });
  }

  public isQualified(athleteEntry: IR4sAthleteEntry): boolean {
    return athleteEntry.qualify === "Q";
  }

  public get getCompDate() {
    return this.r4sCompSchedule.date.length > 0
      ? format(parse(this.r4sCompSchedule.date), "Do MMM YYYY")
      : "";
  }

  public get getLogoPath(): string {
    return CONFIG.E4S_HOST + this.r4sCompSchedule.logo;
  }

  public get getEventName() {
    if (this.scheduleTableRow.scheduledEvent) {
      // return commonService.getIdAndDescription(
      //   this.scheduleTableRow.scheduledEvent as IR4sScheduledEvent
      // );
      return (this.scheduleTableRow.scheduledEvent as IR4sScheduledEvent).name;
    }
    return "";
  }

  public onEventGroupSelected(r4sScheduledEvent: IR4sScheduledEvent) {
    this.scheduledEventSelected = R.clone(r4sScheduledEvent);
  }

  public get getIsEventGroupSelected(): boolean {
    return this.scheduledEventSelected.id > 0;
  }

  public get getIsThisAFeederComp() {
    return this.rs4Service.isFeederComp(this.r4sCompSchedule);
  }

  public editAthletePb(athleteEntry: IR4sAthleteEntry) {
    this.athleteEventEditing = simpleClone(athleteEntry);
    this.isEditingPb = true;
  }

  public editOptions(athleteEntry: IR4sAthleteEntry) {
    this.athleteEventEditing = simpleClone(athleteEntry);
    this.isEditingOptions = true;
  }

  public pbSaved(pbEditV3RouteOutput: IPbEditV3RouteOutput) {
    //  Doing this here is a stop gap and saves a call to the server.
    const entries = this.athleteEntries.map((athleteEntry) => {
      if (athleteEntry.entryId === pbEditV3RouteOutput.entryId) {
        athleteEntry.perf.perf = pbEditV3RouteOutput.perfInfo.perf;
        athleteEntry.perf.perfText = pbEditV3RouteOutput.perfInfo.perfText;
      }
      return athleteEntry;
    });
    this.athleteEntries = entries;
    this.isEditingPb = false;
  }

  public getAthletePerformance(athleteEntry: IR4sAthleteEntry): string {
    if (athleteEntry.perf.perf > 0) {
      return athleteEntry.perf.perfText;
    }
    return "-";
  }

  public editingOptionsClose(_requiresParentReloadOnClose: boolean) {
    // if (requiresParentReloadOnClose) {
    //   console.log("Resultries.editingOptionsClose reloadSchedule");
    //   this.$emit("reloadSchedule");
    // }
    this.$emit("reloadSchedule");
    this.isEditingOptions = false;
  }

  public close() {
    this.$emit("close");
  }

  public save() {
    //  TODO   move to centralised.

    const entries: IAutoEntrySubmit[] = this.athleteEntries.map(
      (athleteEntry) => {
        return {
          sourceEntryId: athleteEntry.entryId,
          targetEventGroupId:
            athleteEntry.entryOptions.autoEntries.targetEventGroup.id,
        };
      }
    );
    this.isLoading = true;
    new AutoEntriesData().submitEntries(entries).finally(() => {
      this.isLoading = false;
    });

    this.$emit("save");
  }
}
</script>

<style scoped>
.result-entries--header th {
  font-size: 1.25rem;
}

.result-entries--narrow {
  width: 10%;
}

.result-entries--centre {
  text-align: center;
}

.result-entries--right {
  text-align: right;
}

.result-entries--header {
  margin: 0.5em 0;
  font-size: 1.25em;
}

.result-entries--cell {
  padding: 2px 0 0 0;
}

.result-entries--options-button {
  width: 80px;
}

.result-entries--big-q {
  background-color: #c9e6c9;
}

.result-entries--big-q-select-match {
  background-color: #c9e6c9;
  border: 3px solid #d78080;
}

.result-entries--little-q {
  background-color: #ecf1ec;
}
</style>
