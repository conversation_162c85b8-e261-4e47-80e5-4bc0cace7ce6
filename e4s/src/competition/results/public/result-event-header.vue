<template>
    <div class="row r4s-schedule-public--header">
        <div class="col s12 m12 l12">
            <div class="left">
                <div class="r4s-schedule-public--comp-name">
                    <span v-text="'(' + resultEvent.comp.id +') ' + resultEvent.comp.name"></span>
                    <a
                      v-if="showEditButton && (isAdmin || hasResultsPermissionForComp)"
                      :href="'#/results-entry/' + r4sCompSchedule.compId + '/' + resultEvent.eventGroup.id"
                    ><i class='material-icons red-text normal'>edit</i></a>
                </div>

                <div class="r4s-schedule-public--comp-date-section">
                    <slot name="heat-date">
                        <span v-text="getCompDate"></span>
                    </slot>

                </div>

            </div>

            <div class="right">
                <img :src="getLogoPath" class="r4s-schedule-public--logo"/>
            </div>
        </div>
    </div>
</template>

<script lang="ts">

import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import {IResultEvent} from "../results-models";
import {IR4sCompSchedule} from "../../scoreboard/rs4/rs4-scoreboard-models";
import {CONFIG} from "../../../common/config";
import {format, parse} from "date-fns";
import {ConfigService} from "../../../config/config-service";
import {mapGetters, mapState} from "vuex";
import {CONFIG_STORE_CONST, IConfigStoreState} from "../../../config/config-store";
import {ResultsService} from "../results-service";
import {Rs4Service} from "../../scoreboard/rs4/rs4-service";
import {IConfigApp} from "../../../config/config-app-models";

const resultsService: ResultsService = new ResultsService();
const rs4Service: Rs4Service = new Rs4Service();

@Component({
    name: "result-event-header",
    computed: {
        ...mapGetters(
          {
              isAdmin: CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME + "/" + CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN
          }
        ),
        ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
            configApp: (state: any) => state.configApp
        })
    }
})
export default class ResultEventHeader extends Vue {
    public readonly isAdmin!: boolean;
    public configApp!: IConfigApp;

    @Prop({
        default: () => {
            return resultsService.factoryResultEvent();
        }
    })
    public readonly resultEvent: IResultEvent;

    @Prop({
        default: () => {
            return rs4Service.factoryR4sCompSchedule();
        }
    })
    public readonly r4sCompSchedule!: IR4sCompSchedule;

    @Prop({
        default: false
    })
    public readonly showEditButton!: boolean;

    public configService: ConfigService = new ConfigService();

    public get getLogoPath(): string {
        return CONFIG.E4S_HOST + this.resultEvent.comp.logo;
    }

    public get hasResultsPermissionForComp() {
        return this.configService.hasResultsPermissionForComp(this.configApp.userInfo, this.r4sCompSchedule.org.id, this.resultEvent.comp.id);
    }

    public get getCompDate() {
        if (this.r4sCompSchedule.date.length === 0) {
            return "";
        }
        return format(parse(this.r4sCompSchedule.date), "Do MMM YYYY");
    }

}
</script>

<style scoped>

.r4s-schedule-public--header {
    border-bottom: 1px solid gray;
    padding: 0.5em 0;
}

.r4s-schedule-public--comp-name {
    font-size: 1.5em;
}

.r4s-schedule-public--comp-date-section {
    margin-top: 1em;
}

.r4s-schedule-public--logo {
    height: 4em;
}

</style>
