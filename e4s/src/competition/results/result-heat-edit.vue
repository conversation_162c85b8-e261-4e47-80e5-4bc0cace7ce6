<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <div class="e4s-force-inline-block">
          <label class="active" for="result-event--type"> Type </label>
          <select
            id="result-event--type"
            v-model="resultEvent.eventGroup.type"
            class="browser-default e4s-input e4s-input-select e4s-input-width-5"
          >
            <option value="T">Time</option>
            <option value="D">Distance</option>
            <option value="H">Height</option>
          </select>
        </div>

        <div class="e4s-force-inline-block-spacer"></div>

        <div class="e4s-force-inline-block">
          <label class="active" for="result-event--wind-2"> Wind </label>
          <select
            id="result-event--wind-2"
            v-model="resultEvent.eventGroup.wind"
            class="browser-default e4s-input e4s-input-select e4s-input-width-5"
          >
            <option value="E">Event</option>
            <option value="A">Athlete</option>
          </select>
        </div>

        <div class="e4s-force-inline-block-spacer"></div>

        <div class="e4s-force-inline-block">
          <label class="active" for="result-event--bib-lookup">
            Bib Lookup
            <span
              v-text="doBibLookup ? 'ON' : 'OFF'"
              class="result-heat-edit--do-bib-lookup-status"
              :class="
                doBibLookup
                  ? 'result-heat-edit--do-bib-lookup-status-on'
                  : 'result-heat-edit--do-bib-lookup-status-off'
              "
            >
            </span>
          </label>
          <select
            id="result-event--bib-lookup"
            v-model="doBibLookup"
            class="browser-default e4s-input e4s-input-select e4s-input-width-5"
            :class="
              doBibLookup
                ? 'result-heat-edit--do-bib-lookup-status-on-select'
                : 'result-heat-edit--do-bib-lookup-status-off-select'
            "
          >
            <option :value="true">ON</option>
            <option :value="false">OFF</option>
          </select>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <div class="e4s-force-inline-block">
          <label class="active" for="result-event--heat">
            <span v-text="(isTrackEvent ? 'Race' : 'Heat') + '#'"></span>
            <ValidationFieldLable
              :validation-controller="validationController"
              :prop-path="'heatNumber'"
            />
          </label>
          <input
            id="result-event--heat"
            class="e4s-input e4s-input-width-5"
            v-model.number="resultHeatInternal.heatNo"
            type="number"
            placeholder=""
          />
        </div>

        <div
          class="e4s-force-inline-block-spacer"
          v-if="getShowWindForEvent"
        ></div>

        <div class="e4s-force-inline-block" v-if="getShowWindForEvent">
          <label class="active" for="result-event--wind">
            Wind (m/s)
            <ValidationFieldLable
              :validation-controller="validationController"
              :prop-path="'wind'"
            />
          </label>
          <input
            id="result-event--wind"
            class="e4s-input e4s-input-width-5"
            v-model="wind"
            v-on:change="windChange"
            type="text"
            placeholder=""
          />
        </div>

        <div class="e4s-force-inline-block-spacer"></div>

        <div class="e4s-force-inline-block">
          <label class="active" for="result-event--heat">
            Actual Time (Scheduled: <span v-text="getScheduledTime"></span>)
            <ValidationFieldLable
              :validation-controller="validationController"
              :prop-path="'actualTime'"
            />
          </label>
          <TimeEntry
            :iso-date-time="resultHeatInternal.actualTime"
            v-on:onSelected="setActualTime"
          ></TimeEntry>
        </div>

        <div class="e4s-force-inline-block-spacer"></div>

        <div class="e4s-force-inline-block">
          <InputRestrictWithLabel
            input-class="e4s-input"
            :max-length="20"
            :label-text="
              (isTrackEvent ? 'Race' : 'Heat') + ' Description (Optional)'
            "
            v-model="resultHeatInternal.description"
          ></InputRestrictWithLabel>
        </div>
      </div>

      <!--            <div class="col s6 m3 l3">-->
      <!--                <label class="active" for="result-event&#45;&#45;heat">-->
      <!--                    Time Actual (Scheduled: <span v-text="getScheduledTime"></span>)-->
      <!--                    <ValidationFieldLable :validation-controller="validationController" :prop-path="'header.actualTime'"/>-->
      <!--                </label>-->
      <!--                <TimeEntry :iso-date-time="resultHeatInternal.heatNo"></TimeEntry>-->
      <!--            </div>-->
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <!--        <button class="e4s-button e4s-button&#45;&#45;green" v-on:click="addRow">-->
        <!--          Add Row-->
        <!--        </button>-->
        <ButtonGenericV2 text="Add Row" @click="addRow" />
        <div class="e4s-force-inline-block">
          <label class="active" for="result-event--tab-type">
            Tab Order
            <FieldHelp
              title="Result Entry Tab Order"
              message="Changes tab order, 'Result' will just tab through results fields and may help speed up data entry."
            >
            </FieldHelp>
          </label>
          <div class="e4s-force-inline-block">
            <label>
              <input
                type="radio"
                id="result-event--tab-type"
                class="browser-default"
                value="ALL"
                v-model="fieldTabType"
              />
              <span>ALL</span>
            </label>
            <label>
              <input
                type="radio"
                id="cheques-allow-no"
                class="browser-default"
                value="RESULT_ONLY"
                v-model="fieldTabType"
              />
              <span>Result</span>
            </label>
          </div>
        </div>
        <div class="right">
          <div class="e4s-flex-row e4s-gap--standard">
            <ButtonGenericV2 text="Save" @click="submit" />
            <ButtonGenericV2
              text="Cancel"
              @click="showCancelConfirm = true"
              button-type="tertiary"
            />
            <ButtonGenericV2
              text="Delete"
              @click="showDeleteConfirm = true"
              button-type="destructive"
            />
          </div>

          <!--          <button-->
          <!--            class="e4s-button e4s-button&#45;&#45;green e4s-button&#45;&#45;medium"-->
          <!--            v-on:click="submit"-->
          <!--          >-->
          <!--            Save-->
          <!--          </button>-->
          <!--          <button-->
          <!--            class="e4s-button e4s-button&#45;&#45;red e4s-button&#45;&#45;medium"-->
          <!--            v-on:click="showCancelConfirm = true"-->
          <!--          >-->
          <!--            Cancel-->
          <!--          </button>-->
          <!--          <button-->
          <!--            class="e4s-button e4s-button&#45;&#45;red e4s-button&#45;&#45;medium"-->
          <!--            v-on:click="showDeleteConfirm = true"-->
          <!--          >-->
          <!--            Delete-->
          <!--          </button>-->
        </div>
      </div>
    </div>

    <div class="row" v-if="!validationController.isValid">
      <div class="col s12 m12 l12">
        <div class="e4s-section-padding-separator"></div>
        <div
          class="result-heat-edit--validation"
          v-text="validationController.getValidationSummaryOutput()"
        ></div>
      </div>
    </div>

    <div class="e4s-vertical-spacer--large"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        <table class="result-heat-edit--table">
          <tr class="result-heat-edit--table-header">
            <th class="result-heat-edit--table-action"></th>
            <th class="result-heat-edit--table-number">
              <!--              <a href="#" v-on:click.prevent="sortResults">Sort</a>-->
              <!--              e4s-button&#45;&#45;slim e4s-flex-row&#45;&#45;end-->
              <div class="e4s-flex-row e4s-gap--tiny">
                <span>Pos#</span
                ><ButtonGenericV2
                  text="Sort"
                  @click="sortResults"
                  class="e4s-button--slim e4s-flex-row--end"
                  style="width: 60px"
                />
              </div>
            </th>
            <th class="result-heat-edit--table-number">Bib</th>
            <th class="result-heat-edit--table-number">Result</th>
            <th
              class="result-heat-edit--table-number"
              v-if="resultEvent.eventGroup.wind === 'A'"
            >
              wind
            </th>
            <th
              class="result-heat-edit--table-number"
              v-if="resultEvent.eventGroup.type === 'T'"
            >
              Lane
            </th>
            <th class="result-heat-edit--table-th-ath">
              <a href="#" v-on:click.prevent="toggleGenders">M/F</a>
            </th>
            <th class="result-heat-edit--table-th-ath">Athlete</th>
            <th class="result-heat-edit--table-th-club">Club</th>
          </tr>

          <ResultAthlete
            :index="index"
            v-for="(resultAthlete, index) in resultHeatInternal.results"
            :result-athlete="resultAthlete"
            :key="index"
            :result-event="resultEvent"
            :result-heat="resultHeat"
            :show-wind="resultEvent.eventGroup.wind === 'A'"
            :show-lane="resultEvent.eventGroup.type === 'T'"
            :field-tab-type="fieldTabType"
            :do-bib-lookup="doBibLookup"
            v-on:inputChanged="resultAthleteInputChanged"
            v-on:deleteResultAthlete="deleteResultAthlete"
          >
          </ResultAthlete>
        </table>
      </div>
    </div>

    <div class="row" v-if="!validationController.isValid">
      <div class="col s12 m12 l12">
        <div
          class="result-heat-edit--validation"
          v-text="validationController.getValidationSummaryOutput()"
        ></div>
      </div>
    </div>

    <E4sModalSimple
      css-class="e4s-modal-container--full-size"
      v-if="showCancelConfirm"
    >
      <StandardForm title="Cancel" slot="body">
        <div slot="form-content">
          <div style="padding: 1rem">
            <div>This will reset the heat to it's original state</div>
            <div slot="buttons">
              <button
                class="e4s-button e4s-button--red e4s-button--medium"
                v-on:click="showCancelConfirm = false"
              >
                Cancel
              </button>
              <button
                class="e4s-button e4s-button--green e4s-button--medium"
                v-on:click="cancelChanges"
              >
                OK
              </button>
            </div>
          </div>
        </div>
      </StandardForm>
    </E4sModalSimple>

    <E4sModalSimple
      css-class="e4s-modal-container--full-size"
      v-if="showDeleteConfirm"
    >
      <StandardForm title="Cancel" slot="body">
        <div slot="form-content">
          <div style="padding: 1rem">
            <div>Confirm heat deletion.</div>
            <div slot="buttons">
              <button
                class="e4s-button e4s-button--red e4s-button--medium"
                v-on:click="showDeleteConfirm = false"
              >
                Cancel
              </button>
              <button
                class="e4s-button e4s-button--green e4s-button--medium"
                v-on:click="deleteHeat"
              >
                OK
              </button>
            </div>
          </div>
        </div>
      </StandardForm>
    </E4sModalSimple>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { ResultsService } from "./results-service";
import {
  IResultAthlete,
  IResultEvent,
  IResultHeat,
  ResultAthleteTabType,
} from "./results-models";
import SimpleObjectDropDown from "../../common/ui/simple-object-drop-down.vue";
import { format, parse } from "date-fns";
import { ValidationController } from "../../validation/validation-controller";
import ValidationFieldLable from "../../validation/validation-field-lable.vue";
import TimeEntry from "../../common/ui/datetime/time-entry.vue";
import ResultAthlete from "./result-athlete.vue";
import { FormController } from "../../common/ui/form/form-controller/form-controller";
import E4sModalSimple from "../../common/ui/modal/e4s-modal-simple.vue";
import StandardForm from "../../common/ui/standard-form/standard-form.vue";
import FieldHelp from "../../common/ui/field/field-help/field-help.vue";
import { GenderType } from "../../common/common-models";
import InputRestrictWithLabel from "../../common/ui/field/input-restrict-length/input-restrict-with-label.vue";
import { simpleClone } from "../../common/common-service-utils";
import ButtonGotoGenericV2 from "../../common/ui/layoutV2/buttons/button-goto-generic-v2.vue";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";

const resultsService: ResultsService = new ResultsService();

@Component({
  name: "result-heat-edit",
  components: {
    ButtonGenericV2,
    ButtonGotoGenericV2,
    InputRestrictWithLabel,
    FieldHelp,
    StandardForm,
    E4sModalSimple,
    ResultAthlete,
    TimeEntry,
    ValidationFieldLable,
    SimpleObjectDropDown,
  },
})
export default class ResultHeatEdit extends Vue {
  @Prop({
    default: () => {
      return resultsService.factoryResultEvent();
    },
  })
  public readonly resultEvent: IResultEvent;

  @Prop({
    default: () => {
      return resultsService.factoryResultHeat();
    },
  })
  public readonly resultHeat: IResultHeat;

  // @Prop({
  //     default: true
  // })
  public doBibLookup: boolean = true;

  public resultHeatInternal: IResultHeat = resultsService.factoryResultHeat();
  public validationController: ValidationController =
    new ValidationController();
  public formController: FormController = new FormController({}, {});
  public resultsService = resultsService;

  // public checkinData: CheckinData = new CheckinData();
  public wind: string = "";

  public showCancelConfirm = false;
  public showDeleteConfirm = false;
  public fieldTabType: ResultAthleteTabType = "RESULT_ONLY";

  public created() {
    this.resultHeatInternal = R.clone(this.resultHeat);
    this.formController.setSources(this.resultHeat, this.resultHeatInternal);
    this.init();
  }

  @Watch("resultHeat")
  public onResultHeatChanged(newValue: IResultHeat, oldValue: IResultHeat) {
    this.resultHeatInternal = R.clone(newValue);
    this.formController.setSources(this.resultHeat, newValue);
    this.init();
  }

  public init() {
    if (!this.resultHeatInternal.description) {
      this.resultHeatInternal.description = "";
    }
    if (this.resultHeatInternal.results.length === 0) {
      this.addResultAthleteRow(8);
    } else {
      this.wind = this.resultHeatInternal.results[0].wind;
    }
  }

  public addResultAthleteRow(rowsToAdd: number) {
    const results = this.resultHeatInternal.results;

    for (let i = 0; i < rowsToAdd; i++) {
      results.push({
        ...resultsService.factoryResultAthlete(),
        position: this.resultHeatInternal.results.length + 1,
        wind: this.wind,
      });
    }

    this.resultHeatInternal.results = R.clone(results);

    // this.resultHeatInternal.results.push({
    //     ...resultsService.factoryResultAthlete(),
    //     position: this.resultHeatInternal.results.length + 1,
    //     wind: this.wind
    // });
    this.formController.processChanges();
  }

  public get getScheduledTime() {
    if (this.resultHeatInternal.scheduledTime.length === 0) {
      return "NA";
    }
    const startTime = format(
      parse(this.resultHeatInternal.scheduledTime),
      "HH:mm"
    );
    return startTime === "00:00" ? "TBC" : startTime;
  }

  public validate() {
    this.validationController.reset();
    this.validationController.setErrors(
      this.resultsService.validateHeat(this.resultHeatInternal)
    );
  }

  public resultAthleteInputChanged(payload: {
    index: number;
    resultAthlete: IResultAthlete;
  }) {
    this.resultHeatInternal.results[payload.index] = payload.resultAthlete;

    // this.resultHeatInternal.results = this.resultHeatInternal.results.map( (result) => {
    //    if (result.bibNo === resultAthlete.bibNo) {
    //        return resultAthlete;
    //    }
    //    return result;
    // });
    this.formController.processChanges();
    this.$emit("edited", R.clone(this.resultHeatInternal));
  }

  public deleteResultAthlete(payload: {
    index: number;
    resultAthlete: IResultAthlete;
  }) {
    this.resultHeatInternal.results = this.resultHeatInternal.results.filter(
      (result, index) => {
        return payload.index !== index;
      }
    );
    this.formController.processChanges();
  }

  // public resultAthleteInputChangedBibNo(resultAthlete: IResultAthlete) {
  //     this.resultAthleteInputChanged(resultAthlete);
  // }

  public windChange() {
    const results = this.resultHeatInternal.results.map((result) => {
      result.wind = this.wind;
      return result;
    });
    this.resultHeatInternal.results = R.clone(results);
  }

  public get getShowWindForEvent() {
    // return this.resultEvent.eventGroup.wind ==="E" || this.resultEvent.eventGroup.type === "T";
    return this.resultEvent.eventGroup.wind === "E";
  }

  public addRow() {
    this.addResultAthleteRow(1);
  }

  public setActualTime(timeISo: string) {
    this.resultHeatInternal.actualTime =
      this.resultHeatInternal.scheduledTime.split("T")[0] +
      "T" +
      timeISo +
      ":00";
  }

  public toggleGenders() {
    if (this.resultHeatInternal.results.length === 0) {
      return;
    }
    const firstResult = this.resultHeatInternal.results[0];
    const toggleGenderTo: GenderType =
      firstResult.gender === "" ? "M" : firstResult.gender === "M" ? "F" : "M";

    const results = this.resultHeatInternal.results.map((result) => {
      result.gender = toggleGenderTo;
      return result;
    });
    this.resultHeatInternal.results = R.clone(results);
  }

  public cancelChanges() {
    this.resultHeatInternal = R.clone(this.resultHeat);
    this.showCancelConfirm = false;
  }

  public submit() {
    this.validate();
    if (!this.validationController.isValid) {
      return;
    }

    //  If valid, strip off rows with no Bib No
    const resultHeat = R.clone(this.resultHeatInternal);
    resultHeat.results = this.resultsService.resultHeatsToSubmit(
      resultHeat.results
    );

    this.$emit("submitHeat", resultHeat);
  }

  public sortResults() {
    const results = simpleClone(
      resultsService.sortResults(
        this.resultHeatInternal.results,
        this.resultEvent.eventGroup.type
      )
    );
    this.resultHeatInternal.results = results;
  }

  public deleteHeat() {
    this.$emit("deleteHeat", R.clone(this.resultHeatInternal));
    this.showDeleteConfirm = false;
  }

  public get isTrackEvent() {
    return this.resultsService.isTrackEvent(this.resultEvent.eventGroup.type);
  }
}
</script>

<style>
.result-heat-edit--do-bib-lookup-status {
  font-weight: 600;
}

.result-heat-edit--do-bib-lookup-status-on-select {
  background-color: #bdf5bd !important;
}

.result-heat-edit--do-bib-lookup-status-off-select {
  background-color: pink !important;
}

.result-heat-edit--do-bib-lookup-status-on {
  color: #0bbf0b;
}
.result-heat-edit--do-bib-lookup-status-off {
  color: orangered;
}
</style>
