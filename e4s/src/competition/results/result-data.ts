import {IServerResponse} from "../../common/common-models";
import {IResultEvent} from "./results-models";
import https from "../../common/https";

export class ResultData {
  public getResultEvent(eventGroupId: number): Promise<IServerResponse<IResultEvent>> {
    return https.get( "/v5/results/read/" + eventGroupId) as any as Promise<IServerResponse<IResultEvent>>
  }
  public saveResultEvent(resultEvent: IResultEvent): Promise<IServerResponse<IResultEvent>> {
    return https.post( "/v5/results/write", resultEvent) as any as Promise<IServerResponse<IResultEvent>>
  }
  public deleteResultHeat(eventGroupId: number, heatNo: number): Promise<IServerResponse<IResultEvent>> {
    return https.delete( "/v5/results/delete/" + eventGroupId + "/" + heatNo) as any as Promise<IServerResponse<IResultEvent>>
  }
}
