import {
  resultAthletesSortMock,
  resultHeatSortMock,
} from "./results-service-mock";
import { simpleClone, sortArray } from "../../common/common-service-utils";
import { IResultAthlete } from "./results-models";
import * as ResultsServiceV2 from "./results-service-V2";
import {
  IR4sScheduledEvent,
  IScheduleTableRow,
} from "../scoreboard/rs4/rs4-scoreboard-models";

describe("ResultsService", () => {
  test("sortResults low to high", () => {
    let athleteResults: IResultAthlete[] = [];
    athleteResults = simpleClone(resultHeatSortMock.results);

    expect(athleteResults[0].score).toBe(14.34);

    athleteResults = simpleClone(resultHeatSortMock.results);
    athleteResults = athleteResults.map((ath, idx) => {
      ath.position = idx + 1;
      return ath;
    });

    expect(athleteResults[0].position).toBe(1);
    expect(athleteResults[0].score).toBe(14.34);

    const res = ResultsServiceV2.sortResults(athleteResults);
    // expect(res).toBe(999);
    expect(res.length).toBe(7);
    expect(res[0].score).toBe(13.49);
    expect(res[6].score).toBe(14.4);
  });

  test("sortResults high to low", () => {
    const res2 = sortArray("score", resultHeatSortMock.results, "ASC");
    expect(res2[0].score).toBe(13.49);
    expect(res2[6].score).toBe(14.4);

    const res3 = sortArray("score", resultHeatSortMock.results, "DESC");
    expect(res3[0].score).toBe(14.4);
    expect(res3[6].score).toBe(13.49);

    const res = ResultsServiceV2.sortResults(resultHeatSortMock.results, "H");
    expect(res.length).toBe(7);
    expect(res[0].score).toBe(14.4);
  });

  test("resultAthletesSortMock", () => {
    const athleteResults = simpleClone(resultAthletesSortMock);

    expect(athleteResults[4].score).toBe(14.34);
    expect(athleteResults[4].scoreValue).toBe("15");

    const res2 = ResultsServiceV2.sortResults(athleteResults);
    expect(res2.length).toBe(7);
    expect(res2[0].score).toBe(13.49);

    expect(res2[6].score).toBe(15);
    expect(res2[6].scoreValue).toBe("15");
    expect(res2[6].position).toBe(7);
    //  @ts-ignore
    // expect(res2[6].x).toBe(5);
  });

  test("getNextResultsEventPositionToShow", () => {
    let data: Pick<IScheduleTableRow, "hasResults">[] = [];

    data = [
      { hasResults: false },
      { hasResults: false },
      { hasResults: true },
      { hasResults: false },
      { hasResults: true },
    ];

    //  Forward
    expect(ResultsServiceV2.getNextResultsEventPositionToShow(0, data)).toBe(2);
    expect(ResultsServiceV2.getNextResultsEventPositionToShow(2, data)).toBe(4);
    expect(ResultsServiceV2.getNextResultsEventPositionToShow(4, data)).toBe(2);

    //  Backward
    expect(
      ResultsServiceV2.getNextResultsEventPositionToShow(4, data, false)
    ).toBe(2);

    expect(
      ResultsServiceV2.getNextResultsEventPositionToShow(2, data, false)
    ).toBe(4);
  });

  test("hasAnyResults", () => {
    let data: IR4sScheduledEvent[] = [];

    data = [
      { results: false },
      { results: false },
      { results: true },
      { results: false },
      { results: true },
    ] as any as IR4sScheduledEvent[];

    expect(ResultsServiceV2.hasAnyResults(data)).toBe(true);

    data = [
      { results: false },
      { results: false },
    ] as any as IR4sScheduledEvent[];

    expect(ResultsServiceV2.hasAnyResults(data)).toBe(false);

    data = [
      { results: true },
      { results: true },
    ] as any as IR4sScheduledEvent[];

    expect(ResultsServiceV2.hasAnyResults(data)).toBe(true);
  });
});
