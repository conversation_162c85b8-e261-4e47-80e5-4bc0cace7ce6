<template>
  <div>
    {{ resultEventInternal }}
    <ResultEventHeader
      :result-event="resultEventInternal"
      :r4s-comp-schedule="r4sCompSchedule"
    ></ResultEventHeader>

    <div class="e4s-section-padding-separator"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        <div class="result-event--header">
          <span v-text="resultEventInternal.eventGroup.name"></span> at
          <span v-text="getScheduledTime"></span>
        </div>
      </div>
    </div>

    <ResultEventHeatLinks
      :show-add-row="isTrackEvent"
      :result-event="resultEventInternal"
      :r4s-comp-schedule="r4sCompSchedule"
      :show-overall="false"
      :is-embedded="false"
      :show-add-race="true"
      v-on:changeHeat="changeHeat"
      v-on:addHeat="addHeat"
      v-on:toSchedule="toSchedule"
      v-on:goToSeeding="goToSeeding"
    />

    <div class="e4s-section-padding-separator"></div>

    <div class="row" v-if="resultHeatEdit.heatNo > 0">
      <div class="col s12 m12 l12">
        <ResultHeatEdit
          :result-event="resultEventInternal"
          :result-heat="resultHeatEdit"
          :r4s-comp-schedule="r4sCompSchedule"
          v-on:submitHeat="savedResult"
          v-on:edited="editedResult"
          v-on:deleteHeat="deleteResult"
        />
      </div>
    </div>

    <LoadingSpinnerModal :show-it="isLoading"></LoadingSpinnerModal>

    <E4sModalSimple
      css-class="e4s-modal-container--full-size"
      v-if="showDirtyRequiresConfirm"
    >
      <StandardForm title="Unsaved changes" slot="body">
        <div slot="form-content" style="padding: 1rem">
          Unsaved changes are on the form. "Continue" to ignore changes.
          <div class="e4s-section-padding-separator"></div>
          <div slot="right-nav-buttons">
            <button
              class="e4s-button e4s-button--red e4s-button--pad"
              v-on:click="showDirtyRequiresConfirm = false"
            >
              Cancel
            </button>
            <button
              class="e4s-button e4s-button--green e4s-button--pad"
              v-on:click="confirmedContinue"
            >
              Continue
            </button>
          </div>
        </div>
      </StandardForm>
    </E4sModalSimple>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { ResultsService } from "./results-service";
import { IResultEvent, IResultHeat } from "./results-models";
import { IR4sCompSchedule } from "../scoreboard/rs4/rs4-scoreboard-models";
import { Rs4Service } from "../scoreboard/rs4/rs4-service";
import { ValidationController } from "../../validation/validation-controller";
import ResultHeatEdit from "./result-heat-edit.vue";
import { CONFIG } from "../../common/config";
import { ResultData } from "./result-data";
import { handleResponseMessages } from "../../common/handle-http-reponse";
import LoadingSpinnerModal from "../../common/ui/modal/loading-spinner-modal.vue";
import { format, parse } from "date-fns";
import { LAUNCH_ROUTES_PATHS } from "../../launch/launch-routes";
import { RawLocation } from "vue-router";
import ResultEventHeader from "./public/result-event-header.vue";
import ResultEventHeatLinks from "./public/result-event-heat-links.vue";
import { FormController } from "../../common/ui/form/form-controller/form-controller";
import E4sModalSimple from "../../common/ui/modal/e4s-modal-simple.vue";
import StandardForm from "../../common/ui/standard-form/standard-form.vue";

const resultsService: ResultsService = new ResultsService();
const rs4Service: Rs4Service = new Rs4Service();

@Component({
  name: "result-event",
  components: {
    StandardForm,
    E4sModalSimple,
    ResultEventHeatLinks,
    ResultEventHeader,
    LoadingSpinnerModal,
    ResultHeatEdit,
  },
})
export default class ResultEvent extends Vue {
  @Prop({
    default: () => {
      return resultsService.factoryResultEvent();
    },
  })
  public readonly resultEvent: IResultEvent;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sCompSchedule();
    },
  })
  public readonly r4sCompSchedule!: IR4sCompSchedule;

  public resultData: ResultData = new ResultData();
  public resultEventInternal: IResultEvent =
    resultsService.factoryResultEvent();
  public resultHeatEdit: IResultHeat = resultsService.factoryResultHeat();
  public heatNumberToDisplay: number = 0;
  public isLoading = false;
  public formController: FormController = new FormController({}, {});
  public isDirtyRequiresConfirm = false;
  public showDirtyRequiresConfirm = false;
  public functionConfirm: Function = () => {};

  public LAUNCH_ROUTES_PATHS = LAUNCH_ROUTES_PATHS;

  public validationController: ValidationController =
    new ValidationController();

  public created() {
    this.resultEventInternal = R.clone(this.resultEvent);
    this.init();
  }

  @Watch("resultEvent")
  public onMessageChanged(newValue: IResultEvent, oldValue: IResultEvent) {
    this.resultEventInternal = R.clone(newValue);
    this.init();
  }

  public init() {
    if (
      this.resultEventInternal.heats.length > 0 &&
      this.heatNumberToDisplay === 0
    ) {
      this.heatNumberToDisplay = 1;
      this.resultHeatEdit = R.clone(this.resultEventInternal.heats[0]);
    }
  }

  public get getLogoPath(): string {
    return CONFIG.E4S_HOST + this.resultEventInternal.comp.logo;
  }

  public changeHeat(heatNumber: number) {
    this.heatNumberToDisplay = heatNumber;
    this.resultHeatEdit = this.resultEventInternal.heats.reduce(
      (accum, resultHeat) => {
        if (resultHeat.heatNo === heatNumber) {
          accum = resultHeat;
        }
        return accum;
      },
      resultsService.factoryResultHeat()
    );
  }

  public get getScheduledTime() {
    if (this.resultEventInternal.eventGroup.scheduledTime.length === 0) {
      return "NA";
    }
    return format(
      parse(this.resultEventInternal.eventGroup.scheduledTime),
      "h:mma"
    );
  }

  public addHeat() {
    const resultHeat = resultsService.factoryResultHeat();
    resultHeat.heatNo = this.resultEventInternal.heats.length + 1;
    resultHeat.scheduledTime =
      this.resultEventInternal.eventGroup.scheduledTime;
    resultHeat.actualTime = this.resultEventInternal.eventGroup.scheduledTime;

    this.resultHeatEdit = resultHeat;
  }

  public editedResult(resultHeat: IResultHeat) {
    //  TODO
    const resultHeatMatch: IResultHeat | null =
      this.resultEventInternal.heats.reduce((accum, heat, index) => {
        if (heat.heatNo === resultHeat.heatNo) {
          accum = heat;
          this.resultEventInternal.heats[index] = resultHeat;
        }
        return accum;
      }, null as IResultHeat | null);

    if (!resultHeatMatch) {
      this.resultEventInternal.heats.push(resultHeat);
      // this.heatNumberToDisplay = resultHeat.heatNo;
    }
    this.heatNumberToDisplay = resultHeat.heatNo;
    this.setIsDirty();
  }

  public savedResult(resultHeat: IResultHeat) {
    const resultHeatMatch: IResultHeat | null =
      this.resultEventInternal.heats.reduce((accum, heat, index) => {
        if (heat.heatNo === resultHeat.heatNo) {
          accum = heat;
          this.resultEventInternal.heats[index] = resultHeat;
        }
        return accum;
      }, null as IResultHeat | null);

    if (!resultHeatMatch) {
      this.resultEventInternal.heats.push(resultHeat);
      // this.heatNumberToDisplay = resultHeat.heatNo;
    }
    this.heatNumberToDisplay = resultHeat.heatNo;
    this.submitResult();
  }

  public deleteResult(resultHeat: IResultHeat) {
    this.isLoading = true;
    const prom = this.resultData.deleteResultHeat(
      this.resultEventInternal.eventGroup.id,
      resultHeat.heatNo
    );
    handleResponseMessages(prom);
    prom
      .then((resp) => {
        if (resp.errNo === 0) {
          // this.resultEventInternal = resp.data;
        }
      })
      .finally(() => {
        // this.init();
        this.isLoading = false;
      });

    // this.resultEventInternal.heats = this.resultEventInternal.heats.filter( (result) => {
    //     return result.heatNo !== resultHeat.heatNo;
    // });
    // // this.heatNumberToDisplay = 1;
    // this.submitResult();
  }

  public submitResult() {
    this.isLoading = true;
    const prom = this.resultData.saveResultEvent(this.resultEventInternal);
    handleResponseMessages(prom);
    prom
      .then((resp) => {
        if (resp.errNo === 0) {
          this.resultEventInternal = resp.data;
          this.isDirtyRequiresConfirm = false;
          this.showDirtyRequiresConfirm = false;
        }
      })
      .finally(() => {
        this.init();
        this.isLoading = false;
      });
  }

  // public toSchedule() {
  //     let location: RawLocation;
  //     location = {
  //         path: "/" + LAUNCH_ROUTES_PATHS.R4S_RESULTS_PUBLIC + "/" + this.resultEventInternal.comp.id
  //     };
  //     this.$router.push(location);
  // }

  public setIsDirty() {
    this.formController.setSources(
      this.resultEvent.heats,
      this.resultEventInternal.heats
    );
    this.formController.processChanges();
    if (this.formController.isDirty) {
      this.isDirtyRequiresConfirm = true;
    }
  }

  public confirmedContinue() {
    this.isDirtyRequiresConfirm = false;
    this.showDirtyRequiresConfirm = false;
    this.functionConfirm();
  }

  public toSchedule() {
    if (this.formController.isDirty && this.isDirtyRequiresConfirm) {
      this.showDirtyRequiresConfirm = true;
      this.functionConfirm = () => {
        this.toSchedule();
      };
      return;
    }

    let location: RawLocation;
    location = {
      path:
        "/" +
        LAUNCH_ROUTES_PATHS.R4S_RESULTS_PUBLIC +
        "/" +
        this.resultEventInternal.comp.id,
    };
    this.$router.push(location);
  }

  public goToSeeding() {
    if (this.formController.isDirty && this.isDirtyRequiresConfirm) {
      this.showDirtyRequiresConfirm = true;
      this.functionConfirm = () => {
        this.toSchedule();
      };
      return;
    }
    const url =
      CONFIG.E4S_HOST +
      "/" +
      this.r4sCompSchedule.compId +
      "/card?eventno=" +
      this.resultEvent.eventGroup.eventNo;
    window.open(url);
    // window.open(
    //   CONFIG.E4S_HOST +
    //     CONFIG.ORGANISER_REPORT +
    //     this.resultEvent.comp.id +
    //     "?eventno=" +
    //     this.resultEvent.eventGroup.eventNo +
    //     "#Card-tab",
    //   "e4sseed"
    // );
  }

  public get getScheduledDate() {
    return format(
      parse(this.resultEventInternal.eventGroup.scheduledTime),
      "Do MMM YYYY"
    );
  }

  public get isTrackEvent(): boolean {
    return resultsService.isTrackEvent(this.resultEvent.eventGroup.type);
  }
}
</script>

<style scoped>
.result-event--header {
  margin: 0.5em 0;
  font-size: 1.25em;
}

.r4s-schedule-public--header {
  border-bottom: 1px solid gray;
  padding: 0.5em 0;
}

.r4s-schedule-public--comp-name {
  font-size: 1.5em;
}

.r4s-schedule-public--comp-date-section {
  margin-top: 1em;
}

.r4s-schedule-public--comp-date-link {
  padding: 0 0.5em;
  border-right: 1px solid #c5c5c5;
}

.r4s-schedule-public--comp-date-link-selected {
  color: black;
}

.r4s-schedule-public--logo {
  height: 4em;
}

.r4s-schedule-public--schedule-row-head {
  margin: 0.5em 0;
}

.r4s-schedule-public--schedule-row-head-label {
  font-weight: 600;
}

.r4s-schedule-public--schedule-row {
  border-top: 1px solid lightgrey;
  border-bottom: 1px solid lightgrey;
  padding: 0.5em 0;
}

.r4s-schedule-public--schedule-row-odd {
  background-color: #f7f7f6;
}

.r4s-schedule-public--ranking-header {
  margin: 0.5em 0;
  font-size: 1.25em;
}

.r4s-ranking--heat-link {
  padding: 0 0.5em;
  border-right: 1px solid #c5c5c5;
}

.r4s-ranking--heat-link-selected {
  color: black;
}
</style>
