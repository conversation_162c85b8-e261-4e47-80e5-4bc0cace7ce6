import {
  ITimeTronicsCompetition,
  ITimeTronicsCompetitionServerResponse,
  ITimeTronicsEvent,
  TimeTronicsResultPosition,
} from "./results-import-models-timetronics";
import { IBuilderCompetition } from "../../builder/builder-models";
import { IEventGroupSummary } from "../../compevent/compevent-models";
import {
  ITimeTronicsEventList,
  ITimeTronicsHeatListParticipation,
  TimeTronicsGender,
} from "./results-import-models-timetronics-list";
import {
  AthleteUrn,
  EventGroupIdString,
  EventGroupIdNumber,
  IBaseConcrete,
  IsoDate,
  Opaque,
  AthleteUrnOrUrns,
} from "../../common/common-models";
import { ISecurity } from "../../athleteCompSched/athletecompsched-models";
import { IParticipantOwner, IParticipantPayee } from "./results-import-data";
import { IR4sAthleteEntry } from "../scoreboard/rs4/rs4-scoreboard-models";
import { ENTITY_LEVEL_NUMBER, IEntity } from "../../config/config-app-models";
import { IAgeGroup } from "../../agegroup/agegroup-models";

// export type TimeTronicsEventNumber = string;
export type TimeTronicsEventNumber = Opaque<"TimeTronicsEventNumber", string>;

export type ResultsImportWhichComp = "source" | "target";

export const TimeTronicsEventsGroups = [
  "SEQNO",
  "SESSION",
  "EVENT_TYPE",
] as const;

export type TimeTronicsEventName = string;

export type TimeTronicsEventsGroupBy = typeof TimeTronicsEventsGroups[number];
export type TimeTronicsEventGroupResult = Partial<
  Record<TimeTronicsEventsGroupBy, ITimeTronicsEvent[]>
>;

export type TimeTronicsEventGroupKey =
  | TimeTronicsEventName
  | TimeTronicsEventsGroupBy;

export type TimeTronicsEventGroupByKey = Record<
  TimeTronicsEventGroupKey,
  ITimeTronicsEvent[]
>;

export interface ITimeTronicsState {
  timeTronicsCompetitionServerUrl: string;
  timeTronicsCompetitionServerResponse: ITimeTronicsCompetitionServerResponse | null;
  timeTronicsCompetition: ITimeTronicsCompetition | null;
  timeTronicsEventsGroupBy: TimeTronicsEventsGroupBy;
  timeTronicsEventsGrouped: TimeTronicsEventGroupResult;
  timeTronicsEventsGroupedFilteredByEventName: TimeTronicsEventGroupByKey;
  timeTronicsFilterTerm: string;
  timeTronics: {
    autoLoad: boolean;
    timeTronicsEventMap: Record<string, ITimeTronicsEventList>;
  };
}

export type MapEntriesByAthleteUrnOrTeamAthleteUrns = Record<
  AthleteUrnOrUrns,
  IR4sAthleteEntry
>;

export type MapEntriesByEventGroupIdByAthleteUrn = Record<
  EventGroupIdString,
  MapEntriesByAthleteUrnOrTeamAthleteUrns
>;

export interface IResultsImportState extends ITimeTronicsState {
  isLoading: boolean;
  isLoadingGlobal: boolean;
  useLocalServer: boolean;
  userEtity: IEntity;
  userEntities: IEntity[];
  sourceComp: {
    id: number;
    builderCompetition: IBuilderCompetition;
    eventGroupSummaries: IResultsImportEventGroupSummary[];
    eventGroupSummariesMap: Record<
      EventGroupIdString,
      IResultsImportEventGroupSummary
    >;
    entries: Record<EventGroupIdString, IR4sAthleteEntry[]>;
    entriesEventGroupUrnMap: MapEntriesByEventGroupIdByAthleteUrn;
    entriesE4sResultImportSeqEventKey: Record<
      E4sResultImportSeqEventKey,
      IR4sAthleteEntry[]
    >;
  };
  targetComp: {
    id: number;
    builderCompetition: IBuilderCompetition;
    eventGroupSummaries: IResultsImportEventGroupSummary[];
    eventGroupSummariesMap: Record<
      EventGroupIdString,
      IResultsImportEventGroupSummary
    >;
    entries: Record<EventGroupIdString, IR4sAthleteEntry[]>;
    entriesEventGroupUrnMap: MapEntriesByEventGroupIdByAthleteUrn;
    entriesE4sResultImportSeqEventKey: Record<
      E4sResultImportSeqEventKey,
      IR4sAthleteEntry[]
    >;
  };
  showOnlyFinals: boolean;
  filterTerm: string;

  e4s: {
    athletes: Record<AthleteUrn, IParticipantOwner>;
    athletesPayees: Record<AthleteUrn, IParticipantPayee>; //  badly named...this is set for a given event group
  };
  onResultsImportEventExpanded: IOutputResultsImportEventOnExpanded;
}

export interface IResultsImportEventState {
  sourceEventGroupSummary: IResultsImportEventGroupSummary;
  targetEventGroupSummary: IResultsImportEventGroupSummary;
  showTopXRows: number;
}

export interface IResultsImportEventOutput extends IResultsImportEventState {
  timeTronicsEvent: ITimeTronicsEvent;
  timeTronicsEventList: ITimeTronicsEventList;
}

export interface IFlattenedParticipation {
  resultPosition: TimeTronicsResultPosition;
  result: string;
  resultValue: number;
  bib: string;
  birthdate: IsoDate;
  firstName: string;
  club: string;
  competitionteam: string;
  team: string;
  gender: TimeTronicsGender;
  lastName: string;
  URN: string;
  owner: IBaseConcrete;
  isSelected: boolean;
  isTeam: boolean;
  timeTronicsHeatListParticipation: ITimeTronicsHeatListParticipation;
}

export interface IResultsImportEventGroupSummary extends IEventGroupSummary {
  security: ISecurity;
  genders: string[];
  eventName: string;
  ageGroup: IAgeGroup;
  isTeam: boolean;
}

export type MapFlattenedParticipations = Record<
  TimeTronicsResultPosition,
  IFlattenedParticipation
>;

export interface IEntityEntry {
  id: number;
  level: ENTITY_LEVEL_NUMBER;
}

export interface ISubmitEntriesPayload {
  entity: IEntityEntry;
  entries: ISubmitEntry[];
}

//  E.g. 1:264, allows us to know some entries have been done for a specific event, Women 1500m
export type E4sResultImportSeqEventKey = string;

export interface ISubmitEntry {
  targetEgId: EventGroupIdNumber;
  ownerId: number;
  result: number;
  resultsKey: E4sResultImportSeqEventKey; //  E.g. "264:1";   a unique Id to the event
}

export interface ISubmitEntryAthlete extends ISubmitEntry {
  urn: number;
}

export interface ISubmitEntryTeam {
  team: {
    teamName: string; //  E.g. "Dundrum South Duiblin A.C. (A)"
    athletes: ISubmitEntryTeamAthlete[];
  };
}

export interface ISubmitEntryTeamAthlete {
  urn: number;
  seqno: number;
}

export interface IOutputResultsImportEventOnExpanded {
  isExpanded: boolean;
  timeTronicsEvent: ITimeTronicsEvent | null;
}
