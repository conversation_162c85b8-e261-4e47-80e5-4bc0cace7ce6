import { IParticipantOwner } from "../results-import-data";
import {IResultsImportEventGroupSummary} from "../results-import-models"

export const ownerCarlaSweeneyMock: IParticipantOwner = {
  urn: 117332,
  id: 186131,
  club: { id: 1302, name: "Rathfarnham W.S.A.F. A.C." },
  county: { id: 30, name: "Dublin" },
  region: { id: 9, name: "Leinster" },
  entries: [],
  owners: {
    "1": { id: 1, name: "<EMAIL>", types: ["County", "Region"] },
    "529": { id: 529, name: "mary<PERSON><PERSON>@Safe-mail.net", types: ["County"] },
    "530": { id: 530, name: "<EMAIL>", types: ["County"] },
    "531": { id: 531, name: "<EMAIL>", types: ["County"] },
    "551": { id: 551, name: "<EMAIL>", types: ["Region"] },
    "552": {
      id: 552,
      name: "<EMAIL>",
      types: ["Region"],
    },
    "553": {
      id: 553,
      name: "<EMAIL>",
      types: ["Region"],
    },
    "563": {
      id: 563,
      name: "<EMAIL>",
      types: ["County"],
    },
    "579": { id: 579, name: "<EMAIL>", types: ["Region"] },
    "582": { id: 582, name: "<EMAIL>", types: ["Region"] },
    "1289": { id: 1289, name: "<EMAIL>", types: ["Club"] },
    "1290": { id: 1290, name: "<EMAIL>", types: ["Club"] },
    "1681": { id: 1681, name: "<EMAIL>", types: ["Region"] },
    "1820": { id: 1820, name: "<EMAIL>", types: ["County"] },
    "2024": { id: 2024, name: "<EMAIL>", types: ["Club"] },
    "2944": {
      id: 2944,
      name: "<EMAIL>",
      types: ["County"],
    },
    "14656": {
      id: 14656,
      name: "<EMAIL>",
      types: ["Region"],
    },
    "15532": {
      id: 15532,
      name: "<EMAIL>",
      types: ["Region"],
    },
  },
} as any as IParticipantOwner;

export const resultsImportEventGroupSummaryCarlaSweeney1500m: IResultsImportEventGroupSummary = [
  {
    type: "T",
    typeNo: 1,
    id: 5320,
    eventNo: 1,
    bibSortNo: "",
    name: "1500m",
    notes: null,
    security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
    genders: ["F", "M"],
    eventName: "1500m",
  },
  {
    type: "T",
    typeNo: 3,
    id: 5321,
    eventNo: 3,
    bibSortNo: "",
    name: "400m Women",
    notes: "",
    security: { clubs: [], counties: [], regions: [] },
    genders: ["F"],
    eventName: "400m",
  },
  {
    type: "T",
    typeNo: 2,
    id: 5322,
    eventNo: 2,
    bibSortNo: "",
    name: "800m",
    notes: null,
    security: { clubs: [], counties: [], regions: [] },
    genders: ["F", "M"],
    eventName: "800m",
  },
  {
    type: "F",
    typeNo: 4,
    id: 5323,
    eventNo: 4,
    bibSortNo: "",
    name: "Shotput",
    notes: null,
    security: { clubs: [], counties: [], regions: [] },
    genders: ["F", "M"],
    eventName: "Shotput",
  },
  {
    type: "T",
    typeNo: 5,
    id: 5325,
    eventNo: 5,
    bibSortNo: "",
    name: "400m Men",
    notes: null,
    security: { clubs: [], counties: [], regions: [] },
    genders: ["M"],
    eventName: "400m",
  },
  {
    type: "T",
    typeNo: 6,
    id: 5326,
    eventNo: 6,
    bibSortNo: "",
    name: "4 x 200",
    notes: null,
    security: { clubs: [], counties: [], regions: [] },
    genders: ["F", "M"],
    eventName: "4 x 200",
  },
  {
    type: "M",
    typeNo: 9,
    id: 5329,
    eventNo: 9,
    bibSortNo: "",
    name: "Donation of  1 pound to XXXXXX",
    notes: null,
    security: { clubs: [], counties: [], regions: [] },
    genders: ["M", "F"],
    eventName: "Donation",
  },
] as any as IResultsImportEventGroupSummary;
