import { IParticipantOwner } from "../results-import-data";

export const ownersMock: Record<string, IParticipantOwner> = {
  "3860903": {
    urn: 3860903,
    id: 131825,
    club: {
      id: 1196,
      name: "Notts A.C.",
    },
    county: {
      id: 113,
      name: "Nottinghamshire",
    },
    region: {
      id: 4,
      name: "England",
    },
    entries: {
      "5222": {
        entryId: 69736,
        ownerId: 521,
      },
    },
    owners: {
      "1": {
        id: 1,
        name: "<EMAIL>",
        types: [
          {
            type: "Owner",
            entityId: 0,
            entityName: "",
          },
        ],
      },
      "521": {
        id: 521,
        name: "<EMAIL>",
        types: ["Owner", "Region"],
      },
      "12605": {
        id: 12605,
        name: "<EMAIL>",
        types: ["Club"],
      },
      "12606": {
        id: 12606,
        name: "<EMAIL>",
        types: ["Owner"],
      },
    },
  },
  "3873743": {
    urn: 3873743,
    id: 159231,
    club: {
      id: 1138,
      name: "Newham and Essex Beagles A.C.",
    },
    county: {
      id: 140,
      name: "London",
    },
    region: {
      id: 4,
      name: "England",
    },
    entries: {
      "5222": {
        entryId: 69738,
        ownerId: 9445,
      },
    },
    owners: {
      "1": {
        id: 1,
        name: "<EMAIL>",
        types: [
          {
            type: "Owner",
            entityId: 0,
            entityName: "",
          },
        ],
      },
      "3": {
        id: 3,
        name: "<EMAIL>",
        types: ["Region"],
      },
      "510": {
        id: 510,
        name: "<EMAIL>",
        types: ["Region"],
      },
      "9445": {
        id: 9445,
        name: "<EMAIL>",
        types: ["Club"],
      },
      "14641": {
        id: 14641,
        name: "<EMAIL>",
        types: ["County"],
      },
      "21045": {
        id: 21045,
        name: "<EMAIL>",
        types: ["Owner"],
      },
    },
  },
  "3933518": {
    urn: 3933518,
    id: 160847,
    club: {
      id: 553,
      name: "Ealing Southall and Middlesex A.C.",
    },
    county: {
      id: 140,
      name: "London",
    },
    region: {
      id: 4,
      name: "England",
    },
    entries: {
      "5222": {
        entryId: 69743,
        ownerId: 1,
      },
    },
    owners: {
      "1": {
        id: 1,
        name: "<EMAIL>",
        types: [
          {
            type: "Owner",
            entityId: 0,
            entityName: "",
          },
        ],
      },
      "22870": {
        id: 22870,
        name: "<EMAIL>",
        types: ["Owner"],
      },
    },
  },
  "3943657": {
    urn: 3943657,
    id: 150382,
    club: {
      id: 177,
      name: "Blackheath and Bromley Harriers A.C.",
    },
    county: {
      id: 140,
      name: "London",
    },
    region: {
      id: 4,
      name: "England",
    },
    entries: {
      "5222": {
        entryId: 69734,
        ownerId: 1,
      },
    },
    owners: {
      "15528": {
        id: 15528,
        name: "<EMAIL>",
        types: ["Owner"],
      },
    },
  },
};
