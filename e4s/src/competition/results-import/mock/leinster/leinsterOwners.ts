export const leinsterO<PERSON><PERSON>Athlete408087 = {
  urn: 408087,
  id: 213421,
  club: {
    id: 1108,
    name: "Mullingar Harriers A.C.",
  },
  county: {
    id: 27,
    name: "Westmeath",
  },
  region: {
    id: 9,
    name: "Leinster",
  },
  entries: [],
  owners: {
    "1": {
      id: 1,
      name: "<EMAIL>",
      types: ["Region"],
    },
    "548": {
      id: 548,
      name: "<EMAIL>",
      types: ["County"],
    },
    "552": {
      id: 552,
      name: "<EMAIL>",
      types: ["Region"],
    },
    "553": {
      id: 553,
      name: "<EMAIL>",
      types: ["Region"],
    },
    "563": {
      id: 563,
      name: "<EMAIL>",
      types: ["Region"],
    },
    "579": {
      id: 579,
      name: "<EMAIL>",
      types: ["Club", "County"],
    },
    "1222": {
      id: 1222,
      name: "walsh<PERSON><PERSON>@yahoo.com",
      types: ["Club"],
    },
    "1223": {
      id: 1223,
      name: "<EMAIL>",
      types: ["Club"],
    },
    "1224": {
      id: 1224,
      name: "<EMAIL>",
      types: ["Club"],
    },
    "1225": {
      id: 1225,
      name: "<EMAIL>",
      types: ["Club"],
    },
    "1681": {
      id: 1681,
      name: "<EMAIL>",
      types: ["Club", "County", "Region"],
    },
    "14656": {
      id: 14656,
      name: "<EMAIL>",
      types: ["Region"],
    },
    "15532": {
      id: 15532,
      name: "<EMAIL>",
      types: ["Region"],
    },
    "15536": {
      id: 15536,
      name: "<EMAIL>",
      types: ["Region"],
    },
    "25693": {
      id: 25693,
      name: "<EMAIL>",
      types: ["Region"],
    },
    "25951": {
      id: 25951,
      name: "<EMAIL>",
      types: ["Region"],
    },
    "26714": {
      id: 26714,
      name: "<EMAIL>",
      types: ["Owner"],
    },
  },
};
