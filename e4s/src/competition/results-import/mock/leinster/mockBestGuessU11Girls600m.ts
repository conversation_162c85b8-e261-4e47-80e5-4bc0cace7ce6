import { IResultsImportEventGroupSummary } from "../../results-import-models";
import { ITimeTronicsEvent } from "../../results-import-models-timetronics";

export const timeTronicsEvent600mU11Girls: ITimeTronicsEvent = {
  abbreviation: "600mU11G",
  categories: [{ abbreviation: "U11 G", id: 46, name: "U11 Girls" }],
  eventtype: {
    abbreviation: "",
    athletesquantity: 1,
    code: "600m",
    combinedeventsquantity: 0,
    distance: 600,
    fieldtype: " ",
    id: 187,
    implement: 0,
    name: "",
    shortcode: "600m",
    windmode: "N",
    windtime: "",
  },
  id: 5,
  info: "",
  name: "U11 Girls 600m",
  rounds: [
    {
      abbreviation: "I",
      datescheduled: "2024-06-02",
      heats: [
        {
          attempts: 0,
          height: 0,
          id: 118,
          info: "0",
          numberofparticipations: 15,
          official: "11:25:32",
          scheduled: "11:00:00",
          seqno: 1,
          status: 5,
        },
        {
          attempts: 0,
          height: 0,
          id: 119,
          info: "0",
          numberofparticipations: 14,
          official: "11:25:32",
          scheduled: "11:04:00",
          seqno: 2,
          status: 5,
        },
        {
          attempts: 0,
          height: 0,
          id: 120,
          info: "0",
          numberofparticipations: 15,
          official: "11:25:32",
          scheduled: "11:08:00",
          seqno: 3,
          status: 5,
        },
      ],
      id: 5,
      info: "",
      name: "Indiv",
      numberofparticipationsround: 44,
      official: "11:25:32",
      scheduled: "11:00:00",
      seqno: 1,
      session: "Distance",
      status: 5,
    },
  ],
  seqno: 5,
  status: 5,
};

export const mockTargetEventSummariesU11Girls600m: IResultsImportEventGroupSummary[] =
  [
    {
      type: "T",
      typeNo: 1,
      id: 6202,
      eventNo: 1,
      bibSortNo: "",
      name: "600m ",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F", "M"],
      eventName: "600m",
      ageGroup: {
        id: 3,
        minAge: 6,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 9",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 9",
        maxAge: 8,
        maxAtDay: 31,
        maxAtMonth: 8,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 9",
      },
    },
    {
      type: "T",
      typeNo: 2,
      id: 6216,
      eventNo: 2,
      bibSortNo: "",
      name: "600m U11",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F", "M"],
      eventName: "600m",
      ageGroup: {
        id: 1,
        minAge: 9,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 11",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 11",
        maxAge: 10,
        maxAtDay: 31,
        maxAtMonth: 8,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 11",
      },
    },
    {
      type: "T",
      typeNo: 3,
      id: 6217,
      eventNo: 3,
      bibSortNo: "",
      name: "600m U13 Boys",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["M"],
      eventName: "600m",
      ageGroup: {
        id: 2,
        minAge: 11,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 13",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 13",
        maxAge: 12,
        maxAtDay: 31,
        maxAtMonth: 8,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 13",
      },
    },
    {
      type: "T",
      typeNo: 4,
      id: 6218,
      eventNo: 4,
      bibSortNo: "",
      name: "600m U13 Girls",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F"],
      eventName: "600m",
      ageGroup: {
        id: 2,
        minAge: 11,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 13",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 13",
        maxAge: 12,
        maxAtDay: 31,
        maxAtMonth: 8,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 13",
      },
    },
    {
      type: "T",
      typeNo: 5,
      id: 6220,
      eventNo: 5,
      bibSortNo: "",
      name: "100m",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F", "M"],
      eventName: "100m",
      ageGroup: {
        id: 1,
        minAge: 9,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 11",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 11",
        maxAge: 10,
        maxAtDay: 31,
        maxAtMonth: 8,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 11",
      },
    },
    {
      type: "T",
      typeNo: 6,
      id: 6221,
      eventNo: 6,
      bibSortNo: "",
      name: "200m",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F", "M"],
      eventName: "200m",
      ageGroup: {
        id: 1,
        minAge: 9,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 11",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 11",
        maxAge: 10,
        maxAtDay: 31,
        maxAtMonth: 8,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 11",
      },
    },
  ] as any as IResultsImportEventGroupSummary[];
