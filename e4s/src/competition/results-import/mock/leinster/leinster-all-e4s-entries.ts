import { IFeederAllEntriesForCompByEventGroupIdByEntryId } from "../../../scoreboard/scoreboard-data";

export const feederAllEntriesForComp1094: IFeederAllEntriesForCompByEventGroupIdByEntryId =
  {
    "22800": {
      "27626": {
        entryId: 27626,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Ratoath A.C. 4x100 Relay Female Under 9 {A}",
        paid: 1,
        egId: 22800,
        gender: "F",
        urn: 363503,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 1304,
          teamEntity: "Ratoath A.C.",
          entityName: "Club",
        },
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
        athletes: [
          {
            id: 238854,
            name: "Do we want this ?",
            urn: 434260,
            pos: 1,
          },
          {
            id: 162346,
            name: "Do we want this ?",
            urn: 368522,
            pos: 2,
          },
          {
            id: 164007,
            name: "Do we want this ?",
            urn: 368654,
            pos: 3,
          },
          {
            id: 165385,
            name: "Do we want this ?",
            urn: 363503,
            pos: 4,
          },
        ],
      },
      "27667": {
        entryId: 27667,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Innyvale A.C. 4x100 Relay Female Under 9 {A}",
        paid: 1,
        egId: 22800,
        gender: "F",
        urn: 409100,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 838,
          teamEntity: "Innyvale A.C.",
          entityName: "Club",
        },
        user: {
          id: 14779,
          email: "<EMAIL>",
          login: "dmulvey001974",
          name: "dmulvey001974",
        },
        athletes: [
          {
            id: 214443,
            name: "Do we want this ?",
            urn: 409119,
            pos: 1,
          },
          {
            id: 194969,
            name: "Do we want this ?",
            urn: 391431,
            pos: 2,
          },
          {
            id: 236505,
            name: "Do we want this ?",
            urn: 431748,
            pos: 3,
          },
          {
            id: 214427,
            name: "Do we want this ?",
            urn: 409100,
            pos: 4,
          },
        ],
      },
      "27730": {
        entryId: 27730,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. 4x100 Relay Female Under 9 {A}",
        paid: 1,
        egId: 22800,
        gender: "F",
        urn: 419422,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 225036,
            name: "Do we want this ?",
            urn: 419419,
            pos: 1,
          },
          {
            id: 225022,
            name: "Do we want this ?",
            urn: 419155,
            pos: 2,
          },
          {
            id: 225252,
            name: "Do we want this ?",
            urn: 419580,
            pos: 3,
          },
          {
            id: 225041,
            name: "Do we want this ?",
            urn: 419423,
            pos: 4,
          },
          {
            id: 225040,
            name: "Do we want this ?",
            urn: 419422,
            pos: 5,
          },
        ],
      },
    },
    "22801": {
      "27547": {
        entryId: 27547,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "East Galway A.C. 4x100 Relay Male Under 9 {A}",
        paid: 1,
        egId: 22801,
        gender: "M",
        urn: 396683,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 563,
          teamEntity: "East Galway A.C.",
          entityName: "Club",
        },
        user: {
          id: 22749,
          email: "<EMAIL>",
          login: "mcdonf1",
          name: "mcdonf1",
        },
        athletes: [
          {
            id: 162028,
            name: "Do we want this ?",
            urn: 364891,
            pos: 1,
          },
          {
            id: 176085,
            name: "Do we want this ?",
            urn: 375133,
            pos: 2,
          },
          {
            id: 199933,
            name: "Do we want this ?",
            urn: 395419,
            pos: 3,
          },
          {
            id: 199934,
            name: "Do we want this ?",
            urn: 395420,
            pos: 4,
          },
          {
            id: 202856,
            name: "Do we want this ?",
            urn: 396683,
            pos: 5,
          },
        ],
      },
      "27639": {
        entryId: 27639,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Crusaders A.C. 4x100 Relay Male Under 9 {A}",
        paid: 1,
        egId: 22801,
        gender: "M",
        urn: 397771,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 436,
          teamEntity: "Crusaders A.C.",
          entityName: "Club",
        },
        user: {
          id: 20815,
          email: "<EMAIL>",
          login: "Tessa",
          name: "Tessa",
        },
        athletes: [
          {
            id: 220285,
            name: "Do we want this ?",
            urn: 414940,
            pos: 1,
          },
          {
            id: 219463,
            name: "Do we want this ?",
            urn: 414119,
            pos: 2,
          },
          {
            id: 224917,
            name: "Do we want this ?",
            urn: 419316,
            pos: 3,
          },
          {
            id: 229581,
            name: "Do we want this ?",
            urn: 423960,
            pos: 4,
          },
          {
            id: 204579,
            name: "Do we want this ?",
            urn: 397772,
            pos: 5,
          },
          {
            id: 204578,
            name: "Do we want this ?",
            urn: 397771,
            pos: 6,
          },
        ],
      },
      "27657": {
        entryId: 27657,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. 4x100 Relay Male Under 9 {A}",
        paid: 1,
        egId: 22801,
        gender: "M",
        urn: 414526,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 230705,
            name: "Do we want this ?",
            urn: 425235,
            pos: 1,
          },
          {
            id: 193356,
            name: "Do we want this ?",
            urn: 390561,
            pos: 2,
          },
          {
            id: 193355,
            name: "Do we want this ?",
            urn: 390560,
            pos: 3,
          },
          {
            id: 231831,
            name: "Do we want this ?",
            urn: 426668,
            pos: 4,
          },
          {
            id: 191891,
            name: "Do we want this ?",
            urn: 389158,
            pos: 5,
          },
          {
            id: 219884,
            name: "Do we want this ?",
            urn: 414526,
            pos: 6,
          },
        ],
      },
      "27711": {
        entryId: 27711,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Kilkenny City Harriers A.C. 4x100 Relay Male Under 9 {A}",
        paid: 0,
        egId: 22801,
        gender: "M",
        urn: 425910,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 884,
          teamEntity: "Kilkenny City Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1074,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "May Hutt",
        },
        athletes: [
          {
            id: 215758,
            name: "Do we want this ?",
            urn: 410247,
            pos: 1,
          },
          {
            id: 215605,
            name: "Do we want this ?",
            urn: 410234,
            pos: 2,
          },
          {
            id: 215543,
            name: "Do we want this ?",
            urn: 410227,
            pos: 3,
          },
          {
            id: 233049,
            name: "Do we want this ?",
            urn: 425910,
            pos: 4,
          },
        ],
      },
    },
    "22802": {
      "27574": {
        entryId: 27574,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Glaslough Harriers A.C. 4x100 Relay Female Under 10 {A}",
        paid: 1,
        egId: 22802,
        gender: "F",
        urn: 421927,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 688,
          teamEntity: "Glaslough Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 22553,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Sabrina Mc Cooey",
        },
        athletes: [
          {
            id: 217063,
            name: "Do we want this ?",
            urn: 411432,
            pos: 1,
          },
          {
            id: 189090,
            name: "Do we want this ?",
            urn: 386486,
            pos: 2,
          },
          {
            id: 232567,
            name: "Do we want this ?",
            urn: 427569,
            pos: 3,
          },
          {
            id: 189840,
            name: "Do we want this ?",
            urn: 387206,
            pos: 4,
          },
          {
            id: 227522,
            name: "Do we want this ?",
            urn: 421927,
            pos: 5,
          },
        ],
      },
      "27627": {
        entryId: 27627,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Ratoath A.C. 4x100 Relay Female Under 10 {A}",
        paid: 1,
        egId: 22802,
        gender: "F",
        urn: 403321,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 1304,
          teamEntity: "Ratoath A.C.",
          entityName: "Club",
        },
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
        athletes: [
          {
            id: 162316,
            name: "Do we want this ?",
            urn: 368869,
            pos: 1,
          },
          {
            id: 154065,
            name: "Do we want this ?",
            urn: 357103,
            pos: 2,
          },
          {
            id: 183002,
            name: "Do we want this ?",
            urn: 380753,
            pos: 3,
          },
          {
            id: 185070,
            name: "Do we want this ?",
            urn: 382668,
            pos: 4,
          },
          {
            id: 209265,
            name: "Do we want this ?",
            urn: 403321,
            pos: 5,
          },
        ],
      },
      "27697": {
        entryId: 27697,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. 4x100 Relay Female Under 10 {A}",
        paid: 1,
        egId: 22802,
        gender: "F",
        urn: 382673,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 183221,
            name: "Do we want this ?",
            urn: 380953,
            pos: 1,
          },
          {
            id: 215879,
            name: "Do we want this ?",
            urn: 410583,
            pos: 2,
          },
          {
            id: 235894,
            name: "Do we want this ?",
            urn: 431139,
            pos: 3,
          },
          {
            id: 182617,
            name: "Do we want this ?",
            urn: 380414,
            pos: 4,
          },
          {
            id: 185075,
            name: "Do we want this ?",
            urn: 382673,
            pos: 5,
          },
        ],
      },
      "27731": {
        entryId: 27731,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. 4x100 Relay Female Under 10 {A}",
        paid: 1,
        egId: 22802,
        gender: "F",
        urn: 414569,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 198972,
            name: "Do we want this ?",
            urn: 394527,
            pos: 1,
          },
          {
            id: 228361,
            name: "Do we want this ?",
            urn: 422833,
            pos: 2,
          },
          {
            id: 210221,
            name: "Do we want this ?",
            urn: 404739,
            pos: 3,
          },
          {
            id: 219938,
            name: "Do we want this ?",
            urn: 414569,
            pos: 4,
          },
        ],
      },
      "27734": {
        entryId: 27734,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Limerick A.C. 4x100 Relay Female Under 10 {A}",
        paid: 0,
        egId: 22802,
        gender: "F",
        urn: 385219,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 955,
          teamEntity: "Limerick A.C.",
          entityName: "Club",
        },
        user: {
          id: 1125,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Ray McInerney",
        },
        athletes: [
          {
            id: 189915,
            name: "Do we want this ?",
            urn: 387277,
            pos: 1,
          },
          {
            id: 169023,
            name: "Do we want this ?",
            urn: 367006,
            pos: 2,
          },
          {
            id: 174842,
            name: "Do we want this ?",
            urn: 373897,
            pos: 3,
          },
          {
            id: 166867,
            name: "Do we want this ?",
            urn: 364559,
            pos: 4,
          },
          {
            id: 187768,
            name: "Do we want this ?",
            urn: 385219,
            pos: 5,
          },
        ],
      },
    },
    "22803": {
      "27276": {
        entryId: 27276,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Skerries A.C. 4x100 Relay Male Under 10 {A}",
        paid: 1,
        egId: 22803,
        gender: "M",
        urn: 406023,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 1443,
          teamEntity: "Skerries A.C.",
          entityName: "Club",
        },
        user: {
          id: 25260,
          email: "<EMAIL>",
          login: "SkerriesACRaceSecretary",
          name: "SkerriesACRaceSecretary",
        },
        athletes: [
          {
            id: 207530,
            name: "Do we want this ?",
            urn: 401463,
            pos: 1,
          },
          {
            id: 209636,
            name: "Do we want this ?",
            urn: 403900,
            pos: 2,
          },
          {
            id: 207659,
            name: "Do we want this ?",
            urn: 401360,
            pos: 3,
          },
          {
            id: 211513,
            name: "Do we want this ?",
            urn: 406023,
            pos: 4,
          },
        ],
      },
      "27588": {
        entryId: 27588,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "4x100 Relay Male Under 10 {A}",
        paid: 1,
        egId: 22803,
        gender: "M",
        urn: 435151,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 0,
          teamEntity: "Unknown",
          entityName: "Unknown",
        },
        user: {
          id: 14939,
          email: "<EMAIL>",
          login: "bernieocallaghan",
          name: "bernieocallaghan",
        },
        athletes: [
          {
            id: 184492,
            name: "Do we want this ?",
            urn: 382120,
            pos: 1,
          },
          {
            id: 195421,
            name: "Do we want this ?",
            urn: 391778,
            pos: 2,
          },
          {
            id: 190422,
            name: "Do we want this ?",
            urn: 387747,
            pos: 3,
          },
          {
            id: 189468,
            name: "Do we want this ?",
            urn: 386846,
            pos: 4,
          },
          {
            id: 239636,
            name: "Do we want this ?",
            urn: 435151,
            pos: 5,
          },
        ],
      },
      "27696": {
        entryId: 27696,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. 4x100 Relay Male Under 10 {A}",
        paid: 1,
        egId: 22803,
        gender: "M",
        urn: 394970,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 215877,
            name: "Do we want this ?",
            urn: 410582,
            pos: 1,
          },
          {
            id: 198427,
            name: "Do we want this ?",
            urn: 394029,
            pos: 2,
          },
          {
            id: 166639,
            name: "Do we want this ?",
            urn: 367513,
            pos: 3,
          },
          {
            id: 182743,
            name: "Do we want this ?",
            urn: 380518,
            pos: 4,
          },
          {
            id: 199441,
            name: "Do we want this ?",
            urn: 394970,
            pos: 5,
          },
        ],
      },
      "27718": {
        entryId: 27718,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Erris A.C. 4x100 Relay Male Under 10 {A}",
        paid: 1,
        egId: 22803,
        gender: "M",
        urn: 386964,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 3036,
          teamEntity: "Erris A.C.",
          entityName: "Club",
        },
        user: {
          id: 22719,
          email: "<EMAIL>",
          login: "Erris AC",
          name: "Erris AC",
        },
        athletes: [
          {
            id: 189614,
            name: "Do we want this ?",
            urn: 386985,
            pos: 1,
          },
          {
            id: 189585,
            name: "Do we want this ?",
            urn: 386956,
            pos: 2,
          },
          {
            id: 189598,
            name: "Do we want this ?",
            urn: 386969,
            pos: 3,
          },
          {
            id: 189600,
            name: "Do we want this ?",
            urn: 386970,
            pos: 4,
          },
          {
            id: 189593,
            name: "Do we want this ?",
            urn: 386964,
            pos: 5,
          },
        ],
      },
      "27733": {
        entryId: 27733,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. 4x100 Relay Male Under 10 {A}",
        paid: 1,
        egId: 22803,
        gender: "M",
        urn: 419553,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 225042,
            name: "Do we want this ?",
            urn: 419421,
            pos: 1,
          },
          {
            id: 210249,
            name: "Do we want this ?",
            urn: 404745,
            pos: 2,
          },
          {
            id: 189695,
            name: "Do we want this ?",
            urn: 387066,
            pos: 3,
          },
          {
            id: 225166,
            name: "Do we want this ?",
            urn: 419553,
            pos: 4,
          },
        ],
      },
    },
    "22804": {
      "27605": {
        entryId: 27605,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Cranford A.C. 4x100 Relay Female Under 11 {A}",
        paid: 0,
        egId: 22804,
        gender: "F",
        urn: 405475,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 419,
          teamEntity: "Cranford A.C.",
          entityName: "Club",
        },
        user: {
          id: 19498,
          email: "<EMAIL>",
          login: "CranfordAC",
          name: "Cranford AC",
        },
        athletes: [
          {
            id: 194462,
            name: "Do we want this ?",
            urn: 391040,
            pos: 1,
          },
          {
            id: 194461,
            name: "Do we want this ?",
            urn: 391039,
            pos: 2,
          },
          {
            id: 171478,
            name: "Do we want this ?",
            urn: 371139,
            pos: 3,
          },
          {
            id: 227147,
            name: "Do we want this ?",
            urn: 421562,
            pos: 4,
          },
          {
            id: 210921,
            name: "Do we want this ?",
            urn: 405475,
            pos: 5,
          },
        ],
      },
      "27608": {
        entryId: 27608,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Newbridge A.C. 4x100 Relay Female Under 11 {A}",
        paid: 1,
        egId: 22804,
        gender: "F",
        urn: 375782,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 1130,
          teamEntity: "Newbridge A.C.",
          entityName: "Club",
        },
        user: {
          id: 1243,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Seamus Cardiff",
        },
        athletes: [
          {
            id: 189825,
            name: "Do we want this ?",
            urn: 387191,
            pos: 1,
          },
          {
            id: 169201,
            name: "Do we want this ?",
            urn: 366542,
            pos: 2,
          },
          {
            id: 163442,
            name: "Do we want this ?",
            urn: 366424,
            pos: 3,
          },
          {
            id: 169312,
            name: "Do we want this ?",
            urn: 365425,
            pos: 4,
          },
          {
            id: 176755,
            name: "Do we want this ?",
            urn: 375782,
            pos: 5,
          },
        ],
      },
      "27628": {
        entryId: 27628,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Ratoath A.C. 4x100 Relay Female Under 11 {A}",
        paid: 1,
        egId: 22804,
        gender: "F",
        urn: 357103,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 1304,
          teamEntity: "Ratoath A.C.",
          entityName: "Club",
        },
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
        athletes: [
          {
            id: 145292,
            name: "Do we want this ?",
            urn: 350799,
            pos: 1,
          },
          {
            id: 166373,
            name: "Do we want this ?",
            urn: 365132,
            pos: 2,
          },
          {
            id: 166839,
            name: "Do we want this ?",
            urn: 363769,
            pos: 3,
          },
          {
            id: 177470,
            name: "Do we want this ?",
            urn: 376453,
            pos: 4,
          },
          {
            id: 185070,
            name: "Do we want this ?",
            urn: 382668,
            pos: 5,
          },
          {
            id: 154065,
            name: "Do we want this ?",
            urn: 357103,
            pos: 6,
          },
        ],
      },
      "27638": {
        entryId: 27638,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Crusaders A.C. 4x100 Relay Female Under 11 {A}",
        paid: 1,
        egId: 22804,
        gender: "F",
        urn: 381895,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 436,
          teamEntity: "Crusaders A.C.",
          entityName: "Club",
        },
        user: {
          id: 20815,
          email: "<EMAIL>",
          login: "Tessa",
          name: "Tessa",
        },
        athletes: [
          {
            id: 200427,
            name: "Do we want this ?",
            urn: 395840,
            pos: 1,
          },
          {
            id: 178728,
            name: "Do we want this ?",
            urn: 377555,
            pos: 2,
          },
          {
            id: 199804,
            name: "Do we want this ?",
            urn: 395292,
            pos: 3,
          },
          {
            id: 184243,
            name: "Do we want this ?",
            urn: 381896,
            pos: 4,
          },
          {
            id: 225944,
            name: "Do we want this ?",
            urn: 420385,
            pos: 5,
          },
          {
            id: 184242,
            name: "Do we want this ?",
            urn: 381895,
            pos: 6,
          },
        ],
      },
      "27694": {
        entryId: 27694,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. 4x100 Relay Female Under 11 {A}",
        paid: 1,
        egId: 22804,
        gender: "F",
        urn: 376063,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 167190,
            name: "Do we want this ?",
            urn: 364717,
            pos: 1,
          },
          {
            id: 205187,
            name: "Do we want this ?",
            urn: 398914,
            pos: 2,
          },
          {
            id: 208437,
            name: "Do we want this ?",
            urn: 402595,
            pos: 3,
          },
          {
            id: 199423,
            name: "Do we want this ?",
            urn: 394950,
            pos: 4,
          },
          {
            id: 177062,
            name: "Do we want this ?",
            urn: 376063,
            pos: 5,
          },
        ],
      },
      "27698": {
        entryId: 27698,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Castlebar A.C. 4x100 Relay Female Under 11 {A}",
        paid: 1,
        egId: 22804,
        gender: "F",
        urn: 388754,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 312,
          teamEntity: "Castlebar A.C.",
          entityName: "Club",
        },
        user: {
          id: 764,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Denise Roddy",
        },
        athletes: [
          {
            id: 191478,
            name: "Do we want this ?",
            urn: 388761,
            pos: 1,
          },
          {
            id: 206804,
            name: "Do we want this ?",
            urn: 400298,
            pos: 2,
          },
          {
            id: 191487,
            name: "Do we want this ?",
            urn: 388770,
            pos: 3,
          },
          {
            id: 191473,
            name: "Do we want this ?",
            urn: 388756,
            pos: 4,
          },
          {
            id: 191471,
            name: "Do we want this ?",
            urn: 388754,
            pos: 5,
          },
        ],
      },
      "27726": {
        entryId: 27726,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. 4x100 Relay Female Under 11 {A}",
        paid: 1,
        egId: 22804,
        gender: "F",
        urn: 384340,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 186826,
            name: "Do we want this ?",
            urn: 384344,
            pos: 1,
          },
          {
            id: 173588,
            name: "Do we want this ?",
            urn: 372913,
            pos: 2,
          },
          {
            id: 195719,
            name: "Do we want this ?",
            urn: 392020,
            pos: 3,
          },
          {
            id: 195718,
            name: "Do we want this ?",
            urn: 392022,
            pos: 4,
          },
          {
            id: 186825,
            name: "Do we want this ?",
            urn: 384343,
            pos: 5,
          },
          {
            id: 186822,
            name: "Do we want this ?",
            urn: 384340,
            pos: 6,
          },
        ],
      },
      "27763": {
        entryId: 27763,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 4x100 Relay Female Under 11 {A}",
        paid: 0,
        egId: 22804,
        gender: "F",
        urn: 392388,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 196198,
            name: "Do we want this ?",
            urn: 392390,
            pos: 1,
          },
          {
            id: 218790,
            name: "Do we want this ?",
            urn: 413748,
            pos: 2,
          },
          {
            id: 196194,
            name: "Do we want this ?",
            urn: 392387,
            pos: 3,
          },
          {
            id: 218781,
            name: "Do we want this ?",
            urn: 413738,
            pos: 4,
          },
          {
            id: 196199,
            name: "Do we want this ?",
            urn: 392392,
            pos: 5,
          },
          {
            id: 196195,
            name: "Do we want this ?",
            urn: 392388,
            pos: 6,
          },
        ],
      },
    },
    "22805": {
      "27349": {
        entryId: 27349,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Cabinteely A.C. 4x100 Relay Male Under 11 {A}",
        paid: 1,
        egId: 22805,
        gender: "M",
        urn: 398131,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 268,
          teamEntity: "Cabinteely A.C.",
          entityName: "Club",
        },
        user: {
          id: 23839,
          email: "<EMAIL>",
          login: "mcummins",
          name: "CabinteelyAC",
        },
        athletes: [
          {
            id: 230232,
            name: "Do we want this ?",
            urn: 424583,
            pos: 1,
          },
          {
            id: 195746,
            name: "Do we want this ?",
            urn: 392041,
            pos: 2,
          },
          {
            id: 230218,
            name: "Do we want this ?",
            urn: 424580,
            pos: 3,
          },
          {
            id: 204145,
            name: "Do we want this ?",
            urn: 398131,
            pos: 4,
          },
        ],
      },
      "27587": {
        entryId: 27587,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Glaslough Harriers A.C. 4x100 Relay Male Under 11 {A}",
        paid: 1,
        egId: 22805,
        gender: "M",
        urn: 376135,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 688,
          teamEntity: "Glaslough Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 22553,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Sabrina Mc Cooey",
        },
        athletes: [
          {
            id: 152284,
            name: "Do we want this ?",
            urn: 355805,
            pos: 1,
          },
          {
            id: 152296,
            name: "Do we want this ?",
            urn: 355807,
            pos: 2,
          },
          {
            id: 227523,
            name: "Do we want this ?",
            urn: 421926,
            pos: 3,
          },
          {
            id: 177134,
            name: "Do we want this ?",
            urn: 376135,
            pos: 4,
          },
        ],
      },
      "27589": {
        entryId: 27589,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "4x100 Relay Male Under 11 {A}",
        paid: 1,
        egId: 22805,
        gender: "M",
        urn: 387747,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 0,
          teamEntity: "Unknown",
          entityName: "Unknown",
        },
        user: {
          id: 14939,
          email: "<EMAIL>",
          login: "bernieocallaghan",
          name: "bernieocallaghan",
        },
        athletes: [
          {
            id: 170576,
            name: "Do we want this ?",
            urn: 367033,
            pos: 1,
          },
          {
            id: 189460,
            name: "Do we want this ?",
            urn: 386838,
            pos: 2,
          },
          {
            id: 184492,
            name: "Do we want this ?",
            urn: 382120,
            pos: 3,
          },
          {
            id: 195421,
            name: "Do we want this ?",
            urn: 391778,
            pos: 4,
          },
          {
            id: 190422,
            name: "Do we want this ?",
            urn: 387747,
            pos: 5,
          },
        ],
      },
      "27629": {
        entryId: 27629,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Ratoath A.C. 4x100 Relay Male Under 11 {A}",
        paid: 1,
        egId: 22805,
        gender: "M",
        urn: 422649,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 1304,
          teamEntity: "Ratoath A.C.",
          entityName: "Club",
        },
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
        athletes: [
          {
            id: 64096,
            name: "Do we want this ?",
            urn: 327271,
            pos: 1,
          },
          {
            id: 134610,
            name: "Do we want this ?",
            urn: 345076,
            pos: 2,
          },
          {
            id: 61889,
            name: "Do we want this ?",
            urn: 326019,
            pos: 3,
          },
          {
            id: 162786,
            name: "Do we want this ?",
            urn: 368343,
            pos: 4,
          },
          {
            id: 136495,
            name: "Do we want this ?",
            urn: 346423,
            pos: 5,
          },
          {
            id: 228483,
            name: "Do we want this ?",
            urn: 422649,
            pos: 6,
          },
        ],
      },
      "27665": {
        entryId: 27665,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. 4x100 Relay Male Under 11 {A}",
        paid: 1,
        egId: 22805,
        gender: "M",
        urn: 430149,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 168846,
            name: "Do we want this ?",
            urn: 369602,
            pos: 1,
          },
          {
            id: 181681,
            name: "Do we want this ?",
            urn: 379630,
            pos: 2,
          },
          {
            id: 192139,
            name: "Do we want this ?",
            urn: 389397,
            pos: 3,
          },
          {
            id: 146671,
            name: "Do we want this ?",
            urn: 351885,
            pos: 4,
          },
          {
            id: 234976,
            name: "Do we want this ?",
            urn: 430149,
            pos: 5,
          },
        ],
      },
      "27764": {
        entryId: 27764,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 4x100 Relay Male Under 11 {A}",
        paid: 0,
        egId: 22805,
        gender: "M",
        urn: 421075,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 196210,
            name: "Do we want this ?",
            urn: 392403,
            pos: 1,
          },
          {
            id: 196206,
            name: "Do we want this ?",
            urn: 392400,
            pos: 2,
          },
          {
            id: 218413,
            name: "Do we want this ?",
            urn: 413313,
            pos: 3,
          },
          {
            id: 196236,
            name: "Do we want this ?",
            urn: 392426,
            pos: 4,
          },
          {
            id: 196209,
            name: "Do we want this ?",
            urn: 392402,
            pos: 5,
          },
          {
            id: 226571,
            name: "Do we want this ?",
            urn: 421075,
            pos: 6,
          },
        ],
      },
    },
    "22806": {
      "27394": {
        entryId: 27394,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Enniscorthy A.C. 300m Female Under 9 {A}",
        paid: 1,
        egId: 22806,
        gender: "F",
        urn: 395686,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 599,
          teamEntity: "Enniscorthy A.C.",
          entityName: "Club",
        },
        user: {
          id: 100,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Edel Byrne",
        },
        athletes: [
          {
            id: 195310,
            name: "Do we want this ?",
            urn: 391678,
            pos: 1,
          },
          {
            id: 200250,
            name: "Do we want this ?",
            urn: 395686,
            pos: 2,
          },
        ],
      },
      "27537": {
        entryId: 27537,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Rathfarnham W.S.A.F. A.C. 300m Female Under 9 {A}",
        paid: 1,
        egId: 22806,
        gender: "F",
        urn: 428270,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 1302,
          teamEntity: "Rathfarnham W.S.A.F. A.C.",
          entityName: "Club",
        },
        user: {
          id: 1290,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Adam Jones",
        },
        athletes: [
          {
            id: 224889,
            name: "Do we want this ?",
            urn: 419250,
            pos: 1,
          },
          {
            id: 233281,
            name: "Do we want this ?",
            urn: 428270,
            pos: 2,
          },
        ],
      },
      "27669": {
        entryId: 27669,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Innyvale A.C. 300m Female Under 9 {A}",
        paid: 1,
        egId: 22806,
        gender: "F",
        urn: 391431,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 838,
          teamEntity: "Innyvale A.C.",
          entityName: "Club",
        },
        user: {
          id: 14779,
          email: "<EMAIL>",
          login: "dmulvey001974",
          name: "dmulvey001974",
        },
        athletes: [
          {
            id: 214427,
            name: "Do we want this ?",
            urn: 409100,
            pos: 1,
          },
          {
            id: 194969,
            name: "Do we want this ?",
            urn: 391431,
            pos: 2,
          },
        ],
      },
      "27670": {
        entryId: 27670,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Innyvale A.C. 300m Female Under 9 {B}",
        paid: 1,
        egId: 22806,
        gender: "F",
        urn: 431748,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 838,
          teamEntity: "Innyvale A.C.",
          entityName: "Club",
        },
        user: {
          id: 14779,
          email: "<EMAIL>",
          login: "dmulvey001974",
          name: "dmulvey001974",
        },
        athletes: [
          {
            id: 214443,
            name: "Do we want this ?",
            urn: 409119,
            pos: 1,
          },
          {
            id: 236505,
            name: "Do we want this ?",
            urn: 431748,
            pos: 2,
          },
        ],
      },
      "27695": {
        entryId: 27695,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Castlebar A.C. 300m Female Under 9 {A}",
        paid: 1,
        egId: 22806,
        gender: "F",
        urn: 424641,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 312,
          teamEntity: "Castlebar A.C.",
          entityName: "Club",
        },
        user: {
          id: 764,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Denise Roddy",
        },
        athletes: [
          {
            id: 230028,
            name: "Do we want this ?",
            urn: 424461,
            pos: 1,
          },
          {
            id: 230290,
            name: "Do we want this ?",
            urn: 424641,
            pos: 2,
          },
        ],
      },
      "27704": {
        entryId: 27704,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. 300m Female Under 9 {A}",
        paid: 1,
        egId: 22806,
        gender: "F",
        urn: 419419,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 225041,
            name: "Do we want this ?",
            urn: 419423,
            pos: 1,
          },
          {
            id: 225036,
            name: "Do we want this ?",
            urn: 419419,
            pos: 2,
          },
        ],
      },
      "27737": {
        entryId: 27737,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 300m Female Under 9 {A}",
        paid: 0,
        egId: 22806,
        gender: "F",
        urn: 421076,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 226096,
            name: "Do we want this ?",
            urn: 420555,
            pos: 1,
          },
          {
            id: 226572,
            name: "Do we want this ?",
            urn: 421076,
            pos: 2,
          },
        ],
      },
    },
    "22807": {
      "27516": {
        entryId: 27516,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Athlone A.C. 300m Male Under 9 {A}",
        paid: 1,
        egId: 22807,
        gender: "M",
        urn: 401571,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 3026,
          teamEntity: "Athlone A.C.",
          entityName: "Club",
        },
        user: {
          id: 19357,
          email: "<EMAIL>",
          login: "SHeald",
          name: "SHeald",
        },
        athletes: [
          {
            id: 228551,
            name: "Do we want this ?",
            urn: 423061,
            pos: 1,
          },
          {
            id: 207592,
            name: "Do we want this ?",
            urn: 401571,
            pos: 2,
          },
        ],
      },
      "27548": {
        entryId: 27548,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "East Galway A.C. 300m Male Under 9 {A}",
        paid: 1,
        egId: 22807,
        gender: "M",
        urn: 395419,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 563,
          teamEntity: "East Galway A.C.",
          entityName: "Club",
        },
        user: {
          id: 22749,
          email: "<EMAIL>",
          login: "mcdonf1",
          name: "mcdonf1",
        },
        athletes: [
          {
            id: 162028,
            name: "Do we want this ?",
            urn: 364891,
            pos: 1,
          },
          {
            id: 199933,
            name: "Do we want this ?",
            urn: 395419,
            pos: 2,
          },
        ],
      },
      "27583": {
        entryId: 27583,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Fergus A.C. 300m Male Under 9 {A}",
        paid: 1,
        egId: 22807,
        gender: "M",
        urn: 403621,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 636,
          teamEntity: "Fergus A.C.",
          entityName: "Club",
        },
        user: {
          id: 21929,
          email: "<EMAIL>",
          login: "Noelette Lynch",
          name: "Noelette Lynch",
        },
        athletes: [
          {
            id: 186224,
            name: "Do we want this ?",
            urn: 383787,
            pos: 1,
          },
          {
            id: 209309,
            name: "Do we want this ?",
            urn: 403621,
            pos: 2,
          },
        ],
      },
      "27593": {
        entryId: 27593,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Newbridge A.C. 300m Male Under 9 {A}",
        paid: 1,
        egId: 22807,
        gender: "M",
        urn: 413228,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 1130,
          teamEntity: "Newbridge A.C.",
          entityName: "Club",
        },
        user: {
          id: 1243,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Seamus Cardiff",
        },
        athletes: [
          {
            id: 216236,
            name: "Do we want this ?",
            urn: 410891,
            pos: 1,
          },
          {
            id: 218886,
            name: "Do we want this ?",
            urn: 413228,
            pos: 2,
          },
        ],
      },
      "27634": {
        entryId: 27634,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Crusaders A.C. 300m Male Under 9 {A}",
        paid: 1,
        egId: 22807,
        gender: "M",
        urn: 414940,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 436,
          teamEntity: "Crusaders A.C.",
          entityName: "Club",
        },
        user: {
          id: 20815,
          email: "<EMAIL>",
          login: "Tessa",
          name: "Tessa",
        },
        athletes: [
          {
            id: 229581,
            name: "Do we want this ?",
            urn: 423960,
            pos: 1,
          },
          {
            id: 220285,
            name: "Do we want this ?",
            urn: 414940,
            pos: 2,
          },
        ],
      },
      "27655": {
        entryId: 27655,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. 300m Male Under 9 {A}",
        paid: 1,
        egId: 22807,
        gender: "M",
        urn: 390561,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 191891,
            name: "Do we want this ?",
            urn: 389158,
            pos: 1,
          },
          {
            id: 193356,
            name: "Do we want this ?",
            urn: 390561,
            pos: 2,
          },
        ],
      },
      "27738": {
        entryId: 27738,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 300m Male Under 9 {A}",
        paid: 0,
        egId: 22807,
        gender: "M",
        urn: 421371,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 235684,
            name: "Do we want this ?",
            urn: 430913,
            pos: 1,
          },
          {
            id: 226927,
            name: "Do we want this ?",
            urn: 421371,
            pos: 2,
          },
        ],
      },
    },
    "22808": {
      "27396": {
        entryId: 27396,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Enniscorthy A.C. 500m Female Under 10 {A}",
        paid: 1,
        egId: 22808,
        gender: "F",
        urn: 431678,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 599,
          teamEntity: "Enniscorthy A.C.",
          entityName: "Club",
        },
        user: {
          id: 100,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Edel Byrne",
        },
        athletes: [
          {
            id: 200249,
            name: "Do we want this ?",
            urn: 395685,
            pos: 1,
          },
          {
            id: 236449,
            name: "Do we want this ?",
            urn: 431678,
            pos: 2,
          },
        ],
      },
      "27553": {
        entryId: 27553,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Glaslough Harriers A.C. 500m Female Under 10 {A}",
        paid: 1,
        egId: 22808,
        gender: "F",
        urn: 411432,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 688,
          teamEntity: "Glaslough Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 22553,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Sabrina Mc Cooey",
        },
        athletes: [
          {
            id: 189090,
            name: "Do we want this ?",
            urn: 386486,
            pos: 1,
          },
          {
            id: 217063,
            name: "Do we want this ?",
            urn: 411432,
            pos: 2,
          },
        ],
      },
      "27619": {
        entryId: 27619,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Ratoath A.C. 500m Female Under 10 {A}",
        paid: 1,
        egId: 22808,
        gender: "F",
        urn: 382668,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 1304,
          teamEntity: "Ratoath A.C.",
          entityName: "Club",
        },
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
        athletes: [
          {
            id: 154065,
            name: "Do we want this ?",
            urn: 357103,
            pos: 1,
          },
          {
            id: 185070,
            name: "Do we want this ?",
            urn: 382668,
            pos: 2,
          },
        ],
      },
      "27643": {
        entryId: 27643,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "West Muskerry A.C. 500m Female Under 10 {A}",
        paid: 0,
        egId: 22808,
        gender: "F",
        urn: 414454,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 1790,
          teamEntity: "West Muskerry A.C.",
          entityName: "Club",
        },
        user: {
          id: 1491,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Collette O'Riordan",
        },
        athletes: [
          {
            id: 221672,
            name: "Do we want this ?",
            urn: 416088,
            pos: 1,
          },
          {
            id: 219834,
            name: "Do we want this ?",
            urn: 414454,
            pos: 2,
          },
        ],
      },
      "27660": {
        entryId: 27660,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. 500m Female Under 10 {A}",
        paid: 1,
        egId: 22808,
        gender: "F",
        urn: 399333,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 210193,
            name: "Do we want this ?",
            urn: 404522,
            pos: 1,
          },
          {
            id: 205823,
            name: "Do we want this ?",
            urn: 399333,
            pos: 2,
          },
        ],
      },
      "27717": {
        entryId: 27717,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Tramore A.C. 500m Female Under 10 {A}",
        paid: 1,
        egId: 22808,
        gender: "F",
        urn: 388646,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 1682,
          teamEntity: "Tramore A.C.",
          entityName: "Club",
        },
        user: {
          id: 26487,
          email: "<EMAIL>",
          login: "tramoreathletics",
          name: "tramoreathletics",
        },
        athletes: [
          {
            id: 191361,
            name: "Do we want this ?",
            urn: 388647,
            pos: 1,
          },
          {
            id: 191362,
            name: "Do we want this ?",
            urn: 388646,
            pos: 2,
          },
        ],
      },
      "27744": {
        entryId: 27744,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 500m Female Under 10 {A}",
        paid: 0,
        egId: 22808,
        gender: "F",
        urn: 421369,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 218777,
            name: "Do we want this ?",
            urn: 413734,
            pos: 1,
          },
          {
            id: 226923,
            name: "Do we want this ?",
            urn: 421369,
            pos: 2,
          },
        ],
      },
      "27745": {
        entryId: 27745,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 500m Female Under 10 {B}",
        paid: 0,
        egId: 22808,
        gender: "F",
        urn: 134530,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 218779,
            name: "Do we want this ?",
            urn: 413735,
            pos: 1,
          },
          {
            id: 235371,
            name: "Do we want this ?",
            urn: 134530,
            pos: 2,
          },
        ],
      },
      "27747": {
        entryId: 27747,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. 500m Female Under 10 {A}",
        paid: 1,
        egId: 22808,
        gender: "F",
        urn: 422833,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 198972,
            name: "Do we want this ?",
            urn: 394527,
            pos: 1,
          },
          {
            id: 228361,
            name: "Do we want this ?",
            urn: 422833,
            pos: 2,
          },
        ],
      },
    },
    "22809": {
      "27600": {
        entryId: 27600,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Suncroft A.C. 500m Male Under 10 {A}",
        paid: 1,
        egId: 22809,
        gender: "M",
        urn: 367277,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 1586,
          teamEntity: "Suncroft A.C.",
          entityName: "Club",
        },
        user: {
          id: 25043,
          email: "<EMAIL>",
          login: "Georgina Cousins",
          name: "Georgina Cousins",
        },
        athletes: [
          {
            id: 190339,
            name: "Do we want this ?",
            urn: 387676,
            pos: 1,
          },
          {
            id: 169122,
            name: "Do we want this ?",
            urn: 367277,
            pos: 2,
          },
        ],
      },
      "27647": {
        entryId: 27647,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Brothers Pearse A.C. 500m Male Under 10 {A}",
        paid: 1,
        egId: 22809,
        gender: "M",
        urn: 388344,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 246,
          teamEntity: "Brothers Pearse A.C.",
          entityName: "Club",
        },
        user: {
          id: 21957,
          email: "<EMAIL>",
          login: "Claresmith",
          name: "Claresmith",
        },
        athletes: [
          {
            id: 212327,
            name: "Do we want this ?",
            urn: 406964,
            pos: 1,
          },
          {
            id: 191044,
            name: "Do we want this ?",
            urn: 388344,
            pos: 2,
          },
        ],
      },
      "27661": {
        entryId: 27661,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. 500m Male Under 10 {A}",
        paid: 1,
        egId: 22809,
        gender: "M",
        urn: 389398,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 205820,
            name: "Do we want this ?",
            urn: 399330,
            pos: 1,
          },
          {
            id: 192140,
            name: "Do we want this ?",
            urn: 389398,
            pos: 2,
          },
        ],
      },
      "27673": {
        entryId: 27673,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. 500m Male Under 10 {A}",
        paid: 1,
        egId: 22809,
        gender: "M",
        urn: 380518,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 198427,
            name: "Do we want this ?",
            urn: 394029,
            pos: 1,
          },
          {
            id: 182743,
            name: "Do we want this ?",
            urn: 380518,
            pos: 2,
          },
        ],
      },
      "27707": {
        entryId: 27707,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. 500m Male Under 10 {A}",
        paid: 1,
        egId: 22809,
        gender: "M",
        urn: 404745,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 189695,
            name: "Do we want this ?",
            urn: 387066,
            pos: 1,
          },
          {
            id: 210249,
            name: "Do we want this ?",
            urn: 404745,
            pos: 2,
          },
        ],
      },
      "27746": {
        entryId: 27746,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 500m Male Under 10 {A}",
        paid: 0,
        egId: 22809,
        gender: "M",
        urn: 413312,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 218774,
            name: "Do we want this ?",
            urn: 413732,
            pos: 1,
          },
          {
            id: 218412,
            name: "Do we want this ?",
            urn: 413312,
            pos: 2,
          },
        ],
      },
    },
    "22810": {
      "27347": {
        entryId: 27347,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Parnell A.C. 600m Female Under 11 {A}",
        paid: 1,
        egId: 22810,
        gender: "F",
        urn: 352596,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 1232,
          teamEntity: "Parnell A.C.",
          entityName: "Club",
        },
        user: {
          id: 17875,
          email: "<EMAIL>",
          login: "sheilaobyrne",
          name: "sheilaobyrne",
        },
        athletes: [
          {
            id: 221643,
            name: "Do we want this ?",
            urn: 416040,
            pos: 1,
          },
          {
            id: 148058,
            name: "Do we want this ?",
            urn: 352596,
            pos: 2,
          },
        ],
      },
      "27623": {
        entryId: 27623,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Ratoath A.C. 600m Female Under 11 {A}",
        paid: 1,
        egId: 22810,
        gender: "F",
        urn: 365132,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 1304,
          teamEntity: "Ratoath A.C.",
          entityName: "Club",
        },
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
        athletes: [
          {
            id: 177470,
            name: "Do we want this ?",
            urn: 376453,
            pos: 1,
          },
          {
            id: 166373,
            name: "Do we want this ?",
            urn: 365132,
            pos: 2,
          },
        ],
      },
      "27646": {
        entryId: 27646,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Kilcoole A.C. 600m Female Under 11 {A}",
        paid: 1,
        egId: 22810,
        gender: "F",
        urn: 404286,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 881,
          teamEntity: "Kilcoole A.C.",
          entityName: "Club",
        },
        user: {
          id: 19262,
          email: "<EMAIL>",
          login: "ClaireUL",
          name: "ClaireUL",
        },
        athletes: [
          {
            id: 171163,
            name: "Do we want this ?",
            urn: 370879,
            pos: 1,
          },
          {
            id: 209941,
            name: "Do we want this ?",
            urn: 404286,
            pos: 2,
          },
        ],
      },
      "27681": {
        entryId: 27681,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. 600m Female Under 11 {A}",
        paid: 1,
        egId: 22810,
        gender: "F",
        urn: 392020,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 186826,
            name: "Do we want this ?",
            urn: 384344,
            pos: 1,
          },
          {
            id: 195719,
            name: "Do we want this ?",
            urn: 392020,
            pos: 2,
          },
        ],
      },
      "27682": {
        entryId: 27682,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. 600m Female Under 11 {A}",
        paid: 1,
        egId: 22810,
        gender: "F",
        urn: 420075,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 177062,
            name: "Do we want this ?",
            urn: 376063,
            pos: 1,
          },
          {
            id: 225653,
            name: "Do we want this ?",
            urn: 420075,
            pos: 2,
          },
        ],
      },
      "27684": {
        entryId: 27684,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. 600m Female Under 11 {B}",
        paid: 1,
        egId: 22810,
        gender: "F",
        urn: 377825,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 173588,
            name: "Do we want this ?",
            urn: 372913,
            pos: 1,
          },
          {
            id: 179049,
            name: "Do we want this ?",
            urn: 377825,
            pos: 2,
          },
        ],
      },
      "27727": {
        entryId: 27727,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Limerick A.C. 600m Female Under 11 {A}",
        paid: 0,
        egId: 22810,
        gender: "F",
        urn: 374562,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 955,
          teamEntity: "Limerick A.C.",
          entityName: "Club",
        },
        user: {
          id: 1125,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Ray McInerney",
        },
        athletes: [
          {
            id: 159541,
            name: "Do we want this ?",
            urn: 361435,
            pos: 1,
          },
          {
            id: 175535,
            name: "Do we want this ?",
            urn: 374562,
            pos: 2,
          },
        ],
      },
      "27751": {
        entryId: 27751,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 600m Female Under 11 {A}",
        paid: 0,
        egId: 22810,
        gender: "F",
        urn: 392391,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 196201,
            name: "Do we want this ?",
            urn: 392394,
            pos: 1,
          },
          {
            id: 196197,
            name: "Do we want this ?",
            urn: 392391,
            pos: 2,
          },
        ],
      },
      "27753": {
        entryId: 27753,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 600m Female Under 11 {B}",
        paid: 0,
        egId: 22810,
        gender: "F",
        urn: 415628,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 218781,
            name: "Do we want this ?",
            urn: 413738,
            pos: 1,
          },
          {
            id: 221142,
            name: "Do we want this ?",
            urn: 415628,
            pos: 2,
          },
        ],
      },
    },
    "22811": {
      "27268": {
        entryId: 27268,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Raheny Shamrock A.C. 600m Male Under 11 {A}",
        paid: 0,
        egId: 22811,
        gender: "M",
        urn: 365023,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 1298,
          teamEntity: "Raheny Shamrock A.C.",
          entityName: "Club",
        },
        user: {
          id: 23469,
          email: "<EMAIL>",
          login: "loukeogh",
          name: "loukeogh",
        },
        athletes: [
          {
            id: 118236,
            name: "Do we want this ?",
            urn: 339392,
            pos: 1,
          },
          {
            id: 168988,
            name: "Do we want this ?",
            urn: 365023,
            pos: 2,
          },
        ],
      },
      "27544": {
        entryId: 27544,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Kilkenny City Harriers A.C. 600m Male Under 11 {A}",
        paid: 0,
        egId: 22811,
        gender: "M",
        urn: 391946,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 884,
          teamEntity: "Kilkenny City Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1074,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "May Hutt",
        },
        athletes: [
          {
            id: 191930,
            name: "Do we want this ?",
            urn: 389197,
            pos: 1,
          },
          {
            id: 195632,
            name: "Do we want this ?",
            urn: 391946,
            pos: 2,
          },
        ],
      },
      "27586": {
        entryId: 27586,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Glaslough Harriers A.C. 600m Male Under 11 {A}",
        paid: 1,
        egId: 22811,
        gender: "M",
        urn: 355807,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 688,
          teamEntity: "Glaslough Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 22553,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Sabrina Mc Cooey",
        },
        athletes: [
          {
            id: 152284,
            name: "Do we want this ?",
            urn: 355805,
            pos: 1,
          },
          {
            id: 152296,
            name: "Do we want this ?",
            urn: 355807,
            pos: 2,
          },
        ],
      },
      "27662": {
        entryId: 27662,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. 600m Male Under 11 {A}",
        paid: 1,
        egId: 22811,
        gender: "M",
        urn: 369602,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 146671,
            name: "Do we want this ?",
            urn: 351885,
            pos: 1,
          },
          {
            id: 168846,
            name: "Do we want this ?",
            urn: 369602,
            pos: 2,
          },
        ],
      },
      "27663": {
        entryId: 27663,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. 600m Male Under 11 {B}",
        paid: 1,
        egId: 22811,
        gender: "M",
        urn: 379630,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 192139,
            name: "Do we want this ?",
            urn: 389397,
            pos: 1,
          },
          {
            id: 181681,
            name: "Do we want this ?",
            urn: 379630,
            pos: 2,
          },
        ],
      },
      "27728": {
        entryId: 27728,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Limerick A.C. 600m Male Under 11 {A}",
        paid: 0,
        egId: 22811,
        gender: "M",
        urn: 394233,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 955,
          teamEntity: "Limerick A.C.",
          entityName: "Club",
        },
        user: {
          id: 1125,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Ray McInerney",
        },
        athletes: [
          {
            id: 198649,
            name: "Do we want this ?",
            urn: 394232,
            pos: 1,
          },
          {
            id: 198650,
            name: "Do we want this ?",
            urn: 394233,
            pos: 2,
          },
        ],
      },
      "27752": {
        entryId: 27752,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 600m Male Under 11 {A}",
        paid: 0,
        egId: 22811,
        gender: "M",
        urn: 421075,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 196236,
            name: "Do we want this ?",
            urn: 392426,
            pos: 1,
          },
          {
            id: 226571,
            name: "Do we want this ?",
            urn: 421075,
            pos: 2,
          },
        ],
      },
    },
    "22812": {
      "279035": {
        athleteId: 241660,
        firstname: "Saidhbhin",
        surname: "Mc Mullan",
        gender: "F",
        urn: 126392,
        clubId: 1065,
        clubname: "Mid Ulster A.C.",
        entryId: 279035,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00mins",
        },
        paid: 1,
        egId: 22812,
        typeNo: "T16",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 1065,
          name: "Mid Ulster A.C.",
        },
        editAccess: true,
        user: {
          id: 19383,
          email: "<EMAIL>",
          login: "Barrie Holmes",
          name: "Barrie Holmes",
        },
      },
      "279785": {
        athleteId: 143990,
        firstname: "Kate",
        surname: "Kelly",
        gender: "F",
        urn: 349864,
        clubId: 884,
        clubname: "Kilkenny City Harriers A.C.",
        entryId: 279785,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00mins",
        },
        paid: 0,
        egId: 22812,
        typeNo: "T16",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 884,
          name: "Kilkenny City Harriers A.C.",
        },
        editAccess: true,
        user: {
          id: 1074,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "May Hutt",
        },
      },
      "279916": {
        athleteId: 172052,
        firstname: "Marianna",
        surname: "Osojca",
        gender: "F",
        urn: 371629,
        clubId: 50,
        clubname: "Annalee A.C.",
        entryId: 279916,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00mins",
        },
        paid: 1,
        egId: 22812,
        typeNo: "T16",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 50,
          name: "Annalee A.C.",
        },
        editAccess: true,
        user: {
          id: 15241,
          email: "<EMAIL>",
          login: "claireminey",
          name: "claireminey",
        },
      },
      "280081": {
        athleteId: 153578,
        firstname: "Stephanie ",
        surname: "Clarke ",
        gender: "F",
        urn: 356731,
        clubId: 1615,
        clubname: "Tara A.C.",
        entryId: 280081,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00mins",
        },
        paid: 1,
        egId: 22812,
        typeNo: "T16",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 1615,
          name: "Tara A.C.",
        },
        editAccess: true,
        user: {
          id: 27266,
          email: "<EMAIL>",
          login: "deirdreclarke81",
          name: "deirdreclarke81",
        },
      },
      "280213": {
        athleteId: 184023,
        firstname: "Anna",
        surname: "Haggan",
        gender: "F",
        urn: 381689,
        clubId: 3006,
        clubname: "Tír Chonaill A.C.",
        entryId: 280213,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00mins",
        },
        paid: 1,
        egId: 22812,
        typeNo: "T16",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 3006,
          name: "Tír Chonaill A.C.",
        },
        editAccess: true,
        user: {
          id: 15222,
          email: "<EMAIL>",
          login: "Marita",
          name: "Marita",
        },
      },
      "280438": {
        athleteId: 176507,
        firstname: "Charlotte",
        surname: "Grimes",
        gender: "F",
        urn: 375540,
        clubId: 3103,
        clubname: "Metro/St. Brigids A.C.",
        entryId: 280438,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00mins",
        },
        paid: 1,
        egId: 22812,
        typeNo: "T16",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 3103,
          name: "Metro/St. Brigids A.C.",
        },
        editAccess: true,
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
      },
      "280439": {
        athleteId: 203055,
        firstname: "Lauren",
        surname: "O Reilly",
        gender: "F",
        urn: 396862,
        clubId: 3103,
        clubname: "Metro/St. Brigids A.C.",
        entryId: 280439,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00mins",
        },
        paid: 1,
        egId: 22812,
        typeNo: "T16",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 3103,
          name: "Metro/St. Brigids A.C.",
        },
        editAccess: true,
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
      },
      "280447": {
        athleteId: 144953,
        firstname: "Emyrose",
        surname: "Tomkin Beddy",
        gender: "F",
        urn: 350576,
        clubId: 181,
        clubname: "Blackrock (Dublin) A.C.",
        entryId: 280447,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00mins",
        },
        paid: 1,
        egId: 22812,
        typeNo: "T16",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 181,
          name: "Blackrock (Dublin) A.C.",
        },
        editAccess: true,
        user: {
          id: 20938,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Audrey Burns &amp; Paul Hunt",
        },
      },
      "280600": {
        athleteId: 169434,
        firstname: "Katelynn ",
        surname: "Woods",
        gender: "F",
        urn: 363121,
        clubId: 537,
        clubname: "Dundalk St. Gerards A.C.",
        entryId: 280600,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "1:187",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 107.81,
          perfText: "1:47.81mins",
        },
        paid: 0,
        egId: 22812,
        typeNo: "T16",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 537,
          name: "Dundalk St. Gerards A.C.",
        },
        editAccess: true,
        user: {
          id: 24883,
          email: "<EMAIL>",
          login: "Katelynn",
          name: "Katelynn",
        },
      },
      "280601": {
        athleteId: 70139,
        firstname: "Fianaith",
        surname: "King",
        gender: "F",
        urn: 332290,
        clubId: 544,
        clubname: "Dunleer A.C.",
        entryId: 280601,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "1:187",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 110.98,
          perfText: "1:50.98mins",
        },
        paid: 0,
        egId: 22812,
        typeNo: "T16",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 544,
          name: "Dunleer A.C.",
        },
        editAccess: true,
        user: {
          id: 18069,
          email: "<EMAIL>",
          login: "Sally Clarke",
          name: "Sally Clarke",
        },
      },
    },
    "22813": {
      "280431": {
        athleteId: 179050,
        firstname: "James ",
        surname: "Howlin",
        gender: "M",
        urn: 377824,
        clubId: 3103,
        clubname: "Metro/St. Brigids A.C.",
        entryId: 280431,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00mins",
        },
        paid: 1,
        egId: 22813,
        typeNo: "T15",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 3103,
          name: "Metro/St. Brigids A.C.",
        },
        editAccess: true,
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
      },
      "280473": {
        athleteId: 134915,
        firstname: "Philip",
        surname: "Griffith",
        gender: "M",
        urn: 345321,
        clubId: 372,
        clubname: "Claremorris A.C.",
        entryId: 280473,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00mins",
        },
        paid: 0,
        egId: 22813,
        typeNo: "T15",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 372,
          name: "Claremorris A.C.",
        },
        editAccess: true,
        user: {
          id: 20174,
          email: "<EMAIL>",
          login: "gjoyce",
          name: "gjoyce",
        },
      },
      "280552": {
        athleteId: 180904,
        firstname: "Isaac",
        surname: "O Neill",
        gender: "M",
        urn: 374898,
        clubId: 379,
        clubname: "Clonliffe Harriers A.C.",
        entryId: 280552,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00mins",
        },
        paid: 1,
        egId: 22813,
        typeNo: "T15",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 379,
          name: "Clonliffe Harriers A.C.",
        },
        editAccess: true,
        user: {
          id: 2486,
          email: "<EMAIL>",
          login: "Sbateson",
          name: "Ebateson",
        },
      },
      "280572": {
        athleteId: 196219,
        firstname: "Evan",
        surname: "Patton",
        gender: "M",
        urn: 392412,
        clubId: 646,
        clubname: "Finn Valley A.C.",
        entryId: 280572,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00mins",
        },
        paid: 0,
        egId: 22813,
        typeNo: "T15",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 646,
          name: "Finn Valley A.C.",
        },
        editAccess: true,
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
      },
      "280586": {
        athleteId: 213421,
        firstname: "Daniel",
        surname: "Cummins",
        gender: "M",
        urn: 408087,
        clubId: 1108,
        clubname: "Mullingar Harriers A.C.",
        entryId: 280586,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "2:187",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 105.73,
          perfText: "1:45.73mins",
        },
        paid: 0,
        egId: 22813,
        typeNo: "T15",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 1108,
          name: "Mullingar Harriers A.C.",
        },
        editAccess: true,
        user: {
          id: 26714,
          email: "<EMAIL>",
          login: "Mary Cummins",
          name: "Mary Cummins",
        },
      },
      "280587": {
        athleteId: 179438,
        firstname: "Cian",
        surname: "D'Rosario",
        gender: "M",
        urn: 378195,
        clubId: 364,
        clubname: "Clane A.C.",
        entryId: 280587,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "2:187",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 105.81,
          perfText: "1:45.81mins",
        },
        paid: 0,
        egId: 22813,
        typeNo: "T15",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 364,
          name: "Clane A.C.",
        },
        editAccess: true,
        user: {
          id: 1909,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "John Finn",
        },
      },
      "280588": {
        athleteId: 170785,
        firstname: "Tom",
        surname: "Doherty",
        gender: "M",
        urn: 370587,
        clubId: 3055,
        clubname: "St. Joseph's A.C.",
        entryId: 280588,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "2:187",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 103.8,
          perfText: "1:43.80mins",
        },
        paid: 0,
        egId: 22813,
        typeNo: "T15",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 3055,
          name: "St. Joseph's A.C.",
        },
        editAccess: true,
        user: {
          id: 25413,
          email: "<EMAIL>",
          login: "John Mc Donald",
          name: "John Mc Donald",
        },
      },
      "280589": {
        athleteId: 48606,
        firstname: "James",
        surname: "O'Toole",
        gender: "M",
        urn: 297479,
        clubId: 10342,
        clubname: "St. Pats A.C.",
        entryId: 280589,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "2:187",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 104.9,
          perfText: "1:44.90mins",
        },
        paid: 0,
        egId: 22813,
        typeNo: "T15",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 10342,
          name: "St. Pats A.C.",
        },
        editAccess: true,
        user: {
          id: 19341,
          email: "<EMAIL>",
          login: "Mbehan",
          name: "Mbehan",
        },
      },
    },
    "22816": {
      "27397": {
        entryId: 27397,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Enniscorthy A.C. 60m Girls Female Under 9 {A}",
        paid: 1,
        egId: 22816,
        gender: "F",
        urn: 439048,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 599,
          teamEntity: "Enniscorthy A.C.",
          entityName: "Club",
        },
        user: {
          id: 100,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Edel Byrne",
        },
        athletes: [
          {
            id: 195310,
            name: "Do we want this ?",
            urn: 391678,
            pos: 1,
          },
          {
            id: 244004,
            name: "Do we want this ?",
            urn: 439048,
            pos: 2,
          },
        ],
      },
      "27517": {
        entryId: 27517,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Athlone A.C. 60m Girls Female Under 9 {A}",
        paid: 1,
        egId: 22816,
        gender: "F",
        urn: 421558,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 3026,
          teamEntity: "Athlone A.C.",
          entityName: "Club",
        },
        user: {
          id: 19357,
          email: "<EMAIL>",
          login: "SHeald",
          name: "SHeald",
        },
        athletes: [
          {
            id: 207362,
            name: "Do we want this ?",
            urn: 401252,
            pos: 1,
          },
          {
            id: 227125,
            name: "Do we want this ?",
            urn: 421558,
            pos: 2,
          },
        ],
      },
      "27536": {
        entryId: 27536,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Rathfarnham W.S.A.F. A.C. 60m Girls Female Under 9 {A}",
        paid: 1,
        egId: 22816,
        gender: "F",
        urn: 419250,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 1302,
          teamEntity: "Rathfarnham W.S.A.F. A.C.",
          entityName: "Club",
        },
        user: {
          id: 1290,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Adam Jones",
        },
        athletes: [
          {
            id: 232488,
            name: "Do we want this ?",
            urn: 427358,
            pos: 1,
          },
          {
            id: 224889,
            name: "Do we want this ?",
            urn: 419250,
            pos: 2,
          },
        ],
      },
      "27616": {
        entryId: 27616,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Ratoath A.C. 60m Girls Female Under 9 {A}",
        paid: 1,
        egId: 22816,
        gender: "F",
        urn: 363503,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 1304,
          teamEntity: "Ratoath A.C.",
          entityName: "Club",
        },
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
        athletes: [
          {
            id: 162346,
            name: "Do we want this ?",
            urn: 368522,
            pos: 1,
          },
          {
            id: 165385,
            name: "Do we want this ?",
            urn: 363503,
            pos: 2,
          },
        ],
      },
      "27651": {
        entryId: 27651,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. 60m Girls Female Under 9 {A}",
        paid: 1,
        egId: 22816,
        gender: "F",
        urn: 399334,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 214815,
            name: "Do we want this ?",
            urn: 409461,
            pos: 1,
          },
          {
            id: 205824,
            name: "Do we want this ?",
            urn: 399334,
            pos: 2,
          },
        ],
      },
      "27668": {
        entryId: 27668,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Innyvale A.C. 60m Girls Female Under 9 {A}",
        paid: 1,
        egId: 22816,
        gender: "F",
        urn: 391431,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 838,
          teamEntity: "Innyvale A.C.",
          entityName: "Club",
        },
        user: {
          id: 14779,
          email: "<EMAIL>",
          login: "dmulvey001974",
          name: "dmulvey001974",
        },
        athletes: [
          {
            id: 214427,
            name: "Do we want this ?",
            urn: 409100,
            pos: 1,
          },
          {
            id: 194969,
            name: "Do we want this ?",
            urn: 391431,
            pos: 2,
          },
        ],
      },
      "27700": {
        entryId: 27700,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Castlebar A.C. 60m Girls Female Under 9 {A}",
        paid: 1,
        egId: 22816,
        gender: "F",
        urn: 424641,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 312,
          teamEntity: "Castlebar A.C.",
          entityName: "Club",
        },
        user: {
          id: 764,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Denise Roddy",
        },
        athletes: [
          {
            id: 230022,
            name: "Do we want this ?",
            urn: 424482,
            pos: 1,
          },
          {
            id: 230290,
            name: "Do we want this ?",
            urn: 424641,
            pos: 2,
          },
        ],
      },
      "27705": {
        entryId: 27705,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. 60m Girls Female Under 9 {A}",
        paid: 1,
        egId: 22816,
        gender: "F",
        urn: 419580,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 225036,
            name: "Do we want this ?",
            urn: 419419,
            pos: 1,
          },
          {
            id: 225252,
            name: "Do we want this ?",
            urn: 419580,
            pos: 2,
          },
        ],
      },
      "27739": {
        entryId: 27739,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 60m Girls Female Under 9 {A}",
        paid: 0,
        egId: 22816,
        gender: "F",
        urn: 420556,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 226096,
            name: "Do we want this ?",
            urn: 420555,
            pos: 1,
          },
          {
            id: 226100,
            name: "Do we want this ?",
            urn: 420556,
            pos: 2,
          },
        ],
      },
    },
    "22817": {
      "27542": {
        entryId: 27542,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Kilkenny City Harriers A.C. 60m Male Under 9 {A}",
        paid: 0,
        egId: 22817,
        gender: "M",
        urn: 410247,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 884,
          teamEntity: "Kilkenny City Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1074,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "May Hutt",
        },
        athletes: [
          {
            id: 215605,
            name: "Do we want this ?",
            urn: 410234,
            pos: 1,
          },
          {
            id: 215758,
            name: "Do we want this ?",
            urn: 410247,
            pos: 2,
          },
        ],
      },
      "27549": {
        entryId: 27549,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "East Galway A.C. 60m Male Under 9 {A}",
        paid: 1,
        egId: 22817,
        gender: "M",
        urn: 395419,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 563,
          teamEntity: "East Galway A.C.",
          entityName: "Club",
        },
        user: {
          id: 22749,
          email: "<EMAIL>",
          login: "mcdonf1",
          name: "mcdonf1",
        },
        athletes: [
          {
            id: 162028,
            name: "Do we want this ?",
            urn: 364891,
            pos: 1,
          },
          {
            id: 199933,
            name: "Do we want this ?",
            urn: 395419,
            pos: 2,
          },
        ],
      },
      "27551": {
        entryId: 27551,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Glaslough Harriers A.C. 60m Male Under 9 {A}",
        paid: 1,
        egId: 22817,
        gender: "M",
        urn: 425854,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 688,
          teamEntity: "Glaslough Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 22553,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Sabrina Mc Cooey",
        },
        athletes: [
          {
            id: 212805,
            name: "Do we want this ?",
            urn: 405984,
            pos: 1,
          },
          {
            id: 231175,
            name: "Do we want this ?",
            urn: 425854,
            pos: 2,
          },
        ],
      },
      "27584": {
        entryId: 27584,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Fergus A.C. 60m Male Under 9 {A}",
        paid: 1,
        egId: 22817,
        gender: "M",
        urn: 403621,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 636,
          teamEntity: "Fergus A.C.",
          entityName: "Club",
        },
        user: {
          id: 21929,
          email: "<EMAIL>",
          login: "Noelette Lynch",
          name: "Noelette Lynch",
        },
        athletes: [
          {
            id: 186224,
            name: "Do we want this ?",
            urn: 383787,
            pos: 1,
          },
          {
            id: 209309,
            name: "Do we want this ?",
            urn: 403621,
            pos: 2,
          },
        ],
      },
      "27594": {
        entryId: 27594,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Newbridge A.C. 60m Male Under 9 {A}",
        paid: 1,
        egId: 22817,
        gender: "M",
        urn: 381820,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 1130,
          teamEntity: "Newbridge A.C.",
          entityName: "Club",
        },
        user: {
          id: 1243,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Seamus Cardiff",
        },
        athletes: [
          {
            id: 216236,
            name: "Do we want this ?",
            urn: 410891,
            pos: 1,
          },
          {
            id: 184167,
            name: "Do we want this ?",
            urn: 381820,
            pos: 2,
          },
        ],
      },
      "27635": {
        entryId: 27635,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Crusaders A.C. 60m Male Under 9 {A}",
        paid: 1,
        egId: 22817,
        gender: "M",
        urn: 419316,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 436,
          teamEntity: "Crusaders A.C.",
          entityName: "Club",
        },
        user: {
          id: 20815,
          email: "<EMAIL>",
          login: "Tessa",
          name: "Tessa",
        },
        athletes: [
          {
            id: 220285,
            name: "Do we want this ?",
            urn: 414940,
            pos: 1,
          },
          {
            id: 224917,
            name: "Do we want this ?",
            urn: 419316,
            pos: 2,
          },
        ],
      },
      "27654": {
        entryId: 27654,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. 60m Male Under 9 {A}",
        paid: 1,
        egId: 22817,
        gender: "M",
        urn: 390561,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 230705,
            name: "Do we want this ?",
            urn: 425235,
            pos: 1,
          },
          {
            id: 193356,
            name: "Do we want this ?",
            urn: 390561,
            pos: 2,
          },
        ],
      },
      "27710": {
        entryId: 27710,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. 60m Male Under 9 {A}",
        paid: 1,
        egId: 22817,
        gender: "M",
        urn: 431142,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 232076,
            name: "Do we want this ?",
            urn: 425651,
            pos: 1,
          },
          {
            id: 236102,
            name: "Do we want this ?",
            urn: 431142,
            pos: 2,
          },
        ],
      },
      "27740": {
        entryId: 27740,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 60m Male Under 9 {A}",
        paid: 0,
        egId: 22817,
        gender: "M",
        urn: 421039,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 235684,
            name: "Do we want this ?",
            urn: 430913,
            pos: 1,
          },
          {
            id: 226559,
            name: "Do we want this ?",
            urn: 421039,
            pos: 2,
          },
        ],
      },
    },
    "22818": {
      "27585": {
        entryId: 27585,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Fergus A.C. 60m Female Under 10 {A}",
        paid: 1,
        egId: 22818,
        gender: "F",
        urn: 403609,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 636,
          teamEntity: "Fergus A.C.",
          entityName: "Club",
        },
        user: {
          id: 21929,
          email: "<EMAIL>",
          login: "Noelette Lynch",
          name: "Noelette Lynch",
        },
        athletes: [
          {
            id: 196360,
            name: "Do we want this ?",
            urn: 392528,
            pos: 1,
          },
          {
            id: 209303,
            name: "Do we want this ?",
            urn: 403609,
            pos: 2,
          },
        ],
      },
      "27592": {
        entryId: 27592,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "60m Female Under 10 {A}",
        paid: 1,
        egId: 22818,
        gender: "F",
        urn: 396963,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 0,
          teamEntity: "Unknown",
          entityName: "Unknown",
        },
        user: {
          id: 14939,
          email: "<EMAIL>",
          login: "bernieocallaghan",
          name: "bernieocallaghan",
        },
        athletes: [
          {
            id: 193094,
            name: "Do we want this ?",
            urn: 390321,
            pos: 1,
          },
          {
            id: 203156,
            name: "Do we want this ?",
            urn: 396963,
            pos: 2,
          },
        ],
      },
      "27618": {
        entryId: 27618,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Ratoath A.C. 60m Female Under 10 {A}",
        paid: 1,
        egId: 22818,
        gender: "F",
        urn: 380753,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 1304,
          teamEntity: "Ratoath A.C.",
          entityName: "Club",
        },
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
        athletes: [
          {
            id: 154065,
            name: "Do we want this ?",
            urn: 357103,
            pos: 1,
          },
          {
            id: 183002,
            name: "Do we want this ?",
            urn: 380753,
            pos: 2,
          },
        ],
      },
      "27632": {
        entryId: 27632,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Trim A.C. 60m Female Under 10 {A}",
        paid: 1,
        egId: 22818,
        gender: "F",
        urn: 425183,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 1694,
          teamEntity: "Trim A.C.",
          entityName: "Club",
        },
        user: {
          id: 1457,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "John Rowe",
        },
        athletes: [
          {
            id: 232614,
            name: "Do we want this ?",
            urn: 427356,
            pos: 1,
          },
          {
            id: 230683,
            name: "Do we want this ?",
            urn: 425183,
            pos: 2,
          },
        ],
      },
      "27659": {
        entryId: 27659,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. 60m Female Under 10 {A}",
        paid: 1,
        egId: 22818,
        gender: "F",
        urn: 399333,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 210193,
            name: "Do we want this ?",
            urn: 404522,
            pos: 1,
          },
          {
            id: 205823,
            name: "Do we want this ?",
            urn: 399333,
            pos: 2,
          },
        ],
      },
      "27675": {
        entryId: 27675,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. 60m Female Under 10 {A}",
        paid: 1,
        egId: 22818,
        gender: "F",
        urn: 380414,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 183221,
            name: "Do we want this ?",
            urn: 380953,
            pos: 1,
          },
          {
            id: 182617,
            name: "Do we want this ?",
            urn: 380414,
            pos: 2,
          },
        ],
      },
      "27676": {
        entryId: 27676,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. 60m Female Under 10 B {A}",
        paid: 1,
        egId: 22818,
        gender: "F",
        urn: 394025,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 235894,
            name: "Do we want this ?",
            urn: 431139,
            pos: 1,
          },
          {
            id: 198423,
            name: "Do we want this ?",
            urn: 394025,
            pos: 2,
          },
        ],
      },
      "27714": {
        entryId: 27714,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Dunboyne A.C. 60m Female Under 10 {A}",
        paid: 1,
        egId: 22818,
        gender: "F",
        urn: 409317,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 536,
          teamEntity: "Dunboyne A.C.",
          entityName: "Club",
        },
        user: {
          id: 29116,
          email: "<EMAIL>",
          login: "manningalison7",
          name: "manningalison7",
        },
        athletes: [
          {
            id: 214730,
            name: "Do we want this ?",
            urn: 409330,
            pos: 1,
          },
          {
            id: 214712,
            name: "Do we want this ?",
            urn: 409317,
            pos: 2,
          },
        ],
      },
    },
    "22819": {
      "27346": {
        entryId: 27346,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Brothers Pearse A.C. 60m Male Under 10 {A}",
        paid: 1,
        egId: 22819,
        gender: "M",
        urn: 438918,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 246,
          teamEntity: "Brothers Pearse A.C.",
          entityName: "Club",
        },
        user: {
          id: 21957,
          email: "<EMAIL>",
          login: "Claresmith",
          name: "Claresmith",
        },
        athletes: [
          {
            id: 212327,
            name: "Do we want this ?",
            urn: 406964,
            pos: 1,
          },
          {
            id: 243839,
            name: "Do we want this ?",
            urn: 438918,
            pos: 2,
          },
        ],
      },
      "27590": {
        entryId: 27590,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "60m Male Under 10 {A}",
        paid: 1,
        egId: 22819,
        gender: "M",
        urn: 391778,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 0,
          teamEntity: "Unknown",
          entityName: "Unknown",
        },
        user: {
          id: 14939,
          email: "<EMAIL>",
          login: "bernieocallaghan",
          name: "bernieocallaghan",
        },
        athletes: [
          {
            id: 184492,
            name: "Do we want this ?",
            urn: 382120,
            pos: 1,
          },
          {
            id: 195421,
            name: "Do we want this ?",
            urn: 391778,
            pos: 2,
          },
        ],
      },
      "27601": {
        entryId: 27601,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Suncroft A.C. 60m Male Under 10 {A}",
        paid: 1,
        egId: 22819,
        gender: "M",
        urn: 367277,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 1586,
          teamEntity: "Suncroft A.C.",
          entityName: "Club",
        },
        user: {
          id: 25043,
          email: "<EMAIL>",
          login: "Georgina Cousins",
          name: "Georgina Cousins",
        },
        athletes: [
          {
            id: 190339,
            name: "Do we want this ?",
            urn: 387676,
            pos: 1,
          },
          {
            id: 169122,
            name: "Do we want this ?",
            urn: 367277,
            pos: 2,
          },
        ],
      },
      "27674": {
        entryId: 27674,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. 60m Male Under 10 {A}",
        paid: 1,
        egId: 22819,
        gender: "M",
        urn: 410582,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 166639,
            name: "Do we want this ?",
            urn: 367513,
            pos: 1,
          },
          {
            id: 215877,
            name: "Do we want this ?",
            urn: 410582,
            pos: 2,
          },
        ],
      },
      "27719": {
        entryId: 27719,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Erris A.C. 60m Male Under 10 {A}",
        paid: 1,
        egId: 22819,
        gender: "M",
        urn: 386970,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 3036,
          teamEntity: "Erris A.C.",
          entityName: "Club",
        },
        user: {
          id: 22719,
          email: "<EMAIL>",
          login: "Erris AC",
          name: "Erris AC",
        },
        athletes: [
          {
            id: 189585,
            name: "Do we want this ?",
            urn: 386956,
            pos: 1,
          },
          {
            id: 189600,
            name: "Do we want this ?",
            urn: 386970,
            pos: 2,
          },
        ],
      },
      "27720": {
        entryId: 27720,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Erris A.C. 60m Male Under 10 {B}",
        paid: 1,
        egId: 22819,
        gender: "M",
        urn: 386964,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 3036,
          teamEntity: "Erris A.C.",
          entityName: "Club",
        },
        user: {
          id: 22719,
          email: "<EMAIL>",
          login: "Erris AC",
          name: "Erris AC",
        },
        athletes: [
          {
            id: 189598,
            name: "Do we want this ?",
            urn: 386969,
            pos: 1,
          },
          {
            id: 189593,
            name: "Do we want this ?",
            urn: 386964,
            pos: 2,
          },
        ],
      },
    },
    "22820": {
      "27622": {
        entryId: 27622,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Ratoath A.C. 60m Female Under 11 {A}",
        paid: 1,
        egId: 22820,
        gender: "F",
        urn: 376453,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 1304,
          teamEntity: "Ratoath A.C.",
          entityName: "Club",
        },
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
        athletes: [
          {
            id: 145292,
            name: "Do we want this ?",
            urn: 350799,
            pos: 1,
          },
          {
            id: 177470,
            name: "Do we want this ?",
            urn: 376453,
            pos: 2,
          },
        ],
      },
      "27636": {
        entryId: 27636,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Crusaders A.C. 60m Female Under 11 {A}",
        paid: 1,
        egId: 22820,
        gender: "F",
        urn: 395292,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 436,
          teamEntity: "Crusaders A.C.",
          entityName: "Club",
        },
        user: {
          id: 20815,
          email: "<EMAIL>",
          login: "Tessa",
          name: "Tessa",
        },
        athletes: [
          {
            id: 225944,
            name: "Do we want this ?",
            urn: 420385,
            pos: 1,
          },
          {
            id: 199804,
            name: "Do we want this ?",
            urn: 395292,
            pos: 2,
          },
        ],
      },
      "27683": {
        entryId: 27683,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. 60m Female Under 11 {A}",
        paid: 1,
        egId: 22820,
        gender: "F",
        urn: 364717,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 199423,
            name: "Do we want this ?",
            urn: 394950,
            pos: 1,
          },
          {
            id: 167190,
            name: "Do we want this ?",
            urn: 364717,
            pos: 2,
          },
        ],
      },
      "27686": {
        entryId: 27686,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. 60m Female Under 11 {A}",
        paid: 1,
        egId: 22820,
        gender: "F",
        urn: 392020,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 186826,
            name: "Do we want this ?",
            urn: 384344,
            pos: 1,
          },
          {
            id: 195719,
            name: "Do we want this ?",
            urn: 392020,
            pos: 2,
          },
        ],
      },
      "27703": {
        entryId: 27703,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Castlebar A.C. 60m Female Under 11 {A}",
        paid: 1,
        egId: 22820,
        gender: "F",
        urn: 388761,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 312,
          teamEntity: "Castlebar A.C.",
          entityName: "Club",
        },
        user: {
          id: 764,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Denise Roddy",
        },
        athletes: [
          {
            id: 191473,
            name: "Do we want this ?",
            urn: 388756,
            pos: 1,
          },
          {
            id: 191478,
            name: "Do we want this ?",
            urn: 388761,
            pos: 2,
          },
        ],
      },
      "27754": {
        entryId: 27754,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 60m Female Under 11 {A}",
        paid: 0,
        egId: 22820,
        gender: "F",
        urn: 392390,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 196194,
            name: "Do we want this ?",
            urn: 392387,
            pos: 1,
          },
          {
            id: 196198,
            name: "Do we want this ?",
            urn: 392390,
            pos: 2,
          },
        ],
      },
      "27755": {
        entryId: 27755,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 60m Female Under 11 {B}",
        paid: 0,
        egId: 22820,
        gender: "F",
        urn: 413738,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 218790,
            name: "Do we want this ?",
            urn: 413748,
            pos: 1,
          },
          {
            id: 218781,
            name: "Do we want this ?",
            urn: 413738,
            pos: 2,
          },
        ],
      },
    },
    "22821": {
      "27348": {
        entryId: 27348,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Cabinteely A.C. 60m Male Under 11 {A}",
        paid: 1,
        egId: 22821,
        gender: "M",
        urn: 424580,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 268,
          teamEntity: "Cabinteely A.C.",
          entityName: "Club",
        },
        user: {
          id: 23839,
          email: "<EMAIL>",
          login: "mcummins",
          name: "CabinteelyAC",
        },
        athletes: [
          {
            id: 195746,
            name: "Do we want this ?",
            urn: 392041,
            pos: 1,
          },
          {
            id: 230218,
            name: "Do we want this ?",
            urn: 424580,
            pos: 2,
          },
        ],
      },
      "27446": {
        entryId: 27446,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Naas A.C. 60m Male Under 11 {A}",
        paid: 1,
        egId: 22821,
        gender: "M",
        urn: 384328,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 1115,
          teamEntity: "Naas A.C.",
          entityName: "Club",
        },
        user: {
          id: 9469,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Ruth Whelan",
        },
        athletes: [
          {
            id: 183004,
            name: "Do we want this ?",
            urn: 380754,
            pos: 1,
          },
          {
            id: 186806,
            name: "Do we want this ?",
            urn: 384328,
            pos: 2,
          },
        ],
      },
      "27554": {
        entryId: 27554,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Glaslough Harriers A.C. 60m Male Under 11 {A}",
        paid: 1,
        egId: 22821,
        gender: "M",
        urn: 421926,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 688,
          teamEntity: "Glaslough Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 22553,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Sabrina Mc Cooey",
        },
        athletes: [
          {
            id: 177134,
            name: "Do we want this ?",
            urn: 376135,
            pos: 1,
          },
          {
            id: 227523,
            name: "Do we want this ?",
            urn: 421926,
            pos: 2,
          },
        ],
      },
      "27599": {
        entryId: 27599,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Castlebar A.C. 60m Male Under 11 {A}",
        paid: 1,
        egId: 22821,
        gender: "M",
        urn: 388766,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 312,
          teamEntity: "Castlebar A.C.",
          entityName: "Club",
        },
        user: {
          id: 764,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Denise Roddy",
        },
        athletes: [
          {
            id: 206716,
            name: "Do we want this ?",
            urn: 400216,
            pos: 1,
          },
          {
            id: 191483,
            name: "Do we want this ?",
            urn: 388766,
            pos: 2,
          },
        ],
      },
      "27664": {
        entryId: 27664,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. 60m Male Under 11 {A}",
        paid: 1,
        egId: 22821,
        gender: "M",
        urn: 379630,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 168846,
            name: "Do we want this ?",
            urn: 369602,
            pos: 1,
          },
          {
            id: 181681,
            name: "Do we want this ?",
            urn: 379630,
            pos: 2,
          },
        ],
      },
      "27729": {
        entryId: 27729,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Limerick A.C. 60m Male Under 11 {A}",
        paid: 0,
        egId: 22821,
        gender: "M",
        urn: 367449,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 955,
          teamEntity: "Limerick A.C.",
          entityName: "Club",
        },
        user: {
          id: 1125,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Ray McInerney",
        },
        athletes: [
          {
            id: 208916,
            name: "Do we want this ?",
            urn: 402932,
            pos: 1,
          },
          {
            id: 168054,
            name: "Do we want this ?",
            urn: 367449,
            pos: 2,
          },
        ],
      },
      "27756": {
        entryId: 27756,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 60m Male Under 11 {A}",
        paid: 0,
        egId: 22821,
        gender: "M",
        urn: 421075,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 196206,
            name: "Do we want this ?",
            urn: 392400,
            pos: 1,
          },
          {
            id: 226571,
            name: "Do we want this ?",
            urn: 421075,
            pos: 2,
          },
        ],
      },
      "27757": {
        entryId: 27757,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 60m Male Under 11 {B}",
        paid: 0,
        egId: 22821,
        gender: "M",
        urn: 413313,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 196209,
            name: "Do we want this ?",
            urn: 392402,
            pos: 1,
          },
          {
            id: 218413,
            name: "Do we want this ?",
            urn: 413313,
            pos: 2,
          },
        ],
      },
    },
    "22822": {
      "277014": {
        athleteId: 172788,
        firstname: "Grace",
        surname: "White",
        gender: "F",
        urn: 372277,
        clubId: 23,
        clubname: "Adamstown A.C.",
        entryId: 277014,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 1,
        egId: 22822,
        typeNo: "T24",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 23,
          name: "Adamstown A.C.",
        },
        editAccess: true,
        user: {
          id: 593,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Marie Mooney",
        },
      },
      "278682": {
        athleteId: 24871,
        firstname: "Chloe",
        surname: "Harrington",
        gender: "F",
        urn: 314628,
        clubId: 306,
        clubname: "Carrick Aces A.C.",
        entryId: 278682,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 1,
        egId: 22822,
        typeNo: "T24",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 306,
          name: "Carrick Aces A.C.",
        },
        editAccess: true,
        user: {
          id: 19367,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Niamh Clarke",
        },
      },
      "279029": {
        athleteId: 239958,
        firstname: "Rosie",
        surname: "Mclaughlin",
        gender: "F",
        urn: 126074,
        clubId: 1065,
        clubname: "Mid Ulster A.C.",
        entryId: 279029,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 1,
        egId: 22822,
        typeNo: "T24",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 1065,
          name: "Mid Ulster A.C.",
        },
        editAccess: true,
        user: {
          id: 19383,
          email: "<EMAIL>",
          login: "Barrie Holmes",
          name: "Barrie Holmes",
        },
      },
      "279919": {
        athleteId: 228447,
        firstname: "Amelia",
        surname: "Keegan",
        gender: "F",
        urn: 422928,
        clubId: 50,
        clubname: "Annalee A.C.",
        entryId: 279919,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 1,
        egId: 22822,
        typeNo: "T24",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 50,
          name: "Annalee A.C.",
        },
        editAccess: true,
        user: {
          id: 15241,
          email: "<EMAIL>",
          login: "claireminey",
          name: "claireminey",
        },
      },
      "279920": {
        athleteId: 229965,
        firstname: "Ciara",
        surname: "Gallagher",
        gender: "F",
        urn: 424373,
        clubId: 50,
        clubname: "Annalee A.C.",
        entryId: 279920,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 1,
        egId: 22822,
        typeNo: "T24",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 50,
          name: "Annalee A.C.",
        },
        editAccess: true,
        user: {
          id: 15241,
          email: "<EMAIL>",
          login: "claireminey",
          name: "claireminey",
        },
      },
      "280211": {
        athleteId: 149043,
        firstname: "Elise",
        surname: "Griffin",
        gender: "F",
        urn: 353217,
        clubId: 181,
        clubname: "Blackrock (Dublin) A.C.",
        entryId: 280211,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 1,
        egId: 22822,
        typeNo: "T24",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 181,
          name: "Blackrock (Dublin) A.C.",
        },
        editAccess: true,
        user: {
          id: 20938,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Audrey Burns &amp; Paul Hunt",
        },
      },
      "280217": {
        athleteId: 164966,
        firstname: "Aoife",
        surname: "Holland",
        gender: "F",
        urn: 363061,
        clubId: 181,
        clubname: "Blackrock (Dublin) A.C.",
        entryId: 280217,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 1,
        egId: 22822,
        typeNo: "T24",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 181,
          name: "Blackrock (Dublin) A.C.",
        },
        editAccess: true,
        user: {
          id: 20938,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Audrey Burns &amp; Paul Hunt",
        },
      },
      "280341": {
        athleteId: 171104,
        firstname: "Ellie",
        surname: "O Doherty",
        gender: "F",
        urn: 370827,
        clubId: 1304,
        clubname: "Ratoath A.C.",
        entryId: 280341,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 1,
        egId: 22822,
        typeNo: "T24",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 1304,
          name: "Ratoath A.C.",
        },
        editAccess: true,
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
      },
      "280436": {
        athleteId: 203055,
        firstname: "Lauren",
        surname: "O Reilly",
        gender: "F",
        urn: 396862,
        clubId: 3103,
        clubname: "Metro/St. Brigids A.C.",
        entryId: 280436,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 1,
        egId: 22822,
        typeNo: "T24",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 3103,
          name: "Metro/St. Brigids A.C.",
        },
        editAccess: true,
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
      },
      "280593": {
        athleteId: 68017,
        firstname: "Claire",
        surname: "Kennedy",
        gender: "F",
        urn: 330381,
        clubId: 1119,
        clubname: "Navan A.C.",
        entryId: 280593,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "13:178",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 8.59,
          perfText: "8.59s",
        },
        paid: 0,
        egId: 22822,
        typeNo: "T24",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 1119,
          name: "Navan A.C.",
        },
        editAccess: true,
        user: {
          id: 20936,
          email: "<EMAIL>",
          login: "Genjimtom1303",
          name: "Genjimtom1303",
        },
      },
      "280594": {
        athleteId: 166872,
        firstname: "Autumn ",
        surname: "Moran",
        gender: "F",
        urn: 363412,
        clubId: 537,
        clubname: "Dundalk St. Gerards A.C.",
        entryId: 280594,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "13:178",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 8.86,
          perfText: "8.86s",
        },
        paid: 0,
        egId: 22822,
        typeNo: "T24",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 537,
          name: "Dundalk St. Gerards A.C.",
        },
        editAccess: true,
        user: {
          id: 25628,
          email: "<EMAIL>",
          login: "Deanomoran",
          name: "Deanomoran",
        },
      },
    },
    "22823": {
      "278422": {
        athleteId: 162185,
        firstname: "Matthew",
        surname: "Bowe",
        gender: "M",
        urn: 369057,
        clubId: 599,
        clubname: "Enniscorthy A.C.",
        entryId: 278422,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 1,
        egId: 22823,
        typeNo: "T23",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 599,
          name: "Enniscorthy A.C.",
        },
        editAccess: true,
        user: {
          id: 100,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Edel Byrne",
        },
      },
      "278423": {
        athleteId: 31698,
        firstname: "Conor",
        surname: "Larkin",
        gender: "M",
        urn: 311834,
        clubId: 599,
        clubname: "Enniscorthy A.C.",
        entryId: 278423,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 1,
        egId: 22823,
        typeNo: "T23",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 599,
          name: "Enniscorthy A.C.",
        },
        editAccess: true,
        user: {
          id: 100,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Edel Byrne",
        },
      },
      "280012": {
        athleteId: 166510,
        firstname: "Ollie",
        surname: "Mc Hugh",
        gender: "M",
        urn: 364501,
        clubId: 268,
        clubname: "Cabinteely A.C.",
        entryId: 280012,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 0,
        egId: 22823,
        typeNo: "T23",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 268,
          name: "Cabinteely A.C.",
        },
        editAccess: true,
        user: {
          id: 23839,
          email: "<EMAIL>",
          login: "mcummins",
          name: "CabinteelyAC",
        },
      },
      "280437": {
        athleteId: 210385,
        firstname: "Craig",
        surname: "Makembe",
        gender: "M",
        urn: 404839,
        clubId: 3103,
        clubname: "Metro/St. Brigids A.C.",
        entryId: 280437,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 1,
        egId: 22823,
        typeNo: "T23",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 3103,
          name: "Metro/St. Brigids A.C.",
        },
        editAccess: true,
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
      },
      "280462": {
        athleteId: 145201,
        firstname: "Ryan ",
        surname: "Donnellan ",
        gender: "M",
        urn: 350741,
        clubId: 1699,
        clubname: "Tuam A.C.",
        entryId: 280462,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 0,
        egId: 22823,
        typeNo: "T23",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 1699,
          name: "Tuam A.C.",
        },
        editAccess: true,
        user: {
          id: 1467,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Marie Moynihan",
        },
      },
      "280472": {
        athleteId: 134915,
        firstname: "Philip",
        surname: "Griffith",
        gender: "M",
        urn: 345321,
        clubId: 372,
        clubname: "Claremorris A.C.",
        entryId: 280472,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 0,
        egId: 22823,
        typeNo: "T23",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 372,
          name: "Claremorris A.C.",
        },
        editAccess: true,
        user: {
          id: 20174,
          email: "<EMAIL>",
          login: "gjoyce",
          name: "gjoyce",
        },
      },
      "280517": {
        athleteId: 205750,
        firstname: "Olamide",
        surname: "Smith",
        gender: "M",
        urn: 391966,
        clubId: 886,
        clubname: "Killarney Valley A.C.",
        entryId: 280517,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 1,
        egId: 22823,
        typeNo: "T23",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 886,
          name: "Killarney Valley A.C.",
        },
        editAccess: true,
        user: {
          id: 1077,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Jean Courtney",
        },
      },
      "280554": {
        athleteId: 163386,
        firstname: "Rhys",
        surname: "Daly",
        gender: "M",
        urn: 369967,
        clubId: 379,
        clubname: "Clonliffe Harriers A.C.",
        entryId: 280554,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 1,
        egId: 22823,
        typeNo: "T23",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 379,
          name: "Clonliffe Harriers A.C.",
        },
        editAccess: true,
        user: {
          id: 2486,
          email: "<EMAIL>",
          login: "Sbateson",
          name: "Ebateson",
        },
      },
      "280563": {
        athleteId: 209770,
        firstname: "Sean ",
        surname: "Brennan",
        gender: "M",
        urn: 404106,
        clubId: 997,
        clubname: "Lucan Harriers A.C.",
        entryId: 280563,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 1,
        egId: 22823,
        typeNo: "T23",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 997,
          name: "Lucan Harriers A.C.",
        },
        editAccess: true,
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
      },
      "280568": {
        athleteId: 196215,
        firstname: "Adam",
        surname: "Breen",
        gender: "M",
        urn: 392409,
        clubId: 646,
        clubname: "Finn Valley A.C.",
        entryId: 280568,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 0,
        egId: 22823,
        typeNo: "T23",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 646,
          name: "Finn Valley A.C.",
        },
        editAccess: true,
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
      },
      "280569": {
        athleteId: 220266,
        firstname: "Jamie ",
        surname: "Lydon",
        gender: "M",
        urn: 414924,
        clubId: 646,
        clubname: "Finn Valley A.C.",
        entryId: 280569,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00s",
        },
        paid: 0,
        egId: 22823,
        typeNo: "T23",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 646,
          name: "Finn Valley A.C.",
        },
        editAccess: true,
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
      },
      "280595": {
        athleteId: 228990,
        firstname: "Leon",
        surname: "Wojciechowski ",
        gender: "M",
        urn: 423468,
        clubId: 537,
        clubname: "Dundalk St. Gerards A.C.",
        entryId: 280595,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "14:178",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 8.7,
          perfText: "8.70s",
        },
        paid: 0,
        egId: 22823,
        typeNo: "T23",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 537,
          name: "Dundalk St. Gerards A.C.",
        },
        editAccess: true,
        user: {
          id: 29048,
          email: "<EMAIL>",
          login: "dominiqz",
          name: "dominiqz",
        },
      },
      "280596": {
        athleteId: 226898,
        firstname: "Elvis",
        surname: "Urbstas",
        gender: "M",
        urn: 421345,
        clubId: 3055,
        clubname: "St. Joseph's A.C.",
        entryId: 280596,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "14:178",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 8.8,
          perfText: "8.80s",
        },
        paid: 0,
        egId: 22823,
        typeNo: "T23",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 3055,
          name: "St. Joseph's A.C.",
        },
        editAccess: true,
        user: {
          id: 25413,
          email: "<EMAIL>",
          login: "John Mc Donald",
          name: "John Mc Donald",
        },
      },
    },
    "22824": {
      "277015": {
        athleteId: 172788,
        firstname: "Grace",
        surname: "White",
        gender: "F",
        urn: 372277,
        clubId: 23,
        clubname: "Adamstown A.C.",
        entryId: 277015,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22824,
        typeNo: "F26",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 23,
          name: "Adamstown A.C.",
        },
        editAccess: true,
        user: {
          id: 593,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Marie Mooney",
        },
      },
      "277984": {
        athleteId: 167508,
        firstname: "Amelie",
        surname: "O Donnell",
        gender: "F",
        urn: 364506,
        clubId: 268,
        clubname: "Cabinteely A.C.",
        entryId: 277984,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22824,
        typeNo: "F26",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 268,
          name: "Cabinteely A.C.",
        },
        editAccess: true,
        user: {
          id: 23839,
          email: "<EMAIL>",
          login: "mcummins",
          name: "CabinteelyAC",
        },
      },
      "280082": {
        athleteId: 69725,
        firstname: "Isla",
        surname: "Naughton",
        gender: "F",
        urn: 331885,
        clubId: 1615,
        clubname: "Tara A.C.",
        entryId: 280082,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22824,
        typeNo: "F26",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 1615,
          name: "Tara A.C.",
        },
        editAccess: true,
        user: {
          id: 27266,
          email: "<EMAIL>",
          login: "deirdreclarke81",
          name: "deirdreclarke81",
        },
      },
      "280343": {
        athleteId: 171104,
        firstname: "Ellie",
        surname: "O Doherty",
        gender: "F",
        urn: 370827,
        clubId: 1304,
        clubname: "Ratoath A.C.",
        entryId: 280343,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22824,
        typeNo: "F26",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 1304,
          name: "Ratoath A.C.",
        },
        editAccess: true,
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
      },
      "280424": {
        athleteId: 207162,
        firstname: "Aoibhinn",
        surname: "Hegarty",
        gender: "F",
        urn: 400973,
        clubId: 950,
        clubname: "Letterkenny A.C.",
        entryId: 280424,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22824,
        typeNo: "F26",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 950,
          name: "Letterkenny A.C.",
        },
        editAccess: true,
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
      },
      "280573": {
        athleteId: 173705,
        firstname: "Ailson",
        surname: "Mabuli",
        gender: "F",
        urn: 373011,
        clubId: 646,
        clubname: "Finn Valley A.C.",
        entryId: 280573,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22824,
        typeNo: "F26",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 646,
          name: "Finn Valley A.C.",
        },
        editAccess: true,
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
      },
      "280574": {
        athleteId: 196217,
        firstname: "Aoibheann",
        surname: "Moss",
        gender: "F",
        urn: 392408,
        clubId: 646,
        clubname: "Finn Valley A.C.",
        entryId: 280574,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22824,
        typeNo: "F26",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 646,
          name: "Finn Valley A.C.",
        },
        editAccess: true,
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
      },
    },
    "22825": {
      "277722": {
        athleteId: 220746,
        firstname: "Joey",
        surname: "Nolan",
        gender: "M",
        urn: 415321,
        clubId: 10350,
        clubname: "Nuenna A.C.",
        entryId: 277722,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22825,
        typeNo: "F25",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 10350,
          name: "Nuenna A.C.",
        },
        editAccess: true,
        user: {
          id: 28536,
          email: "<EMAIL>",
          login: "caoimh nolan",
          name: "caoimh nolan",
        },
      },
      "278825": {
        athleteId: 198791,
        firstname: "Patrick",
        surname: "Potterton",
        gender: "M",
        urn: 394365,
        clubId: 661,
        clubname: "Fr. Murphy A.C.",
        entryId: 278825,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22825,
        typeNo: "F25",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 661,
          name: "Fr. Murphy A.C.",
        },
        editAccess: true,
        user: {
          id: 574,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Philip Cogavin",
        },
      },
      "280203": {
        athleteId: 65503,
        firstname: "Paddy",
        surname: "Crampton",
        gender: "M",
        urn: 328596,
        clubId: 1130,
        clubname: "Newbridge A.C.",
        entryId: 280203,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22825,
        typeNo: "F25",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 1130,
          name: "Newbridge A.C.",
        },
        editAccess: true,
        user: {
          id: 1243,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Seamus Cardiff",
        },
      },
      "280229": {
        athleteId: 72417,
        firstname: "Faolán",
        surname: "Caldwell",
        gender: "M",
        urn: 334155,
        clubId: 419,
        clubname: "Cranford A.C.",
        entryId: 280229,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22825,
        typeNo: "F25",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 419,
          name: "Cranford A.C.",
        },
        editAccess: true,
        user: {
          id: 19498,
          email: "<EMAIL>",
          login: "CranfordAC",
          name: "Cranford AC",
        },
      },
      "280370": {
        athleteId: 135067,
        firstname: "Otto",
        surname: "Bowles",
        gender: "M",
        urn: 345492,
        clubId: 881,
        clubname: "Kilcoole A.C.",
        entryId: 280370,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22825,
        typeNo: "F25",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 881,
          name: "Kilcoole A.C.",
        },
        editAccess: true,
        user: {
          id: 19262,
          email: "<EMAIL>",
          login: "ClaireUL",
          name: "ClaireUL",
        },
      },
      "280434": {
        athleteId: 179753,
        firstname: "Odhran",
        surname: "O Sullivan",
        gender: "M",
        urn: 378477,
        clubId: 3103,
        clubname: "Metro/St. Brigids A.C.",
        entryId: 280434,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22825,
        typeNo: "F25",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 3103,
          name: "Metro/St. Brigids A.C.",
        },
        editAccess: true,
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
      },
      "280474": {
        athleteId: 17795,
        firstname: "Luke",
        surname: "Duffy",
        gender: "M",
        urn: 308306,
        clubId: 372,
        clubname: "Claremorris A.C.",
        entryId: 280474,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22825,
        typeNo: "F25",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 372,
          name: "Claremorris A.C.",
        },
        editAccess: true,
        user: {
          id: 20174,
          email: "<EMAIL>",
          login: "gjoyce",
          name: "gjoyce",
        },
      },
      "280516": {
        athleteId: 182661,
        firstname: "Finian ",
        surname: "Swarbrick ",
        gender: "M",
        urn: 380455,
        clubId: 886,
        clubname: "Killarney Valley A.C.",
        entryId: 280516,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22825,
        typeNo: "F25",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 886,
          name: "Killarney Valley A.C.",
        },
        editAccess: true,
        user: {
          id: 1077,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Jean Courtney",
        },
      },
      "280551": {
        athleteId: 180904,
        firstname: "Isaac",
        surname: "O Neill",
        gender: "M",
        urn: 374898,
        clubId: 379,
        clubname: "Clonliffe Harriers A.C.",
        entryId: 280551,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22825,
        typeNo: "F25",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 379,
          name: "Clonliffe Harriers A.C.",
        },
        editAccess: true,
        user: {
          id: 2486,
          email: "<EMAIL>",
          login: "Sbateson",
          name: "Ebateson",
        },
      },
      "280570": {
        athleteId: 196216,
        firstname: "Matthew ",
        surname: "Lynch",
        gender: "M",
        urn: 392410,
        clubId: 646,
        clubname: "Finn Valley A.C.",
        entryId: 280570,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22825,
        typeNo: "F25",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 646,
          name: "Finn Valley A.C.",
        },
        editAccess: true,
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
      },
      "280575": {
        athleteId: 196222,
        firstname: "Martin",
        surname: "Burke",
        gender: "M",
        urn: 392414,
        clubId: 646,
        clubname: "Finn Valley A.C.",
        entryId: 280575,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22825,
        typeNo: "F25",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 646,
          name: "Finn Valley A.C.",
        },
        editAccess: true,
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
      },
      "280602": {
        athleteId: 176853,
        firstname: "Oisin",
        surname: "Burke",
        gender: "M",
        urn: 375876,
        clubId: 536,
        clubname: "Dunboyne A.C.",
        entryId: 280602,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "26:243",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22825,
        typeNo: "F25",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 536,
          name: "Dunboyne A.C.",
        },
        editAccess: true,
        user: {
          id: 1,
          email: "<EMAIL>",
          login: "E4SAdmin",
          name: "E4S Admin",
        },
      },
      "280603": {
        athleteId: 237924,
        firstname: "Sean",
        surname: "Roturier",
        gender: "M",
        urn: 433298,
        clubId: 536,
        clubname: "Dunboyne A.C.",
        entryId: 280603,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "26:243",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          autoEntry: true,
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22825,
        typeNo: "F25",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 536,
          name: "Dunboyne A.C.",
        },
        editAccess: true,
        user: {
          id: 510,
          email: "<EMAIL>",
          login: "Entry4Sports",
          name: "England User",
        },
      },
    },
    "22830": {
      "27398": {
        entryId: 27398,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Enniscorthy A.C. Long Jump Female Under 9 {A}",
        paid: 1,
        egId: 22830,
        gender: "F",
        urn: 439048,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 599,
          teamEntity: "Enniscorthy A.C.",
          entityName: "Club",
        },
        user: {
          id: 100,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Edel Byrne",
        },
        athletes: [
          {
            id: 200250,
            name: "Do we want this ?",
            urn: 395686,
            pos: 1,
          },
          {
            id: 244004,
            name: "Do we want this ?",
            urn: 439048,
            pos: 2,
          },
        ],
      },
      "27443": {
        entryId: 27443,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Naas A.C. Long Jump Female Under 9 {A}",
        paid: 1,
        egId: 22830,
        gender: "F",
        urn: 424911,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 1115,
          teamEntity: "Naas A.C.",
          entityName: "Club",
        },
        user: {
          id: 9469,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Ruth Whelan",
        },
        athletes: [
          {
            id: 234245,
            name: "Do we want this ?",
            urn: 425342,
            pos: 1,
          },
          {
            id: 230509,
            name: "Do we want this ?",
            urn: 424911,
            pos: 2,
          },
        ],
      },
      "27617": {
        entryId: 27617,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Ratoath A.C. Long Jump Female Under 9 {A}",
        paid: 1,
        egId: 22830,
        gender: "F",
        urn: 368522,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 1304,
          teamEntity: "Ratoath A.C.",
          entityName: "Club",
        },
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
        athletes: [
          {
            id: 165385,
            name: "Do we want this ?",
            urn: 363503,
            pos: 1,
          },
          {
            id: 162346,
            name: "Do we want this ?",
            urn: 368522,
            pos: 2,
          },
        ],
      },
      "27652": {
        entryId: 27652,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. Long Jump Female Under 9 {A}",
        paid: 1,
        egId: 22830,
        gender: "F",
        urn: 389404,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 219883,
            name: "Do we want this ?",
            urn: 414525,
            pos: 1,
          },
          {
            id: 192145,
            name: "Do we want this ?",
            urn: 389404,
            pos: 2,
          },
        ],
      },
      "27653": {
        entryId: 27653,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. Long Jump Female Under 9 {B}",
        paid: 1,
        egId: 22830,
        gender: "F",
        urn: 397697,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 214815,
            name: "Do we want this ?",
            urn: 409461,
            pos: 1,
          },
          {
            id: 204939,
            name: "Do we want this ?",
            urn: 397697,
            pos: 2,
          },
        ],
      },
      "27701": {
        entryId: 27701,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Castlebar A.C. Long Jump Female Under 9 {A}",
        paid: 1,
        egId: 22830,
        gender: "F",
        urn: 424482,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 312,
          teamEntity: "Castlebar A.C.",
          entityName: "Club",
        },
        user: {
          id: 764,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Denise Roddy",
        },
        athletes: [
          {
            id: 230028,
            name: "Do we want this ?",
            urn: 424461,
            pos: 1,
          },
          {
            id: 230022,
            name: "Do we want this ?",
            urn: 424482,
            pos: 2,
          },
        ],
      },
      "27741": {
        entryId: 27741,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. Long Jump Female Under 9 {A}",
        paid: 0,
        egId: 22830,
        gender: "F",
        urn: 420998,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 243946,
            name: "Do we want this ?",
            urn: 439005,
            pos: 1,
          },
          {
            id: 226524,
            name: "Do we want this ?",
            urn: 420998,
            pos: 2,
          },
        ],
      },
    },
    "22831": {
      "27334": {
        entryId: 27334,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Crookstown Millview A.C. Long Jump Male Under 9 {A}",
        paid: 1,
        egId: 22831,
        gender: "M",
        urn: 439208,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 430,
          teamEntity: "Crookstown Millview A.C.",
          entityName: "Club",
        },
        user: {
          id: 853,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Neil Taylor",
        },
        athletes: [
          {
            id: 244209,
            name: "Do we want this ?",
            urn: 439250,
            pos: 1,
          },
          {
            id: 244164,
            name: "Do we want this ?",
            urn: 439208,
            pos: 2,
          },
        ],
      },
      "27541": {
        entryId: 27541,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Kilkenny City Harriers A.C. Long Jump Male Under 9 {A}",
        paid: 0,
        egId: 22831,
        gender: "M",
        urn: 410247,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 884,
          teamEntity: "Kilkenny City Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1074,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "May Hutt",
        },
        athletes: [
          {
            id: 215605,
            name: "Do we want this ?",
            urn: 410234,
            pos: 1,
          },
          {
            id: 215758,
            name: "Do we want this ?",
            urn: 410247,
            pos: 2,
          },
        ],
      },
      "27656": {
        entryId: 27656,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. Long Jump Male Under 9 {A}",
        paid: 1,
        egId: 22831,
        gender: "M",
        urn: 390560,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 230705,
            name: "Do we want this ?",
            urn: 425235,
            pos: 1,
          },
          {
            id: 193355,
            name: "Do we want this ?",
            urn: 390560,
            pos: 2,
          },
        ],
      },
      "27712": {
        entryId: 27712,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Claremorris A.C. Long Jump Male Under 9 {A}",
        paid: 0,
        egId: 22831,
        gender: "M",
        urn: 420745,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 372,
          teamEntity: "Claremorris A.C.",
          entityName: "Club",
        },
        user: {
          id: 20174,
          email: "<EMAIL>",
          login: "gjoyce",
          name: "gjoyce",
        },
        athletes: [
          {
            id: 225477,
            name: "Do we want this ?",
            urn: 419862,
            pos: 1,
          },
          {
            id: 226351,
            name: "Do we want this ?",
            urn: 420745,
            pos: 2,
          },
        ],
      },
      "27725": {
        entryId: 27725,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finisk Valley A.C. Long Jump Male Under 9 {A}",
        paid: 0,
        egId: 22831,
        gender: "M",
        urn: 436537,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 645,
          teamEntity: "Finisk Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 995,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Shane Scanlon",
        },
        athletes: [
          {
            id: 182150,
            name: "Do we want this ?",
            urn: 379999,
            pos: 1,
          },
          {
            id: 240986,
            name: "Do we want this ?",
            urn: 436537,
            pos: 2,
          },
        ],
      },
      "27742": {
        entryId: 27742,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. Long Jump Male Under 9 {A}",
        paid: 0,
        egId: 22831,
        gender: "M",
        urn: 421039,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 226927,
            name: "Do we want this ?",
            urn: 421371,
            pos: 1,
          },
          {
            id: 226559,
            name: "Do we want this ?",
            urn: 421039,
            pos: 2,
          },
        ],
      },
    },
    "22832": {
      "27399": {
        entryId: 27399,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Enniscorthy A.C. Long Jump Female Under 10 {A}",
        paid: 1,
        egId: 22832,
        gender: "F",
        urn: 438603,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 599,
          teamEntity: "Enniscorthy A.C.",
          entityName: "Club",
        },
        user: {
          id: 100,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Edel Byrne",
        },
        athletes: [
          {
            id: 222620,
            name: "Do we want this ?",
            urn: 416939,
            pos: 1,
          },
          {
            id: 243490,
            name: "Do we want this ?",
            urn: 438603,
            pos: 2,
          },
        ],
      },
      "27445": {
        entryId: 27445,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Naas A.C. Long Jump Female Under 10 {A}",
        paid: 1,
        egId: 22832,
        gender: "F",
        urn: 402200,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 1115,
          teamEntity: "Naas A.C.",
          entityName: "Club",
        },
        user: {
          id: 9469,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Ruth Whelan",
        },
        athletes: [
          {
            id: 207384,
            name: "Do we want this ?",
            urn: 401188,
            pos: 1,
          },
          {
            id: 210130,
            name: "Do we want this ?",
            urn: 402200,
            pos: 2,
          },
        ],
      },
      "27528": {
        entryId: 27528,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Donore Harriers A.C. Long Jump Female Under 10 {A}",
        paid: 1,
        egId: 22832,
        gender: "F",
        urn: 423265,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 501,
          teamEntity: "Donore Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 875,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Leonie Newman",
        },
        athletes: [
          {
            id: 197463,
            name: "Do we want this ?",
            urn: 393348,
            pos: 1,
          },
          {
            id: 228747,
            name: "Do we want this ?",
            urn: 423265,
            pos: 2,
          },
        ],
      },
      "27604": {
        entryId: 27604,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Cranford A.C. Long Jump Female Under 10 {A}",
        paid: 0,
        egId: 22832,
        gender: "F",
        urn: 371139,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 419,
          teamEntity: "Cranford A.C.",
          entityName: "Club",
        },
        user: {
          id: 19498,
          email: "<EMAIL>",
          login: "CranfordAC",
          name: "Cranford AC",
        },
        athletes: [
          {
            id: 188232,
            name: "Do we want this ?",
            urn: 385645,
            pos: 1,
          },
          {
            id: 171478,
            name: "Do we want this ?",
            urn: 371139,
            pos: 2,
          },
        ],
      },
      "27677": {
        entryId: 27677,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. Long Jump Female Under 10 {A}",
        paid: 1,
        egId: 22832,
        gender: "F",
        urn: 380414,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 235894,
            name: "Do we want this ?",
            urn: 431139,
            pos: 1,
          },
          {
            id: 182617,
            name: "Do we want this ?",
            urn: 380414,
            pos: 2,
          },
        ],
      },
      "27693": {
        entryId: 27693,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. Long Jump Female Under 10 {A}",
        paid: 1,
        egId: 22832,
        gender: "F",
        urn: 422833,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 198972,
            name: "Do we want this ?",
            urn: 394527,
            pos: 1,
          },
          {
            id: 228361,
            name: "Do we want this ?",
            urn: 422833,
            pos: 2,
          },
        ],
      },
      "27715": {
        entryId: 27715,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Dunboyne A.C. Long Jump Female Under 10 {A}",
        paid: 1,
        egId: 22832,
        gender: "F",
        urn: 409317,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 536,
          teamEntity: "Dunboyne A.C.",
          entityName: "Club",
        },
        user: {
          id: 29116,
          email: "<EMAIL>",
          login: "manningalison7",
          name: "manningalison7",
        },
        athletes: [
          {
            id: 214730,
            name: "Do we want this ?",
            urn: 409330,
            pos: 1,
          },
          {
            id: 214712,
            name: "Do we want this ?",
            urn: 409317,
            pos: 2,
          },
        ],
      },
      "27732": {
        entryId: 27732,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Limerick A.C. Long Jump Female Under 10 {A}",
        paid: 0,
        egId: 22832,
        gender: "F",
        urn: 367006,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 955,
          teamEntity: "Limerick A.C.",
          entityName: "Club",
        },
        user: {
          id: 1125,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Ray McInerney",
        },
        athletes: [
          {
            id: 189915,
            name: "Do we want this ?",
            urn: 387277,
            pos: 1,
          },
          {
            id: 169023,
            name: "Do we want this ?",
            urn: 367006,
            pos: 2,
          },
        ],
      },
      "27748": {
        entryId: 27748,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. Long Jump Female Under 10 {A}",
        paid: 0,
        egId: 22832,
        gender: "F",
        urn: 413735,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 218777,
            name: "Do we want this ?",
            urn: 413734,
            pos: 1,
          },
          {
            id: 218779,
            name: "Do we want this ?",
            urn: 413735,
            pos: 2,
          },
        ],
      },
    },
    "22833": {
      "27529": {
        entryId: 27529,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Donore Harriers A.C. Long Jump Male Under 10 {A}",
        paid: 1,
        egId: 22833,
        gender: "M",
        urn: 429178,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 501,
          teamEntity: "Donore Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 875,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Leonie Newman",
        },
        athletes: [
          {
            id: 211065,
            name: "Do we want this ?",
            urn: 405357,
            pos: 1,
          },
          {
            id: 234107,
            name: "Do we want this ?",
            urn: 429178,
            pos: 2,
          },
        ],
      },
      "27543": {
        entryId: 27543,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Kilkenny City Harriers A.C. Long Jump Male Under 10 {A}",
        paid: 0,
        egId: 22833,
        gender: "M",
        urn: 387782,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 884,
          teamEntity: "Kilkenny City Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1074,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "May Hutt",
        },
        athletes: [
          {
            id: 193341,
            name: "Do we want this ?",
            urn: 390548,
            pos: 1,
          },
          {
            id: 190456,
            name: "Do we want this ?",
            urn: 387782,
            pos: 2,
          },
        ],
      },
      "27591": {
        entryId: 27591,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Long Jump Male Under 10 {A}",
        paid: 1,
        egId: 22833,
        gender: "M",
        urn: 391778,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 0,
          teamEntity: "Unknown",
          entityName: "Unknown",
        },
        user: {
          id: 14939,
          email: "<EMAIL>",
          login: "bernieocallaghan",
          name: "bernieocallaghan",
        },
        athletes: [
          {
            id: 184492,
            name: "Do we want this ?",
            urn: 382120,
            pos: 1,
          },
          {
            id: 195421,
            name: "Do we want this ?",
            urn: 391778,
            pos: 2,
          },
        ],
      },
      "27678": {
        entryId: 27678,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. Long Jump Male Under 10 {A}",
        paid: 1,
        egId: 22833,
        gender: "M",
        urn: 380518,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 198427,
            name: "Do we want this ?",
            urn: 394029,
            pos: 1,
          },
          {
            id: 182743,
            name: "Do we want this ?",
            urn: 380518,
            pos: 2,
          },
        ],
      },
      "27709": {
        entryId: 27709,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. Long Jump Male Under 10 {A}",
        paid: 1,
        egId: 22833,
        gender: "M",
        urn: 414565,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 210298,
            name: "Do we want this ?",
            urn: 404748,
            pos: 1,
          },
          {
            id: 219933,
            name: "Do we want this ?",
            urn: 414565,
            pos: 2,
          },
        ],
      },
      "27721": {
        entryId: 27721,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Erris A.C. Long Jump Male Under 10 {A}",
        paid: 1,
        egId: 22833,
        gender: "M",
        urn: 386964,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 3036,
          teamEntity: "Erris A.C.",
          entityName: "Club",
        },
        user: {
          id: 22719,
          email: "<EMAIL>",
          login: "Erris AC",
          name: "Erris AC",
        },
        athletes: [
          {
            id: 189585,
            name: "Do we want this ?",
            urn: 386956,
            pos: 1,
          },
          {
            id: 189593,
            name: "Do we want this ?",
            urn: 386964,
            pos: 2,
          },
        ],
      },
      "27749": {
        entryId: 27749,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. Long Jump Male Under 10 {A}",
        paid: 0,
        egId: 22833,
        gender: "M",
        urn: 413732,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 218776,
            name: "Do we want this ?",
            urn: 413731,
            pos: 1,
          },
          {
            id: 218774,
            name: "Do we want this ?",
            urn: 413732,
            pos: 2,
          },
        ],
      },
    },
    "22834": {
      "27637": {
        entryId: 27637,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Crusaders A.C. Long Jump Female Under 11 {A}",
        paid: 1,
        egId: 22834,
        gender: "F",
        urn: 377555,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 436,
          teamEntity: "Crusaders A.C.",
          entityName: "Club",
        },
        user: {
          id: 20815,
          email: "<EMAIL>",
          login: "Tessa",
          name: "Tessa",
        },
        athletes: [
          {
            id: 199804,
            name: "Do we want this ?",
            urn: 395292,
            pos: 1,
          },
          {
            id: 178728,
            name: "Do we want this ?",
            urn: 377555,
            pos: 2,
          },
        ],
      },
      "27722": {
        entryId: 27722,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Castlebar A.C. Long Jump Female Under 11 {A}",
        paid: 1,
        egId: 22834,
        gender: "F",
        urn: 388770,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 312,
          teamEntity: "Castlebar A.C.",
          entityName: "Club",
        },
        user: {
          id: 764,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Denise Roddy",
        },
        athletes: [
          {
            id: 191473,
            name: "Do we want this ?",
            urn: 388756,
            pos: 1,
          },
          {
            id: 191487,
            name: "Do we want this ?",
            urn: 388770,
            pos: 2,
          },
        ],
      },
      "27758": {
        entryId: 27758,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. Long Jump Female Under 11 {A}",
        paid: 0,
        egId: 22834,
        gender: "F",
        urn: 392388,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 196197,
            name: "Do we want this ?",
            urn: 392391,
            pos: 1,
          },
          {
            id: 196195,
            name: "Do we want this ?",
            urn: 392388,
            pos: 2,
          },
        ],
      },
      "27759": {
        entryId: 27759,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. Long Jump Female Under 11 {B}",
        paid: 0,
        egId: 22834,
        gender: "F",
        urn: 392390,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 218790,
            name: "Do we want this ?",
            urn: 413748,
            pos: 1,
          },
          {
            id: 196198,
            name: "Do we want this ?",
            urn: 392390,
            pos: 2,
          },
        ],
      },
    },
    "22835": {
      "27447": {
        entryId: 27447,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Naas A.C. Long Jump Male Under 11 {A}",
        paid: 1,
        egId: 22835,
        gender: "M",
        urn: 382535,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 1115,
          teamEntity: "Naas A.C.",
          entityName: "Club",
        },
        user: {
          id: 9469,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Ruth Whelan",
        },
        athletes: [
          {
            id: 183004,
            name: "Do we want this ?",
            urn: 380754,
            pos: 1,
          },
          {
            id: 184929,
            name: "Do we want this ?",
            urn: 382535,
            pos: 2,
          },
        ],
      },
      "27531": {
        entryId: 27531,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Donore Harriers A.C. Long Jump Male Under 11 {A}",
        paid: 1,
        egId: 22835,
        gender: "M",
        urn: 345669,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 501,
          teamEntity: "Donore Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 875,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Leonie Newman",
        },
        athletes: [
          {
            id: 239099,
            name: "Do we want this ?",
            urn: 434129,
            pos: 1,
          },
          {
            id: 135334,
            name: "Do we want this ?",
            urn: 345669,
            pos: 2,
          },
        ],
      },
      "27532": {
        entryId: 27532,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Donore Harriers A.C. Long Jump Male Under 11 B {A}",
        paid: 1,
        egId: 22835,
        gender: "M",
        urn: 420004,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 501,
          teamEntity: "Donore Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 875,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Leonie Newman",
        },
        athletes: [
          {
            id: 198676,
            name: "Do we want this ?",
            urn: 394258,
            pos: 1,
          },
          {
            id: 226424,
            name: "Do we want this ?",
            urn: 420004,
            pos: 2,
          },
        ],
      },
      "27555": {
        entryId: 27555,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Glaslough Harriers A.C. Long Jump Male Under 11 {A}",
        paid: 1,
        egId: 22835,
        gender: "M",
        urn: 355805,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 688,
          teamEntity: "Glaslough Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 22553,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Sabrina Mc Cooey",
        },
        athletes: [
          {
            id: 177134,
            name: "Do we want this ?",
            urn: 376135,
            pos: 1,
          },
          {
            id: 152284,
            name: "Do we want this ?",
            urn: 355805,
            pos: 2,
          },
        ],
      },
      "27625": {
        entryId: 27625,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Ratoath A.C. Long Jump Male Under 11 {A}",
        paid: 1,
        egId: 22835,
        gender: "M",
        urn: 327271,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 1304,
          teamEntity: "Ratoath A.C.",
          entityName: "Club",
        },
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
        athletes: [
          {
            id: 134610,
            name: "Do we want this ?",
            urn: 345076,
            pos: 1,
          },
          {
            id: 64096,
            name: "Do we want this ?",
            urn: 327271,
            pos: 2,
          },
        ],
      },
      "27760": {
        entryId: 27760,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. Long Jump Male Under 11 {A}",
        paid: 0,
        egId: 22835,
        gender: "M",
        urn: 392403,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 196209,
            name: "Do we want this ?",
            urn: 392402,
            pos: 1,
          },
          {
            id: 196210,
            name: "Do we want this ?",
            urn: 392403,
            pos: 2,
          },
        ],
      },
      "27761": {
        entryId: 27761,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. Long Jump Male Under 11 {B}",
        paid: 0,
        egId: 22835,
        gender: "M",
        urn: 413313,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 196206,
            name: "Do we want this ?",
            urn: 392400,
            pos: 1,
          },
          {
            id: 218413,
            name: "Do we want this ?",
            urn: 413313,
            pos: 2,
          },
        ],
      },
    },
    "22836": {
      "278683": {
        athleteId: 24871,
        firstname: "Chloe",
        surname: "Harrington",
        gender: "F",
        urn: 314628,
        clubId: 306,
        clubname: "Carrick Aces A.C.",
        entryId: 278683,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22836,
        typeNo: "F34",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 306,
          name: "Carrick Aces A.C.",
        },
        editAccess: true,
        user: {
          id: 19367,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Niamh Clarke",
        },
      },
      "280214": {
        athleteId: 149043,
        firstname: "Elise",
        surname: "Griffin",
        gender: "F",
        urn: 353217,
        clubId: 181,
        clubname: "Blackrock (Dublin) A.C.",
        entryId: 280214,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22836,
        typeNo: "F34",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 181,
          name: "Blackrock (Dublin) A.C.",
        },
        editAccess: true,
        user: {
          id: 20938,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Audrey Burns &amp; Paul Hunt",
        },
      },
      "280216": {
        athleteId: 144953,
        firstname: "Emyrose",
        surname: "Tomkin Beddy",
        gender: "F",
        urn: 350576,
        clubId: 181,
        clubname: "Blackrock (Dublin) A.C.",
        entryId: 280216,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22836,
        typeNo: "F34",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 181,
          name: "Blackrock (Dublin) A.C.",
        },
        editAccess: true,
        user: {
          id: 20938,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Audrey Burns &amp; Paul Hunt",
        },
      },
      "280231": {
        athleteId: 178572,
        firstname: "Megan ",
        surname: "Logue",
        gender: "F",
        urn: 377430,
        clubId: 419,
        clubname: "Cranford A.C.",
        entryId: 280231,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22836,
        typeNo: "F34",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 419,
          name: "Cranford A.C.",
        },
        editAccess: true,
        user: {
          id: 19498,
          email: "<EMAIL>",
          login: "CranfordAC",
          name: "Cranford AC",
        },
      },
      "280479": {
        athleteId: 201450,
        firstname: "Ava",
        surname: "Murphy",
        gender: "F",
        urn: 396332,
        clubId: 372,
        clubname: "Claremorris A.C.",
        entryId: 280479,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22836,
        typeNo: "F34",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 372,
          name: "Claremorris A.C.",
        },
        editAccess: true,
        user: {
          id: 20174,
          email: "<EMAIL>",
          login: "gjoyce",
          name: "gjoyce",
        },
      },
    },
    "22837": {
      "280200": {
        athleteId: 177128,
        firstname: "Keelan",
        surname: "O Donnell",
        gender: "M",
        urn: 376129,
        clubId: 10026,
        clubname: "Killybegs A.C.",
        entryId: 280200,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22837,
        typeNo: "F33",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 10026,
          name: "Killybegs A.C.",
        },
        editAccess: true,
        user: {
          id: 14939,
          email: "<EMAIL>",
          login: "bernieocallaghan",
          name: "bernieocallaghan",
        },
      },
      "280230": {
        athleteId: 72417,
        firstname: "Faolán",
        surname: "Caldwell",
        gender: "M",
        urn: 334155,
        clubId: 419,
        clubname: "Cranford A.C.",
        entryId: 280230,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22837,
        typeNo: "F33",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 419,
          name: "Cranford A.C.",
        },
        editAccess: true,
        user: {
          id: 19498,
          email: "<EMAIL>",
          login: "CranfordAC",
          name: "Cranford AC",
        },
      },
      "280471": {
        athleteId: 134915,
        firstname: "Philip",
        surname: "Griffith",
        gender: "M",
        urn: 345321,
        clubId: 372,
        clubname: "Claremorris A.C.",
        entryId: 280471,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22837,
        typeNo: "F33",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 372,
          name: "Claremorris A.C.",
        },
        editAccess: true,
        user: {
          id: 20174,
          email: "<EMAIL>",
          login: "gjoyce",
          name: "gjoyce",
        },
      },
      "280518": {
        athleteId: 205750,
        firstname: "Olamide",
        surname: "Smith",
        gender: "M",
        urn: 391966,
        clubId: 886,
        clubname: "Killarney Valley A.C.",
        entryId: 280518,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22837,
        typeNo: "F33",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 886,
          name: "Killarney Valley A.C.",
        },
        editAccess: true,
        user: {
          id: 1077,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Jean Courtney",
        },
      },
      "280555": {
        athleteId: 163386,
        firstname: "Rhys",
        surname: "Daly",
        gender: "M",
        urn: 369967,
        clubId: 379,
        clubname: "Clonliffe Harriers A.C.",
        entryId: 280555,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22837,
        typeNo: "F33",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 379,
          name: "Clonliffe Harriers A.C.",
        },
        editAccess: true,
        user: {
          id: 2486,
          email: "<EMAIL>",
          login: "Sbateson",
          name: "Ebateson",
        },
      },
      "280571": {
        athleteId: 196216,
        firstname: "Matthew ",
        surname: "Lynch",
        gender: "M",
        urn: 392410,
        clubId: 646,
        clubname: "Finn Valley A.C.",
        entryId: 280571,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22837,
        typeNo: "F33",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 646,
          name: "Finn Valley A.C.",
        },
        editAccess: true,
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
      },
    },
    "22840": {
      "277986": {
        athleteId: 169410,
        firstname: "Jessica",
        surname: "Williamson",
        gender: "F",
        urn: 364513,
        clubId: 268,
        clubname: "Cabinteely A.C.",
        entryId: 277986,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22840,
        typeNo: "F36",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 268,
          name: "Cabinteely A.C.",
        },
        editAccess: true,
        user: {
          id: 23839,
          email: "<EMAIL>",
          login: "mcummins",
          name: "CabinteelyAC",
        },
      },
      "279786": {
        athleteId: 159078,
        firstname: "Teagin",
        surname: "Mtinsi",
        gender: "F",
        urn: 359880,
        clubId: 884,
        clubname: "Kilkenny City Harriers A.C.",
        entryId: 279786,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22840,
        typeNo: "F36",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 884,
          name: "Kilkenny City Harriers A.C.",
        },
        editAccess: true,
        user: {
          id: 1074,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "May Hutt",
        },
      },
      "279787": {
        athleteId: 168247,
        firstname: "Isabella",
        surname: "Quaglia",
        gender: "F",
        urn: 363492,
        clubId: 884,
        clubname: "Kilkenny City Harriers A.C.",
        entryId: 279787,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22840,
        typeNo: "F36",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 884,
          name: "Kilkenny City Harriers A.C.",
        },
        editAccess: true,
        user: {
          id: 1074,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "May Hutt",
        },
      },
      "279921": {
        athleteId: 229965,
        firstname: "Ciara",
        surname: "Gallagher",
        gender: "F",
        urn: 424373,
        clubId: 50,
        clubname: "Annalee A.C.",
        entryId: 279921,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22840,
        typeNo: "F36",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 50,
          name: "Annalee A.C.",
        },
        editAccess: true,
        user: {
          id: 15241,
          email: "<EMAIL>",
          login: "claireminey",
          name: "claireminey",
        },
      },
      "280426": {
        athleteId: 173768,
        firstname: "Charley",
        surname: "Mc Gee O Donnell",
        gender: "F",
        urn: 373064,
        clubId: 950,
        clubname: "Letterkenny A.C.",
        entryId: 280426,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22840,
        typeNo: "F36",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 950,
          name: "Letterkenny A.C.",
        },
        editAccess: true,
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
      },
      "280470": {
        athleteId: 174825,
        firstname: "Erin",
        surname: "Matthews",
        gender: "F",
        urn: 373880,
        clubId: 181,
        clubname: "Blackrock (Dublin) A.C.",
        entryId: 280470,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22840,
        typeNo: "F36",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 181,
          name: "Blackrock (Dublin) A.C.",
        },
        editAccess: true,
        user: {
          id: 20938,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Audrey Burns &amp; Paul Hunt",
        },
      },
      "280532": {
        athleteId: 116169,
        firstname: "Emma",
        surname: "Naughton",
        gender: "F",
        urn: 302577,
        clubId: 3036,
        clubname: "Erris A.C.",
        entryId: 280532,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22840,
        typeNo: "F36",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 3036,
          name: "Erris A.C.",
        },
        editAccess: true,
        user: {
          id: 22719,
          email: "<EMAIL>",
          login: "Erris AC",
          name: "Erris AC",
        },
      },
      "280539": {
        athleteId: 14289,
        firstname: "Sarah",
        surname: "Cullinane",
        gender: "F",
        urn: 308896,
        clubId: 645,
        clubname: "Finisk Valley A.C.",
        entryId: 280539,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22840,
        typeNo: "F36",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 645,
          name: "Finisk Valley A.C.",
        },
        editAccess: true,
        user: {
          id: 995,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Shane Scanlon",
        },
      },
      "280561": {
        athleteId: 134639,
        firstname: "Hannah",
        surname: "Dunne",
        gender: "F",
        urn: 345089,
        clubId: 997,
        clubname: "Lucan Harriers A.C.",
        entryId: 280561,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22840,
        typeNo: "F36",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 997,
          name: "Lucan Harriers A.C.",
        },
        editAccess: true,
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
      },
      "280562": {
        athleteId: 226174,
        firstname: "Maria ",
        surname: " Ciomek",
        gender: "F",
        urn: 420643,
        clubId: 997,
        clubname: "Lucan Harriers A.C.",
        entryId: 280562,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22840,
        typeNo: "F36",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 997,
          name: "Lucan Harriers A.C.",
        },
        editAccess: true,
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
      },
    },
    "22841": {
      "277940": {
        athleteId: 225092,
        firstname: "Mathieu ",
        surname: "Gasset Driane",
        gender: "M",
        urn: 419520,
        clubId: 1443,
        clubname: "Skerries A.C.",
        entryId: 277940,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22841,
        typeNo: "F35",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 1443,
          name: "Skerries A.C.",
        },
        editAccess: true,
        user: {
          id: 25260,
          email: "<EMAIL>",
          login: "SkerriesACRaceSecretary",
          name: "SkerriesACRaceSecretary",
        },
      },
      "277985": {
        athleteId: 158082,
        firstname: "Conor",
        surname: "Helme",
        gender: "M",
        urn: 360889,
        clubId: 268,
        clubname: "Cabinteely A.C.",
        entryId: 277985,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22841,
        typeNo: "F35",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 268,
          name: "Cabinteely A.C.",
        },
        editAccess: true,
        user: {
          id: 23839,
          email: "<EMAIL>",
          login: "mcummins",
          name: "CabinteelyAC",
        },
      },
      "279788": {
        athleteId: 197365,
        firstname: "Caelan",
        surname: "Bambrick",
        gender: "M",
        urn: 393255,
        clubId: 884,
        clubname: "Kilkenny City Harriers A.C.",
        entryId: 279788,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22841,
        typeNo: "F35",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 884,
          name: "Kilkenny City Harriers A.C.",
        },
        editAccess: true,
        user: {
          id: 1074,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "May Hutt",
        },
      },
      "279917": {
        athleteId: 234854,
        firstname: "Conor",
        surname: "McNamara",
        gender: "M",
        urn: 430027,
        clubId: 50,
        clubname: "Annalee A.C.",
        entryId: 279917,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22841,
        typeNo: "F35",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 50,
          name: "Annalee A.C.",
        },
        editAccess: true,
        user: {
          id: 15241,
          email: "<EMAIL>",
          login: "claireminey",
          name: "claireminey",
        },
      },
      "280182": {
        athleteId: 70909,
        firstname: "Noah",
        surname: "Mc Caul",
        gender: "M",
        urn: 330764,
        clubId: 688,
        clubname: "Glaslough Harriers A.C.",
        entryId: 280182,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22841,
        typeNo: "F35",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 688,
          name: "Glaslough Harriers A.C.",
        },
        editAccess: true,
        user: {
          id: 22553,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Sabrina Mc Cooey",
        },
      },
      "280201": {
        athleteId: 17943,
        firstname: "Aidan",
        surname: "Duignan",
        gender: "M",
        urn: 311758,
        clubId: 1130,
        clubname: "Newbridge A.C.",
        entryId: 280201,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22841,
        typeNo: "F35",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 1130,
          name: "Newbridge A.C.",
        },
        editAccess: true,
        user: {
          id: 1243,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Seamus Cardiff",
        },
      },
      "280202": {
        athleteId: 166700,
        firstname: "Olivier",
        surname: "Meder",
        gender: "M",
        urn: 366544,
        clubId: 1130,
        clubname: "Newbridge A.C.",
        entryId: 280202,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22841,
        typeNo: "F35",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 1130,
          name: "Newbridge A.C.",
        },
        editAccess: true,
        user: {
          id: 1243,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Seamus Cardiff",
        },
      },
      "280425": {
        athleteId: 199460,
        firstname: "Scott",
        surname: "Doherty",
        gender: "M",
        urn: 394987,
        clubId: 950,
        clubname: "Letterkenny A.C.",
        entryId: 280425,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22841,
        typeNo: "F35",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 950,
          name: "Letterkenny A.C.",
        },
        editAccess: true,
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
      },
      "280576": {
        athleteId: 244135,
        firstname: "Alex",
        surname: "Martin",
        gender: "M",
        urn: 439189,
        clubId: 646,
        clubname: "Finn Valley A.C.",
        entryId: 280576,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22841,
        typeNo: "F35",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 646,
          name: "Finn Valley A.C.",
        },
        editAccess: true,
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
      },
    },
    "22844": {
      "27442": {
        entryId: 27442,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Naas A.C. Turbo Javelin Female Under 9 {A}",
        paid: 1,
        egId: 22844,
        gender: "F",
        urn: 432431,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 1115,
          teamEntity: "Naas A.C.",
          entityName: "Club",
        },
        user: {
          id: 9469,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Ruth Whelan",
        },
        athletes: [
          {
            id: 240652,
            name: "Do we want this ?",
            urn: 436220,
            pos: 1,
          },
          {
            id: 237862,
            name: "Do we want this ?",
            urn: 432431,
            pos: 2,
          },
        ],
      },
      "27552": {
        entryId: 27552,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Glaslough Harriers A.C. Turbo Javelin Female Under 9 {A}",
        paid: 1,
        egId: 22844,
        gender: "F",
        urn: 421898,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 688,
          teamEntity: "Glaslough Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 22553,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Sabrina Mc Cooey",
        },
        athletes: [
          {
            id: 212793,
            name: "Do we want this ?",
            urn: 405965,
            pos: 1,
          },
          {
            id: 227474,
            name: "Do we want this ?",
            urn: 421898,
            pos: 2,
          },
        ],
      },
      "27595": {
        entryId: 27595,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Newbridge A.C. Turbo Javelin Female Under 9 {A}",
        paid: 1,
        egId: 22844,
        gender: "F",
        urn: 412326,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 1130,
          teamEntity: "Newbridge A.C.",
          entityName: "Club",
        },
        user: {
          id: 1243,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Seamus Cardiff",
        },
        athletes: [
          {
            id: 199866,
            name: "Do we want this ?",
            urn: 395353,
            pos: 1,
          },
          {
            id: 217821,
            name: "Do we want this ?",
            urn: 412326,
            pos: 2,
          },
        ],
      },
      "27671": {
        entryId: 27671,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Innyvale A.C. Turbo Javelin Female Under 9 {A}",
        paid: 1,
        egId: 22844,
        gender: "F",
        urn: 409119,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 838,
          teamEntity: "Innyvale A.C.",
          entityName: "Club",
        },
        user: {
          id: 14779,
          email: "<EMAIL>",
          login: "dmulvey001974",
          name: "dmulvey001974",
        },
        athletes: [
          {
            id: 236505,
            name: "Do we want this ?",
            urn: 431748,
            pos: 1,
          },
          {
            id: 214443,
            name: "Do we want this ?",
            urn: 409119,
            pos: 2,
          },
        ],
      },
    },
    "22845": {
      "27444": {
        entryId: 27444,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Naas A.C. Turbo Javelin Female Under 10 {A}",
        paid: 1,
        egId: 22845,
        gender: "F",
        urn: 413499,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 1115,
          teamEntity: "Naas A.C.",
          entityName: "Club",
        },
        user: {
          id: 9469,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Ruth Whelan",
        },
        athletes: [
          {
            id: 217347,
            name: "Do we want this ?",
            urn: 412091,
            pos: 1,
          },
          {
            id: 218583,
            name: "Do we want this ?",
            urn: 413499,
            pos: 2,
          },
        ],
      },
      "27602": {
        entryId: 27602,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Suncroft A.C. Turbo Javelin Female Under 10 {A}",
        paid: 1,
        egId: 22845,
        gender: "F",
        urn: 368910,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 1586,
          teamEntity: "Suncroft A.C.",
          entityName: "Club",
        },
        user: {
          id: 25043,
          email: "<EMAIL>",
          login: "Georgina Cousins",
          name: "Georgina Cousins",
        },
        athletes: [
          {
            id: 177014,
            name: "Do we want this ?",
            urn: 376025,
            pos: 1,
          },
          {
            id: 165816,
            name: "Do we want this ?",
            urn: 368910,
            pos: 2,
          },
        ],
      },
      "27620": {
        entryId: 27620,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Ratoath A.C. Turbo Javelin Female Under 10 {A}",
        paid: 1,
        egId: 22845,
        gender: "F",
        urn: 403321,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 1304,
          teamEntity: "Ratoath A.C.",
          entityName: "Club",
        },
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
        athletes: [
          {
            id: 162316,
            name: "Do we want this ?",
            urn: 368869,
            pos: 1,
          },
          {
            id: 209265,
            name: "Do we want this ?",
            urn: 403321,
            pos: 2,
          },
        ],
      },
      "27658": {
        entryId: 27658,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. Turbo Javelin Female Under 10 {A}",
        paid: 1,
        egId: 22845,
        gender: "F",
        urn: 397696,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 174052,
            name: "Do we want this ?",
            urn: 373291,
            pos: 1,
          },
          {
            id: 204020,
            name: "Do we want this ?",
            urn: 397696,
            pos: 2,
          },
        ],
      },
      "27680": {
        entryId: 27680,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. Turbo Javelin Female Under 10 {A}",
        paid: 1,
        egId: 22845,
        gender: "F",
        urn: 410583,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 183220,
            name: "Do we want this ?",
            urn: 380950,
            pos: 1,
          },
          {
            id: 215879,
            name: "Do we want this ?",
            urn: 410583,
            pos: 2,
          },
        ],
      },
      "27702": {
        entryId: 27702,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. Turbo Javelin Female Under 10 {A}",
        paid: 1,
        egId: 22845,
        gender: "F",
        urn: 422833,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 198972,
            name: "Do we want this ?",
            urn: 394527,
            pos: 1,
          },
          {
            id: 228361,
            name: "Do we want this ?",
            urn: 422833,
            pos: 2,
          },
        ],
      },
    },
    "22846": {
      "27522": {
        entryId: 27522,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lusk A.C. Turbo Javelin Male Under 10 {A}",
        paid: 1,
        egId: 22846,
        gender: "M",
        urn: 351552,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 999,
          teamEntity: "Lusk A.C.",
          entityName: "Club",
        },
        user: {
          id: 22469,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Claire Handley",
        },
        athletes: [
          {
            id: 165291,
            name: "Do we want this ?",
            urn: 366578,
            pos: 1,
          },
          {
            id: 146234,
            name: "Do we want this ?",
            urn: 351552,
            pos: 2,
          },
        ],
      },
      "27530": {
        entryId: 27530,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Donore Harriers A.C. Turbo Javelin Male Under 10 {A}",
        paid: 1,
        egId: 22846,
        gender: "M",
        urn: 429178,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 501,
          teamEntity: "Donore Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 875,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Leonie Newman",
        },
        athletes: [
          {
            id: 211065,
            name: "Do we want this ?",
            urn: 405357,
            pos: 1,
          },
          {
            id: 234107,
            name: "Do we want this ?",
            urn: 429178,
            pos: 2,
          },
        ],
      },
      "27621": {
        entryId: 27621,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Ratoath A.C. Turbo Javelin Male Under 10 {A}",
        paid: 1,
        egId: 22846,
        gender: "M",
        urn: 403381,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 1304,
          teamEntity: "Ratoath A.C.",
          entityName: "Club",
        },
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
        athletes: [
          {
            id: 208552,
            name: "Do we want this ?",
            urn: 402733,
            pos: 1,
          },
          {
            id: 209326,
            name: "Do we want this ?",
            urn: 403381,
            pos: 2,
          },
        ],
      },
      "27679": {
        entryId: 27679,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. Turbo Javelin Male Under 10 {A}",
        paid: 1,
        egId: 22846,
        gender: "M",
        urn: 367513,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 215877,
            name: "Do we want this ?",
            urn: 410582,
            pos: 1,
          },
          {
            id: 166639,
            name: "Do we want this ?",
            urn: 367513,
            pos: 2,
          },
        ],
      },
      "27708": {
        entryId: 27708,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. Turbo Javelin Male Under 10 {A}",
        paid: 1,
        egId: 22846,
        gender: "M",
        urn: 404745,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 189695,
            name: "Do we want this ?",
            urn: 387066,
            pos: 1,
          },
          {
            id: 210249,
            name: "Do we want this ?",
            urn: 404745,
            pos: 2,
          },
        ],
      },
      "27750": {
        entryId: 27750,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. Turbo Javelin Male Under 10 {A}",
        paid: 0,
        egId: 22846,
        gender: "M",
        urn: 439123,
        ageGroup: {
          id: 46,
          name: "Under 10",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 226932,
            name: "Do we want this ?",
            urn: 421373,
            pos: 1,
          },
          {
            id: 244069,
            name: "Do we want this ?",
            urn: 439123,
            pos: 2,
          },
        ],
      },
    },
    "22847": {
      "27449": {
        entryId: 27449,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Naas A.C. Turbo Javelin Male Under 11 {A}",
        paid: 1,
        egId: 22847,
        gender: "M",
        urn: 382213,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 1115,
          teamEntity: "Naas A.C.",
          entityName: "Club",
        },
        user: {
          id: 9469,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Ruth Whelan",
        },
        athletes: [
          {
            id: 194023,
            name: "Do we want this ?",
            urn: 390731,
            pos: 1,
          },
          {
            id: 184592,
            name: "Do we want this ?",
            urn: 382213,
            pos: 2,
          },
        ],
      },
      "27533": {
        entryId: 27533,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Donore Harriers A.C. Turbo Javelin Male Under 11  {A}",
        paid: 1,
        egId: 22847,
        gender: "M",
        urn: 394258,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 501,
          teamEntity: "Donore Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 875,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Leonie Newman",
        },
        athletes: [
          {
            id: 157811,
            name: "Do we want this ?",
            urn: 360632,
            pos: 1,
          },
          {
            id: 198676,
            name: "Do we want this ?",
            urn: 394258,
            pos: 2,
          },
        ],
      },
      "27545": {
        entryId: 27545,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Kilkenny City Harriers A.C. Turbo Javelin Male Under 11 {A}",
        paid: 0,
        egId: 22847,
        gender: "M",
        urn: 387381,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 884,
          teamEntity: "Kilkenny City Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1074,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "May Hutt",
        },
        athletes: [
          {
            id: 191930,
            name: "Do we want this ?",
            urn: 389197,
            pos: 1,
          },
          {
            id: 190024,
            name: "Do we want this ?",
            urn: 387381,
            pos: 2,
          },
        ],
      },
      "27607": {
        entryId: 27607,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lusk A.C. Turbo Javelin Male Under 11 {A}",
        paid: 1,
        egId: 22847,
        gender: "M",
        urn: 362568,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 999,
          teamEntity: "Lusk A.C.",
          entityName: "Club",
        },
        user: {
          id: 22469,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Claire Handley",
        },
        athletes: [
          {
            id: 198937,
            name: "Do we want this ?",
            urn: 394507,
            pos: 1,
          },
          {
            id: 166860,
            name: "Do we want this ?",
            urn: 362568,
            pos: 2,
          },
        ],
      },
      "27624": {
        entryId: 27624,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Ratoath A.C. Turbo Javelin Male Under 11 {A}",
        paid: 1,
        egId: 22847,
        gender: "M",
        urn: 345076,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 1304,
          teamEntity: "Ratoath A.C.",
          entityName: "Club",
        },
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
        athletes: [
          {
            id: 162786,
            name: "Do we want this ?",
            urn: 368343,
            pos: 1,
          },
          {
            id: 134610,
            name: "Do we want this ?",
            urn: 345076,
            pos: 2,
          },
        ],
      },
      "27650": {
        entryId: 27650,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Aughagower A.C. Turbo Javelin Male Under 11 {A}",
        paid: 1,
        egId: 22847,
        gender: "M",
        urn: 348661,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 71,
          teamEntity: "Aughagower A.C.",
          entityName: "Club",
        },
        user: {
          id: 23690,
          email: "<EMAIL>",
          login: "Aughagowerathletics",
          name: "Aughagowerathletics",
        },
        athletes: [
          {
            id: 175710,
            name: "Do we want this ?",
            urn: 374719,
            pos: 1,
          },
          {
            id: 140664,
            name: "Do we want this ?",
            urn: 348661,
            pos: 2,
          },
        ],
      },
      "27685": {
        entryId: 27685,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. Turbo Javelin Male Under 11 {A}",
        paid: 1,
        egId: 22847,
        gender: "M",
        urn: 385849,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 173765,
            name: "Do we want this ?",
            urn: 373061,
            pos: 1,
          },
          {
            id: 188451,
            name: "Do we want this ?",
            urn: 385849,
            pos: 2,
          },
        ],
      },
      "27687": {
        entryId: 27687,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. Turbo Javelin Male Under 11 {B}",
        paid: 1,
        egId: 22847,
        gender: "M",
        urn: 388836,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 208132,
            name: "Do we want this ?",
            urn: 402241,
            pos: 1,
          },
          {
            id: 191557,
            name: "Do we want this ?",
            urn: 388836,
            pos: 2,
          },
        ],
      },
      "27706": {
        entryId: 27706,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. Turbo Javelin Male Under 11 {A}",
        paid: 1,
        egId: 22847,
        gender: "M",
        urn: 384336,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 233346,
            name: "Do we want this ?",
            urn: 428413,
            pos: 1,
          },
          {
            id: 186817,
            name: "Do we want this ?",
            urn: 384336,
            pos: 2,
          },
        ],
      },
    },
    "22848": {
      "280218": {
        athleteId: 144953,
        firstname: "Emyrose",
        surname: "Tomkin Beddy",
        gender: "F",
        urn: 350576,
        clubId: 181,
        clubname: "Blackrock (Dublin) A.C.",
        entryId: 280218,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22848,
        typeNo: "F44",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 181,
          name: "Blackrock (Dublin) A.C.",
        },
        editAccess: true,
        user: {
          id: 20938,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Audrey Burns &amp; Paul Hunt",
        },
      },
      "280219": {
        athleteId: 174825,
        firstname: "Erin",
        surname: "Matthews",
        gender: "F",
        urn: 373880,
        clubId: 181,
        clubname: "Blackrock (Dublin) A.C.",
        entryId: 280219,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22848,
        typeNo: "F44",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 181,
          name: "Blackrock (Dublin) A.C.",
        },
        editAccess: true,
        user: {
          id: 20938,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Audrey Burns &amp; Paul Hunt",
        },
      },
      "280220": {
        athleteId: 143797,
        firstname: "Sophia",
        surname: "Kelly",
        gender: "F",
        urn: 349697,
        clubId: 181,
        clubname: "Blackrock (Dublin) A.C.",
        entryId: 280220,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22848,
        typeNo: "F44",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 181,
          name: "Blackrock (Dublin) A.C.",
        },
        editAccess: true,
        user: {
          id: 20938,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Audrey Burns &amp; Paul Hunt",
        },
      },
      "280428": {
        athleteId: 177080,
        firstname: "Aoibhe",
        surname: "Gibson",
        gender: "F",
        urn: 376082,
        clubId: 950,
        clubname: "Letterkenny A.C.",
        entryId: 280428,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22848,
        typeNo: "F44",
        ageGroup: {
          currentAge: 10,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 950,
          name: "Letterkenny A.C.",
        },
        editAccess: true,
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
      },
      "280429": {
        athleteId: 173768,
        firstname: "Charley",
        surname: "Mc Gee O Donnell",
        gender: "F",
        urn: 373064,
        clubId: 950,
        clubname: "Letterkenny A.C.",
        entryId: 280429,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22848,
        typeNo: "F44",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 950,
          name: "Letterkenny A.C.",
        },
        editAccess: true,
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
      },
      "280531": {
        athleteId: 116169,
        firstname: "Emma",
        surname: "Naughton",
        gender: "F",
        urn: 302577,
        clubId: 3036,
        clubname: "Erris A.C.",
        entryId: 280531,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22848,
        typeNo: "F44",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 3036,
          name: "Erris A.C.",
        },
        editAccess: true,
        user: {
          id: 22719,
          email: "<EMAIL>",
          login: "Erris AC",
          name: "Erris AC",
        },
      },
      "280540": {
        athleteId: 14289,
        firstname: "Sarah",
        surname: "Cullinane",
        gender: "F",
        urn: 308896,
        clubId: 645,
        clubname: "Finisk Valley A.C.",
        entryId: 280540,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 0,
        egId: 22848,
        typeNo: "F44",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 645,
          name: "Finisk Valley A.C.",
        },
        editAccess: true,
        user: {
          id: 995,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Shane Scanlon",
        },
      },
      "280564": {
        athleteId: 112807,
        firstname: "Sarah",
        surname: "King",
        gender: "F",
        urn: 338334,
        clubId: 997,
        clubname: "Lucan Harriers A.C.",
        entryId: 280564,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22848,
        typeNo: "F44",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 997,
          name: "Lucan Harriers A.C.",
        },
        editAccess: true,
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
      },
    },
    "22849": {
      "279995": {
        athleteId: 158082,
        firstname: "Conor",
        surname: "Helme",
        gender: "M",
        urn: 360889,
        clubId: 268,
        clubname: "Cabinteely A.C.",
        entryId: 279995,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22849,
        typeNo: "F43",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 268,
          name: "Cabinteely A.C.",
        },
        editAccess: true,
        user: {
          id: 23839,
          email: "<EMAIL>",
          login: "mcummins",
          name: "CabinteelyAC",
        },
      },
      "280183": {
        athleteId: 70909,
        firstname: "Noah",
        surname: "Mc Caul",
        gender: "M",
        urn: 330764,
        clubId: 688,
        clubname: "Glaslough Harriers A.C.",
        entryId: 280183,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22849,
        typeNo: "F43",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 688,
          name: "Glaslough Harriers A.C.",
        },
        editAccess: true,
        user: {
          id: 22553,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Sabrina Mc Cooey",
        },
      },
      "280255": {
        athleteId: 143111,
        firstname: "Joe",
        surname: "Keohane",
        gender: "M",
        urn: 349217,
        clubId: 183,
        clubname: "Blackwater A.C.",
        entryId: 280255,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22849,
        typeNo: "F43",
        ageGroup: {
          currentAge: 10,
          competitionAge: 10,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 183,
          name: "Blackwater A.C.",
        },
        editAccess: true,
        user: {
          id: 19234,
          email: "<EMAIL>",
          login: "BLACKWATERAC",
          name: "BLACKWATERAC",
        },
      },
      "280427": {
        athleteId: 177071,
        firstname: "Rian",
        surname: "Walsh",
        gender: "M",
        urn: 376073,
        clubId: 950,
        clubname: "Letterkenny A.C.",
        entryId: 280427,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22849,
        typeNo: "F43",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 950,
          name: "Letterkenny A.C.",
        },
        editAccess: true,
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
      },
      "280433": {
        athleteId: 179050,
        firstname: "James ",
        surname: "Howlin",
        gender: "M",
        urn: 377824,
        clubId: 3103,
        clubname: "Metro/St. Brigids A.C.",
        entryId: 280433,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22849,
        typeNo: "F43",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 3103,
          name: "Metro/St. Brigids A.C.",
        },
        editAccess: true,
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
      },
      "280435": {
        athleteId: 179753,
        firstname: "Odhran",
        surname: "O Sullivan",
        gender: "M",
        urn: 378477,
        clubId: 3103,
        clubname: "Metro/St. Brigids A.C.",
        entryId: 280435,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        perf: {
          perf: 0,
          perfText: "0.00m",
        },
        paid: 1,
        egId: 22849,
        typeNo: "F43",
        ageGroup: {
          currentAge: 11,
          competitionAge: 11,
          name: "Under 12",
          shortName: "U12",
        },
        club: {
          id: 3103,
          name: "Metro/St. Brigids A.C.",
        },
        editAccess: true,
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
      },
    },
    "22850": {
      "27448": {
        entryId: 27448,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Naas A.C. Turbo Javelin Female Under 11 {A}",
        paid: 1,
        egId: 22850,
        gender: "F",
        urn: 371963,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 1115,
          teamEntity: "Naas A.C.",
          entityName: "Club",
        },
        user: {
          id: 9469,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Ruth Whelan",
        },
        athletes: [
          {
            id: 190408,
            name: "Do we want this ?",
            urn: 387740,
            pos: 1,
          },
          {
            id: 172440,
            name: "Do we want this ?",
            urn: 371963,
            pos: 2,
          },
        ],
      },
      "27596": {
        entryId: 27596,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Fanahan Mc Sweeney A.C. Turbo Javelin Female Under 11 {A}",
        paid: 1,
        egId: 22850,
        gender: "F",
        urn: 405825,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 624,
          teamEntity: "Fanahan Mc Sweeney A.C.",
          entityName: "Club",
        },
        user: {
          id: 23133,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Shane Thornton",
        },
        athletes: [
          {
            id: 169021,
            name: "Do we want this ?",
            urn: 365973,
            pos: 1,
          },
          {
            id: 211295,
            name: "Do we want this ?",
            urn: 405825,
            pos: 2,
          },
        ],
      },
      "27603": {
        entryId: 27603,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Suncroft A.C. Turbo Javelin Female Under 11 {A}",
        paid: 1,
        egId: 22850,
        gender: "F",
        urn: 407567,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 1586,
          teamEntity: "Suncroft A.C.",
          entityName: "Club",
        },
        user: {
          id: 25043,
          email: "<EMAIL>",
          login: "Georgina Cousins",
          name: "Georgina Cousins",
        },
        athletes: [
          {
            id: 66503,
            name: "Do we want this ?",
            urn: 329208,
            pos: 1,
          },
          {
            id: 214773,
            name: "Do we want this ?",
            urn: 407567,
            pos: 2,
          },
        ],
      },
      "27630": {
        entryId: 27630,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Ratoath A.C. Turbo Javelin Female Under 11 {A}",
        paid: 1,
        egId: 22850,
        gender: "F",
        urn: 377150,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 1304,
          teamEntity: "Ratoath A.C.",
          entityName: "Club",
        },
        user: {
          id: 1296,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Peter Doherty",
        },
        athletes: [
          {
            id: 227224,
            name: "Do we want this ?",
            urn: 421636,
            pos: 1,
          },
          {
            id: 178269,
            name: "Do we want this ?",
            urn: 377150,
            pos: 2,
          },
        ],
      },
      "27666": {
        entryId: 27666,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lucan Harriers A.C. Turbo Javelin Female Under 11 {A}",
        paid: 1,
        egId: 22850,
        gender: "F",
        urn: 420595,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 997,
          teamEntity: "Lucan Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1146,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Keith Gallagher",
        },
        athletes: [
          {
            id: 160773,
            name: "Do we want this ?",
            urn: 362066,
            pos: 1,
          },
          {
            id: 226091,
            name: "Do we want this ?",
            urn: 420595,
            pos: 2,
          },
        ],
      },
      "27688": {
        entryId: 27688,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. Turbo Javelin Female Under 11 {A}",
        paid: 1,
        egId: 22850,
        gender: "F",
        urn: 373062,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 177062,
            name: "Do we want this ?",
            urn: 376063,
            pos: 1,
          },
          {
            id: 173766,
            name: "Do we want this ?",
            urn: 373062,
            pos: 2,
          },
        ],
      },
      "27690": {
        entryId: 27690,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. Turbo Javelin Female Under 11 {A}",
        paid: 1,
        egId: 22850,
        gender: "F",
        urn: 392021,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 186822,
            name: "Do we want this ?",
            urn: 384340,
            pos: 1,
          },
          {
            id: 195720,
            name: "Do we want this ?",
            urn: 392021,
            pos: 2,
          },
        ],
      },
      "27692": {
        entryId: 27692,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Metro/St. Brigids A.C. Turbo Javelin Female Under 11 {B}",
        paid: 1,
        egId: 22850,
        gender: "F",
        urn: 419279,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 3103,
          teamEntity: "Metro/St. Brigids A.C.",
          entityName: "Club",
        },
        user: {
          id: 19607,
          email: "<EMAIL>",
          login: "mark.woodger",
          name: "mark.woodger",
        },
        athletes: [
          {
            id: 186839,
            name: "Do we want this ?",
            urn: 384355,
            pos: 1,
          },
          {
            id: 225027,
            name: "Do we want this ?",
            urn: 419279,
            pos: 2,
          },
        ],
      },
      "27723": {
        entryId: 27723,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finisk Valley A.C. Turbo Javelin Female Under 11 {A}",
        paid: 0,
        egId: 22850,
        gender: "F",
        urn: 352563,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 645,
          teamEntity: "Finisk Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 995,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Shane Scanlon",
        },
        athletes: [
          {
            id: 168676,
            name: "Do we want this ?",
            urn: 365570,
            pos: 1,
          },
          {
            id: 147930,
            name: "Do we want this ?",
            urn: 352563,
            pos: 2,
          },
        ],
      },
      "27762": {
        entryId: 27762,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. Turbo Javelin Female Under 11 {A}",
        paid: 0,
        egId: 22850,
        gender: "F",
        urn: 392395,
        ageGroup: {
          id: 47,
          name: "Under 11",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 196193,
            name: "Do we want this ?",
            urn: 392386,
            pos: 1,
          },
          {
            id: 196202,
            name: "Do we want this ?",
            urn: 392395,
            pos: 2,
          },
        ],
      },
    },
    "22851": {
      "27353": {
        entryId: 27353,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Rathkenny A.C. Turbo Javelin Male Under 9 {A}",
        paid: 1,
        egId: 22851,
        gender: "M",
        urn: 390902,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 1303,
          teamEntity: "Rathkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1293,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Brendan White",
        },
        athletes: [
          {
            id: 183383,
            name: "Do we want this ?",
            urn: 381107,
            pos: 1,
          },
          {
            id: 194256,
            name: "Do we want this ?",
            urn: 390902,
            pos: 2,
          },
        ],
      },
      "27441": {
        entryId: 27441,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Naas A.C. Turbo Javelin Male Under 9 {A}",
        paid: 1,
        egId: 22851,
        gender: "M",
        urn: 439422,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 1115,
          teamEntity: "Naas A.C.",
          entityName: "Club",
        },
        user: {
          id: 9469,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Ruth Whelan",
        },
        athletes: [
          {
            id: 236932,
            name: "Do we want this ?",
            urn: 429405,
            pos: 1,
          },
          {
            id: 244389,
            name: "Do we want this ?",
            urn: 439422,
            pos: 2,
          },
        ],
      },
      "27606": {
        entryId: 27606,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Lusk A.C. Turbo Javelin Male Under 9 {A}",
        paid: 1,
        egId: 22851,
        gender: "M",
        urn: 419041,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 999,
          teamEntity: "Lusk A.C.",
          entityName: "Club",
        },
        user: {
          id: 22469,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Claire Handley",
        },
        athletes: [
          {
            id: 218932,
            name: "Do we want this ?",
            urn: 413723,
            pos: 1,
          },
          {
            id: 225145,
            name: "Do we want this ?",
            urn: 419041,
            pos: 2,
          },
        ],
      },
      "27672": {
        entryId: 27672,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. Turbo Javelin Male Under 9 {A}",
        paid: 1,
        egId: 22851,
        gender: "M",
        urn: 402608,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 232021,
            name: "Do we want this ?",
            urn: 426883,
            pos: 1,
          },
          {
            id: 208459,
            name: "Do we want this ?",
            urn: 402608,
            pos: 2,
          },
        ],
      },
      "27724": {
        entryId: 27724,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finisk Valley A.C. Turbo Javelin Male Under 9 {A}",
        paid: 0,
        egId: 22851,
        gender: "M",
        urn: 436537,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 645,
          teamEntity: "Finisk Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 995,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Shane Scanlon",
        },
        athletes: [
          {
            id: 182150,
            name: "Do we want this ?",
            urn: 379999,
            pos: 1,
          },
          {
            id: 240986,
            name: "Do we want this ?",
            urn: 436537,
            pos: 2,
          },
        ],
      },
      "27743": {
        entryId: 27743,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. Turbo Javelin Male Under 9 {A}",
        paid: 0,
        egId: 22851,
        gender: "M",
        urn: 439270,
        ageGroup: {
          id: 45,
          name: "Under 9",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 244236,
            name: "Do we want this ?",
            urn: 439263,
            pos: 1,
          },
          {
            id: 244242,
            name: "Do we want this ?",
            urn: 439270,
            pos: 2,
          },
        ],
      },
    },
    "22852": {
      "27550": {
        entryId: 27550,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Annalee A.C. 4x100 Relay Female Under 12 {A}",
        paid: 1,
        egId: 22852,
        gender: "F",
        urn: 424373,
        ageGroup: {
          id: 48,
          name: "Under 12",
        },
        entity: {
          id: 50,
          teamEntity: "Annalee A.C.",
          entityName: "Club",
        },
        user: {
          id: 15241,
          email: "<EMAIL>",
          login: "claireminey",
          name: "claireminey",
        },
        athletes: [
          {
            id: 172052,
            name: "Do we want this ?",
            urn: 371629,
            pos: 1,
          },
          {
            id: 228447,
            name: "Do we want this ?",
            urn: 422928,
            pos: 2,
          },
          {
            id: 229962,
            name: "Do we want this ?",
            urn: 424376,
            pos: 3,
          },
          {
            id: 229965,
            name: "Do we want this ?",
            urn: 424373,
            pos: 4,
          },
        ],
      },
      "27578": {
        entryId: 27578,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Tara A.C. 4x100 Relay Female Under 12 {A}",
        paid: 1,
        egId: 22852,
        gender: "F",
        urn: 351376,
        ageGroup: {
          id: 48,
          name: "Under 12",
        },
        entity: {
          id: 1615,
          teamEntity: "Tara A.C.",
          entityName: "Club",
        },
        user: {
          id: 27266,
          email: "<EMAIL>",
          login: "deirdreclarke81",
          name: "deirdreclarke81",
        },
        athletes: [
          {
            id: 154339,
            name: "Do we want this ?",
            urn: 357319,
            pos: 1,
          },
          {
            id: 69725,
            name: "Do we want this ?",
            urn: 331885,
            pos: 2,
          },
          {
            id: 177745,
            name: "Do we want this ?",
            urn: 376705,
            pos: 3,
          },
          {
            id: 153578,
            name: "Do we want this ?",
            urn: 356731,
            pos: 4,
          },
          {
            id: 146050,
            name: "Do we want this ?",
            urn: 351376,
            pos: 5,
          },
        ],
      },
      "27689": {
        entryId: 27689,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. 4x100 Relay Female Under 12 {A}",
        paid: 1,
        egId: 22852,
        gender: "F",
        urn: 410162,
        ageGroup: {
          id: 48,
          name: "Under 12",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 166364,
            name: "Do we want this ?",
            urn: 363934,
            pos: 1,
          },
          {
            id: 162565,
            name: "Do we want this ?",
            urn: 366518,
            pos: 2,
          },
          {
            id: 211090,
            name: "Do we want this ?",
            urn: 405660,
            pos: 3,
          },
          {
            id: 177080,
            name: "Do we want this ?",
            urn: 376082,
            pos: 4,
          },
          {
            id: 215491,
            name: "Do we want this ?",
            urn: 410162,
            pos: 5,
          },
        ],
      },
      "27699": {
        entryId: 27699,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Castlebar A.C. 4x100 Relay Female Under 12 {A}",
        paid: 1,
        egId: 22852,
        gender: "F",
        urn: 400283,
        ageGroup: {
          id: 48,
          name: "Under 12",
        },
        entity: {
          id: 312,
          teamEntity: "Castlebar A.C.",
          entityName: "Club",
        },
        user: {
          id: 764,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Denise Roddy",
        },
        athletes: [
          {
            id: 183750,
            name: "Do we want this ?",
            urn: 381440,
            pos: 1,
          },
          {
            id: 182174,
            name: "Do we want this ?",
            urn: 380022,
            pos: 2,
          },
          {
            id: 182211,
            name: "Do we want this ?",
            urn: 380058,
            pos: 3,
          },
          {
            id: 182208,
            name: "Do we want this ?",
            urn: 380055,
            pos: 4,
          },
          {
            id: 206789,
            name: "Do we want this ?",
            urn: 400283,
            pos: 5,
          },
        ],
      },
      "27765": {
        entryId: 27765,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 4x100 Relay Female Under 12 {A}",
        paid: 0,
        egId: 22852,
        gender: "F",
        urn: 392404,
        ageGroup: {
          id: 48,
          name: "Under 12",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 173705,
            name: "Do we want this ?",
            urn: 373011,
            pos: 1,
          },
          {
            id: 192898,
            name: "Do we want this ?",
            urn: 390132,
            pos: 2,
          },
          {
            id: 190867,
            name: "Do we want this ?",
            urn: 388179,
            pos: 3,
          },
          {
            id: 196217,
            name: "Do we want this ?",
            urn: 392408,
            pos: 4,
          },
          {
            id: 196211,
            name: "Do we want this ?",
            urn: 392404,
            pos: 5,
          },
        ],
      },
      "27767": {
        entryId: 27767,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "41:190",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Offaly - U11G {A}",
        paid: 0,
        egId: 22852,
        gender: "F",
        urn: 367108,
        ageGroup: {
          id: 48,
          name: "Under 12",
        },
        entity: {
          id: 9,
          teamEntity: "Leinster",
          entityName: "Region",
        },
        user: {
          id: 26807,
          email: "<EMAIL>",
          login: "Noonange",
          name: "Noonange",
        },
        athletes: [
          {
            id: 221502,
            name: "Do we want this ?",
            urn: 415536,
            pos: 1,
          },
          {
            id: 196660,
            name: "Do we want this ?",
            urn: 392781,
            pos: 2,
          },
          {
            id: 228241,
            name: "Do we want this ?",
            urn: 419587,
            pos: 3,
          },
          {
            id: 208339,
            name: "Do we want this ?",
            urn: 402469,
            pos: 4,
          },
          {
            id: 118657,
            name: "Do we want this ?",
            urn: 339585,
            pos: 5,
          },
          {
            id: 165222,
            name: "Do we want this ?",
            urn: 367108,
            pos: 6,
          },
        ],
      },
      "27768": {
        entryId: 27768,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "41:190",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Laois - U11G {A}",
        paid: 0,
        egId: 22852,
        gender: "F",
        urn: 361509,
        ageGroup: {
          id: 48,
          name: "Under 12",
        },
        entity: {
          id: 9,
          teamEntity: "Leinster",
          entityName: "Region",
        },
        user: {
          id: 1,
          email: "<EMAIL>",
          login: "E4SAdmin",
          name: "E4S Admin",
        },
        athletes: [
          {
            id: 191405,
            name: "Do we want this ?",
            urn: 388690,
            pos: 1,
          },
          {
            id: 191406,
            name: "Do we want this ?",
            urn: 388689,
            pos: 2,
          },
          {
            id: 209082,
            name: "Do we want this ?",
            urn: 403368,
            pos: 3,
          },
          {
            id: 159597,
            name: "Do we want this ?",
            urn: 361509,
            pos: 4,
          },
        ],
      },
      "27769": {
        entryId: 27769,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "41:190",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Kilkenny - U11G {A}",
        paid: 0,
        egId: 22852,
        gender: "F",
        urn: 390457,
        ageGroup: {
          id: 48,
          name: "Under 12",
        },
        entity: {
          id: 9,
          teamEntity: "Leinster",
          entityName: "Region",
        },
        user: {
          id: 1,
          email: "<EMAIL>",
          login: "E4SAdmin",
          name: "E4S Admin",
        },
        athletes: [
          {
            id: 196130,
            name: "Do we want this ?",
            urn: 392353,
            pos: 1,
          },
          {
            id: 183624,
            name: "Do we want this ?",
            urn: 381329,
            pos: 2,
          },
          {
            id: 197253,
            name: "Do we want this ?",
            urn: 393181,
            pos: 3,
          },
          {
            id: 193243,
            name: "Do we want this ?",
            urn: 390457,
            pos: 4,
          },
        ],
      },
    },
    "22853": {
      "27609": {
        entryId: 27609,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Kilkenny City Harriers A.C. 4x100 Relay Male Under 12 {A}",
        paid: 0,
        egId: 22853,
        gender: "M",
        urn: 414721,
        ageGroup: {
          id: 48,
          name: "Under 12",
        },
        entity: {
          id: 884,
          teamEntity: "Kilkenny City Harriers A.C.",
          entityName: "Club",
        },
        user: {
          id: 1074,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "May Hutt",
        },
        athletes: [
          {
            id: 207877,
            name: "Do we want this ?",
            urn: 401914,
            pos: 1,
          },
          {
            id: 197365,
            name: "Do we want this ?",
            urn: 393255,
            pos: 2,
          },
          {
            id: 234108,
            name: "Do we want this ?",
            urn: 429056,
            pos: 3,
          },
          {
            id: 191251,
            name: "Do we want this ?",
            urn: 388540,
            pos: 4,
          },
          {
            id: 220092,
            name: "Do we want this ?",
            urn: 414721,
            pos: 5,
          },
        ],
      },
      "27691": {
        entryId: 27691,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
          lastStatus: "completed",
        },
        teamName: "Letterkenny A.C. 4x100 Relay Male Under 12 {A}",
        paid: 1,
        egId: 22853,
        gender: "M",
        urn: 380644,
        ageGroup: {
          id: 48,
          name: "Under 12",
        },
        entity: {
          id: 950,
          teamEntity: "Letterkenny A.C.",
          entityName: "Club",
        },
        user: {
          id: 1114,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Grit McGee",
        },
        athletes: [
          {
            id: 205185,
            name: "Do we want this ?",
            urn: 398912,
            pos: 1,
          },
          {
            id: 61641,
            name: "Do we want this ?",
            urn: 325880,
            pos: 2,
          },
          {
            id: 177071,
            name: "Do we want this ?",
            urn: 376073,
            pos: 3,
          },
          {
            id: 199460,
            name: "Do we want this ?",
            urn: 394987,
            pos: 4,
          },
          {
            id: 182882,
            name: "Do we want this ?",
            urn: 380644,
            pos: 5,
          },
        ],
      },
      "27713": {
        entryId: 27713,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Claremorris A.C. 4x100 Relay Male Under 12 {A}",
        paid: 0,
        egId: 22853,
        gender: "M",
        urn: 339943,
        ageGroup: {
          id: 48,
          name: "Under 12",
        },
        entity: {
          id: 372,
          teamEntity: "Claremorris A.C.",
          entityName: "Club",
        },
        user: {
          id: 20174,
          email: "<EMAIL>",
          login: "gjoyce",
          name: "gjoyce",
        },
        athletes: [
          {
            id: 134915,
            name: "Do we want this ?",
            urn: 345321,
            pos: 1,
          },
          {
            id: 205924,
            name: "Do we want this ?",
            urn: 399372,
            pos: 2,
          },
          {
            id: 17795,
            name: "Do we want this ?",
            urn: 308306,
            pos: 3,
          },
          {
            id: 173473,
            name: "Do we want this ?",
            urn: 372814,
            pos: 4,
          },
          {
            id: 199118,
            name: "Do we want this ?",
            urn: 394662,
            pos: 5,
          },
          {
            id: 119625,
            name: "Do we want this ?",
            urn: 339943,
            pos: 6,
          },
        ],
      },
      "27766": {
        entryId: 27766,
        entryOptions: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          waitingInfo: {
            originalPos: 0,
            wentOn: "",
            cameOff: "",
          },
          resultsKey: "",
          trackPB: true,
          checkIn: {
            from: null,
            to: null,
          },
          autoEntries: {
            targetEntry: {
              id: 0,
              paid: 0,
              orderId: 0,
            },
            targetEventGroup: {
              id: 0,
              name: "",
            },
          },
        },
        teamName: "Finn Valley A.C. 4x100 Relay Male Under 12 {A}",
        paid: 0,
        egId: 22853,
        gender: "M",
        urn: 373014,
        ageGroup: {
          id: 48,
          name: "Under 12",
        },
        entity: {
          id: 646,
          teamEntity: "Finn Valley A.C.",
          entityName: "Club",
        },
        user: {
          id: 999,
          email: "<EMAIL>",
          login: "<EMAIL>",
          name: "Dermot McGranaghan",
        },
        athletes: [
          {
            id: 196215,
            name: "Do we want this ?",
            urn: 392409,
            pos: 1,
          },
          {
            id: 220266,
            name: "Do we want this ?",
            urn: 414924,
            pos: 2,
          },
          {
            id: 221138,
            name: "Do we want this ?",
            urn: 415631,
            pos: 3,
          },
          {
            id: 173706,
            name: "Do we want this ?",
            urn: 373013,
            pos: 4,
          },
          {
            id: 173708,
            name: "Do we want this ?",
            urn: 373014,
            pos: 5,
          },
        ],
      },
    },
  } as any as IFeederAllEntriesForCompByEventGroupIdByEntryId;
