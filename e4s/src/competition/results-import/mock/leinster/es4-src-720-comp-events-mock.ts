import { ICompEvent } from "../../../../compevent/compevent-models";

export const src720CompEvents: ICompEvent[] = [
  {
    maxGroup: 11516,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: true,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 47,
          minAge: 10,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 11 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 11",
          maxAge: 10,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 11",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: null,
        to: null,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 11516,
      eventNo: 3,
      bibSortNo: "",
      name: "Even Ages Boys",
      notes: null,
    },
    eventGroup: "Even Ages Boys",
    eventNo: 3,
    id: 60758,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 300,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 48,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 12 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 12",
      maxAge: 11,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 12",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11535,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 7,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 8,
      id: 11535,
      eventNo: 8,
      bibSortNo: "",
      name: "Pole Vault",
      notes: null,
    },
    eventGroup: "Pole Vault",
    eventNo: 8,
    id: 60822,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 30,
      name: "Pole Vault",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 49,
      minAge: 12,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 13 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 1773,
      fee: 0.59,
      saleFee: 0.59,
      price: 8.59,
      salePrice: 4.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U12 to U16",
      description: "U12 to U16",
    },
    multiEventInfo: {
      id: 0,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11518,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 0.05,
      max: 2.5,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 1,
      id: 11518,
      eventNo: 5,
      bibSortNo: "",
      name: "High Jump",
      notes: null,
    },
    eventGroup: "High Jump",
    eventNo: 5,
    id: 60728,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 24,
      name: "High Jump",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 58,
      minAge: 16,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 17 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 6,
      waiting: 0,
    },
  },
  {
    maxGroup: 11518,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 0.05,
      max: 2.5,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 1,
      id: 11518,
      eventNo: 5,
      bibSortNo: "",
      name: "High Jump",
      notes: null,
    },
    eventGroup: "High Jump",
    eventNo: 5,
    id: 60729,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 24,
      name: "High Jump",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 59,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 18 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 18",
      maxAge: 17,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 18",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 5,
      waiting: 0,
    },
  },
  {
    maxGroup: 11518,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 0.05,
      max: 2.5,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 1,
      id: 11518,
      eventNo: 5,
      bibSortNo: "",
      name: "High Jump",
      notes: null,
    },
    eventGroup: "High Jump",
    eventNo: 5,
    id: 60730,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 24,
      name: "High Jump",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 60,
      minAge: 18,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 19 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 19",
      maxAge: 18,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 19",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 2,
      waiting: 0,
    },
  },
  {
    maxGroup: 11518,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 0.05,
      max: 2.5,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 1,
      id: 11518,
      eventNo: 5,
      bibSortNo: "",
      name: "High Jump",
      notes: null,
    },
    eventGroup: "High Jump",
    eventNo: 5,
    id: 60731,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 25,
      name: "High Jump",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 58,
      minAge: 16,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 17 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 8,
      waiting: 0,
    },
  },
  {
    maxGroup: 11518,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 0.05,
      max: 2.5,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 1,
      id: 11518,
      eventNo: 5,
      bibSortNo: "",
      name: "High Jump",
      notes: null,
    },
    eventGroup: "High Jump",
    eventNo: 5,
    id: 60732,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 25,
      name: "High Jump",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 59,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 18 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 18",
      maxAge: 17,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 18",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 3,
      waiting: 0,
    },
  },
  {
    maxGroup: 11518,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 0.05,
      max: 2.5,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 1,
      id: 11518,
      eventNo: 5,
      bibSortNo: "",
      name: "High Jump",
      notes: null,
    },
    eventGroup: "High Jump",
    eventNo: 5,
    id: 60733,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 25,
      name: "High Jump",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 60,
      minAge: 18,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 19 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 19",
      maxAge: 18,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 19",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 2,
      waiting: 0,
    },
  },
  {
    maxGroup: 11520,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 48,
          minAge: 11,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 12 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 12",
          maxAge: 11,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 12",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 11520,
      eventNo: 2,
      bibSortNo: "",
      name: "Odd Relays Girls",
      notes: null,
    },
    eventGroup: "Odd Relays Girls",
    eventNo: 2,
    id: 60746,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 299,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 49,
      minAge: 12,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 13 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11520,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 57,
          minAge: 15,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 16 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 16",
          maxAge: 15,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 16",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 11520,
      eventNo: 2,
      bibSortNo: "",
      name: "Odd Relays Girls",
      notes: null,
    },
    eventGroup: "Odd Relays Girls",
    eventNo: 2,
    id: 60747,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 299,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 58,
      minAge: 16,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 17 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11520,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 59,
          minAge: 17,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 18 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 18",
          maxAge: 17,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 18",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 11520,
      eventNo: 2,
      bibSortNo: "",
      name: "Odd Relays Girls",
      notes: null,
    },
    eventGroup: "Odd Relays Girls",
    eventNo: 2,
    id: 60748,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 299,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 60,
      minAge: 18,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 19 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 19",
      maxAge: 18,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 19",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11520,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 50,
          minAge: 13,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 14 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 14",
          maxAge: 13,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 14",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 11520,
      eventNo: 2,
      bibSortNo: "",
      name: "Odd Relays Girls",
      notes: null,
    },
    eventGroup: "Odd Relays Girls",
    eventNo: 2,
    id: 60749,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 299,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 83,
      minAge: 14,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 15 3112",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11519,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 48,
          minAge: 11,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 12 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 12",
          maxAge: 11,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 12",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 11519,
      eventNo: 1,
      bibSortNo: "",
      name: "Odd Relays Boys",
      notes: null,
    },
    eventGroup: "Odd Relays Boys",
    eventNo: 1,
    id: 60754,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 300,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 49,
      minAge: 12,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 13 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11519,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 57,
          minAge: 15,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 16 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 16",
          maxAge: 15,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 16",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 11519,
      eventNo: 1,
      bibSortNo: "",
      name: "Odd Relays Boys",
      notes: null,
    },
    eventGroup: "Odd Relays Boys",
    eventNo: 1,
    id: 60755,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 300,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 58,
      minAge: 16,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 17 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11519,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 59,
          minAge: 17,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 18 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 18",
          maxAge: 17,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 18",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 11519,
      eventNo: 1,
      bibSortNo: "",
      name: "Odd Relays Boys",
      notes: null,
    },
    eventGroup: "Odd Relays Boys",
    eventNo: 1,
    id: 60756,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 300,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 60,
      minAge: 18,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 19 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 19",
      maxAge: 18,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 19",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11519,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 50,
          minAge: 13,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 14 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 14",
          maxAge: 13,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 14",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 11519,
      eventNo: 1,
      bibSortNo: "",
      name: "Odd Relays Boys",
      notes: null,
    },
    eventGroup: "Odd Relays Boys",
    eventNo: 1,
    id: 60757,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 300,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 83,
      minAge: 14,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 15 3112",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11520,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 48,
          minAge: 11,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 12 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 12",
          maxAge: 11,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 12",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 11520,
      eventNo: 2,
      bibSortNo: "",
      name: "Odd Relays Girls",
      notes: null,
    },
    eventGroup: "Odd Relays Girls",
    eventNo: 2,
    id: 60762,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 301,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 49,
      minAge: 12,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 13 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11520,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 57,
          minAge: 15,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 16 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 16",
          maxAge: 15,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 16",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 11520,
      eventNo: 2,
      bibSortNo: "",
      name: "Odd Relays Girls",
      notes: null,
    },
    eventGroup: "Odd Relays Girls",
    eventNo: 2,
    id: 60763,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 301,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 58,
      minAge: 16,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 17 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11520,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 4,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 59,
          minAge: 17,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 18 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 18",
          maxAge: 17,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 18",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 11520,
      eventNo: 2,
      bibSortNo: "",
      name: "Odd Relays Girls",
      notes: null,
    },
    eventGroup: "Odd Relays Girls",
    eventNo: 2,
    id: 60764,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 301,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 60,
      minAge: 18,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 19 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 19",
      maxAge: 18,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 19",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11520,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 50,
          minAge: 13,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 14 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 14",
          maxAge: 13,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 14",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 11520,
      eventNo: 2,
      bibSortNo: "",
      name: "Odd Relays Girls",
      notes: null,
    },
    eventGroup: "Odd Relays Girls",
    eventNo: 2,
    id: 60765,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 301,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 83,
      minAge: 14,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 15 3112",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11519,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 48,
          minAge: 11,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 12 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 12",
          maxAge: 11,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 12",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 11519,
      eventNo: 1,
      bibSortNo: "",
      name: "Odd Relays Boys",
      notes: null,
    },
    eventGroup: "Odd Relays Boys",
    eventNo: 1,
    id: 60770,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 302,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 49,
      minAge: 12,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 13 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11519,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 57,
          minAge: 15,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 16 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 16",
          maxAge: 15,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 16",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 11519,
      eventNo: 1,
      bibSortNo: "",
      name: "Odd Relays Boys",
      notes: null,
    },
    eventGroup: "Odd Relays Boys",
    eventNo: 1,
    id: 60771,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 302,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 58,
      minAge: 16,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 17 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11519,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 59,
          minAge: 17,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 18 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 18",
          maxAge: 17,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 18",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 11519,
      eventNo: 1,
      bibSortNo: "",
      name: "Odd Relays Boys",
      notes: null,
    },
    eventGroup: "Odd Relays Boys",
    eventNo: 1,
    id: 60772,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 302,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 60,
      minAge: 18,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 19 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 19",
      maxAge: 18,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 19",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11519,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 50,
          minAge: 13,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 14 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 14",
          maxAge: 13,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 14",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 11519,
      eventNo: 1,
      bibSortNo: "",
      name: "Odd Relays Boys",
      notes: null,
    },
    eventGroup: "Odd Relays Boys",
    eventNo: 1,
    id: 60773,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 302,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 83,
      minAge: 14,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 15 3112",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11520,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 48,
          minAge: 11,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 12 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 12",
          maxAge: 11,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 12",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 11520,
      eventNo: 2,
      bibSortNo: "",
      name: "Odd Relays Girls",
      notes: null,
    },
    eventGroup: "Odd Relays Girls",
    eventNo: 2,
    id: 60778,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 303,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 49,
      minAge: 12,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 13 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11520,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 57,
          minAge: 15,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 16 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 16",
          maxAge: 15,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 16",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 11520,
      eventNo: 2,
      bibSortNo: "",
      name: "Odd Relays Girls",
      notes: null,
    },
    eventGroup: "Odd Relays Girls",
    eventNo: 2,
    id: 60779,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 303,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 58,
      minAge: 16,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 17 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11520,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 4,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 59,
          minAge: 17,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 18 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 18",
          maxAge: 17,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 18",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 11520,
      eventNo: 2,
      bibSortNo: "",
      name: "Odd Relays Girls",
      notes: null,
    },
    eventGroup: "Odd Relays Girls",
    eventNo: 2,
    id: 60780,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 303,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 60,
      minAge: 18,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 19 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 19",
      maxAge: 18,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 19",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11520,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 50,
          minAge: 13,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 14 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 14",
          maxAge: 13,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 14",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 11520,
      eventNo: 2,
      bibSortNo: "",
      name: "Odd Relays Girls",
      notes: null,
    },
    eventGroup: "Odd Relays Girls",
    eventNo: 2,
    id: 60781,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 303,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 83,
      minAge: 14,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 15 3112",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11519,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 48,
          minAge: 11,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 12 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 12",
          maxAge: 11,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 12",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 11519,
      eventNo: 1,
      bibSortNo: "",
      name: "Odd Relays Boys",
      notes: null,
    },
    eventGroup: "Odd Relays Boys",
    eventNo: 1,
    id: 60786,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 304,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 49,
      minAge: 12,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 13 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11519,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 57,
          minAge: 15,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 16 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 16",
          maxAge: 15,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 16",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 11519,
      eventNo: 1,
      bibSortNo: "",
      name: "Odd Relays Boys",
      notes: null,
    },
    eventGroup: "Odd Relays Boys",
    eventNo: 1,
    id: 60787,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 304,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 58,
      minAge: 16,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 17 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11519,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 59,
          minAge: 17,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 18 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 18",
          maxAge: 17,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 18",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 11519,
      eventNo: 1,
      bibSortNo: "",
      name: "Odd Relays Boys",
      notes: null,
    },
    eventGroup: "Odd Relays Boys",
    eventNo: 1,
    id: 60788,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 304,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 60,
      minAge: 18,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 19 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 19",
      maxAge: 18,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 19",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11519,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 50,
          minAge: 13,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 14 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 14",
          maxAge: 13,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 14",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 11519,
      eventNo: 1,
      bibSortNo: "",
      name: "Odd Relays Boys",
      notes: null,
    },
    eventGroup: "Odd Relays Boys",
    eventNo: 1,
    id: 60789,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T10:30:00",
    sortDateTime: "2023-03-12T10:30:00",
    event: {
      id: 304,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 83,
      minAge: 14,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 15 3112",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11522,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 3,
      max: 25,
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 2,
      id: 11522,
      eventNo: 6,
      bibSortNo: "",
      name: "Triple Jump",
      notes: null,
    },
    eventGroup: "Triple Jump",
    eventNo: 6,
    id: 60734,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 26,
      name: "Triple Jump",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 58,
      minAge: 16,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 17 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 3,
      waiting: 0,
    },
  },
  {
    maxGroup: 11522,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 3,
      max: 25,
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 2,
      id: 11522,
      eventNo: 6,
      bibSortNo: "",
      name: "Triple Jump",
      notes: null,
    },
    eventGroup: "Triple Jump",
    eventNo: 6,
    id: 60735,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 26,
      name: "Triple Jump",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 59,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 18 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 18",
      maxAge: 17,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 18",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 4,
      waiting: 0,
    },
  },
  {
    maxGroup: 11522,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 3,
      max: 25,
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 2,
      id: 11522,
      eventNo: 6,
      bibSortNo: "",
      name: "Triple Jump",
      notes: null,
    },
    eventGroup: "Triple Jump",
    eventNo: 6,
    id: 60736,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 26,
      name: "Triple Jump",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 60,
      minAge: 18,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 19 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 19",
      maxAge: 18,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 19",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 3,
      waiting: 0,
    },
  },
  {
    maxGroup: 11522,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 3,
      max: 25,
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 2,
      id: 11522,
      eventNo: 6,
      bibSortNo: "",
      name: "Triple Jump",
      notes: null,
    },
    eventGroup: "Triple Jump",
    eventNo: 6,
    id: 60737,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 26,
      name: "Triple Jump",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 50,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 14 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 14",
      maxAge: 13,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 14",
    },
    price: {
      id: 1773,
      fee: 0.59,
      saleFee: 0.59,
      price: 8.59,
      salePrice: 4.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U12 to U16",
      description: "U12 to U16",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 1,
      waiting: 0,
    },
  },
  {
    maxGroup: 11522,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 3,
      max: 25,
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 2,
      id: 11522,
      eventNo: 6,
      bibSortNo: "",
      name: "Triple Jump",
      notes: null,
    },
    eventGroup: "Triple Jump",
    eventNo: 6,
    id: 60738,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 26,
      name: "Triple Jump",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 57,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 16 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 16",
      maxAge: 15,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 16",
    },
    price: {
      id: 1773,
      fee: 0.59,
      saleFee: 0.59,
      price: 8.59,
      salePrice: 4.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U12 to U16",
      description: "U12 to U16",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 5,
      waiting: 0,
    },
  },
  {
    maxGroup: 11522,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 3,
      max: 25,
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 2,
      id: 11522,
      eventNo: 6,
      bibSortNo: "",
      name: "Triple Jump",
      notes: null,
    },
    eventGroup: "Triple Jump",
    eventNo: 6,
    id: 60739,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 26,
      name: "Triple Jump",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 83,
      minAge: 14,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 15 3112",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 1773,
      fee: 0.59,
      saleFee: 0.59,
      price: 8.59,
      salePrice: 4.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U12 to U16",
      description: "U12 to U16",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 7,
      waiting: 0,
    },
  },
  {
    maxGroup: 11522,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 3,
      max: 25,
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 2,
      id: 11522,
      eventNo: 6,
      bibSortNo: "",
      name: "Triple Jump",
      notes: null,
    },
    eventGroup: "Triple Jump",
    eventNo: 6,
    id: 60740,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 27,
      name: "Triple Jump",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 58,
      minAge: 16,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 17 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 1,
      waiting: 0,
    },
  },
  {
    maxGroup: 11522,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 3,
      max: 25,
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 2,
      id: 11522,
      eventNo: 6,
      bibSortNo: "",
      name: "Triple Jump",
      notes: null,
    },
    eventGroup: "Triple Jump",
    eventNo: 6,
    id: 60741,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 27,
      name: "Triple Jump",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 59,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 18 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 18",
      maxAge: 17,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 18",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 3,
      waiting: 0,
    },
  },
  {
    maxGroup: 11522,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 3,
      max: 25,
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 2,
      id: 11522,
      eventNo: 6,
      bibSortNo: "",
      name: "Triple Jump",
      notes: null,
    },
    eventGroup: "Triple Jump",
    eventNo: 6,
    id: 60742,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 27,
      name: "Triple Jump",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 60,
      minAge: 18,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 19 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 19",
      maxAge: 18,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 19",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 1,
      waiting: 0,
    },
  },
  {
    maxGroup: 11522,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 3,
      max: 25,
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 2,
      id: 11522,
      eventNo: 6,
      bibSortNo: "",
      name: "Triple Jump",
      notes: null,
    },
    eventGroup: "Triple Jump",
    eventNo: 6,
    id: 60743,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 27,
      name: "Triple Jump",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 50,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 14 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 14",
      maxAge: 13,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 14",
    },
    price: {
      id: 1773,
      fee: 0.59,
      saleFee: 0.59,
      price: 8.59,
      salePrice: 4.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U12 to U16",
      description: "U12 to U16",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11522,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 3,
      max: 25,
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 2,
      id: 11522,
      eventNo: 6,
      bibSortNo: "",
      name: "Triple Jump",
      notes: null,
    },
    eventGroup: "Triple Jump",
    eventNo: 6,
    id: 60744,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 27,
      name: "Triple Jump",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 57,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 16 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 16",
      maxAge: 15,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 16",
    },
    price: {
      id: 1773,
      fee: 0.59,
      saleFee: 0.59,
      price: 8.59,
      salePrice: 4.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U12 to U16",
      description: "U12 to U16",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 2,
      waiting: 0,
    },
  },
  {
    maxGroup: 11522,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 3,
      max: 25,
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 2,
      id: 11522,
      eventNo: 6,
      bibSortNo: "",
      name: "Triple Jump",
      notes: null,
    },
    eventGroup: "Triple Jump",
    eventNo: 6,
    id: 60745,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 27,
      name: "Triple Jump",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 83,
      minAge: 14,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 15 3112",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 1773,
      fee: 0.59,
      saleFee: 0.59,
      price: 8.59,
      salePrice: 4.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U12 to U16",
      description: "U12 to U16",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 4,
      waiting: 0,
    },
  },
  {
    maxGroup: 11535,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 7,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 8,
      id: 11535,
      eventNo: 8,
      bibSortNo: "",
      name: "Pole Vault",
      notes: null,
    },
    eventGroup: "Pole Vault",
    eventNo: 8,
    id: 60824,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 30,
      name: "Pole Vault",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 50,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 14 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 14",
      maxAge: 13,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 14",
    },
    price: {
      id: 1773,
      fee: 0.59,
      saleFee: 0.59,
      price: 8.59,
      salePrice: 4.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U12 to U16",
      description: "U12 to U16",
    },
    multiEventInfo: {
      id: 0,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 3,
      waiting: 0,
    },
  },
  {
    maxGroup: 11535,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 7,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 8,
      id: 11535,
      eventNo: 8,
      bibSortNo: "",
      name: "Pole Vault",
      notes: null,
    },
    eventGroup: "Pole Vault",
    eventNo: 8,
    id: 60826,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 30,
      name: "Pole Vault",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 57,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 16 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 16",
      maxAge: 15,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 16",
    },
    price: {
      id: 1773,
      fee: 0.59,
      saleFee: 0.59,
      price: 8.59,
      salePrice: 4.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U12 to U16",
      description: "U12 to U16",
    },
    multiEventInfo: {
      id: 0,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 4,
      waiting: 0,
    },
  },
  {
    maxGroup: 11535,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 7,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 8,
      id: 11535,
      eventNo: 8,
      bibSortNo: "",
      name: "Pole Vault",
      notes: null,
    },
    eventGroup: "Pole Vault",
    eventNo: 8,
    id: 60828,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 30,
      name: "Pole Vault",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 58,
      minAge: 16,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 17 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 0,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 5,
      waiting: 0,
    },
  },
  {
    maxGroup: 11535,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 7,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 8,
      id: 11535,
      eventNo: 8,
      bibSortNo: "",
      name: "Pole Vault",
      notes: null,
    },
    eventGroup: "Pole Vault",
    eventNo: 8,
    id: 60830,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 30,
      name: "Pole Vault",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 59,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 18 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 18",
      maxAge: 17,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 18",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 0,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11535,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 7,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 8,
      id: 11535,
      eventNo: 8,
      bibSortNo: "",
      name: "Pole Vault",
      notes: null,
    },
    eventGroup: "Pole Vault",
    eventNo: 8,
    id: 60832,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 30,
      name: "Pole Vault",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 60,
      minAge: 18,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 19 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 19",
      maxAge: 18,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 19",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 0,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 2,
      waiting: 0,
    },
  },
  {
    maxGroup: 11535,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 7,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 8,
      id: 11535,
      eventNo: 8,
      bibSortNo: "",
      name: "Pole Vault",
      notes: null,
    },
    eventGroup: "Pole Vault",
    eventNo: 8,
    id: 60834,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 30,
      name: "Pole Vault",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 83,
      minAge: 14,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 15 3112",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 1773,
      fee: 0.59,
      saleFee: 0.59,
      price: 8.59,
      salePrice: 4.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U12 to U16",
      description: "U12 to U16",
    },
    multiEventInfo: {
      id: 0,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11535,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 7,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 8,
      id: 11535,
      eventNo: 8,
      bibSortNo: "",
      name: "Pole Vault",
      notes: null,
    },
    eventGroup: "Pole Vault",
    eventNo: 8,
    id: 60823,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 31,
      name: "Pole Vault",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 49,
      minAge: 12,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 13 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 1773,
      fee: 0.59,
      saleFee: 0.59,
      price: 8.59,
      salePrice: 4.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U12 to U16",
      description: "U12 to U16",
    },
    multiEventInfo: {
      id: 0,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11535,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 7,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 8,
      id: 11535,
      eventNo: 8,
      bibSortNo: "",
      name: "Pole Vault",
      notes: null,
    },
    eventGroup: "Pole Vault",
    eventNo: 8,
    id: 60825,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 31,
      name: "Pole Vault",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 50,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 14 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 14",
      maxAge: 13,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 14",
    },
    price: {
      id: 1773,
      fee: 0.59,
      saleFee: 0.59,
      price: 8.59,
      salePrice: 4.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U12 to U16",
      description: "U12 to U16",
    },
    multiEventInfo: {
      id: 0,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 2,
      waiting: 0,
    },
  },
  {
    maxGroup: 11535,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 7,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 8,
      id: 11535,
      eventNo: 8,
      bibSortNo: "",
      name: "Pole Vault",
      notes: null,
    },
    eventGroup: "Pole Vault",
    eventNo: 8,
    id: 60827,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 31,
      name: "Pole Vault",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 57,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 16 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 16",
      maxAge: 15,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 16",
    },
    price: {
      id: 1773,
      fee: 0.59,
      saleFee: 0.59,
      price: 8.59,
      salePrice: 4.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U12 to U16",
      description: "U12 to U16",
    },
    multiEventInfo: {
      id: 0,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 5,
      waiting: 0,
    },
  },
  {
    maxGroup: 11535,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 7,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 8,
      id: 11535,
      eventNo: 8,
      bibSortNo: "",
      name: "Pole Vault",
      notes: null,
    },
    eventGroup: "Pole Vault",
    eventNo: 8,
    id: 60829,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 31,
      name: "Pole Vault",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 58,
      minAge: 16,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 17 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 0,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 5,
      waiting: 0,
    },
  },
  {
    maxGroup: 11535,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 7,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 8,
      id: 11535,
      eventNo: 8,
      bibSortNo: "",
      name: "Pole Vault",
      notes: null,
    },
    eventGroup: "Pole Vault",
    eventNo: 8,
    id: 60831,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 31,
      name: "Pole Vault",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 59,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 18 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 18",
      maxAge: 17,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 18",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 0,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 1,
      waiting: 0,
    },
  },
  {
    maxGroup: 11535,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 7,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 8,
      id: 11535,
      eventNo: 8,
      bibSortNo: "",
      name: "Pole Vault",
      notes: null,
    },
    eventGroup: "Pole Vault",
    eventNo: 8,
    id: 60833,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 31,
      name: "Pole Vault",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 60,
      minAge: 18,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 19 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 19",
      maxAge: 18,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 19",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 0,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11535,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
        checkInMins: 60,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 7,
      cardType: "H",
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 8,
      id: 11535,
      eventNo: 8,
      bibSortNo: "",
      name: "Pole Vault",
      notes: null,
    },
    eventGroup: "Pole Vault",
    eventNo: 8,
    id: 60835,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T11:00:00",
    sortDateTime: "2023-03-12T11:00:00",
    event: {
      id: 31,
      name: "Pole Vault",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 83,
      minAge: 14,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 15 3112",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 1773,
      fee: 0.59,
      saleFee: 0.59,
      price: 8.59,
      salePrice: 4.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U12 to U16",
      description: "U12 to U16",
    },
    multiEventInfo: {
      id: 0,
    },
    uom: {
      id: 12,
      type: "H",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 3,
      waiting: 0,
    },
  },
  {
    maxGroup: 11521,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 30,
      unique: [
        {
          e: 36,
        },
        {
          e: 48,
        },
      ],
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 3,
      id: 11521,
      eventNo: 7,
      bibSortNo: "",
      name: "Shot Putt",
      notes: null,
    },
    eventGroup: "Shot Putt",
    eventNo: 7,
    id: 60722,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T12:30:00",
    sortDateTime: "2023-03-12T12:30:00",
    event: {
      id: 20,
      name: "Shotput",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 58,
      minAge: 16,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 17 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 9,
      waiting: 0,
    },
  },
  {
    maxGroup: 11521,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 30,
      unique: [
        {
          e: 36,
        },
        {
          e: 48,
        },
      ],
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 3,
      id: 11521,
      eventNo: 7,
      bibSortNo: "",
      name: "Shot Putt",
      notes: null,
    },
    eventGroup: "Shot Putt",
    eventNo: 7,
    id: 60723,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T12:30:00",
    sortDateTime: "2023-03-12T12:30:00",
    event: {
      id: 20,
      name: "Shotput",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 60,
      minAge: 18,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 19 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 19",
      maxAge: 18,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 19",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 4,
      waiting: 0,
    },
  },
  {
    maxGroup: 11521,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 30,
      unique: [
        {
          e: 36,
        },
        {
          e: 48,
        },
      ],
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 3,
      id: 11521,
      eventNo: 7,
      bibSortNo: "",
      name: "Shot Putt",
      notes: null,
    },
    eventGroup: "Shot Putt",
    eventNo: 7,
    id: 60724,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T12:30:00",
    sortDateTime: "2023-03-12T12:30:00",
    event: {
      id: 20,
      name: "Shotput",
      eventnameextra: "",
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 59,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 18 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 18",
      maxAge: 17,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 18",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 2,
      waiting: 0,
    },
  },
  {
    maxGroup: 11521,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 30,
      unique: [
        {
          e: 37,
        },
        {
          e: 49,
        },
      ],
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 3,
      id: 11521,
      eventNo: 7,
      bibSortNo: "",
      name: "Shot Putt",
      notes: null,
    },
    eventGroup: "Shot Putt",
    eventNo: 7,
    id: 60725,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T12:30:00",
    sortDateTime: "2023-03-12T12:30:00",
    event: {
      id: 21,
      name: "Shotput",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 58,
      minAge: 16,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 17 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 2,
      waiting: 0,
    },
  },
  {
    maxGroup: 11521,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 30,
      unique: [
        {
          e: 37,
        },
        {
          e: 49,
        },
      ],
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 3,
      id: 11521,
      eventNo: 7,
      bibSortNo: "",
      name: "Shot Putt",
      notes: null,
    },
    eventGroup: "Shot Putt",
    eventNo: 7,
    id: 60726,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T12:30:00",
    sortDateTime: "2023-03-12T12:30:00",
    event: {
      id: 21,
      name: "Shotput",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 60,
      minAge: 18,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 19 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 19",
      maxAge: 18,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 19",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 2,
      waiting: 0,
    },
  },
  {
    maxGroup: 11521,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      min: 1,
      max: 30,
      unique: [
        {
          e: 37,
        },
        {
          e: 49,
        },
      ],
    },
    eventGroupSummary: {
      type: "F",
      typeNo: 3,
      id: 11521,
      eventNo: 7,
      bibSortNo: "",
      name: "Shot Putt",
      notes: null,
    },
    eventGroup: "Shot Putt",
    eventNo: 7,
    id: 60727,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T12:30:00",
    sortDateTime: "2023-03-12T12:30:00",
    event: {
      id: 21,
      name: "Shotput",
      eventnameextra: "",
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 59,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 18 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 18",
      maxAge: 17,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 18",
    },
    price: {
      id: 1774,
      fee: 0.59,
      saleFee: 0.59,
      price: 10.59,
      salePrice: 5.3,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "U17 to U19",
      description: "U17 to U19",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 5,
      type: "D",
      options: [
        {
          pattern: 0.99,
          text: "metres",
          short: "mt",
        },
      ],
    },
    entryCount: {
      total: 5,
      waiting: 0,
    },
  },
  {
    maxGroup: 11517,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 47,
          minAge: 10,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 11 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 11",
          maxAge: 10,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 11",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 4,
      id: 11517,
      eventNo: 4,
      bibSortNo: "",
      name: "Even Ages Girls",
      notes: null,
    },
    eventGroup: "Even Ages Girls",
    eventNo: 4,
    id: 60750,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 299,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 48,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 12 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 12",
      maxAge: 11,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 12",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11517,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 49,
          minAge: 12,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 13 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 13",
          maxAge: 12,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 13",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 4,
      id: 11517,
      eventNo: 4,
      bibSortNo: "",
      name: "Even Ages Girls",
      notes: null,
    },
    eventGroup: "Even Ages Girls",
    eventNo: 4,
    id: 60751,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 299,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 50,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 14 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 14",
      maxAge: 13,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 14",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11517,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 83,
          minAge: 14,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 15 3112",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 15",
          maxAge: 14,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 15",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 4,
      id: 11517,
      eventNo: 4,
      bibSortNo: "",
      name: "Even Ages Girls",
      notes: null,
    },
    eventGroup: "Even Ages Girls",
    eventNo: 4,
    id: 60752,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 299,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 57,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 16 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 16",
      maxAge: 15,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 16",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11517,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 58,
          minAge: 16,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 17 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 17",
          maxAge: 16,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 17",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 4,
      id: 11517,
      eventNo: 4,
      bibSortNo: "",
      name: "Even Ages Girls",
      notes: null,
    },
    eventGroup: "Even Ages Girls",
    eventNo: 4,
    id: 60753,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 299,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 59,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 18 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 18",
      maxAge: 17,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 18",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11516,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 49,
          minAge: 12,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 13 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 13",
          maxAge: 12,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 13",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: null,
        to: null,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 11516,
      eventNo: 3,
      bibSortNo: "",
      name: "Even Ages Boys",
      notes: null,
    },
    eventGroup: "Even Ages Boys",
    eventNo: 3,
    id: 60759,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 300,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 50,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 14 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 14",
      maxAge: 13,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 14",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11516,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 83,
          minAge: 14,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 15 3112",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 15",
          maxAge: 14,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 15",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: null,
        to: null,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 11516,
      eventNo: 3,
      bibSortNo: "",
      name: "Even Ages Boys",
      notes: null,
    },
    eventGroup: "Even Ages Boys",
    eventNo: 3,
    id: 60760,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 300,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 57,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 16 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 16",
      maxAge: 15,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 16",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11516,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 58,
          minAge: 16,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 17 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 17",
          maxAge: 16,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 17",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: null,
        to: null,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 11516,
      eventNo: 3,
      bibSortNo: "",
      name: "Even Ages Boys",
      notes: null,
    },
    eventGroup: "Even Ages Boys",
    eventNo: 3,
    id: 60761,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 300,
      name: "4x200 (A Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 59,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 18 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 18",
      maxAge: 17,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 18",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11517,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 8,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 47,
          minAge: 10,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 11 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 11",
          maxAge: 10,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 11",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 4,
      id: 11517,
      eventNo: 4,
      bibSortNo: "",
      name: "Even Ages Girls",
      notes: null,
    },
    eventGroup: "Even Ages Girls",
    eventNo: 4,
    id: 60766,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 301,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 48,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 12 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 12",
      maxAge: 11,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 12",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11517,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 49,
          minAge: 12,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 13 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 13",
          maxAge: 12,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 13",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 4,
      id: 11517,
      eventNo: 4,
      bibSortNo: "",
      name: "Even Ages Girls",
      notes: null,
    },
    eventGroup: "Even Ages Girls",
    eventNo: 4,
    id: 60767,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 301,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 50,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 14 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 14",
      maxAge: 13,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 14",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11517,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 83,
          minAge: 14,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 15 3112",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 15",
          maxAge: 14,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 15",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 4,
      id: 11517,
      eventNo: 4,
      bibSortNo: "",
      name: "Even Ages Girls",
      notes: null,
    },
    eventGroup: "Even Ages Girls",
    eventNo: 4,
    id: 60768,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 301,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 57,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 16 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 16",
      maxAge: 15,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 16",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11517,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: "",
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 58,
          minAge: 16,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 17 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 17",
          maxAge: 16,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 17",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 4,
      id: 11517,
      eventNo: 4,
      bibSortNo: "",
      name: "Even Ages Girls",
      notes: null,
    },
    eventGroup: "Even Ages Girls",
    eventNo: 4,
    id: 60769,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 301,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 59,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 18 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 18",
      maxAge: 17,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 18",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11516,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 47,
          minAge: 10,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 11 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 11",
          maxAge: 10,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 11",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: null,
        to: null,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 11516,
      eventNo: 3,
      bibSortNo: "",
      name: "Even Ages Boys",
      notes: null,
    },
    eventGroup: "Even Ages Boys",
    eventNo: 3,
    id: 60774,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 302,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 48,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 12 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 12",
      maxAge: 11,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 12",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11516,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 49,
          minAge: 12,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 13 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 13",
          maxAge: 12,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 13",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: null,
        to: null,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 11516,
      eventNo: 3,
      bibSortNo: "",
      name: "Even Ages Boys",
      notes: null,
    },
    eventGroup: "Even Ages Boys",
    eventNo: 3,
    id: 60775,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 302,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 50,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 14 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 14",
      maxAge: 13,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 14",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11516,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 83,
          minAge: 14,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 15 3112",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 15",
          maxAge: 14,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 15",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: null,
        to: null,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 11516,
      eventNo: 3,
      bibSortNo: "",
      name: "Even Ages Boys",
      notes: null,
    },
    eventGroup: "Even Ages Boys",
    eventNo: 3,
    id: 60776,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 302,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 57,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 16 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 16",
      maxAge: 15,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 16",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11516,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 58,
          minAge: 16,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 17 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 17",
          maxAge: 16,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 17",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: null,
        to: null,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 11516,
      eventNo: 3,
      bibSortNo: "",
      name: "Even Ages Boys",
      notes: null,
    },
    eventGroup: "Even Ages Boys",
    eventNo: 3,
    id: 60777,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 302,
      name: "4x200 (B Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 59,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 18 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 18",
      maxAge: 17,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 18",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11517,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 47,
          minAge: 10,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 11 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 11",
          maxAge: 10,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 11",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 4,
      id: 11517,
      eventNo: 4,
      bibSortNo: "",
      name: "Even Ages Girls",
      notes: null,
    },
    eventGroup: "Even Ages Girls",
    eventNo: 4,
    id: 60782,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 303,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 48,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 12 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 12",
      maxAge: 11,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 12",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11517,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 49,
          minAge: 12,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 13 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 13",
          maxAge: 12,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 13",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 4,
      id: 11517,
      eventNo: 4,
      bibSortNo: "",
      name: "Even Ages Girls",
      notes: null,
    },
    eventGroup: "Even Ages Girls",
    eventNo: 4,
    id: 60783,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 303,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 50,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 14 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 14",
      maxAge: 13,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 14",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11517,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 83,
          minAge: 14,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 15 3112",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 15",
          maxAge: 14,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 15",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 4,
      id: 11517,
      eventNo: 4,
      bibSortNo: "",
      name: "Even Ages Girls",
      notes: null,
    },
    eventGroup: "Even Ages Girls",
    eventNo: 4,
    id: 60784,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 303,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 57,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 16 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 16",
      maxAge: 15,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 16",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11517,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 58,
          minAge: 16,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 17 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 17",
          maxAge: 16,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 17",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: -1,
        to: -1,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 4,
      id: 11517,
      eventNo: 4,
      bibSortNo: "",
      name: "Even Ages Girls",
      notes: null,
    },
    eventGroup: "Even Ages Girls",
    eventNo: 4,
    id: 60785,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 303,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 59,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 18 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 18",
      maxAge: 17,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 18",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11516,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 47,
          minAge: 10,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 11 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 11",
          maxAge: 10,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 11",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: null,
        to: null,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 11516,
      eventNo: 3,
      bibSortNo: "",
      name: "Even Ages Boys",
      notes: null,
    },
    eventGroup: "Even Ages Boys",
    eventNo: 3,
    id: 60790,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 304,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 48,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 12 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 12",
      maxAge: 11,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 12",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11516,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 49,
          minAge: 12,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 13 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 13",
          maxAge: 12,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 13",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: null,
        to: null,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 11516,
      eventNo: 3,
      bibSortNo: "",
      name: "Even Ages Boys",
      notes: null,
    },
    eventGroup: "Even Ages Boys",
    eventNo: 3,
    id: 60791,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 304,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 50,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 14 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 14",
      maxAge: 13,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 14",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11516,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 2,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 83,
          minAge: 14,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 15 3112",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 15",
          maxAge: 14,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 15",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: null,
        to: null,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 11516,
      eventNo: 3,
      bibSortNo: "",
      name: "Even Ages Boys",
      notes: null,
    },
    eventGroup: "Even Ages Boys",
    eventNo: 3,
    id: 60792,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 304,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 57,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 16 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 16",
      maxAge: 15,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 16",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
  {
    maxGroup: 11516,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: true,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: true,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: {
        useLanes: "A",
        heatDurationMins: 0,
      },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [
        {
          id: 58,
          minAge: 16,
          minAtDay: 31,
          minAtMonth: 12,
          keyName: "Under 17 Eire",
          options: [
            {
              aocode: "IRL",
              default: true,
              base: 1,
            },
            {
              aocode: "ANI",
              default: true,
              base: 1,
            },
          ],
          name: "Under 17",
          maxAge: 16,
          maxAtDay: 31,
          maxAtMonth: 12,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 17",
        },
      ],
      singleAge: false,
      security: {
        clubs: [],
        counties: [],
        regions: [],
      },
      athleteSecurity: {
        clubs: [],
      },
      checkIn: {
        from: null,
        to: null,
        seedOnEntries: false,
      },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: {
        trialInfo: "",
        reportInfo: "",
      },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
        },
      },
      secondarySpend: {
        isParent: false,
        parentCeid: 0,
      },
    },
    eoptions: {
      maxInHeat: 16,
      min: 60,
      max: 240,
      unit: "mins",
      eventTeam: {
        maxInHeat: 16,
        min: 4,
        max: 6,
      },
    },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 11516,
      eventNo: 3,
      bibSortNo: "",
      name: "Even Ages Boys",
      notes: null,
    },
    eventGroup: "Even Ages Boys",
    eventNo: 3,
    id: 60793,
    compId: 720,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-12T13:00:00",
    sortDateTime: "2023-03-12T13:00:00",
    event: {
      id: 304,
      name: "4x200 (C Team)",
      eventnameextra: "",
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 59,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 18 Eire",
      options: [
        {
          aocode: "IRL",
          default: true,
          base: 1,
        },
        {
          aocode: "ANI",
          default: true,
          base: 1,
        },
      ],
      name: "Under 18",
      maxAge: 17,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 18",
    },
    price: {
      id: 1775,
      fee: 1.05,
      saleFee: 0.62,
      price: 21.05,
      salePrice: 12.32,
      saleEndDate: "2023-03-02",
      options: {
        displayFee: true,
        feeIncluded: true,
        freeEntry: false,
        discount: {
          count: 0,
          price: 0,
          validToDate: "",
        },
      },
      name: "",
      description: "Relay",
    },
    multiEventInfo: {
      id: 740,
    },
    uom: {
      id: 3,
      type: "T",
      options: [
        {
          pattern: "m.ss.SS",
          text: "mins",
          short: "m",
        },
      ],
    },
    entryCount: {
      total: 0,
      waiting: 0,
    },
  },
] as any as ICompEvent[];
