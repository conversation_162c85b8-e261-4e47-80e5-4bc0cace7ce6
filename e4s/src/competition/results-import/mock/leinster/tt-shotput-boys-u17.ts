import { ITimeTronicsEvent } from "../../results-import-models-timetronics";

export const ttShotputBoysU17: ITimeTronicsEvent = {
  abbreviation: "SP (5kg)",
  categories: [{ abbreviation: "BU17", id: 116, name: "Boys U17" }],
  eventtype: {
    abbreviation: "",
    athletesquantity: 1,
    code: "Shot Putt Indoor (5kg)",
    combinedeventsquantity: 0,
    distance: 0,
    fieldtype: "H",
    id: 410,
    implement: 5,
    name: "",
    shortcode: "SP (5kg)",
    windmode: "N",
    windtime: "",
  },
  id: 1027,
  info: "CR: 14.80m - <PERSON><PERSON><PERSON>, North Westmeath A.C. (2013) ",
  name: "U17 Boys Shot Put (5kg)",
  rounds: [
    {
      abbreviation: "",
      datescheduled: "2023-03-12",
      heats: [
        {
          attempts: 6,
          height: 0,
          id: 1769,
          info: "0",
          numberofparticipations: 2,
          official: "11:52:17",
          scheduled: "10:30:00",
          seqno: 1,
          status: 5,
        },
      ],
      id: 1570,
      info: "",
      name: "*",
      numberofparticipationsround: 2,
      official: "11:52:17",
      scheduled: "10:30:00",
      seqno: 1,
      session: "Shot Put",
      status: 5,
    },
  ],
  seqno: 46,
  status: 5,
};
