import { IResultsImportEventGroupSummary } from "../results-import-models";
import { ITimeTronicsEvent } from "../results-import-models-timetronics";

export const targetResultsImportEventGroupSummariesMock: IResultsImportEventGroupSummary[] =
  [
    {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      genders: ["F", "M"],
      eventName: "1500m",
    },
    {
      type: "T",
      typeNo: 3,
      id: 5321,
      eventNo: 3,
      bibSortNo: "",
      name: "400m Women",
      notes: "",
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F"],
      eventName: "400m",
    },
    {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F", "M"],
      eventName: "800m",
    },
    {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F", "M"],
      eventName: "Shotput",
    },
    {
      type: "T",
      typeNo: 5,
      id: 5325,
      eventNo: 5,
      bibSortNo: "",
      name: "400m Men",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["M"],
      eventName: "400m",
    },
    {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F", "M"],
      eventName: "4 x 200",
    },
    {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["M", "F"],
      eventName: "Donation",
    },
  ] as any as IResultsImportEventGroupSummary[];

export const bestGuessTimeTronicsEventMock1500: ITimeTronicsEvent = {
  abbreviation: "1500m",
  categories: [{ abbreviation: "SEN M", id: 63, name: "Senior Men" }],
  eventtype: {
    abbreviation: "",
    athletesquantity: 1,
    code: "1500m",
    combinedeventsquantity: 0,
    distance: 1500,
    fieldtype: " ",
    id: 264,
    implement: 0,
    name: "",
    shortcode: "1500m",
    windmode: "N",
    windtime: "",
  },
  id: 2,
  info: "NR: 3.35.4 - Marcus O’Sullivan (1988)  CR: 3.41.36 - Andrew Coscoran, Star of the Sea A.C. (2020)  NL: 3:37.95 - Andrew Coscoran",
  name: "Senior Men 1500m",
  rounds: [
    {
      abbreviation: "",
      datescheduled: "2023-02-18",
      heats: [
        {
          attempts: 0,
          height: 0,
          id: 84,
          info: "0",
          numberofparticipations: 10,
          official: "16:09:05",
          scheduled: "15:50:00",
          seqno: 1,
          status: 5,
        },
        {
          attempts: 0,
          height: 0,
          id: 85,
          info: "0",
          numberofparticipations: 10,
          official: "16:09:05",
          scheduled: "15:55:00",
          seqno: 2,
          status: 5,
        },
      ],
      id: 3,
      info: "5;2",
      name: "Heat",
      numberofparticipationsround: 20,
      official: "16:09:05",
      scheduled: "15:50:00",
      seqno: 1,
      session: "Distance Day 1",
      status: 5,
    },
    {
      abbreviation: "",
      datescheduled: "2023-02-19",
      heats: [
        {
          attempts: 0,
          height: 0,
          id: 88,
          info: "0",
          numberofparticipations: 12,
          official: "13:24:39",
          scheduled: "13:20:00",
          seqno: 1,
          status: 5,
        },
      ],
      id: 4,
      info: "",
      name: "Final",
      numberofparticipationsround: 12,
      official: "13:24:39",
      scheduled: "13:20:00",
      seqno: 2,
      session: "Distance Day 2",
      status: 5,
    },
  ],
  seqno: 2,
  status: 5,
};

export const bestGuessTimeTronicsEventMockHighJump: ITimeTronicsEvent = {
  abbreviation: "HJ",
  categories: [{ abbreviation: "SEN W", id: 64, name: "Senior Women" }],
  eventtype: {
    abbreviation: "",
    athletesquantity: 1,
    code: "High Jump",
    combinedeventsquantity: 0,
    distance: 0,
    fieldtype: "V",
    id: 243,
    implement: 0,
    name: "",
    shortcode: "HJ",
    windmode: "N",
    windtime: "",
  },
  id: 23,
  info: "NR: 1.93m - Deirdre Ryan (2009)  CR: 1.86m - Sommer Lecky, Finn Valley (2019) NL: 1.79m - Kate O'Connor ",
  name: "Senior Women High Jump",
  rounds: [
    {
      abbreviation: "",
      datescheduled: "2023-02-19",
      heats: [
        {
          attempts: 0,
          height: 0,
          id: 35,
          info: "0",
          numberofparticipations: 8,
          official: "14:28:03",
          scheduled: "13:15:00",
          seqno: 1,
          status: 5,
        },
      ],
      id: 38,
      info: "",
      name: "*",
      numberofparticipationsround: 8,
      official: "14:28:03",
      scheduled: "13:15:00",
      seqno: 1,
      session: "Jumps Day 2",
      status: 5,
    },
  ],
  seqno: 19,
  status: 5,
};

export const bestGuessTimeTronicsEventMockShotPut: ITimeTronicsEvent = {
  abbreviation: "SP",
  categories: [{ abbreviation: "SEN M", id: 63, name: "Senior Men" }],
  eventtype: {
    abbreviation: "",
    athletesquantity: 1,
    code: "Shot Put Indoor (7.26kg)",
    combinedeventsquantity: 0,
    distance: 0,
    fieldtype: "H",
    id: 270,
    implement: 7.26,
    name: "",
    shortcode: "SP (7.26kg)I",
    windmode: "N",
    windtime: "",
  },
  id: 31,
  info: "NR/NL: 20.16m - Eric Favors (2023)  CR: 19.48m - Eric Favors (2022) ",
  name: "Senior Men Shot Put (7.26kg)",
  rounds: [
    {
      abbreviation: "",
      datescheduled: "2023-02-18",
      heats: [
        {
          attempts: 6,
          height: 0,
          id: 28,
          info: "0",
          numberofparticipations: 10,
          official: "14:14:37",
          scheduled: "13:00:00",
          seqno: 1,
          status: 5,
        },
      ],
      id: 46,
      info: "",
      name: "*",
      numberofparticipationsround: 10,
      official: "14:14:37",
      scheduled: "13:00:00",
      seqno: 1,
      session: "Throws Day 1",
      status: 5,
    },
  ],
  seqno: 26,
  status: 5,
};

export const bestGuessTimeTronicsEventMock400Women: ITimeTronicsEvent = {
  abbreviation: "400m I",
  categories: [{ abbreviation: "SEN W", id: 64, name: "Senior Women" }],
  eventtype: {
    abbreviation: "",
    athletesquantity: 1,
    code: "400m Indoor",
    combinedeventsquantity: 0,
    distance: 400,
    fieldtype: " ",
    id: 283,
    implement: 0,
    name: "",
    shortcode: "400m I",
    windmode: "N",
    windtime: "",
  },
  id: 10,
  info: "NR/NL: 50.45a - Rhasidat Adeleke (2023)  CR: 51.75 - Phil Healy, Bandon (2022) ",
  name: "Senior Women 400m ",
  rounds: [
    {
      abbreviation: "",
      datescheduled: "2023-02-18",
      heats: [
        {
          attempts: 0,
          height: 0,
          id: 59,
          info: "0",
          numberofparticipations: 5,
          official: "14:23:28",
          scheduled: "13:30:00",
          seqno: 1,
          status: 5,
        },
        {
          attempts: 0,
          height: 0,
          id: 60,
          info: "0",
          numberofparticipations: 5,
          official: "14:23:28",
          scheduled: "13:35:00",
          seqno: 2,
          status: 5,
        },
        {
          attempts: 0,
          height: 0,
          id: 61,
          info: "0",
          numberofparticipations: 5,
          official: "14:23:28",
          scheduled: "13:40:00",
          seqno: 3,
          status: 5,
        },
        {
          attempts: 0,
          height: 0,
          id: 62,
          info: "0",
          numberofparticipations: 5,
          official: "14:23:28",
          scheduled: "13:45:00",
          seqno: 4,
          status: 5,
        },
      ],
      id: 15,
      info: "First 3 to final",
      name: "Heats",
      numberofparticipationsround: 20,
      official: "14:23:28",
      scheduled: "13:30:00",
      seqno: 1,
      session: "400m Day 1",
      status: 5,
    },
    {
      abbreviation: "",
      datescheduled: "2023-02-18",
      heats: [
        {
          attempts: 0,
          height: 0,
          id: 72,
          info: "0",
          numberofparticipations: 6,
          official: "16:37:12",
          scheduled: "16:30:00",
          seqno: 1,
          status: 5,
        },
        {
          attempts: 0,
          height: 0,
          id: 73,
          info: "0",
          numberofparticipations: 6,
          official: "16:37:12",
          scheduled: "16:35:00",
          seqno: 2,
          status: 5,
        },
      ],
      id: 16,
      info: "First 3 to Final",
      name: "Semi",
      numberofparticipationsround: 12,
      official: "16:37:12",
      scheduled: "16:30:00",
      seqno: 2,
      session: "400m Day 1",
      status: 5,
    },
    {
      abbreviation: "",
      datescheduled: "2023-02-19",
      heats: [
        {
          attempts: 0,
          height: 0,
          id: 89,
          info: "0",
          numberofparticipations: 6,
          official: "13:36:14",
          scheduled: "13:35:00",
          seqno: 1,
          status: 5,
        },
      ],
      id: 17,
      info: "",
      name: "Final",
      numberofparticipationsround: 6,
      official: "13:36:14",
      scheduled: "13:35:00",
      seqno: 3,
      session: "400m Day 2",
      status: 5,
    },
  ],
  seqno: 8,
  status: 5,
};
