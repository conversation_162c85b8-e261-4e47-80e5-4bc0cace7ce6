import { ICompEvent } from "../../../compevent/compevent-models";

export const compEventsMock354: ICompEvent[] =[
  {
    maxGroup: 5321,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 5321,
      eventNo: 3,
      bibSortNo: "",
      name: "400m Women",
      notes: "",
    },
    eventGroup: "400m Women",
    eventNo: 3,
    id: 37317,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 10, name: "400m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 1,
      minAge: 9,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 11",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 11",
      maxAge: 10,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 11",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5321,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 5321,
      eventNo: 3,
      bibSortNo: "",
      name: "400m Women",
      notes: "",
    },
    eventGroup: "400m Women",
    eventNo: 3,
    id: 37318,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 10, name: "400m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 2,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 13",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5321,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 5321,
      eventNo: 3,
      bibSortNo: "",
      name: "400m Women",
      notes: "",
    },
    eventGroup: "400m Women",
    eventNo: 3,
    id: 37319,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 10, name: "400m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 3,
      minAge: 6,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 9",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 9",
      maxAge: 8,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 9",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5321,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 5321,
      eventNo: 3,
      bibSortNo: "",
      name: "400m Women",
      notes: "",
    },
    eventGroup: "400m Women",
    eventNo: 3,
    id: 37320,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 10, name: "400m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 8,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 17",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5321,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 5321,
      eventNo: 3,
      bibSortNo: "",
      name: "400m Women",
      notes: "",
    },
    eventGroup: "400m Women",
    eventNo: 3,
    id: 37321,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 10, name: "400m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 9,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 15",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5321,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 5321,
      eventNo: 3,
      bibSortNo: "",
      name: "400m Women",
      notes: "",
    },
    eventGroup: "400m Women",
    eventNo: 3,
    id: 37322,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 10, name: "400m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 11,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 20",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 20",
      maxAge: 19,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 20",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5321,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 5321,
      eventNo: 3,
      bibSortNo: "",
      name: "400m Women",
      notes: "",
    },
    eventGroup: "400m Women",
    eventNo: 3,
    id: 37323,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 10, name: "400m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 12,
      minAge: 20,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 23",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Under 23",
      maxAge: 22,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 23",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5321,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 5321,
      eventNo: 3,
      bibSortNo: "",
      name: "400m Women",
      notes: "",
    },
    eventGroup: "400m Women",
    eventNo: 3,
    id: 37324,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 10, name: "400m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 13,
      minAge: 23,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Senior",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Senior",
      maxAge: 34,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Senior",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5321,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 5321,
      eventNo: 3,
      bibSortNo: "",
      name: "400m Women",
      notes: "",
    },
    eventGroup: "400m Women",
    eventNo: 3,
    id: 37325,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 10, name: "400m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 14,
      minAge: 35,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Masters",
      options: [
        { aocode: "EA", default: true, base: 2 },
        { aocode: "AAI", default: false, base: 2 },
        { aocode: "IRL", default: false, base: 2 },
      ],
      name: "Masters",
      maxAge: 100,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Masters",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5321,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 3,
      id: 5321,
      eventNo: 3,
      bibSortNo: "",
      name: "400m Women",
      notes: "",
    },
    eventGroup: "400m Women",
    eventNo: 3,
    id: 37326,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 10, name: "400m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 209,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 9,
      keyName: "Under 13 last year",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: -1,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5325,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 5,
      id: 5325,
      eventNo: 5,
      bibSortNo: "",
      name: "400m Men",
      notes: null,
    },
    eventGroup: "400m Men",
    eventNo: 5,
    id: 37327,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T01:00:00",
    sortDateTime: "2023-03-28T01:00:00",
    event: { id: 11, name: "400m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 1,
      minAge: 9,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 11",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 11",
      maxAge: 10,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 11",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5325,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 5,
      id: 5325,
      eventNo: 5,
      bibSortNo: "",
      name: "400m Men",
      notes: null,
    },
    eventGroup: "400m Men",
    eventNo: 5,
    id: 37328,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T01:00:00",
    sortDateTime: "2023-03-28T01:00:00",
    event: { id: 11, name: "400m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 2,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 13",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5325,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 5,
      id: 5325,
      eventNo: 5,
      bibSortNo: "",
      name: "400m Men",
      notes: null,
    },
    eventGroup: "400m Men",
    eventNo: 5,
    id: 37329,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T01:00:00",
    sortDateTime: "2023-03-28T01:00:00",
    event: { id: 11, name: "400m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 3,
      minAge: 6,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 9",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 9",
      maxAge: 8,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 9",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5325,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 5,
      id: 5325,
      eventNo: 5,
      bibSortNo: "",
      name: "400m Men",
      notes: null,
    },
    eventGroup: "400m Men",
    eventNo: 5,
    id: 37330,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T01:00:00",
    sortDateTime: "2023-03-28T01:00:00",
    event: { id: 11, name: "400m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 8,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 17",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5325,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 5,
      id: 5325,
      eventNo: 5,
      bibSortNo: "",
      name: "400m Men",
      notes: null,
    },
    eventGroup: "400m Men",
    eventNo: 5,
    id: 37331,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T01:00:00",
    sortDateTime: "2023-03-28T01:00:00",
    event: { id: 11, name: "400m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 9,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 15",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5325,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 5,
      id: 5325,
      eventNo: 5,
      bibSortNo: "",
      name: "400m Men",
      notes: null,
    },
    eventGroup: "400m Men",
    eventNo: 5,
    id: 37332,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T01:00:00",
    sortDateTime: "2023-03-28T01:00:00",
    event: { id: 11, name: "400m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 11,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 20",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 20",
      maxAge: 19,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 20",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5325,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 5,
      id: 5325,
      eventNo: 5,
      bibSortNo: "",
      name: "400m Men",
      notes: null,
    },
    eventGroup: "400m Men",
    eventNo: 5,
    id: 37333,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T01:00:00",
    sortDateTime: "2023-03-28T01:00:00",
    event: { id: 11, name: "400m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 12,
      minAge: 20,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 23",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Under 23",
      maxAge: 22,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 23",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5325,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 5,
      id: 5325,
      eventNo: 5,
      bibSortNo: "",
      name: "400m Men",
      notes: null,
    },
    eventGroup: "400m Men",
    eventNo: 5,
    id: 37334,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T01:00:00",
    sortDateTime: "2023-03-28T01:00:00",
    event: { id: 11, name: "400m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 13,
      minAge: 23,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Senior",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Senior",
      maxAge: 34,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Senior",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5325,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 5,
      id: 5325,
      eventNo: 5,
      bibSortNo: "",
      name: "400m Men",
      notes: null,
    },
    eventGroup: "400m Men",
    eventNo: 5,
    id: 37335,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T01:00:00",
    sortDateTime: "2023-03-28T01:00:00",
    event: { id: 11, name: "400m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 14,
      minAge: 35,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Masters",
      options: [
        { aocode: "EA", default: true, base: 2 },
        { aocode: "AAI", default: false, base: 2 },
        { aocode: "IRL", default: false, base: 2 },
      ],
      name: "Masters",
      maxAge: 100,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Masters",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5325,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "", eventNo: 0 },
        seeded: false,
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 40, max: 120 },
    eventGroupSummary: {
      type: "T",
      typeNo: 5,
      id: 5325,
      eventNo: 5,
      bibSortNo: "",
      name: "400m Men",
      notes: null,
    },
    eventGroup: "400m Men",
    eventNo: 5,
    id: 37336,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T01:00:00",
    sortDateTime: "2023-03-28T01:00:00",
    event: { id: 11, name: "400m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 209,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 9,
      keyName: "Under 13 last year",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: -1,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37337,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 14,
      name: "1500m",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 1,
      minAge: 9,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 11",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 11",
      maxAge: 10,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 11",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37338,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 14,
      name: "1500m",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 2,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 13",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37339,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 14,
      name: "1500m",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 3,
      minAge: 6,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 9",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 9",
      maxAge: 8,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 9",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37340,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 14,
      name: "1500m",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 8,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 17",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37341,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 14,
      name: "1500m",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 9,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 15",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37342,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 14,
      name: "1500m",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 11,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 20",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 20",
      maxAge: 19,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 20",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37343,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 14,
      name: "1500m",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 12,
      minAge: 20,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 23",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Under 23",
      maxAge: 22,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 23",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37344,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 14,
      name: "1500m",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 13,
      minAge: 23,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Senior",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Senior",
      maxAge: 34,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Senior",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 1, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37345,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 14,
      name: "1500m",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 14,
      minAge: 35,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Masters",
      options: [
        { aocode: "EA", default: true, base: 2 },
        { aocode: "AAI", default: false, base: 2 },
        { aocode: "IRL", default: false, base: 2 },
      ],
      name: "Masters",
      maxAge: 100,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Masters",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37346,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 14,
      name: "1500m",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 209,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 9,
      keyName: "Under 13 last year",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: -1,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37347,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 15,
      name: "1500m",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 1,
      minAge: 9,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 11",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 11",
      maxAge: 10,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 11",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37348,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 15,
      name: "1500m",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 2,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 13",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37349,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 15,
      name: "1500m",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 3,
      minAge: 6,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 9",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 9",
      maxAge: 8,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 9",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37350,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 15,
      name: "1500m",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 8,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 17",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37351,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 15,
      name: "1500m",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 9,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 15",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37352,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 15,
      name: "1500m",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 11,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 20",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 20",
      maxAge: 19,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 20",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37353,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 15,
      name: "1500m",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 12,
      minAge: 20,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 23",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Under 23",
      maxAge: 22,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 23",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37354,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 15,
      name: "1500m",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 13,
      minAge: 23,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Senior",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Senior",
      maxAge: 34,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Senior",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37355,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 15,
      name: "1500m",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 14,
      minAge: 35,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Masters",
      options: [
        { aocode: "EA", default: true, base: 2 },
        { aocode: "AAI", default: false, base: 2 },
        { aocode: "IRL", default: false, base: 2 },
      ],
      name: "Masters",
      maxAge: 100,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Masters",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5320,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 16, min: 150, max: 420 },
    eventGroupSummary: {
      type: "T",
      typeNo: 1,
      id: 5320,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
    },
    eventGroup: "1500m",
    eventNo: 1,
    id: 37356,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 15,
      name: "1500m",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 209,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 9,
      keyName: "Under 13 last year",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: -1,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37357,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 16, name: "800m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 1,
      minAge: 9,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 11",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 11",
      maxAge: 10,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 11",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37358,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 16, name: "800m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 2,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 13",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37359,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 16, name: "800m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 3,
      minAge: 6,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 9",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 9",
      maxAge: 8,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 9",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37360,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 16, name: "800m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 8,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 17",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37361,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 16, name: "800m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 9,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 15",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37362,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 16, name: "800m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 11,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 20",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 20",
      maxAge: 19,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 20",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37363,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 16, name: "800m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 12,
      minAge: 20,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 23",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Under 23",
      maxAge: 22,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 23",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37364,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 16, name: "800m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 13,
      minAge: 23,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Senior",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Senior",
      maxAge: 34,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Senior",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37365,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 16, name: "800m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 14,
      minAge: 35,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Masters",
      options: [
        { aocode: "EA", default: true, base: 2 },
        { aocode: "AAI", default: false, base: 2 },
        { aocode: "IRL", default: false, base: 2 },
      ],
      name: "Masters",
      maxAge: 100,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Masters",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37366,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 16, name: "800m", eventnameextra: null, gender: "F", tf: "T" },
    ageGroup: {
      id: 209,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 9,
      keyName: "Under 13 last year",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: -1,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37376,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 17, name: "800m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 209,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 9,
      keyName: "Under 13 last year",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: -1,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37367,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 17, name: "800m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 1,
      minAge: 9,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 11",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 11",
      maxAge: 10,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 11",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37368,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 17, name: "800m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 2,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 13",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37369,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 17, name: "800m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 3,
      minAge: 6,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 9",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 9",
      maxAge: 8,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 9",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37370,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 17, name: "800m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 8,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 17",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37371,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 17, name: "800m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 9,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 15",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37372,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 17, name: "800m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 11,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 20",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 20",
      maxAge: 19,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 20",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37373,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 17, name: "800m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 12,
      minAge: 20,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 23",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Under 23",
      maxAge: 22,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 23",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37374,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 17, name: "800m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 13,
      minAge: 23,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Senior",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Senior",
      maxAge: 34,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Senior",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5322,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    eventGroupSummary: {
      type: "T",
      typeNo: 2,
      id: 5322,
      eventNo: 2,
      bibSortNo: "",
      name: "800m",
      notes: null,
    },
    eventGroup: "800m",
    eventNo: 2,
    id: 37375,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: { id: 17, name: "800m", eventnameextra: null, gender: "M", tf: "T" },
    ageGroup: {
      id: 14,
      minAge: 35,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Masters",
      options: [
        { aocode: "EA", default: true, base: 2 },
        { aocode: "AAI", default: false, base: 2 },
        { aocode: "IRL", default: false, base: 2 },
      ],
      name: "Masters",
      maxAge: 100,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Masters",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 36 }, { e: 48 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37377,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 20,
      name: "Shotput",
      eventnameextra: null,
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 1,
      minAge: 9,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 11",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 11",
      maxAge: 10,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 11",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 36 }, { e: 48 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37378,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 20,
      name: "Shotput",
      eventnameextra: null,
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 2,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 13",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 36 }, { e: 48 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37379,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 20,
      name: "Shotput",
      eventnameextra: null,
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 3,
      minAge: 6,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 9",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 9",
      maxAge: 8,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 9",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 36 }, { e: 48 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37380,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 20,
      name: "Shotput",
      eventnameextra: null,
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 8,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 17",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 36 }, { e: 48 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37381,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 20,
      name: "Shotput",
      eventnameextra: null,
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 9,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 15",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 36 }, { e: 48 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37382,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 20,
      name: "Shotput",
      eventnameextra: null,
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 11,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 20",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 20",
      maxAge: 19,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 20",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 36 }, { e: 48 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37383,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 20,
      name: "Shotput",
      eventnameextra: null,
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 12,
      minAge: 20,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 23",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Under 23",
      maxAge: 22,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 23",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 36 }, { e: 48 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37384,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 20,
      name: "Shotput",
      eventnameextra: null,
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 13,
      minAge: 23,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Senior",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Senior",
      maxAge: 34,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Senior",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 36 }, { e: 48 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37385,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 20,
      name: "Shotput",
      eventnameextra: null,
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 14,
      minAge: 35,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Masters",
      options: [
        { aocode: "EA", default: true, base: 2 },
        { aocode: "AAI", default: false, base: 2 },
        { aocode: "IRL", default: false, base: 2 },
      ],
      name: "Masters",
      maxAge: 100,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Masters",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 36 }, { e: 48 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37386,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 20,
      name: "Shotput",
      eventnameextra: null,
      gender: "F",
      tf: "F",
    },
    ageGroup: {
      id: 209,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 9,
      keyName: "Under 13 last year",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: -1,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 37 }, { e: 49 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37387,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 21,
      name: "Shotput",
      eventnameextra: null,
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 1,
      minAge: 9,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 11",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 11",
      maxAge: 10,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 11",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 37 }, { e: 49 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37388,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 21,
      name: "Shotput",
      eventnameextra: null,
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 2,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 13",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 37 }, { e: 49 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37389,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 21,
      name: "Shotput",
      eventnameextra: null,
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 3,
      minAge: 6,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 9",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 9",
      maxAge: 8,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 9",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 37 }, { e: 49 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37390,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 21,
      name: "Shotput",
      eventnameextra: null,
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 8,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 17",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 37 }, { e: 49 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37391,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 21,
      name: "Shotput",
      eventnameextra: null,
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 9,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 15",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 37 }, { e: 49 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37392,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 21,
      name: "Shotput",
      eventnameextra: null,
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 11,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 20",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 20",
      maxAge: 19,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 20",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 37 }, { e: 49 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37393,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 21,
      name: "Shotput",
      eventnameextra: null,
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 12,
      minAge: 20,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 23",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Under 23",
      maxAge: 22,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 23",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 37 }, { e: 49 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37394,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 21,
      name: "Shotput",
      eventnameextra: null,
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 13,
      minAge: 23,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Senior",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Senior",
      maxAge: 34,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Senior",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 37 }, { e: 49 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37395,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 21,
      name: "Shotput",
      eventnameextra: null,
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 14,
      minAge: 35,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Masters",
      options: [
        { aocode: "EA", default: true, base: 2 },
        { aocode: "AAI", default: false, base: 2 },
        { aocode: "IRL", default: false, base: 2 },
      ],
      name: "Masters",
      maxAge: 100,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Masters",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5323,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: { min: 1, max: 30, unique: [{ e: 37 }, { e: 49 }] },
    eventGroupSummary: {
      type: "F",
      typeNo: 4,
      id: 5323,
      eventNo: 4,
      bibSortNo: "",
      name: "Shotput",
      notes: null,
    },
    eventGroup: "Shotput",
    eventNo: 4,
    id: 37396,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 21,
      name: "Shotput",
      eventnameextra: null,
      gender: "M",
      tf: "F",
    },
    ageGroup: {
      id: 209,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 9,
      keyName: "Under 13 last year",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: -1,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 5,
      type: "D",
      options: [{ pattern: 0.99, text: "metres", short: "mt" }],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37438,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 923,
      name: "Donation",
      eventnameextra: null,
      gender: "M",
      tf: "M",
    },
    ageGroup: {
      id: 1,
      minAge: 9,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 11",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 11",
      maxAge: 10,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 11",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37440,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 923,
      name: "Donation",
      eventnameextra: null,
      gender: "M",
      tf: "M",
    },
    ageGroup: {
      id: 2,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 13",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37442,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 923,
      name: "Donation",
      eventnameextra: null,
      gender: "M",
      tf: "M",
    },
    ageGroup: {
      id: 3,
      minAge: 6,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 9",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 9",
      maxAge: 8,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 9",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37444,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 923,
      name: "Donation",
      eventnameextra: null,
      gender: "M",
      tf: "M",
    },
    ageGroup: {
      id: 8,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 17",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37446,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 923,
      name: "Donation",
      eventnameextra: null,
      gender: "M",
      tf: "M",
    },
    ageGroup: {
      id: 9,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 15",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37448,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 923,
      name: "Donation",
      eventnameextra: null,
      gender: "M",
      tf: "M",
    },
    ageGroup: {
      id: 11,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 20",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 20",
      maxAge: 19,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 20",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37450,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 923,
      name: "Donation",
      eventnameextra: null,
      gender: "M",
      tf: "M",
    },
    ageGroup: {
      id: 12,
      minAge: 20,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 23",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Under 23",
      maxAge: 22,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 23",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37452,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 923,
      name: "Donation",
      eventnameextra: null,
      gender: "M",
      tf: "M",
    },
    ageGroup: {
      id: 13,
      minAge: 23,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Senior",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Senior",
      maxAge: 34,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Senior",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37454,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 923,
      name: "Donation",
      eventnameextra: null,
      gender: "M",
      tf: "M",
    },
    ageGroup: {
      id: 14,
      minAge: 35,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Masters",
      options: [
        { aocode: "EA", default: true, base: 2 },
        { aocode: "AAI", default: false, base: 2 },
        { aocode: "IRL", default: false, base: 2 },
      ],
      name: "Masters",
      maxAge: 100,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Masters",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37456,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 923,
      name: "Donation",
      eventnameextra: null,
      gender: "M",
      tf: "M",
    },
    ageGroup: {
      id: 209,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 9,
      keyName: "Under 13 last year",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: -1,
      shortName: "Under 13",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37437,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 924,
      name: "Donation",
      eventnameextra: null,
      gender: "F",
      tf: "M",
    },
    ageGroup: {
      id: 1,
      minAge: 9,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 11",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 11",
      maxAge: 10,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 11",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37439,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 924,
      name: "Donation",
      eventnameextra: null,
      gender: "F",
      tf: "M",
    },
    ageGroup: {
      id: 2,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 13",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37441,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 924,
      name: "Donation",
      eventnameextra: null,
      gender: "F",
      tf: "M",
    },
    ageGroup: {
      id: 3,
      minAge: 6,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 9",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 9",
      maxAge: 8,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 9",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37443,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 924,
      name: "Donation",
      eventnameextra: null,
      gender: "F",
      tf: "M",
    },
    ageGroup: {
      id: 8,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 17",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37445,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 924,
      name: "Donation",
      eventnameextra: null,
      gender: "F",
      tf: "M",
    },
    ageGroup: {
      id: 9,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 15",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37447,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 924,
      name: "Donation",
      eventnameextra: null,
      gender: "F",
      tf: "M",
    },
    ageGroup: {
      id: 11,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 20",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 20",
      maxAge: 19,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 20",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37449,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 924,
      name: "Donation",
      eventnameextra: null,
      gender: "F",
      tf: "M",
    },
    ageGroup: {
      id: 12,
      minAge: 20,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 23",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Under 23",
      maxAge: 22,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 23",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37451,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 924,
      name: "Donation",
      eventnameextra: null,
      gender: "F",
      tf: "M",
    },
    ageGroup: {
      id: 13,
      minAge: 23,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Senior",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Senior",
      maxAge: 34,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Senior",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37453,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 924,
      name: "Donation",
      eventnameextra: null,
      gender: "F",
      tf: "M",
    },
    ageGroup: {
      id: 14,
      minAge: 35,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Masters",
      options: [
        { aocode: "EA", default: true, base: 2 },
        { aocode: "AAI", default: false, base: 2 },
        { aocode: "IRL", default: false, base: 2 },
      ],
      name: "Masters",
      maxAge: 100,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Masters",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5329,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: false,
        showPrice: false,
        showEntryCount: false,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "M",
      typeNo: 9,
      id: 5329,
      eventNo: 9,
      bibSortNo: "",
      name: "Donation of  1 pound to XXXXXX",
      notes: null,
    },
    eventGroup: "Donation of  1 pound to XXXXXX",
    eventNo: 9,
    id: 37455,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T00:00:00",
    sortDateTime: "2023-03-28T00:00:00",
    event: {
      id: 924,
      name: "Donation",
      eventnameextra: null,
      gender: "F",
      tf: "M",
    },
    ageGroup: {
      id: 209,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 9,
      keyName: "Under 13 last year",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: -1,
      shortName: "Under 13",
    },
    price: {
      id: 483,
      fee: 0.5,
      saleFee: 0.5,
      price: 1,
      salePrice: 1,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: false,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "Donation",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 11,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "minutes", short: "m" },
        { pattern: "m:ss.SS", text: "minutes", short: "m" },
        { pattern: "s.SS", text: "seconds", short: "s" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37417,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 823,
      name: "4 x 200",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 1,
      minAge: 9,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 11",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 11",
      maxAge: 10,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 11",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37419,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 823,
      name: "4 x 200",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 2,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 13",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37421,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 823,
      name: "4 x 200",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 3,
      minAge: 6,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 9",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 9",
      maxAge: 8,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 9",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37423,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 823,
      name: "4 x 200",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 8,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 17",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37425,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 823,
      name: "4 x 200",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 9,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 15",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37427,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 823,
      name: "4 x 200",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 11,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 20",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 20",
      maxAge: 19,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 20",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37429,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 823,
      name: "4 x 200",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 12,
      minAge: 20,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 23",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Under 23",
      maxAge: 22,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 23",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37431,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 823,
      name: "4 x 200",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 13,
      minAge: 23,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Senior",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Senior",
      maxAge: 34,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Senior",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37433,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 823,
      name: "4 x 200",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 14,
      minAge: 35,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Masters",
      options: [
        { aocode: "EA", default: true, base: 2 },
        { aocode: "AAI", default: false, base: 2 },
        { aocode: "IRL", default: false, base: 2 },
      ],
      name: "Masters",
      maxAge: 100,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Masters",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37435,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 823,
      name: "4 x 200",
      eventnameextra: null,
      gender: "F",
      tf: "T",
    },
    ageGroup: {
      id: 209,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 9,
      keyName: "Under 13 last year",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: -1,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37418,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 824,
      name: "4 x 200",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 1,
      minAge: 9,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 11",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 11",
      maxAge: 10,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 11",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37420,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 824,
      name: "4 x 200",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 2,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 13",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37422,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 824,
      name: "4 x 200",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 3,
      minAge: 6,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 9",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 9",
      maxAge: 8,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 9",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37424,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 824,
      name: "4 x 200",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 8,
      minAge: 15,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 17",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 17",
      maxAge: 16,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 17",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37426,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 824,
      name: "4 x 200",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 9,
      minAge: 13,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 15",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 15",
      maxAge: 14,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 15",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37428,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 824,
      name: "4 x 200",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 11,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 20",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 20",
      maxAge: 19,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 20",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37430,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 824,
      name: "4 x 200",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 12,
      minAge: 20,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Under 23",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Under 23",
      maxAge: 22,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 23",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37432,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 824,
      name: "4 x 200",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 13,
      minAge: 23,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Senior",
      options: [
        { aocode: "EA", default: true, base: 1 },
        { aocode: "IRL", default: false },
      ],
      name: "Senior",
      maxAge: 34,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Senior",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37434,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 824,
      name: "4 x 200",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 14,
      minAge: 35,
      minAtDay: 31,
      minAtMonth: 12,
      keyName: "Masters",
      options: [
        { aocode: "EA", default: true, base: 2 },
        { aocode: "AAI", default: false, base: 2 },
        { aocode: "IRL", default: false, base: 2 },
      ],
      name: "Masters",
      maxAge: 100,
      maxAtDay: 0,
      maxAtMonth: 0,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Masters",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
  {
    maxGroup: 5326,
    split: 0,
    options: {
      min: 0,
      max: 0,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: true,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 4,
        max: 6,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "",
        showForm: false,
        formType: "DEFAULT",
        price: "",
        maxTeamsForAthlete: 0,
        disableTeamNameEdit: false,
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 0,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: { clubs: [], counties: [], regions: [] },
      athleteSecurity: { clubs: [] },
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
      eventGroupInfo: { trialInfo: "", reportInfo: "" },
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: { id: 0, compId: 0, name: "" },
      },
      secondarySpend: { isParent: false, parentCeid: 0 },
    },
    eoptions: {},
    eventGroupSummary: {
      type: "T",
      typeNo: 6,
      id: 5326,
      eventNo: 6,
      bibSortNo: "",
      name: "4 x 200",
      notes: null,
    },
    eventGroup: "4 x 200",
    eventNo: 6,
    id: 37436,
    compId: 354,
    isOpen: 1,
    maxAthletes: 0,
    startDateTime: "2023-03-28T10:00:00",
    sortDateTime: "2023-03-28T10:00:00",
    event: {
      id: 824,
      name: "4 x 200",
      eventnameextra: null,
      gender: "M",
      tf: "T",
    },
    ageGroup: {
      id: 209,
      minAge: 11,
      minAtDay: 31,
      minAtMonth: 9,
      keyName: "Under 13 last year",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 13",
      maxAge: 12,
      maxAtDay: 31,
      maxAtMonth: 8,
      maxAtYear: 0,
      minAtYear: -1,
      shortName: "Under 13",
    },
    price: {
      id: 481,
      fee: 0,
      saleFee: 0,
      price: 0,
      salePrice: 0,
      saleEndDate: null,
      options: {
        displayFee: false,
        feeIncluded: true,
        freeEntry: true,
        discount: { count: 0, price: 0, validToDate: "" },
      },
      name: "",
      description: "strd ewf we",
    },
    multiEventInfo: { id: 0 },
    uom: {
      id: 3,
      type: "T",
      options: [
        { pattern: "m.ss.SS", text: "mins", short: "m" },
        { pattern: "m:ss.SS", text: "mins", short: "m" },
      ],
    },
    entryCount: { total: 0, waiting: 0 },
  },
] as any as ICompEvent[];
