import { TimeTronicsEventGroupBy<PERSON>ey } from "../../results-import-models";

//  WTF does it keep complaingin about key "60m Sprint Indoor" not matching the type "TimeTronicsEventGroupByEventName"?
//  It's a string, and it's a key in the type.  What's the problem?
//  I think it's because the type is a partial, and the key is not required.
//  So, I need to make sure the key is required.
export const mockTimeTronicsEventGroupResult: TimeTronicsEventGroupByKey = {
  "60m Sprint Indoor": [
    {
      abbreviation: "60m G U11 ",
      categories: [
        { abbreviation: "U11 G", id: 46, name: "U11 Girls" },
        { abbreviation: "U12 G", id: 48, name: "U12 Girls" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "60m Sprint Indoor",
        combinedeventsquantity: 0,
        distance: 60,
        fieldtype: " ",
        id: 209,
        implement: 0,
        name: "",
        shortcode: "60m I",
        windmode: "N",
        windtime: "",
      },
      id: 1,
      info: "",
      name: "U11-U12 Girls 60m Sprint",
      rounds: [
        {
          abbreviation: "60m Girls U11 12",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 94,
              info: "0",
              numberofparticipations: 7,
              official: "12:00:00",
              scheduled: "18:13:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 95,
              info: "0",
              numberofparticipations: 7,
              official: "12:00:00",
              scheduled: "18:16:00",
              seqno: 2,
              status: 2,
            },
          ],
          id: 1,
          info: "",
          name: "60m Girls U11 12",
          numberofparticipationsround: 14,
          official: "12:00:00",
          scheduled: "18:13:00",
          seqno: 1,
          session: "Juveniles",
          status: 2,
        },
      ],
      seqno: 1,
      status: 0,
    },
    {
      abbreviation: "60m B U11 ",
      categories: [
        { abbreviation: "U11 B", id: 45, name: "U11 Boys" },
        { abbreviation: "U12 B", id: 47, name: "U12 Boys" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "60m Sprint Indoor",
        combinedeventsquantity: 0,
        distance: 60,
        fieldtype: " ",
        id: 209,
        implement: 0,
        name: "",
        shortcode: "60m I",
        windmode: "N",
        windtime: "",
      },
      id: 2,
      info: "",
      name: "U11-U12 Boys 60m Sprint",
      rounds: [
        {
          abbreviation: "60m Boys U11 12",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 92,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "18:19:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 93,
              info: "0",
              numberofparticipations: 7,
              official: "12:00:00",
              scheduled: "18:22:00",
              seqno: 2,
              status: 2,
            },
          ],
          id: 2,
          info: "",
          name: "60m Boys U11 12",
          numberofparticipationsround: 13,
          official: "12:00:00",
          scheduled: "18:19:00",
          seqno: 1,
          session: "Juveniles",
          status: 2,
        },
      ],
      seqno: 2,
      status: 0,
    },
    {
      abbreviation: "60m G U13 ",
      categories: [
        { abbreviation: "U13 G", id: 50, name: "U13 Girls" },
        { abbreviation: "U14 G", id: 52, name: "U14 Girls" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "60m Sprint Indoor",
        combinedeventsquantity: 0,
        distance: 60,
        fieldtype: " ",
        id: 209,
        implement: 0,
        name: "",
        shortcode: "60m I",
        windmode: "N",
        windtime: "",
      },
      id: 3,
      info: "",
      name: "U13-U14 Girls 60m Sprint",
      rounds: [
        {
          abbreviation: "60m Girls U13 14",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 98,
              info: "0",
              numberofparticipations: 7,
              official: "12:00:00",
              scheduled: "18:25:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 99,
              info: "0",
              numberofparticipations: 8,
              official: "12:00:00",
              scheduled: "18:28:00",
              seqno: 2,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 100,
              info: "0",
              numberofparticipations: 8,
              official: "12:00:00",
              scheduled: "18:31:00",
              seqno: 3,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 101,
              info: "0",
              numberofparticipations: 8,
              official: "12:00:00",
              scheduled: "18:34:00",
              seqno: 4,
              status: 2,
            },
          ],
          id: 3,
          info: "",
          name: "60m Girls U13 14",
          numberofparticipationsround: 31,
          official: "12:00:00",
          scheduled: "18:25:00",
          seqno: 1,
          session: "Juveniles",
          status: 2,
        },
      ],
      seqno: 3,
      status: 0,
    },
    {
      abbreviation: "60m B U13",
      categories: [
        { abbreviation: "U13 B", id: 49, name: "U13 Boys" },
        { abbreviation: "U14 B", id: 51, name: "U14 Boys" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "60m Sprint Indoor",
        combinedeventsquantity: 0,
        distance: 60,
        fieldtype: " ",
        id: 209,
        implement: 0,
        name: "",
        shortcode: "60m I",
        windmode: "N",
        windtime: "",
      },
      id: 5,
      info: "",
      name: "U13-U14 Boys 60m Sprint",
      rounds: [
        {
          abbreviation: "60m Boys U13 14",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 96,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "18:37:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 97,
              info: "0",
              numberofparticipations: 7,
              official: "12:00:00",
              scheduled: "18:40:00",
              seqno: 2,
              status: 2,
            },
          ],
          id: 5,
          info: "",
          name: "60m Boys U13 14",
          numberofparticipationsround: 13,
          official: "12:00:00",
          scheduled: "18:37:00",
          seqno: 1,
          session: "Juveniles",
          status: 2,
        },
      ],
      seqno: 4,
      status: 0,
    },
    {
      abbreviation: "60m G U15",
      categories: [
        { abbreviation: "U15 G", id: 54, name: "U15 Girls" },
        { abbreviation: "U16 G", id: 56, name: "U16 Girls" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "60m Sprint Indoor",
        combinedeventsquantity: 0,
        distance: 60,
        fieldtype: " ",
        id: 209,
        implement: 0,
        name: "",
        shortcode: "60m I",
        windmode: "N",
        windtime: "",
      },
      id: 6,
      info: "",
      name: "U15-U16 Girls 60m Sprint",
      rounds: [
        {
          abbreviation: "60m Girls U15 16",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 102,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "18:43:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 103,
              info: "0",
              numberofparticipations: 7,
              official: "12:00:00",
              scheduled: "18:46:00",
              seqno: 2,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 104,
              info: "0",
              numberofparticipations: 7,
              official: "12:00:00",
              scheduled: "18:49:00",
              seqno: 3,
              status: 2,
            },
          ],
          id: 6,
          info: "",
          name: "60m Girls U15 16",
          numberofparticipationsround: 20,
          official: "12:00:00",
          scheduled: "18:43:00",
          seqno: 1,
          session: "Juveniles",
          status: 2,
        },
      ],
      seqno: 5,
      status: 0,
    },
    {
      abbreviation: "60m B U15",
      categories: [
        { abbreviation: "U15 B", id: 53, name: "U15 Boys" },
        { abbreviation: "U16 B", id: 55, name: "U16 Boys" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "60m Sprint Indoor",
        combinedeventsquantity: 0,
        distance: 60,
        fieldtype: " ",
        id: 209,
        implement: 0,
        name: "",
        shortcode: "60m I",
        windmode: "N",
        windtime: "",
      },
      id: 7,
      info: "",
      name: "U15-U16 Boys 60m Sprint",
      rounds: [
        {
          abbreviation: "60m Boys U15 16",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 107,
              info: "0",
              numberofparticipations: 8,
              official: "12:00:00",
              scheduled: "18:52:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 108,
              info: "0",
              numberofparticipations: 7,
              official: "12:00:00",
              scheduled: "18:55:00",
              seqno: 2,
              status: 2,
            },
          ],
          id: 7,
          info: "",
          name: "60m Boys U15 16",
          numberofparticipationsround: 15,
          official: "12:00:00",
          scheduled: "18:52:00",
          seqno: 1,
          session: "Juveniles",
          status: 2,
        },
      ],
      seqno: 6,
      status: 0,
    },
    {
      abbreviation: "60m W S1",
      categories: [{ abbreviation: "SEN W", id: 64, name: "Senior Women" }],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "60m Sprint Indoor",
        combinedeventsquantity: 0,
        distance: 60,
        fieldtype: " ",
        id: 209,
        implement: 0,
        name: "",
        shortcode: "60m I",
        windmode: "N",
        windtime: "",
      },
      id: 8,
      info: "",
      name: "Senior Women 60m Series 1",
      rounds: [
        {
          abbreviation: "60m W Series1",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 109,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "17:45:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 110,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "17:48:00",
              seqno: 2,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 111,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "17:51:00",
              seqno: 3,
              status: 2,
            },
          ],
          id: 8,
          info: "",
          name: "60m W Series1",
          numberofparticipationsround: 18,
          official: "12:00:00",
          scheduled: "17:45:00",
          seqno: 1,
          session: "Seniors",
          status: 2,
        },
      ],
      seqno: 7,
      status: 0,
    },
    {
      abbreviation: "60m M S1",
      categories: [{ abbreviation: "SEN M", id: 63, name: "Senior Men" }],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "60m Sprint Indoor",
        combinedeventsquantity: 0,
        distance: 60,
        fieldtype: " ",
        id: 209,
        implement: 0,
        name: "",
        shortcode: "60m I",
        windmode: "N",
        windtime: "",
      },
      id: 9,
      info: "",
      name: "Senior Men 60m Sprint",
      rounds: [
        {
          abbreviation: "60m M Series 1",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 112,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "17:57:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 113,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "18:00:00",
              seqno: 2,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 114,
              info: "0",
              numberofparticipations: 7,
              official: "12:00:00",
              scheduled: "18:03:00",
              seqno: 3,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 115,
              info: "0",
              numberofparticipations: 7,
              official: "12:00:00",
              scheduled: "18:06:00",
              seqno: 4,
              status: 2,
            },
          ],
          id: 9,
          info: "",
          name: "60m M Series 1",
          numberofparticipationsround: 26,
          official: "12:00:00",
          scheduled: "17:57:00",
          seqno: 1,
          session: "Seniors",
          status: 2,
        },
      ],
      seqno: 8,
      status: 0,
    },
    {
      abbreviation: "60m W S2",
      categories: [{ abbreviation: "SEN W", id: 64, name: "Senior Women" }],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "60m Sprint Indoor",
        combinedeventsquantity: 0,
        distance: 60,
        fieldtype: " ",
        id: 209,
        implement: 0,
        name: "",
        shortcode: "60m I",
        windmode: "N",
        windtime: "",
      },
      id: 21,
      info: "",
      name: "Senior Women 60m Series 2",
      rounds: [
        {
          abbreviation: "60m W Series2",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 116,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "18:58:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 117,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "19:01:00",
              seqno: 2,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 118,
              info: "0",
              numberofparticipations: 7,
              official: "12:00:00",
              scheduled: "19:04:00",
              seqno: 3,
              status: 2,
            },
          ],
          id: 21,
          info: "",
          name: "60m W Series2",
          numberofparticipationsround: 19,
          official: "12:00:00",
          scheduled: "18:58:00",
          seqno: 1,
          session: "Seniors",
          status: 2,
        },
      ],
      seqno: 18,
      status: 0,
    },
    {
      abbreviation: "60m M S2",
      categories: [{ abbreviation: "SEN M", id: 63, name: "Senior Men" }],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "60m Sprint Indoor",
        combinedeventsquantity: 0,
        distance: 60,
        fieldtype: " ",
        id: 209,
        implement: 0,
        name: "",
        shortcode: "60m I",
        windmode: "N",
        windtime: "",
      },
      id: 22,
      info: "",
      name: "Senior Men 60m Series 2",
      rounds: [
        {
          abbreviation: "60m M Series2",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 119,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "19:10:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 120,
              info: "0",
              numberofparticipations: 7,
              official: "12:00:00",
              scheduled: "19:13:00",
              seqno: 2,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 121,
              info: "0",
              numberofparticipations: 7,
              official: "12:00:00",
              scheduled: "19:16:00",
              seqno: 3,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 122,
              info: "0",
              numberofparticipations: 7,
              official: "12:00:00",
              scheduled: "19:19:00",
              seqno: 4,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 123,
              info: "0",
              numberofparticipations: 7,
              official: "12:00:00",
              scheduled: "19:22:00",
              seqno: 5,
              status: 2,
            },
          ],
          id: 22,
          info: "",
          name: "60m M Series2",
          numberofparticipationsround: 34,
          official: "12:00:00",
          scheduled: "19:10:00",
          seqno: 1,
          session: "Seniors",
          status: 2,
        },
      ],
      seqno: 19,
      status: 0,
    },
  ],
  "600m Indoor": [
    {
      abbreviation: "600m G U11",
      categories: [
        { abbreviation: "U11 G", id: 46, name: "U11 Girls" },
        { abbreviation: "U12 G", id: 48, name: "U12 Girls" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "600m Indoor",
        combinedeventsquantity: 0,
        distance: 600,
        fieldtype: " ",
        id: 220,
        implement: 0,
        name: "",
        shortcode: "600m I",
        windmode: "N",
        windtime: "",
      },
      id: 10,
      info: "",
      name: "U11-U12 Girls 600m",
      rounds: [
        {
          abbreviation: "600m G U11 12",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 75,
              info: "0",
              numberofparticipations: 14,
              official: "12:00:00",
              scheduled: "19:30:00",
              seqno: 1,
              status: 2,
            },
          ],
          id: 10,
          info: "",
          name: "600m Girls U11 12",
          numberofparticipationsround: 14,
          official: "12:00:00",
          scheduled: "19:30:00",
          seqno: 1,
          session: "Juveniles",
          status: 2,
        },
      ],
      seqno: 9,
      status: 0,
    },
    {
      abbreviation: "600m  B U11",
      categories: [
        { abbreviation: "U11 B", id: 45, name: "U11 Boys" },
        { abbreviation: "U12 B", id: 47, name: "U12 Boys" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "600m Indoor",
        combinedeventsquantity: 0,
        distance: 600,
        fieldtype: " ",
        id: 220,
        implement: 0,
        name: "",
        shortcode: "600m I",
        windmode: "N",
        windtime: "",
      },
      id: 11,
      info: "",
      name: "U11-U12 Boys 600m",
      rounds: [
        {
          abbreviation: "600m Boys U11 12",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 76,
              info: "0",
              numberofparticipations: 8,
              official: "12:00:00",
              scheduled: "19:33:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 77,
              info: "0",
              numberofparticipations: 9,
              official: "12:00:00",
              scheduled: "19:36:00",
              seqno: 2,
              status: 2,
            },
          ],
          id: 11,
          info: "",
          name: "600m Boys U11 12",
          numberofparticipationsround: 17,
          official: "12:00:00",
          scheduled: "19:33:00",
          seqno: 1,
          session: "Juveniles",
          status: 2,
        },
      ],
      seqno: 10,
      status: 0,
    },
  ],
  "800m Indoor": [
    {
      abbreviation: "800m G U13",
      categories: [
        { abbreviation: "U13 G", id: 50, name: "U13 Girls" },
        { abbreviation: "U14 G", id: 52, name: "U14 Girls" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "800m Indoor",
        combinedeventsquantity: 0,
        distance: 800,
        fieldtype: " ",
        id: 282,
        implement: 0,
        name: "",
        shortcode: "800m I",
        windmode: "N",
        windtime: "",
      },
      id: 12,
      info: "",
      name: "U13-U14 Girls 800m",
      rounds: [
        {
          abbreviation: "800m G U13 14",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 84,
              info: "0",
              numberofparticipations: 11,
              official: "12:00:00",
              scheduled: "19:39:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 85,
              info: "0",
              numberofparticipations: 11,
              official: "12:00:00",
              scheduled: "19:42:00",
              seqno: 2,
              status: 2,
            },
          ],
          id: 12,
          info: "",
          name: "800m Girls U13 14",
          numberofparticipationsround: 22,
          official: "12:00:00",
          scheduled: "19:39:00",
          seqno: 1,
          session: "Juveniles",
          status: 2,
        },
      ],
      seqno: 11,
      status: 0,
    },
    {
      abbreviation: "800m G U15",
      categories: [
        { abbreviation: "U15 G", id: 54, name: "U15 Girls" },
        { abbreviation: "U16 G", id: 56, name: "U16 Girls" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "800m Indoor",
        combinedeventsquantity: 0,
        distance: 800,
        fieldtype: " ",
        id: 282,
        implement: 0,
        name: "",
        shortcode: "800m I",
        windmode: "N",
        windtime: "",
      },
      id: 14,
      info: "",
      name: "U15-U16 Girls 800m",
      rounds: [
        {
          abbreviation: "800m G U15 16",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 89,
              info: "0",
              numberofparticipations: 10,
              official: "12:00:00",
              scheduled: "19:45:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 90,
              info: "0",
              numberofparticipations: 10,
              official: "12:00:00",
              scheduled: "19:48:00",
              seqno: 2,
              status: 2,
            },
          ],
          id: 14,
          info: "",
          name: "800m Girls U15-U16",
          numberofparticipationsround: 20,
          official: "12:00:00",
          scheduled: "19:45:00",
          seqno: 1,
          session: "Juveniles",
          status: 2,
        },
      ],
      seqno: 13,
      status: 0,
    },
    {
      abbreviation: "800m B U15",
      categories: [
        { abbreviation: "U13 B", id: 49, name: "U13 Boys" },
        { abbreviation: "U14 B", id: 51, name: "U14 Boys" },
        { abbreviation: "U15 B", id: 53, name: "U15 Boys" },
        { abbreviation: "U16 B", id: 55, name: "U16 Boys" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "800m Indoor",
        combinedeventsquantity: 0,
        distance: 800,
        fieldtype: " ",
        id: 282,
        implement: 0,
        name: "",
        shortcode: "800m I",
        windmode: "N",
        windtime: "",
      },
      id: 15,
      info: "",
      name: "U13-U16 Boys 800m",
      rounds: [
        {
          abbreviation: "800m Boys U13 16",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 91,
              info: "0",
              numberofparticipations: 12,
              official: "12:00:00",
              scheduled: "19:51:00",
              seqno: 1,
              status: 2,
            },
          ],
          id: 15,
          info: "",
          name: "800m Boys U13 16",
          numberofparticipationsround: 12,
          official: "12:00:00",
          scheduled: "19:51:00",
          seqno: 1,
          session: "Juveniles",
          status: 2,
        },
      ],
      seqno: 14,
      status: 0,
    },
    {
      abbreviation: "800m W",
      categories: [{ abbreviation: "SEN W", id: 64, name: "Senior Women" }],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "800m Indoor",
        combinedeventsquantity: 0,
        distance: 800,
        fieldtype: " ",
        id: 282,
        implement: 0,
        name: "",
        shortcode: "800m I",
        windmode: "N",
        windtime: "",
      },
      id: 18,
      info: "",
      name: "Senior Women 800m",
      rounds: [
        {
          abbreviation: "800m Women ",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 124,
              info: "0",
              numberofparticipations: 8,
              official: "12:00:00",
              scheduled: "21:16:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 125,
              info: "0",
              numberofparticipations: 8,
              official: "12:00:00",
              scheduled: "21:19:00",
              seqno: 2,
              status: 2,
            },
          ],
          id: 18,
          info: "",
          name: "800m Women ",
          numberofparticipationsround: 16,
          official: "12:00:00",
          scheduled: "21:16:00",
          seqno: 1,
          session: "Seniors",
          status: 2,
        },
      ],
      seqno: 15,
      status: 0,
    },
    {
      abbreviation: "800m M",
      categories: [{ abbreviation: "SEN M", id: 63, name: "Senior Men" }],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "800m Indoor",
        combinedeventsquantity: 0,
        distance: 800,
        fieldtype: " ",
        id: 282,
        implement: 0,
        name: "",
        shortcode: "800m I",
        windmode: "N",
        windtime: "",
      },
      id: 19,
      info: "",
      name: "Senior Men 800m",
      rounds: [
        {
          abbreviation: "800m Men",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 126,
              info: "0",
              numberofparticipations: 8,
              official: "12:00:00",
              scheduled: "21:28:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 127,
              info: "0",
              numberofparticipations: 8,
              official: "12:00:00",
              scheduled: "21:31:00",
              seqno: 2,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 128,
              info: "0",
              numberofparticipations: 9,
              official: "12:00:00",
              scheduled: "21:34:00",
              seqno: 3,
              status: 2,
            },
          ],
          id: 19,
          info: "",
          name: "800m Men",
          numberofparticipationsround: 25,
          official: "12:00:00",
          scheduled: "21:28:00",
          seqno: 1,
          session: "Seniors",
          status: 2,
        },
      ],
      seqno: 16,
      status: 0,
    },
    {
      abbreviation: "4x200m G U11",
      categories: [
        { abbreviation: "U11 G", id: 46, name: "U11 Girls" },
        { abbreviation: "U12 G", id: 48, name: "U12 Girls" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "800m Indoor",
        combinedeventsquantity: 0,
        distance: 800,
        fieldtype: " ",
        id: 282,
        implement: 0,
        name: "",
        shortcode: "800m I",
        windmode: "N",
        windtime: "",
      },
      id: 29,
      info: "",
      name: "U11-U12 Girls 4x200m Relay",
      rounds: [
        {
          abbreviation: "4x200 G U11 12",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 66,
              info: "0",
              numberofparticipations: 3,
              official: "12:00:00",
              scheduled: "20:04:00",
              seqno: 1,
              status: 2,
            },
          ],
          id: 29,
          info: "",
          name: "4x200 Girls U11 12",
          numberofparticipationsround: 3,
          official: "12:00:00",
          scheduled: "20:04:00",
          seqno: 1,
          session: "Juveniles",
          status: 2,
        },
      ],
      seqno: 22,
      status: 0,
    },
    {
      abbreviation: "4x200m B U11 16",
      categories: [
        { abbreviation: "U11 B", id: 45, name: "U11 Boys" },
        { abbreviation: "U12 B", id: 47, name: "U12 Boys" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "800m Indoor",
        combinedeventsquantity: 0,
        distance: 800,
        fieldtype: " ",
        id: 282,
        implement: 0,
        name: "",
        shortcode: "800m I",
        windmode: "N",
        windtime: "",
      },
      id: 30,
      info: "",
      name: "U11-U16 Boys 4x200m Relay",
      rounds: [
        {
          abbreviation: "4x200 B U11 16",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 67,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "20:09:00",
              seqno: 1,
              status: 2,
            },
          ],
          id: 30,
          info: "",
          name: "4x200 Boys U11 16",
          numberofparticipationsround: 6,
          official: "12:00:00",
          scheduled: "20:09:00",
          seqno: 1,
          session: "Juveniles",
          status: 2,
        },
      ],
      seqno: 23,
      status: 0,
    },
    {
      abbreviation: "4x200m G U13",
      categories: [
        { abbreviation: "U13 G", id: 50, name: "U13 Girls" },
        { abbreviation: "U14 G", id: 52, name: "U14 Girls" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "800m Indoor",
        combinedeventsquantity: 0,
        distance: 800,
        fieldtype: " ",
        id: 282,
        implement: 0,
        name: "",
        shortcode: "800m I",
        windmode: "N",
        windtime: "",
      },
      id: 31,
      info: "",
      name: "U13-U14 Girls 4x200m Relay",
      rounds: [
        {
          abbreviation: "4x200 G  U13 14",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 68,
              info: "0",
              numberofparticipations: 5,
              official: "12:00:00",
              scheduled: "20:14:00",
              seqno: 1,
              status: 2,
            },
          ],
          id: 31,
          info: "",
          name: "4x200 Girls  U13 14",
          numberofparticipationsround: 5,
          official: "12:00:00",
          scheduled: "20:14:00",
          seqno: 1,
          session: "Juveniles",
          status: 2,
        },
      ],
      seqno: 24,
      status: 0,
    },
    {
      abbreviation: "4x200m G U15",
      categories: [
        { abbreviation: "U15 G", id: 54, name: "U15 Girls" },
        { abbreviation: "U16 G", id: 56, name: "U16 Girls" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "800m Indoor",
        combinedeventsquantity: 0,
        distance: 800,
        fieldtype: " ",
        id: 282,
        implement: 0,
        name: "",
        shortcode: "800m I",
        windmode: "N",
        windtime: "",
      },
      id: 33,
      info: "",
      name: "U15-U16 Girls 4x200m Relay",
      rounds: [
        {
          abbreviation: "4x200 G U15 16",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 69,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "20:19:00",
              seqno: 1,
              status: 2,
            },
          ],
          id: 33,
          info: "",
          name: "4x200 Girls  U15 16",
          numberofparticipationsround: 6,
          official: "12:00:00",
          scheduled: "20:19:00",
          seqno: 1,
          session: "Juveniles",
          status: 2,
        },
      ],
      seqno: 26,
      status: 0,
    },
  ],
  "400m Indoor": [
    {
      abbreviation: "400m W",
      categories: [{ abbreviation: "SEN W", id: 64, name: "Senior Women" }],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "400m Indoor",
        combinedeventsquantity: 0,
        distance: 400,
        fieldtype: " ",
        id: 283,
        implement: 0,
        name: "",
        shortcode: "400m I",
        windmode: "N",
        windtime: "",
      },
      id: 23,
      info: "",
      name: "Senior Women 400m",
      rounds: [
        {
          abbreviation: "400m Women",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 140,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "20:24:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 141,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "20:27:00",
              seqno: 2,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 142,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "20:30:00",
              seqno: 3,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 143,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "20:33:00",
              seqno: 4,
              status: 2,
            },
          ],
          id: 23,
          info: "",
          name: "400m Women",
          numberofparticipationsround: 24,
          official: "12:00:00",
          scheduled: "20:24:00",
          seqno: 1,
          session: "Seniors",
          status: 2,
        },
      ],
      seqno: 20,
      status: 0,
    },
    {
      abbreviation: "400m M",
      categories: [{ abbreviation: "SEN M", id: 63, name: "Senior Men" }],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "400m Indoor",
        combinedeventsquantity: 0,
        distance: 400,
        fieldtype: " ",
        id: 283,
        implement: 0,
        name: "",
        shortcode: "400m I",
        windmode: "N",
        windtime: "",
      },
      id: 24,
      info: "",
      name: "Senior Men 400m",
      rounds: [
        {
          abbreviation: "400m Men",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 135,
              info: "0",
              numberofparticipations: 5,
              official: "12:00:00",
              scheduled: "20:40:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 136,
              info: "0",
              numberofparticipations: 5,
              official: "12:00:00",
              scheduled: "20:43:00",
              seqno: 2,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 137,
              info: "0",
              numberofparticipations: 5,
              official: "12:00:00",
              scheduled: "20:46:00",
              seqno: 3,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 138,
              info: "0",
              numberofparticipations: 5,
              official: "12:00:00",
              scheduled: "20:49:00",
              seqno: 4,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 139,
              info: "0",
              numberofparticipations: 6,
              official: "12:00:00",
              scheduled: "20:52:00",
              seqno: 5,
              status: 2,
            },
          ],
          id: 24,
          info: "",
          name: "400m Men",
          numberofparticipationsround: 26,
          official: "12:00:00",
          scheduled: "20:40:00",
          seqno: 1,
          session: "Seniors",
          status: 2,
        },
      ],
      seqno: 21,
      status: 0,
    },
  ],
  "High Jump Indoor": [
    {
      abbreviation: "HJ I",
      categories: [{ abbreviation: "SEN W", id: 64, name: "Senior Women" }],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "High Jump Indoor",
        combinedeventsquantity: 0,
        distance: 0,
        fieldtype: "V",
        id: 244,
        implement: 0,
        name: "",
        shortcode: "HJ I",
        windmode: "N",
        windtime: "",
      },
      id: 25,
      info: "",
      name: "Senior Women High Jump",
      rounds: [
        {
          abbreviation: "",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 73,
              info: "0",
              numberofparticipations: 2,
              official: "12:00:00",
              scheduled: "19:00:00",
              seqno: 1,
              status: 2,
            },
          ],
          id: 25,
          info: "",
          name: "*",
          numberofparticipationsround: 2,
          official: "12:00:00",
          scheduled: "19:00:00",
          seqno: 1,
          session: "Field",
          status: 2,
        },
      ],
      seqno: 31,
      status: 0,
    },
    {
      abbreviation: "HJ I",
      categories: [{ abbreviation: "SEN M", id: 63, name: "Senior Men" }],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "High Jump Indoor",
        combinedeventsquantity: 0,
        distance: 0,
        fieldtype: "V",
        id: 244,
        implement: 0,
        name: "",
        shortcode: "HJ I",
        windmode: "N",
        windtime: "",
      },
      id: 26,
      info: "",
      name: "Senior Men High Jump",
      rounds: [
        {
          abbreviation: "",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 70,
              info: "0",
              numberofparticipations: 3,
              official: "12:00:00",
              scheduled: "19:45:00",
              seqno: 1,
              status: 2,
            },
          ],
          id: 26,
          info: "",
          name: "*",
          numberofparticipationsround: 3,
          official: "12:00:00",
          scheduled: "19:45:00",
          seqno: 1,
          session: "Field",
          status: 2,
        },
      ],
      seqno: 32,
      status: 0,
    },
  ],
  "Pole Vault Indoor": [
    {
      abbreviation: "PV I",
      categories: [
        { abbreviation: "U14 G", id: 52, name: "U14 Girls" },
        { abbreviation: "U15 G", id: 54, name: "U15 Girls" },
        { abbreviation: "U16 G", id: 56, name: "U16 Girls" },
        { abbreviation: "SEN W", id: 64, name: "Senior Women" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "Pole Vault Indoor",
        combinedeventsquantity: 0,
        distance: 0,
        fieldtype: "V",
        id: 307,
        implement: 0,
        name: "",
        shortcode: "PV I",
        windmode: "N",
        windtime: "",
      },
      id: 27,
      info: "",
      name: "Women Pole Vault",
      rounds: [
        {
          abbreviation: "",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 72,
              info: "0",
              numberofparticipations: 2,
              official: "12:00:00",
              scheduled: "18:30:00",
              seqno: 1,
              status: 2,
            },
          ],
          id: 27,
          info: "",
          name: "*",
          numberofparticipationsround: 2,
          official: "12:00:00",
          scheduled: "18:30:00",
          seqno: 1,
          session: "Field",
          status: 2,
        },
      ],
      seqno: 33,
      status: 0,
    },
    {
      abbreviation: "PV I",
      categories: [
        { abbreviation: "U14 B", id: 51, name: "U14 Boys" },
        { abbreviation: "U15 B", id: 53, name: "U15 Boys" },
        { abbreviation: "U16 B", id: 55, name: "U16 Boys" },
        { abbreviation: "SEN M", id: 63, name: "Senior Men" },
      ],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "Pole Vault Indoor",
        combinedeventsquantity: 0,
        distance: 0,
        fieldtype: "V",
        id: 307,
        implement: 0,
        name: "",
        shortcode: "PV I",
        windmode: "N",
        windtime: "",
      },
      id: 28,
      info: "",
      name: "Men Pole Vault",
      rounds: [
        {
          abbreviation: "",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 71,
              info: "0",
              numberofparticipations: 2,
              official: "12:00:00",
              scheduled: "18:30:00",
              seqno: 1,
              status: 2,
            },
          ],
          id: 28,
          info: "",
          name: "*",
          numberofparticipationsround: 2,
          official: "12:00:00",
          scheduled: "18:30:00",
          seqno: 1,
          session: "Field",
          status: 2,
        },
      ],
      seqno: 34,
      status: 0,
    },
  ],
  "3000m Indoor": [
    {
      abbreviation: "3000m Men",
      categories: [{ abbreviation: "SEN M", id: 63, name: "Senior Men" }],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "3000m Indoor",
        combinedeventsquantity: 0,
        distance: 3000,
        fieldtype: " ",
        id: 222,
        implement: 0,
        name: "",
        shortcode: "3000m I",
        windmode: "N",
        windtime: "",
      },
      id: 35,
      info: "",
      name: "Senior Men 3000m",
      rounds: [
        {
          abbreviation: "3000m Men",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 74,
              info: "0",
              numberofparticipations: 14,
              official: "12:00:00",
              scheduled: "19:54:00",
              seqno: 1,
              status: 2,
            },
          ],
          id: 35,
          info: "",
          name: "3000m Men",
          numberofparticipationsround: 14,
          official: "12:00:00",
          scheduled: "19:54:00",
          seqno: 1,
          session: "Seniors",
          status: 2,
        },
      ],
      seqno: 35,
      status: 1,
    },
  ],
  "Mile Indoor": [
    {
      abbreviation: "Mile W",
      categories: [{ abbreviation: "SEN W", id: 64, name: "Senior Women" }],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "Mile Indoor",
        combinedeventsquantity: 0,
        distance: 1609,
        fieldtype: " ",
        id: 221,
        implement: 0,
        name: "",
        shortcode: "Mile I",
        windmode: "N",
        windtime: "",
      },
      id: 36,
      info: "",
      name: "Senior Women Mile",
      rounds: [
        {
          abbreviation: "",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 78,
              info: "0",
              numberofparticipations: 14,
              official: "12:00:00",
              scheduled: "21:00:00",
              seqno: 1,
              status: 2,
            },
          ],
          id: 36,
          info: "",
          name: "*",
          numberofparticipationsround: 14,
          official: "12:00:00",
          scheduled: "21:00:00",
          seqno: 1,
          session: "Seniors",
          status: 2,
        },
      ],
      seqno: 36,
      status: 0,
    },
    {
      abbreviation: "Mile M",
      categories: [{ abbreviation: "SEN M", id: 63, name: "Senior Men" }],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "Mile Indoor",
        combinedeventsquantity: 0,
        distance: 1609,
        fieldtype: " ",
        id: 221,
        implement: 0,
        name: "",
        shortcode: "Mile I",
        windmode: "N",
        windtime: "",
      },
      id: 37,
      info: "",
      name: "Senior Men Mile",
      rounds: [
        {
          abbreviation: "",
          datescheduled: "2023-12-14",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 79,
              info: "0",
              numberofparticipations: 11,
              official: "12:00:00",
              scheduled: "21:06:00",
              seqno: 1,
              status: 2,
            },
            {
              attempts: 0,
              height: 0,
              id: 80,
              info: "0",
              numberofparticipations: 12,
              official: "12:00:00",
              scheduled: "21:09:00",
              seqno: 2,
              status: 2,
            },
          ],
          id: 37,
          info: "",
          name: "*",
          numberofparticipationsround: 23,
          official: "12:00:00",
          scheduled: "21:06:00",
          seqno: 1,
          session: "Seniors",
          status: 2,
        },
      ],
      seqno: 37,
      status: 0,
    },
  ],
};
