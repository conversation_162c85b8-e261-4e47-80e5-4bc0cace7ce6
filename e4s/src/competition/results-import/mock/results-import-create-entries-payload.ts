import { ITimeTronicsEvent } from "../results-import-models-timetronics";
import {
  IResultsImportEventGroupSummary,
  MapFlattenedParticipations,
} from "../results-import-models";

export const entriesPayloadTimeTronicsEvent: ITimeTronicsEvent = {
  abbreviation: "1500m",
  categories: [{ abbreviation: "SEN W", id: 64, name: "Senior Women" }],
  eventtype: {
    abbreviation: "",
    athletesquantity: 1,
    code: "1500m",
    combinedeventsquantity: 0,
    distance: 1500,
    fieldtype: " ",
    id: 264,
    implement: 0,
    name: "",
    shortcode: "1500m",
    windmode: "N",
    windtime: "",
  },
  id: 1,
  info: "NR: 4.06.42 - <PERSON><PERSON> (2020)  CR: 4.13.96 - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> (2013)  NL: 4:18.85 - <PERSON> ",
  name: "Senior Women 1500m",
  rounds: [
    {
      abbreviation: "",
      datescheduled: "2023-02-18",
      heats: [
        {
          attempts: 0,
          height: 0,
          id: 82,
          info: "0",
          numberofparticipations: 9,
          official: "16:01:13",
          scheduled: "15:40:00",
          seqno: 1,
          status: 5,
        },
        {
          attempts: 0,
          height: 0,
          id: 83,
          info: "0",
          numberofparticipations: 9,
          official: "16:01:13",
          scheduled: "15:45:00",
          seqno: 2,
          status: 5,
        },
      ],
      id: 1,
      info: "First 5 + 2 Fastest",
      name: "Heats",
      numberofparticipationsround: 18,
      official: "16:01:14",
      scheduled: "15:40:00",
      seqno: 1,
      session: "Distance Day 1",
      status: 5,
    },
    {
      abbreviation: "",
      datescheduled: "2023-02-19",
      heats: [
        {
          attempts: 0,
          height: 0,
          id: 87,
          info: "0",
          numberofparticipations: 13,
          official: "13:14:51",
          scheduled: "13:10:00",
          seqno: 1,
          status: 5,
        },
      ],
      id: 2,
      info: "",
      name: "Final",
      numberofparticipationsround: 13,
      official: "13:14:51",
      scheduled: "13:10:00",
      seqno: 2,
      session: "Distance Day 2",
      status: 5,
    },
  ],
  seqno: 1,
  status: 5,
};

export const entriesPayloadEventGroupSummary: IResultsImportEventGroupSummary =
  {
    type: "T",
    typeNo: 1,
    id: 5320,
    eventNo: 1,
    bibSortNo: "",
    name: "1500m",
    notes: "",
    security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
  } as any as IResultsImportEventGroupSummary;

export const entriesPayloadMapFlattenedParticipations: MapFlattenedParticipations =
  {
    "1": {
      URN: "117332",
      bib: "501",
      birthdate: "1998-01-26",
      firstName: "Carla",
      lastName: "SWEENEY",
      gender: "M",
      club: "Rathfarnham W.S.A.F. A.C.",
      competitionteam: "",
      team: "Rathfarnham W.S.A.F. A.C.",
      resultPosition: 1,
      result: "4:22.23",
      resultValue: 262.23,
      owner: { id: 0, name: "" },
      isSelected: false,
      isTeam: false,
      timeTronicsHeatListParticipation: {
        category: { abbreviation: "SEN W", id: 64, name: "Senior Women" },
        currentorder: 1,
        currentorder_combinedevent: 0,
        currentorder_round: 1,
        id: 2350,
        info: "",
        initialorder: 4,
        outofcompetition_combinedevent: 0,
        participants: [
          {
            competitor: {
              athlete: {
                birthdate: "1998-01-26",
                firstname: "Carla",
                gender: "W",
                id: 2095,
                lastname: "SWEENEY",
                middlename: "",
                nationality: "Ireland",
              },
              bib: "501",
              competitionteam: {},
              displayname: "Carla SWEENEY",
              id: 1623,
              license: "117332",
              team: {
                abbreviation: "Rathfarnham W.S.A.F. A.C.",
                id: 1265,
                name: "Rathfarnham W.S.A.F. A.C.",
              },
            },
            id: 2582,
            seqno: 1,
          },
        ],
        penalties: [],
        personalbest: {
          alltimeflag: 1,
          date: "2022-02-18",
          id: 1577,
          result_value: 257,
          seasonflag: 1,
          value: "4:17.00",
          wind: 0,
        },
        points: 0,
        qualified: "",
        relay: false,
        results: [
          {
            bestresult: true,
            id: 899,
            info: "PHF0-3-35",
            metavalue: "",
            result_value: 262.23,
            seqno: 1,
            type: "PHF",
            value: "4:22.23",
            wind: 0,
          },
        ],
        teampoints: 0,
      },
    },
    "2": {
      URN: "121638",
      bib: "512",
      birthdate: "1994-01-27",
      firstName: "Amy",
      lastName: "O DONOGHUE",
      gender: "M",
      club: "Dundrum South Dublin A.C.",
      competitionteam: "",
      team: "Dundrum South Dublin A.C.",
      resultPosition: 2,
      result: "4:25.62",
      resultValue: 265.613,
      owner: { id: 19828, name: "nick wall" },
      isSelected: true,
      isTeam: false,
      timeTronicsHeatListParticipation: {
        category: { abbreviation: "SEN W", id: 64, name: "Senior Women" },
        currentorder: 2,
        currentorder_combinedevent: 0,
        currentorder_round: 2,
        id: 2356,
        info: "",
        initialorder: 5,
        outofcompetition_combinedevent: 0,
        participants: [
          {
            competitor: {
              athlete: {
                birthdate: "1994-01-27",
                firstname: "Amy",
                gender: "W",
                id: 2084,
                lastname: "O DONOGHUE",
                middlename: "",
                nationality: "Ireland",
              },
              bib: "512",
              competitionteam: {},
              displayname: "Amy O DONOGHUE",
              id: 1634,
              license: "121638",
              team: {
                abbreviation: "Dundrum South Dublin A.C.",
                id: 1248,
                name: "Dundrum South Dublin A.C.",
              },
            },
            id: 2588,
            seqno: 1,
          },
        ],
        penalties: [],
        personalbest: {
          alltimeflag: 1,
          date: "2022-02-18",
          id: 1587,
          result_value: 254.5,
          seasonflag: 1,
          value: "4:14.50",
          wind: 0,
        },
        points: 0,
        qualified: "",
        relay: false,
        results: [
          {
            bestresult: true,
            id: 900,
            info: "PHF0-3-35",
            metavalue: "",
            result_value: 265.613,
            seqno: 1,
            type: "PHF",
            value: "4:25.62",
            wind: 0,
          },
        ],
        teampoints: 0,
      },
    },
    "3": {
      URN: "192378",
      bib: "514",
      birthdate: "1992-06-13",
      firstName: "Ellie",
      lastName: "HARTNETT",
      gender: "M",
      club: "U.C.D. A.C.",
      competitionteam: "",
      team: "U.C.D. A.C.",
      resultPosition: 3,
      result: "4:26.43",
      resultValue: 266.427,
      owner: { id: 0, name: "" },
      isSelected: false,
      isTeam: false,
      timeTronicsHeatListParticipation: {
        category: { abbreviation: "SEN W", id: 64, name: "Senior Women" },
        currentorder: 3,
        currentorder_combinedevent: 0,
        currentorder_round: 3,
        id: 2357,
        info: "",
        initialorder: 6,
        outofcompetition_combinedevent: 0,
        participants: [
          {
            competitor: {
              athlete: {
                birthdate: "1992-06-13",
                firstname: "Ellie",
                gender: "W",
                id: 2082,
                lastname: "HARTNETT",
                middlename: "",
                nationality: "Ireland",
              },
              bib: "514",
              competitionteam: {},
              displayname: "Ellie HARTNETT",
              id: 1636,
              license: "192378",
              team: {
                abbreviation: "U.C.D. A.C.",
                id: 1270,
                name: "U.C.D. A.C.",
              },
            },
            id: 2589,
            seqno: 1,
          },
        ],
        penalties: [],
        personalbest: {
          alltimeflag: 1,
          date: "2022-02-18",
          id: 1588,
          result_value: 259.1,
          seasonflag: 1,
          value: "4:19.10",
          wind: 0,
        },
        points: 0,
        qualified: "",
        relay: false,
        results: [
          {
            bestresult: true,
            id: 901,
            info: "PHF0-3-35",
            metavalue: "",
            result_value: 266.427,
            seqno: 1,
            type: "PHF",
            value: "4:26.43",
            wind: 0,
          },
        ],
        teampoints: 0,
      },
    },
  };
