import { IResultsImportEventGroupSummary } from "../results-import-models";
import {ITimeTronicsEvent} from "../results-import-models-timetronics"

export const resultsImportEventGroupSummariesForComp355Mock: IResultsImportEventGroupSummary[] =
  [
    {
      type: "T",
      typeNo: 1,
      id: 5354,
      eventNo: 1,
      bibSortNo: "",
      name: "1500m",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F", "M"],
      eventName: "1500m",
    },
    {
      type: "T",
      typeNo: 2,
      id: 5355,
      eventNo: 2,
      bibSortNo: "",
      name: "200m",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F", "M"],
      eventName: "200m",
    },
    {
      type: "T",
      typeNo: 3,
      id: 5356,
      eventNo: 3,
      bibSortNo: "",
      name: "3000m Track Men",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["M"],
      eventName: "3000m",
    },
    {
      type: "T",
      typeNo: 4,
      id: 5357,
      eventNo: 4,
      bibSortNo: "",
      name: "3000m Track Women",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F"],
      eventName: "3000m",
    },
    {
      type: "T",
      typeNo: 5,
      id: 5358,
      eventNo: 5,
      bibSortNo: "",
      name: "3000m Walk",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F"],
      eventName: "3000m Walk",
    },
    {
      type: "T",
      typeNo: 6,
      id: 5359,
      eventNo: 6,
      bibSortNo: "",
      name: "400m",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F", "M"],
      eventName: "400m",
    },
    {
      type: "T",
      typeNo: 7,
      id: 5360,
      eventNo: 7,
      bibSortNo: "",
      name: "4x200 Relay",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F", "M"],
      eventName: "4 x 200",
    },
    {
      type: "T",
      typeNo: 8,
      id: 5361,
      eventNo: 8,
      bibSortNo: "",
      name: "5000m Walk",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["M"],
      eventName: "5000m Walk",
    },
    {
      type: "T",
      typeNo: 9,
      id: 5362,
      eventNo: 9,
      bibSortNo: "",
      name: "60m",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F", "M"],
      eventName: "60m",
    },
    {
      type: "T",
      typeNo: 10,
      id: 5363,
      eventNo: 10,
      bibSortNo: "",
      name: "60m Hurdles",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F", "M"],
      eventName: "60m Hurdles",
    },
    {
      type: "T",
      typeNo: 11,
      id: 5364,
      eventNo: 11,
      bibSortNo: "",
      name: "800m",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F", "M"],
      eventName: "800m",
    },
    {
      type: "F",
      typeNo: 12,
      id: 5365,
      eventNo: 12,
      bibSortNo: "",
      name: "High Jump Men",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["M"],
      eventName: "High Jump",
    },
    {
      type: "F",
      typeNo: 13,
      id: 5366,
      eventNo: 13,
      bibSortNo: "",
      name: "High Jump Women",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F"],
      eventName: "High Jump",
    },
    {
      type: "F",
      typeNo: 14,
      id: 5367,
      eventNo: 14,
      bibSortNo: "",
      name: "Long Jump Men",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["M"],
      eventName: "Long Jump",
    },
    {
      type: "F",
      typeNo: 15,
      id: 5368,
      eventNo: 15,
      bibSortNo: "",
      name: "Long Jump Women",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F"],
      eventName: "Long Jump",
    },
    {
      type: "F",
      typeNo: 16,
      id: 5369,
      eventNo: 16,
      bibSortNo: "",
      name: "Pole Vault Men",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["M"],
      eventName: "Pole Vault",
    },
    {
      type: "F",
      typeNo: 17,
      id: 5370,
      eventNo: 17,
      bibSortNo: "",
      name: "Pole Vault Women",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F"],
      eventName: "Pole Vault",
    },
    {
      type: "F",
      typeNo: 18,
      id: 5371,
      eventNo: 18,
      bibSortNo: "",
      name: "Shotput Men",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["M"],
      eventName: "Shotput",
    },
    {
      type: "F",
      typeNo: 19,
      id: 5372,
      eventNo: 19,
      bibSortNo: "",
      name: "Shotput Women",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F"],
      eventName: "Shotput",
    },
    {
      type: "F",
      typeNo: 20,
      id: 5373,
      eventNo: 20,
      bibSortNo: "",
      name: "Triple Jump Men",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["M"],
      eventName: "Triple Jump",
    },
    {
      type: "F",
      typeNo: 21,
      id: 5374,
      eventNo: 21,
      bibSortNo: "",
      name: "Triple Jump Women",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F"],
      eventName: "Triple Jump",
    },
    {
      type: "F",
      typeNo: 23,
      id: 5375,
      eventNo: 23,
      bibSortNo: "",
      name: "Weight for Distance 28lbs",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["F"],
      eventName: "Weight for Distance 28lbs",
    },
    {
      type: "F",
      typeNo: 22,
      id: 5376,
      eventNo: 22,
      bibSortNo: "",
      name: "Weight for Distance 56lbs",
      notes: null,
      security: { clubs: [], counties: [], regions: [] },
      genders: ["M"],
      eventName: "Weight for Distance 56lbs",
    },
  ] as any as IResultsImportEventGroupSummary[];

export const timeTronicsEventWalk3000mWomenMock: ITimeTronicsEvent = {
  abbreviation: "3000m Walk I",
  categories: [{ abbreviation: "SEN W", id: 64, name: "Senior Women" }],
  eventtype: {
    abbreviation: "",
    athletesquantity: 1,
    code: "3000m Walk Indoor",
    combinedeventsquantity: 0,
    distance: 3000,
    fieldtype: " ",
    id: 227,
    implement: 0,
    name: "",
    shortcode: "3000m Walk I",
    windmode: "N",
    windtime: "",
  },
  id: 9,
  info: "NR + CR: 11.35.34 - Gillian O'Sullivan, Farranfore Maine Valley (2003)  NL: 12:49.18 - Kate Veale ",
  name: "Senior Women 3000m Walk ",
  rounds: [
    {
      abbreviation: "",
      datescheduled: "2023-02-18",
      heats: [
        {
          attempts: 0,
          height: 0,
          id: 55,
          info: "0",
          numberofparticipations: 6,
          official: "13:05:33",
          scheduled: "12:45:00",
          seqno: 1,
          status: 5,
        },
      ],
      id: 14,
      info: "",
      name: "Final",
      numberofparticipationsround: 6,
      official: "13:05:33",
      scheduled: "12:45:00",
      seqno: 1,
      session: "Walks",
      status: 5,
    },
  ],
  seqno: 7,
  status: 5,
};
