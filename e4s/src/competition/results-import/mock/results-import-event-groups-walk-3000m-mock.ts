import {ITimeTronicsEvent} from "../results-import-models-timetronics"

export const timeTronicsEventWalk3000mWomenMock: ITimeTronicsEvent = {
  abbreviation: "3000m Walk I",
  categories: [{ abbreviation: "SEN W", id: 64, name: "Senior Women" }],
  eventtype: {
    abbreviation: "",
    athletesquantity: 1,
    code: "3000m Walk Indoor",
    combinedeventsquantity: 0,
    distance: 3000,
    fieldtype: " ",
    id: 227,
    implement: 0,
    name: "",
    shortcode: "3000m Walk I",
    windmode: "N",
    windtime: "",
  },
  id: 9,
  info: "NR + CR: 11.35.34 - <PERSON>, Farranfore Maine Valley (2003)  NL: 12:49.18 - <PERSON> ",
  name: "Senior Women 3000m Walk ",
  rounds: [
    {
      abbreviation: "",
      datescheduled: "2023-02-18",
      heats: [
        {
          attempts: 0,
          height: 0,
          id: 55,
          info: "0",
          numberofparticipations: 6,
          official: "13:05:33",
          scheduled: "12:45:00",
          seqno: 1,
          status: 5,
        },
      ],
      id: 14,
      info: "",
      name: "Final",
      numberofparticipationsround: 6,
      official: "13:05:33",
      scheduled: "12:45:00",
      seqno: 1,
      session: "Walks",
      status: 5,
    },
  ],
  seqno: 7,
  status: 5,
};
