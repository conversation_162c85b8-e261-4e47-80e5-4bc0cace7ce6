import {
  IFlattenedParticipation,
  IOutputResultsImportEventOnExpanded,
  IResultsImportEventGroupSummary,
  IResultsImportEventOutput,
  IResultsImportState,
  MapFlattenedParticipations,
  ResultsImportWhichComp,
  TimeTronicsEventGroupByKey,
} from "./results-import-models";
import * as ResultsImportService from "./results-import-service";
import { handleResponseMessages } from "../../common/handle-http-reponse";
import * as ResultsImportData from "./results-import-data";
import {
  convertArrayToObject,
  eventDateDisplay,
  simpleClone,
} from "../../common/common-service-utils";
import { BuilderData } from "../../builder/builder-data";
import {
  ITimeTronicsCompetitionServerResponse,
  ITimeTronicsEvent,
} from "./results-import-models-timetronics";
import {
  IParticipantOwner,
  IParticipantOwnersPayload,
  IParticipantPayeesPayload,
} from "./results-import-data";
import { ITimeTronicsHeatList } from "./results-import-models-timetronics-list";
import {
  IAthleteEntryFeeder,
  IFeederAllEntriesForCompByEventGroupIdByEntryId,
  ITeamEntryFeeder,
  ScoreboardData,
} from "../scoreboard/scoreboard-data";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { IR4sAthleteEntry } from "../scoreboard/rs4/rs4-scoreboard-models";
import { ENTITY_LEVEL_NUMBER, IEntity } from "../../config/config-app-models";
import {
  EventGroupIdNumber,
  EventGroupIdString,
} from "../../common/common-models";
import { IBuilderCompetition } from "../../builder/builder-models";

const builderData = new BuilderData();

const state: IResultsImportState =
  ResultsImportService.factoryResultsImportState();

const scoreboardData = new ScoreboardData();

export function resultsImportServiceController() {
  /**
   * Inits comp data
   * @param targetComp
   */
  function init(targetCompId: number, sourceCompId: number): Promise<unknown> {
    state.targetComp.id = targetCompId;
    state.sourceComp.id = sourceCompId;

    state.isLoading = true;
    let promSource;
    let promTarget;
    const proms: Promise<unknown>[] = [];

    if (state.sourceComp.id > 0) {
      promSource = builderData.read(state.sourceComp.id);
      proms.push(promSource);
      handleResponseMessages(promSource);
      promSource.then((resp) => {
        if (resp.errNo === 0) {
          setBuilderData(resp.data, "source");
        }
      });
    }

    if (state.targetComp.id > 0) {
      promTarget = builderData.read(state.targetComp.id);
      proms.push(promTarget);
      handleResponseMessages(promTarget);
      promTarget.then((resp) => {
        if (resp.errNo === 0) {
          setBuilderData(resp.data, "target");
        }
      });
    }

    return Promise.all(proms)
      .then(() => {
        if (state.userEntities.length === 1) {
          //  Will kick off the auto load of whole comp
          setEnity(state.userEntities[0]);
        }
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  function setBuilderData(
    builderCompetition: IBuilderCompetition,
    whichComp: ResultsImportWhichComp
  ) {
    const comp = whichComp === "target" ? state.targetComp : state.sourceComp;
    comp.builderCompetition = builderCompetition;
    comp.eventGroupSummaries = ResultsImportService.getEventGroupSummaries(
      builderCompetition.meta.compEvents
    );
    comp.eventGroupSummariesMap = convertArrayToObject((evetGrpSummary) => {
      return evetGrpSummary.id.toString();
    }, comp.eventGroupSummaries);

    // if (whichComp === "source") {
    //   const timeTronicsUrl =
    //     builderCompetition.options.autoEntries.selectedTargetComp
    //       .timeTronicsUrl;
    //   if (timeTronicsUrl && timeTronicsUrl.length > 0) {
    //     state.timeTronicsCompetitionServerUrl = timeTronicsUrl;
    //   }
    // }
  }

  function getCompDisplayName(source: "SOURCE" | "TARGET"): string {
    const comp = source === "SOURCE" ? state.sourceComp : state.targetComp;

    if (comp) {
      return (
        "(" +
        eventDateDisplay(comp.builderCompetition.date) +
        ") " +
        comp.builderCompetition.name
      );
    }
    return "";
  }

  /**
   * Gets comps and TT data
   */
  function getAllData() {
    const isTimeTronicsLoadedFromUrl =
      state.timeTronicsCompetitionServerUrl.length > 0;

    if (isTimeTronicsLoadedFromUrl) {
      resetTimeTronics();
    }

    const proms: Promise<unknown>[] = [
      init(state.targetComp.id, state.sourceComp.id),
      isTimeTronicsLoadedFromUrl
        ? getTimeTronicsServerResponse()
        : Promise.resolve(),
    ];
    return Promise.all(proms)
      .then(processTimeTronicsSchedule)
      .finally(() => {
        state.isLoading = false;
      });
  }

  function getTimeTronicsServerResponse(): Promise<void> {
    state.isLoading = true;
    const url = state.timeTronicsCompetitionServerUrl;
    if (url.length === 0) {
      return Promise.resolve();
    }
    return ResultsImportData.getTimeTronicsCompetition(
      url,
      state.useLocalServer
    )
      .then((resp) => {
        if (resp.errNo === 0) {
          state.timeTronicsCompetitionServerResponse = resp.data;
          processTimeTronicsSchedule();
        }
      })
      .catch((err) => {
        console.error("getTimeTronicsServerResponse() error", err);
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  function gotTimeTronicsFileFromDisk(file: File) {
    console.log("resultsImportController.gotTimeTronicsFileFromDisk", file);
    file.text().then((text) => {
      const payload = JSON.parse(
        text
      ) as any as ITimeTronicsCompetitionServerResponse;
      state.timeTronicsCompetitionServerResponse = payload;
      processTimeTronicsSchedule();
    });
  }

  /**
   * Gets state and applies ordering, etc.  badly named.
   */
  function processTimeTronicsSchedule() {
    console.log("resultsImportController.processTimeTronicsSchedule()");
    let timeTronicsCompetition =
      ResultsImportService.extractCompetitionFromResponse(
        simpleClone(state.timeTronicsCompetitionServerResponse!)
      );
    if (state.showOnlyFinals) {
      timeTronicsCompetition = ResultsImportService.removeEventsNoFinalEvents(
        timeTronicsCompetition!
      );
    }
    state.timeTronicsCompetition = timeTronicsCompetition;

    // const timeTronicsEvents = state.timeTronicsCompetition!.events;
    // if (state.timeTronicsFilterTerm.length > 0) {
    //   const timeTronicsEvents = state.timeTronicsCompetition!.events.filter(
    //     (timeTronicsEvent) => {
    //       return timeTronicsEvent.name
    //         .toLowerCase()
    //         .includes(state.timeTronicsFilterTerm.toLowerCase());
    //     }
    //   );
    // }

    state.timeTronicsEventsGrouped = ResultsImportService.groupEventsBy(
      state.timeTronicsCompetition!.events,
      state.timeTronicsEventsGroupBy
    );
    if (state.filterTerm.length > 0) {
      console.log(
        "resultsImportController.processTimeTronicsSchedule() filtering by term: " +
          state.filterTerm
      );
      state.timeTronicsEventsGroupedFilteredByEventName =
        ResultsImportService.filterTimeTronicsEventGroupResult(
          state.timeTronicsEventsGrouped as TimeTronicsEventGroupByKey,
          state.filterTerm
        );
    } else {
      console.log(
        "resultsImportController.processTimeTronicsSchedule() no filter term."
      );
      state.timeTronicsEventsGroupedFilteredByEventName = simpleClone(
        state.timeTronicsEventsGrouped as TimeTronicsEventGroupByKey
      );
    }
  }

  function resetTimeTronics() {
    // const ttstate = ResultsImportService.factoryTimeTronicsState();
    // const timeTronicsProps: (keyof ITimeTronicsState)[] = Object.keys(ttstate) as (keyof ITimeTronicsState)[];
    // timeTronicsProps.forEach((prop)=>{
    //   //  @ts-ignore
    //   state[prop] = ttstate[prop];
    // })

    state.timeTronics.timeTronicsEventMap = {};
    state.timeTronicsCompetition = null;
    state.timeTronicsCompetitionServerResponse = null;
    state.timeTronicsEventsGroupBy = "EVENT_TYPE";
    state.timeTronicsEventsGrouped = {};
  }

  /**
   * Given Sen Women 1500...i.e.  what we call an EventGroup, get it's data
   * @param resultsImportEventOutput
   */
  function getDataForEventList(
    resultsImportEventOutput: IResultsImportEventOutput
  ): Promise<void> {
    state.isLoadingGlobal = true;
    const timeTronicsEventList =
      state.timeTronics.timeTronicsEventMap[
        resultsImportEventOutput.timeTronicsEvent.id
      ];
    if (timeTronicsEventList) {
      //  Been loaded before.
      // messageDispatchHelper(
      //   "DEBUG: getDataForEventList " +
      //     timeTronicsEventList.name +
      //     " loaded before."
      // );
      return getEntriesAndOwners(resultsImportEventOutput).finally(() => {
        state.isLoadingGlobal = false;
      });
    }

    //  TimeTronics Results
    return getResults(resultsImportEventOutput.timeTronicsEvent)
      .then(() => {
        return getEntriesAndOwners(resultsImportEventOutput);
      })
      .finally(() => {
        state.isLoadingGlobal = false;
      });
  }

  function getEntriesAndOwners(
    resultsImportEventOutput: IResultsImportEventOutput
  ): Promise<void> {
    const proms: Promise<void>[] = [];
    //  E4S Entries

    proms.push(getEntries(resultsImportEventOutput.targetEventGroupSummary.id));

    if (
      resultsImportEventOutput.sourceEventGroupSummary &&
      resultsImportEventOutput.sourceEventGroupSummary.id
    ) {
      proms.push(
        getEntries(
          resultsImportEventOutput.sourceEventGroupSummary.id,
          "source"
        )
      );
    }

    //  E4S get heat Owners.
    // const promsOwnersHeats: Promise<unknown>[] = [];

    const timeTronicsEventList =
      state.timeTronics.timeTronicsEventMap[
        resultsImportEventOutput.timeTronicsEvent.id
      ];
    if (!timeTronicsEventList) {
      console.error(
        "resultsImportServiceController().getEntriesAndOwners resultsImportServiceController().getEntriesAndOwners no timeTronicsEventList!!! id: " +
          resultsImportEventOutput.timeTronicsEvent.id,
        {
          resultsImportEventOutput,
        }
      );
      messageDispatchHelper(
        "DEBUG: resultsImportServiceController().getEntriesAndOwners no timeTronicsEventList!!!"
      );
      return Promise.resolve();
    }

    //  TODO...@see 10 lines down.
    const urns = ResultsImportService.getUrnsFromTimeTronicsEventList(
      timeTronicsEventList,
      4
    );
    // resultsImportEventOutput.showTopXRows.

    const participantOwnersPayload: IParticipantOwnersPayload = {
      sourceEventGroupSummaryIds: [],
      urns,
    };

    if (
      resultsImportEventOutput.sourceEventGroupSummary &&
      resultsImportEventOutput.sourceEventGroupSummary.id !== 0
    ) {
      participantOwnersPayload.sourceEventGroupSummaryIds.push(
        resultsImportEventOutput.sourceEventGroupSummary.id
      );
      proms.push(
        retrieveParticipantPayees({
          sourceEventGroupSummaryId:
            resultsImportEventOutput.sourceEventGroupSummary.id,
          urns,
        })
      );
    }

    if (
      resultsImportEventOutput.targetEventGroupSummary &&
      resultsImportEventOutput.targetEventGroupSummary.id !== 0
    ) {
      participantOwnersPayload.sourceEventGroupSummaryIds.push(
        resultsImportEventOutput.targetEventGroupSummary.id
      );
    }

    // const participantOwnersPayload: IParticipantOwnersPayload =
    //   ResultsImportService.createParticipantOwnersPayload(
    //     resultsImportEventOutput
    //   );

    proms.push(setParticipantOwners(participantOwnersPayload));

    // @ts-ignore
    return Promise.all(proms).finally(() => {
      console.log("getDataForEventList() finished.");
    });
  }

  function getResults(timeTronicsEvent: ITimeTronicsEvent): Promise<void> {
    // http://results.athleticsireland.ie/results_19022023/menu.json
    // http://results.athleticsireland.ie/results_19022023/5_list.json

    const url = state.timeTronicsCompetitionServerUrl
      .replace(".html", ".json")
      .replace("menu.json", timeTronicsEvent.id + "_list.json");

    // const url =
    //   "http://results.athleticsleinster.com/Outdoors-2024/PairsU12U13/1_list.json";

    state.isLoading = true;
    const prom = ResultsImportData.getTimeTronicsEventList(
      url,
      state.useLocalServer
    );
    handleResponseMessages(prom);
    return prom
      .then((resp) => {
        if (resp.errNo === 0) {
          resp.data.forEach((timeTronicsEventList) => {
            state.timeTronics.timeTronicsEventMap[timeTronicsEventList.id] =
              timeTronicsEventList;
          });
        }
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  function getOwnersForParticipants(
    flattenedParticipations: IFlattenedParticipation[],
    sourceEventGroupSummaryId: number
  ): Promise<unknown> {
    if (sourceEventGroupSummaryId > 0) {
      const urns = flattenedParticipations.map((participation) => {
        return Number(participation.URN);
      });

      return doParticipantOwners(urns, [sourceEventGroupSummaryId]);
    }
    return Promise.resolve();
  }

  function getParticipantOwners(
    timeTronicsHeatList: ITimeTronicsHeatList,
    eventGroupIds: number[],
    topX: number = 4
  ): Promise<unknown> {
    console.warn("ResultsImportServiceController.getParticipantOwners()", {
      timeTronicsHeatList,
      eventGroupIds,
      topX,
    });

    // TT's are string, e4s, number...
    const urns: number[] = ResultsImportService.getUrnsFromHeat(
      timeTronicsHeatList,
      topX
    ).map((urn) => {
      return Number(urn);
    });

    return doParticipantOwners(urns, eventGroupIds);

    /*
    const participantOwnersPayload: IParticipantOwnersPayload = {
      sourceEventGroupSummaryIds: eventGroupIds,
      urns,
    };

    return Promise.all([
      setParticipantOwners(participantOwnersPayload),
      retrieveParticipantPayees({
        sourceEventGroupSummaryId: eventGroupIds[0],
        urns,
      }),
    ]);
    */

    // return setParticipantOwners(participantOwnersPayload);
  }

  function doParticipantOwners(
    urns: number[],
    eventGroupIds: number[]
  ): Promise<unknown> {
    const participantOwnersPayload: IParticipantOwnersPayload = {
      sourceEventGroupSummaryIds: eventGroupIds,
      urns,
    };

    return Promise.all([
      setParticipantOwners(participantOwnersPayload),
      retrieveParticipantPayees({
        sourceEventGroupSummaryId: eventGroupIds[0],
        urns,
      }),
    ]);
  }

  function setParticipantOwners(
    participantOwnersPayload: IParticipantOwnersPayload
  ): Promise<void> {
    console.log("ResultsImportServiceController.setParticipantOwners()", {
      participantOwnersPayload,
    });
    return ResultsImportData.getParticipantOwnersApi(
      participantOwnersPayload
    ).then((resp) => {
      if (resp.errNo === 0) {
        //  May need to just keep appeneding to athletes "owners"...or store via all the event groups????
        //  we don't get the eventgroupid back with the matches...and u can send in
        //  multiple group ids, so you don't know which owner matches to which....
        //  so...phase one...just "append".
        // const currentState = state.e4s.athletes;

        const newState = simpleClone(state.e4s.athletes);
        const athleteUrns: number[] = Object.keys(resp.data.athletes).map(
          (urn) => Number(urn)
        );
        athleteUrns.forEach((urn) => {
          const participantOwnerResp: IParticipantOwner = newState[urn];
          if (!newState[urn]) {
            newState[urn] = resp.data.athletes[urn];
          } else {
            newState[urn].entries = {
              ...newState[urn].entries,
              ...participantOwnerResp.entries,
            };

            newState[urn].owners = {
              ...newState[urn].owners,
              ...participantOwnerResp.owners,
            };
          }
        });
        state.e4s.athletes = newState;
      }
    });
  }

  function retrieveParticipantPayees(
    participantPayeesPayload: IParticipantPayeesPayload
  ): Promise<void> {
    return ResultsImportData.getParticipantPayeesApi(
      participantPayeesPayload
    ).then((resp) => {
      if (resp.errNo === 0) {
        state.e4s.athletesPayees = convertArrayToObject("urn", resp.data);
      }

      /*
      //[5:187]: U11 Girls 600m
      // No Finals
      state.e4s.athletesPayees = {
        "329208": {
          entryId: 267679,
          athleteId: 66503,
          urn: 329208,
          user: {
            id: 25043,
            name: "georgina-cousins",
            displayName: "Georgina Cousins",
            email: "<EMAIL>",
          },
        },
        "352596": {
          entryId: 268573,
          athleteId: 148058,
          urn: 352596,
          user: {
            id: 17875,
            name: "sheilaobyrne",
            displayName: "sheilaobyrne",
            email: "<EMAIL>",
          },
        },
        "365132": {
          entryId: 267271,
          athleteId: 166373,
          urn: 365132,
          user: {
            id: 1296,
            name: "pdoheircom-net",
            displayName: "Peter Doherty",
            email: "<EMAIL>",
          },
        },
        "368941": {
          entryId: 269072,
          athleteId: 163844,
          urn: 368941,
          user: {
            id: 24568,
            name: "andrew-quinn",
            displayName: "Andrew Quinn",
            email: "<EMAIL>",
          },
        },
        "370879": {
          entryId: 271168,
          athleteId: 171163,
          urn: 370879,
          user: {
            id: 19262,
            name: "claireul",
            displayName: "ClaireUL",
            email: "<EMAIL>",
          },
        },
        "376453": {
          entryId: 267270,
          athleteId: 177470,
          urn: 376453,
          user: {
            id: 1296,
            name: "pdoheircom-net",
            displayName: "Peter Doherty",
            email: "<EMAIL>",
          },
        },
        "416040": {
          entryId: 268580,
          athleteId: 221643,
          urn: 416040,
          user: {
            id: 17875,
            name: "sheilaobyrne",
            displayName: "sheilaobyrne",
            email: "<EMAIL>",
          },
        },
        "432251": {
          entryId: 269186,
          athleteId: 236980,
          urn: 432251,
          user: {
            id: 1474,
            name: "anna-guilfoyle",
            displayName: "Anna Hyland Guilfoyle",
            email: "<EMAIL>",
          },
        },
      };
      */

      /*
      state.e4s.athletesPayees = {
        "328473": {
          entryId: 267672,
          athleteId: 65459,
          urn: 328473,
          user: {
            id: 25043,
            name: "georgina-cousins",
            displayName: "Georgina Cousins",
            email: "<EMAIL>",
          },
        },
        "332290": {
          entryId: 264064,
          athleteId: 70139,
          urn: 332290,
          user: {
            id: 18069,
            name: "sally-clarke",
            displayName: "Sally Clarke",
            email: "<EMAIL>",
          },
        },
        "352328": {
          entryId: 268886,
          athleteId: 147559,
          urn: 352328,
          user: {
            id: 582,
            name: "csmythtcd-ie",
            displayName: "Cyril J Smyth",
            email: "<EMAIL>",
          },
        },
        "356731": {
          entryId: 265236,
          athleteId: 153578,
          urn: 356731,
          user: {
            id: 27266,
            name: "deirdreclarke81",
            displayName: "deirdreclarke81",
            email: "<EMAIL>",
          },
        },
        "356806": {
          entryId: 268652,
          athleteId: 153688,
          urn: 356806,
          user: {
            id: 722,
            name: "deirdremcteggartgmail-com",
            displayName: "Deirdre McTeggart",
            email: "<EMAIL>",
          },
        },
        "360575": {
          entryId: 263910,
          athleteId: 157755,
          urn: 360575,
          user: {
            id: 1909,
            name: "sjfeircom-net",
            displayName: "John Finn",
            email: "<EMAIL>",
          },
        },
        "363121": {
          entryId: 268223,
          athleteId: 169434,
          urn: 363121,
          user: {
            id: 24883,
            name: "katelynn",
            displayName: "Katelynn",
            email: "<EMAIL>",
          },
        },
        "369313": {
          entryId: 268787,
          athleteId: 166800,
          urn: 369313,
          user: {
            id: 24578,
            name: "jasperemma",
            displayName: "Jasperemma",
            email: "<EMAIL>",
          },
        },
        "410418": {
          entryId: 267389,
          athleteId: 216241,
          urn: 410418,
          user: {
            id: 962,
            name: "sineadgleeson2gmail-com",
            displayName: "Sinead Gleeson",
            email: "<EMAIL>",
          },
        },
        "424954": {
          entryId: 269598,
          athleteId: 230462,
          urn: 424954,
          user: {
            id: 19397,
            name: "aceathleticsclub-ie",
            displayName: "aceathleticsclub.ie",
            email: "<EMAIL>",
          },
        },
      };
      */
    });
  }

  function setEnity(entity: IEntity) {
    state.userEtity = simpleClone(entity);
    state.isLoadingGlobal = true;
    getAllEntries().then(() => {
      state.isLoadingGlobal = false;
      // getAllData();
    });
  }

  function getAllEntries(): Promise<void> {
    // .getAllEntriesForComp(
    //     state.targetComp.builderCompetition.id,
    //     state.userEtity.id,
    //     state.userEtity.entityLevel as ENTITY_LEVEL_NUMBER
    //   )

    return scoreboardData
      .getAllEntriesForCompIncludingUnpaid(
        state.targetComp.builderCompetition.id,
        0,
        0
      )
      .then((resp) => {
        if (resp.errNo === 0) {
          const feederAllEntriesForComp: IFeederAllEntriesForCompByEventGroupIdByEntryId =
            resp.data;

          const eventGroupIds: (EventGroupIdNumber | EventGroupIdString)[] =
            Object.keys(feederAllEntriesForComp);

          /*

          const entriesByEventGroupIdByAthleteUrn: MapEntriesByEventGroupIdByAthleteUrn =
            eventGroupIds.reduce<MapEntriesByEventGroupIdByAthleteUrn>(
              (accum, eventGroupId) => {
                const eventGroupIdString = eventGroupId.toString();

                //  This is how we map the entries in the event group to an athlete or team.
                //  Add the event group id as key, then add athlete urn(s) as key in the event group.
                const feederAllEntriesByEntryId: IFeederAllEntriesByEntryId =
                  feederAllEntriesForComp[eventGroupId as any as number];

                const athleteEntriesWithinEventGroup: IR4sAthleteEntry[] =
                  Object.values(feederAllEntriesByEntryId);

                //  Set up an object with event group and in it all athletes by urn.
                accum[eventGroupIdString] = convertArrayToObject(
                  (athleteEntry) => {
                    const athleteEntryTemp:
                      | ITeamEntryFeeder
                      | IAthleteEntryFeeder = athleteEntry as any as IAthleteEntryFeeder;

                    //  If athlete, use the urn, if team, use concatentation of athlete urns.
                    const entryKey =
                      ResultsImportService.getKeyForEntriesMap(
                        athleteEntryTemp
                      );

                    // return athleteEntry.urn ? athleteEntry.urn : -1;
                    return entryKey;
                  },
                  athleteEntriesWithinEventGroup
                );

                return accum;
              },
              {}
            );
*/

          state.targetComp.entriesEventGroupUrnMap =
            ResultsImportService.convertAllEntriesForCompToEventGroupUrns(
              feederAllEntriesForComp
            );

          //  TODO
          /*
          state.targetComp.entriesEventGroupUrnMap = Object.keys(
            resp.data
          ).reduce<
            Record<
              EventGroupIdNumber,
              Record<AthleteUrnNumber, IR4sAthleteEntry>
            >
          >((accum, key) => {
            const keyTemp = key as any as number;

            //  This is how we map the entries in the event group to an athlete or team.
            accum[keyTemp] = convertArrayToObject((athleteEntry) => {
              const athleteEntryTemp: ITeamEntryFeeder | IAthleteEntryFeeder =
                athleteEntry as any as IAthleteEntryFeeder;

              const entryKey =
                ResultsImportService.getKeyForEntriesMap(athleteEntryTemp);

              // return athleteEntry.urn ? athleteEntry.urn : -1;
              return entryKey;
            }, Object.values(data[keyTemp]));
            return accum;
          }, {});
          */

          state.targetComp.entriesE4sResultImportSeqEventKey =
            ResultsImportService.mapAllEntriesByResultKey(
              feederAllEntriesForComp
            );

          state.targetComp.entries = eventGroupIds.reduce<
            Record<EventGroupIdNumber, IR4sAthleteEntry[]>
          >((accum, key) => {
            const eventGroupId = key as any as number;
            accum[eventGroupId] = Object.values(
              feederAllEntriesForComp[eventGroupId]
            );
            return accum;
          }, {});

          // state.targetComp.entries[eventGroupId] = response.data;
        }
      });
  }

  function getEntries(
    eventGroupId: number,
    whichComp: ResultsImportWhichComp = "target"
  ): Promise<void> {
    const compState =
      whichComp === "target" ? state.targetComp : state.sourceComp;

    // .getEntriesForEventGroup(
    //     compState.builderCompetition.id,
    //     eventGroupId,
    //     state.userEtity.id,
    //     state.userEtity.entityLevel as ENTITY_LEVEL_NUMBER
    //   )..

    return scoreboardData
      .getEntriesForEventGroupIncludingUnpaid(
        compState.builderCompetition.id,
        eventGroupId,
        0,
        0
      )
      .then((response) => {
        if (response.errNo === 0) {
          // compState.entries[eventGroupId] = response.data
          const entries = simpleClone(compState.entries);
          entries[eventGroupId] = response.data;

          compState.entries = entries;

          if (response.data.length > 0) {
            compState.entriesEventGroupUrnMap[eventGroupId] =
              ResultsImportService.mapEntriesEventGroupUrnMap(
                response.data as (ITeamEntryFeeder | IAthleteEntryFeeder)[]
              );
          }
        }
      });
  }

  function doSubmitEntries(
    timeTronicsEvent: ITimeTronicsEvent,
    resultsImportEventGroupSummary: IResultsImportEventGroupSummary,
    mapFlattenedParticipations: MapFlattenedParticipations
  ): Promise<void> {
    console.log("resultsImportServiceController().doSubmitEntries()", {
      timeTronicsEvent,
      resultsImportEventGroupSummary,
      mapFlattenedParticipations,
    });
    const payload = ResultsImportService.createSubmitEntryPayload(
      {
        id: state.userEtity.id,
        level: state.userEtity.entityLevel as ENTITY_LEVEL_NUMBER,
      },
      timeTronicsEvent,
      resultsImportEventGroupSummary,
      mapFlattenedParticipations
    );
    state.isLoadingGlobal = true;
    return ResultsImportData.submitEntries(payload)
      .then((resp) => {
        if (resp.errNo === 0) {
          return getEntries(resultsImportEventGroupSummary.id);
        }
        return;
      })
      .finally(() => {
        state.isLoadingGlobal = false;
      });
  }

  function getResultsImportEventGroupSummaryFor(
    timeTronicsEvent: ITimeTronicsEvent,
    whichComp: ResultsImportWhichComp
  ) {
    return ResultsImportService.getResultsImportEventGroupSummaryFor(
      state,
      timeTronicsEvent,
      whichComp
    );
  }

  function showFinalsOnlyChanged(showfinals: boolean) {
    console.log("showFinalsOnlyChanged() showfinals: " + showfinals);
    state.showOnlyFinals = showfinals;
    startFilter();
    processTimeTronicsSchedule();
  }

  function filterTermChanged(filterTerm: string) {
    console.log("filterTermChanged() filterTerm: " + filterTerm);
    state.filterTerm = filterTerm;
    startFilter();
    processTimeTronicsSchedule();
  }

  function startFilter() {
    console.log("startFilter()");
  }

  function onResultsImportEventExpanded(
    outputResultsImportEventOnExpanded: IOutputResultsImportEventOnExpanded
  ) {
    console.log(
      "resultsImportServiceController().onResultsImportEventExpanded()",
      {
        outputResultsImportEventOnExpanded,
      }
    );
  }

  return {
    state,
    getAllData,
    init,
    getTimeTronicsServerResponse,
    loadData: processTimeTronicsSchedule,
    getDataForEventList,
    getParticipantOwners,
    getOwnersForParticipants,
    getEntries,
    getAllEntries,
    setEnity,
    doSubmitEntries,
    getResultsImportEventGroupSummaryFor,
    resetTimeTronics,
    gotTimeTronicsFileFromDisk,
    showFinalsOnlyChanged,
    filterTermChanged,
    retrieveParticipantPayees,
    onResultsImportEventExpanded,
    getCompDisplayName,
  };
}
