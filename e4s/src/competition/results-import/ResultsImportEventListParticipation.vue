<template>
  <tr
    class="e4s-tt-import-results-grid-row"
    :class="
      alreadyEntered
        ? 'results-import-event-list-participation--already-entered'
        : ''
    "
  >
    <td
      class="results-import-event-list-participation--td"
      v-text="flattenedParticipationInternal.resultPosition"
    ></td>
    <td class="results-import-event-list-participation--td">
      <abbr
        v-text="flattenedParticipationInternal.bib"
        :title="getHoverTitle"
      ></abbr>
    </td>
    <td class="results-import-event-list-participation--td">
      <PrimaryLink
        :link-text="getName"
        :link="getAthleteUrlLink"
        target="_blank"
      ></PrimaryLink>
      <!--      <span v-text="getName"></span>-->
      <!--      <div class="e4s-flex-row e4s-gap&#45;&#45;standard e4s-form-sub-header">-->
      <!--        <span v-text="athleteEntry ? 'y' : '-'"></span>-->
      <!--        :-->
      <!--        <span v-text="athleteEntry<PERSON>ey"></span>-->
      <!--      </div>-->
    </td>
    <td
      class="results-import-event-list-participation--td"
      v-text="getClubOrTeamTitle"
    ></td>
    <td
      class="results-import-event-list-participation--td"
      v-text="flattenedParticipationInternal.result"
    ></td>
    <td class="results-import-event-list-participation--td">
      <!--      <UserTypeAhead v-on:onSelected="onUserSelected"/>-->
      <!--      <input-->
      <!--        type="text"-->
      <!--        class="browser-default e4s-input-field e4s-input-field&#45;&#45;primary"-->
      <!--      />-->
      <!--      <div class="e4s-flex-row">ownerSetHow: {{ ownerSetHow }}</div>-->

      <div class="e4s-flex-column">
        <div
          class="
            e4s-flex-row
            e4s-gap--standard
            e4s-justify-flex-row-vert-center
          "
        >
          <div
            v-if="!alreadyEntered"
            class="
              e4s-flex-row
              e4s-gap--standard
              e4s-justify-flex-row-vert-center
            "
          >
            <PrimaryLink
              link-text="View"
              v-if="hasOwner"
              :link="getUserUrlLink"
              :disabled="!isOwnerPresent"
              target="_blank"
            ></PrimaryLink>
            <select
              v-model="ownerSelected"
              class="browser-default e4s-input-field e4s-input-field--primary"
              :class="getMatchCss"
              v-if="hasAnyOwners && !showTypeAheadOverride"
            >
              <option></option>
              <option
                v-for="owner in owners"
                :value="owner"
                :key="owner.id"
                v-text="getOwnerDisplayName(owner)"
              ></option>
            </select>

            <PrimaryLink
              :link-text="showBestMatchMessage ? '-' : '+'"
              @onClick="showBestMatchMessage = !showBestMatchMessage"
            ></PrimaryLink>

            <div v-if="!hasAnyOwners">
              <UserTypeAheadSimple
                v-on:onSelected="onUserSelected"
                v-if="!hasOwner"
              />
              <div
                v-if="hasOwner"
                class="
                  e4s-flex-row
                  e4s-gap--standard
                  e4s-justify-flex-row-vert-center
                "
              >
                <span v-text="getOwnerName"></span>
                <PrimaryLink
                  link-text="Edit"
                  @onClick="onUserEdit"
                ></PrimaryLink>
              </div>
            </div>

            <!--We might want to override the dropdown.-->
            <ButtonGenericV2
              v-if="!hasOwner && hasAnyOwners"
              class="e4s-button--slim"
              :text="showTypeAheadOverride ? '-' : '+'"
              @click="showTypeAheadOverride = !showTypeAheadOverride"
              style="width: 30px"
            />
          </div>

          <!--          <span v-if="alreadyEntered" v-text="getOwnerName"></span>-->
          <PrimaryLink
            :link-text="getOwnerName"
            v-if="alreadyEntered"
            :link="getUserUrlLink"
            target="_blank"
          ></PrimaryLink>
        </div>

        <!--Debug section-->
        <div v-if="showBestMatchMessage">
          <div
            v-text="
              bestMatchOwner2.message +
              (bestMatchOwner2.owner
                ? ' (' + bestMatchOwner2.owner.name + ')'
                : '')
            "
          ></div>
          <div class="e4s-flex-row">ownerSetHow: {{ ownerSetHow }}</div>
          <!--If we have owners, so drop down is showing...but we need another name.-->
          <div class="e4s-flex-row e4s-gap--large">
            <PrimaryLink
              v-if="hasAnyOwners"
              link-text="TypeAhead"
              @onClick="showTypeAheadOverride = !showTypeAheadOverride"
            ></PrimaryLink>
            <UserTypeAheadSimple
              v-on:onSelected="onUserSelected"
              v-if="showTypeAheadOverride"
            />
          </div>
        </div>
        <!--/Debug section-->
      </div>
    </td>
    <td class="results-import-event-list-participation--td">
      <span
        v-if="alreadyEntered"
        v-text="athleteEntry.paid ? 'Paid' : 'Cart'"
      ></span>
      <p v-if="!alreadyEntered">
        <label>
          <input
            :disabled="!isOwnerPresent"
            class="e4s-checkbox"
            type="checkbox"
            v-model="flattenedParticipationInternal.isSelected"
            v-on:change="onChanged"
          />
          <span class="e4s-bold"> </span>
        </label>
      </p>
    </td>
  </tr>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "vue";
import UserTypeAhead from "../../admin/user/user-type-ahead.vue";
import { IUserSummary } from "../../admin/user/user-models";
import {
  IFlattenedParticipation,
  IResultsImportEventGroupSummary,
} from "./results-import-models";
import { simpleClone, unique } from "../../common/common-service-utils";
import {
  IOwner,
  IParticipantOwner,
  IParticipantPayee,
  OwnerType,
} from "./results-import-data";
import { IR4sAthleteEntry } from "../scoreboard/rs4/rs4-scoreboard-models";
import UserTypeAheadSimple from "../../admin/user/user-type-ahead-simple.vue";
import * as ResultsImportService from "./results-import-service";
import { IBestMatchOwnerResult } from "./results-import-service";
import PrimaryLink from "../../common/ui/layoutV2/href/PrimaryLink.vue";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";

export default defineComponent({
  name: "ResultsImportEventListParticipation",
  components: {
    ButtonGenericV2,
    PrimaryLink,
    UserTypeAheadSimple,
    UserTypeAhead,
  },
  props: {
    flattenedParticipation: {
      type: Object as PropType<IFlattenedParticipation>,
      required: true,
    },
    participantOwner: {
      type: Object as PropType<IParticipantOwner | undefined>,
    },
    participantPayee: {
      type: Object as PropType<IParticipantPayee | undefined>,
    },
    athleteEntry: {
      type: Object as PropType<IR4sAthleteEntry | undefined>,
    },
    athleteEntryKey: {
      type: String,
      default: "",
    },
    targetEventGroupSummary: {
      type: Object as PropType<IResultsImportEventGroupSummary | undefined>,
    },
    sourceEventGroupSummary: {
      type: Object as PropType<IResultsImportEventGroupSummary | undefined>,
    },
  },
  setup(
    props: {
      flattenedParticipation: IFlattenedParticipation;
      participantOwner: IParticipantOwner | undefined;
      participantPayee: IParticipantPayee | undefined;
      athleteEntry: IR4sAthleteEntry | undefined;
      athleteEntryKey: string;
      targetEventGroupSummary: IResultsImportEventGroupSummary | undefined;
      sourceEventGroupSummary: IResultsImportEventGroupSummary | undefined;
    },
    context: SetupContext
  ) {
    const flattenedParticipationInternal = ref(
      simpleClone(props.flattenedParticipation)
    );

    //  No owners or the owner is not in the list...I need ot override the dropdown.
    const showTypeAheadOverride = ref(false);

    const owners = ref<IOwner[]>([]);
    const ownerSelected = ref<IOwner | null>(null);
    const ownerSetHow = ref("");
    const bestMatchOwner = ref<IOwner | null>(null);
    const bestMatchOwner2 = ref<IBestMatchOwnerResult>({
      owner: null,
      message: "",
      code: "NA",
    });

    const showBestMatchMessage = ref(false);

    setOwners();

    watch(
      () => props.flattenedParticipation,
      (newValue: IFlattenedParticipation, oldValue: any) => {
        if (simpleClone(newValue) !== simpleClone(oldValue)) {
          flattenedParticipationInternal.value = simpleClone(newValue);
        }
      }
    );

    watch(
      () => props.participantOwner,
      (
        newValue: IParticipantOwner | undefined,
        oldValue: IParticipantOwner | undefined
      ) => {
        if (hasParticipantOwner(newValue)) {
          setOwners();
        }
      }
    );

    watch(
      () => props.participantPayee,
      (
        newValue: IParticipantPayee | undefined,
        oldValue: IParticipantPayee | undefined
      ) => {
        if (hasParticipantPayee(newValue) && newValue !== oldValue) {
          setOwners();
        }
      }
    );

    watch(
      () => props.athleteEntry,
      (
        newValue: IR4sAthleteEntry | undefined,
        oldValue: IR4sAthleteEntry | undefined
      ) => {
        if (newValue) {
          // setBestMatchOwner();
        }
      }
    );

    watch(
      () => ownerSelected.value,
      (newValue: IOwner | null, oldValue: any) => {
        if (newValue) {
          flattenedParticipationInternal.value.owner = {
            id: newValue.id,
            name: newValue.name,
          };
        }
      }
    );

    function onChanged() {
      console.log(
        "ResultsImportEventListParticipation onChanged",
        flattenedParticipationInternal.value
      );
      context.emit(
        "onParticipationChanged",
        simpleClone(flattenedParticipationInternal.value)
      );
    }

    function onUserSelected(userSummary: IUserSummary) {
      flattenedParticipationInternal.value.owner = {
        id: userSummary.id,
        name: userSummary.displayName,
      };
      flattenedParticipationInternal.value.isSelected = userSummary.id > 0;

      ownerSelected.value = {
        id: userSummary.id,
        name: userSummary.displayName,
        types: [],
      };

      onChanged();
    }

    function onUserEdit() {
      // flattenedParticipationInternal.value.owner = {
      //   id: 0,
      //   name: "",
      // };
      // flattenedParticipationInternal.value.isSelected = false;
      onUserSelected({
        id: 0,
        name: "",
      } as IUserSummary);
    }

    const hasOwner = computed(() => {
      return (
        flattenedParticipationInternal.value.owner &&
        flattenedParticipationInternal.value.owner.id > 0
      );
    });

    function hasParticipantOwner(
      participantOwner: IParticipantOwner | undefined
    ): boolean {
      if (!participantOwner) {
        return false;
      }
      return participantOwner && participantOwner.id > 0;
      // Object.keys(participantOwner.owners).length > 0;
    }

    function howMayParticipantOwners(
      participantOwner: IParticipantOwner | undefined
    ): number {
      if (hasParticipantOwner(participantOwner)) {
        console.log(
          "howMayParticipantOwners",
          Object.keys(participantOwner!.owners).length
        );
        return Object.keys(participantOwner!.owners).length;
      }
      return 0;
    }

    const getOwnerName = computed(() => {
      if (props.athleteEntry && props.athleteEntry.athleteId > 0) {
        const aEntry: IR4sAthleteEntry = props.athleteEntry as IR4sAthleteEntry;
        return aEntry.user ? aEntry.user.id + ": " + aEntry.user.name : "UNK";
      }
      return (
        flattenedParticipationInternal.value.owner.id +
        ": " +
        flattenedParticipationInternal.value.owner.name
      );
    });

    const getName = computed(() => {
      return ResultsImportService.getNameFromFlattenedParticipation(
        props.flattenedParticipation
      );
    });

    const getClubOrTeamTitle = computed(() => {
      return ResultsImportService.getClubOrTeamAthletesFromFlattenedParticipation(
        props.flattenedParticipation
      );
    });

    const alreadyEntered = computed(() => {
      return !!(props.athleteEntry && props.athleteEntry.entryId > 0);
    });

    const hasAnyOwners = computed(() => {
      const hasOwners = !!(
        props.participantOwner &&
        Object.keys(props.participantOwner.owners).length > 0
      );
      const hasPayee = hasParticipantPayee(props.participantPayee);

      return hasOwners || hasPayee;
    });

    function hasParticipantPayee(
      participantPayee: IParticipantPayee | undefined
    ): boolean {
      if (!participantPayee) {
        return false;
      }
      return participantPayee && participantPayee.user.id > 0;
    }

    function setOwners(): void {
      const hasPayee = hasParticipantPayee(props.participantPayee);
      const howManyOwners = howMayParticipantOwners(props.participantOwner);
      const hasAnyOwners = howManyOwners > 0;

      if (hasAnyOwners || hasPayee) {
        let ownersLocal: IOwner[] = simpleClone(owners.value);
        if (hasAnyOwners) {
          const existingOwners: IOwner[] = Object.values(
            props.participantOwner!.owners
          );
          existingOwners.forEach((owner) => {
            if (hasPayee) {
              if (owner.id === props.participantPayee!.user.id) {
                (owner.types as OwnerType[]).push("Payee");
              }
            }

            ownersLocal.push(owner);
          });
        }
        if (hasPayee) {
          //  Merge owners and payee.  Convert payee to owner
          const payeeOwner: IOwner = {
            id: props.participantPayee!.user.id,
            name: props.participantPayee!.user.displayName,
            types: ["Payee"],
          };

          if (hasAnyOwners) {
            //  Check if payee is already in the list
            let found = false;
            ownersLocal.forEach((owner) => {
              if (owner.id === payeeOwner.id) {
                found = true;
                (owner.types as OwnerType[]).push("Payee");
              }
            });

            if (!found) {
              ownersLocal.push(payeeOwner);
            }
          } else {
            //  No owners, just payee
            ownersLocal.push(payeeOwner);
          }
        }

        ownersLocal = ownersLocal.map((owner) => {
          owner.types = unique<OwnerType>(owner.types as OwnerType[]);
          return owner;
        });

        // owners.value = Object.values(props.participantOwner.owners);
        owners.value = simpleClone(ownersLocal);
        setBestMatchOwner();
      }
    }

    function setBestMatchOwner(): void {
      if (!props.participantOwner && !props.participantPayee) {
        ownerSetHow.value = "No Owner or Payee";
        return;
      }

      //  Only got payee.
      if (!props.participantOwner && props.participantPayee) {
        bestMatchOwner.value = {
          id: props.participantPayee.user.id,
          name: props.participantPayee.user.displayName,
          types: ["Payee"],
        } as IOwner;
        bestMatchOwner2.value = {
          owner: bestMatchOwner.value,
          message: "Payee",
          code: "PAYEE",
        };

        owners.value = [bestMatchOwner.value];
        ownerSelected.value = owners.value[0];
        ownerSetHow.value = "Payee Only";
        //  onChanged();
        return;
      }

      if (!props.participantOwner) {
        ownerSetHow.value = "Got payee but no owner.";
        return;
      }

      const payeesFromOwnerList: IOwner[] = owners.value.filter((owner) => {
        const isPayee = (owner.types as OwnerType[]).indexOf("Payee") > -1;
        return isPayee;
      });
      if (payeesFromOwnerList.length > 0) {
        bestMatchOwner.value = payeesFromOwnerList[0];
        bestMatchOwner2.value = {
          owner: bestMatchOwner.value,
          message: "Payee",
          code: "PAYEE",
        };
        // ownerSelected.value = bestMatchOwner.value;
        // ownerSetHow.value = "Got payee from owners list";
        setThisOwnerIdAsSelected(
          bestMatchOwner.value.id,
          "Got payee from owners list"
        );
        // onChanged();
        return;
      }

      // const bestMatch = ResultsImportService.getBestOwnerMatch(
      //   props.participantOwner,
      //   props.targetEventGroupSummary?.id!,
      //   props.targetEventGroupSummary,
      //   props.athleteEntry ? props.athleteEntry.user : undefined,
      //   props.sourceEventGroupSummary
      // );

      const _bestMatchOwner2 = ResultsImportService.getBestOwnerMatch2(
        props.participantOwner,
        props.participantPayee,
        props.targetEventGroupSummary,
        props.athleteEntry ? props.athleteEntry.user : undefined,
        props.sourceEventGroupSummary
      );
      bestMatchOwner2.value = simpleClone(_bestMatchOwner2);

      /*
      if (bestMatch) {
        bestMatchOwner.value = simpleClone(bestMatch);
        flattenedParticipationInternal.value.owner = simpleClone(bestMatch);
        if (owners.value.length > 0) {
          owners.value.forEach((own) => {
            if (own.id === bestMatch.id) {
              ownerSelected.value = own;
            }
          });
        }
      }
      */
      if (_bestMatchOwner2.owner) {
        bestMatchOwner.value = simpleClone(_bestMatchOwner2.owner);
        flattenedParticipationInternal.value.owner = simpleClone(
          _bestMatchOwner2.owner
        );
        if (owners.value.length > 0) {
          setThisOwnerIdAsSelected(
            _bestMatchOwner2.owner!.id,
            "Setting from best match"
          );
          /*
          owners.value.forEach((own) => {
            if (own.id === _bestMatchOwner2.owner!.id) {
              ownerSelected.value = own;
              ownerSetHow.value = "Setting from best match";

              //  Auto select top 4 if we have a best match
              if (
                flattenedParticipationInternal.value.resultPosition > 0 &&
                flattenedParticipationInternal.value.resultPosition < 5
              ) {
                //  flattenedParticipationInternal.value.isSelected = true;
                //  onChanged();
              }
            }
          });
          */
        }
      }
    }

    function setThisOwnerIdAsSelected(id: number, message: string): void {
      owners.value.forEach((own) => {
        if (own.id === id) {
          ownerSelected.value = own;
          ownerSetHow.value = message;
          flattenedParticipationInternal.value.owner = simpleClone(own);
        }
      });
    }

    const isOwnerPresent = computed(() => {
      return (
        flattenedParticipationInternal.value.owner &&
        flattenedParticipationInternal.value.owner.id > 0
      );
    });

    function getOwnerDisplayName(owner: IOwner): string {
      return ResultsImportService.getOwnerDisplayName(owner);
    }

    const getMatchCss = computed(() => {
      const css = [];

      if (ownerSelected.value && bestMatchOwner2.value) {
        if (ownerSelected.value.id === bestMatchOwner2.value.owner?.id) {
          if (bestMatchOwner2.value.code === "PAYEE") {
            css.push("results-import--best-match-payee");
          } else if (
            bestMatchOwner2.value.code === "1_OWNER" ||
            bestMatchOwner2.value.code === "1_ACTUAL_OWNER"
          ) {
            css.push("results-import--best-match-owner");
          }
        }
      }

      return css;
    });

    const getUserUrlLink = computed(() => {
      if (ownerSelected.value && ownerSelected.value.id > 0) {
        return `#/user/${ownerSelected.value.id}`;
      }
      return "";
    });

    const getAthleteUrlLink = computed(() => {
      if (props.flattenedParticipation && props.flattenedParticipation.URN) {
        return "#/athletes?urn=" + props.flattenedParticipation.URN;
      }
      return "";
    });

    const getHoverTitle = computed(() => {
      return (
        "URN: " +
        props.flattenedParticipation.URN +
        ", Bib: " +
        props.flattenedParticipation.bib +
        ", Pos: " +
        props.flattenedParticipation.resultPosition
      );
    });

    return {
      flattenedParticipationInternal,
      onUserSelected,
      onUserEdit,
      owners,
      ownerSelected,
      ownerSetHow,
      bestMatchOwner,
      bestMatchOwner2,
      showBestMatchMessage,
      alreadyEntered,
      hasOwner,
      hasAnyOwners,
      getOwnerName,
      getName,
      getClubOrTeamTitle,
      isOwnerPresent,
      onChanged,
      getOwnerDisplayName,
      getMatchCss,
      getAthleteUrlLink,
      getUserUrlLink,
      showTypeAheadOverride,
      getHoverTitle,
    };
  },
});
</script>

<style scoped>
.results-import-event-list-participation--td {
  border-bottom: 1px solid var(--slate-200);
  padding: 0;
}

.results-import--best-match-payee {
  background-color: var(--green-200);
}

.results-import--best-match-payee {
  background-color: var(--green-50);
}
</style>
