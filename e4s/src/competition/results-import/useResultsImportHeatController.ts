import { ITimeTronicsHeatList } from "./results-import-models-timetronics-list";
import { reactive } from "vue";
import { simpleClone, sortArray } from "../../common/common-service-utils";
import * as ResultsImportService from "./results-import-service";
import { IFlattenedParticipation } from "./results-import-models";

export interface IResultsImportHeatControllerState {
  timeTronicsHeatList: ITimeTronicsHeatList;
  atLeastOnePositionAvailable: boolean;
  flattenedParticipations: IFlattenedParticipation[];
  // mapFlattenedByPosition: MapFlattenedParticipations;
  showTopXRows: number;
  flattenedParticipationsDisplay: IFlattenedParticipation[];
}

export function useResultsImportHeatController(
  timeTronicsHeatList: ITimeTronicsHeatList
) {
  const state = reactive<IResultsImportHeatControllerState>({
    timeTronicsHeatList: simpleClone(timeTronicsHeatList),
    atLeastOnePositionAvailable: false,
    flattenedParticipations: [],
    flattenedParticipationsDisplay: [],
    showTopXRows: 4,
  });

  init(timeTronicsHeatList);

  function init(timeTronicsHeatList: ITimeTronicsHeatList) {
    console.log("useResultsImportHeatController.init");
    if (timeTronicsHeatList) {
      state.timeTronicsHeatList = simpleClone(timeTronicsHeatList);
    }
    state.atLeastOnePositionAvailable =
      ResultsImportService.arePositionsAvailable(state.timeTronicsHeatList);
    state.flattenedParticipations =
      ResultsImportService.mapHeatToFlattenedParticipants(
        state.timeTronicsHeatList
      );
    // state.mapFlattenedByPosition =
    //   ResultsImportService.convertFlattenedParticipationsToMap(
    //     state.flattenedParticipations
    //   );

    state.flattenedParticipationsDisplay = sortArray(
      state.atLeastOnePositionAvailable
        ? "resultPosition"
        : (part) => {
            return part.timeTronicsHeatListParticipation.results[0]
              .result_value;
          },
      state.flattenedParticipations
    );
  }

  return { state, init };
}
