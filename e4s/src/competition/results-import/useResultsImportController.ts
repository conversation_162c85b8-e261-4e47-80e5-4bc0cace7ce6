import { computed, reactive, ref, watch } from "vue";
import { resultsImportServiceController } from "./results-import-service-controller";
import { useConfigStore } from "../../config/useConfigStore";
import { IConfigApp } from "../../config/config-app-models";
import { ConfigService } from "../../config/config-service";

export function useResultsImportController() {
  const isReady = ref(false);
  const importServiceController = resultsImportServiceController();
  const state = reactive(importServiceController.state);
  const configStore = useConfigStore();
  const configService = new ConfigService();

  state.userEntities = configService.getEntitiesFromUserInfo(
    configStore.configApp.userInfo
  );

  isReady.value = true;
  // window.setTimeout(() => {
  //   isReady.value = true;
  // }, 1000);

  watch(
    () => configStore.configApp,
    (newValue: IConfigApp, oldValue: any) => {
      state.userEntities = configService.getEntitiesFromUserInfo(
        newValue.userInfo
      );
    }
  );

  const isTimeTronicsCompLoaded = computed(() => {
    return state.timeTronicsCompetition !== null;
  });

  const getTimeTronicsDisplayName = computed(() => {
    if (state.timeTronicsCompetition) {
      return (
        state.timeTronicsCompetition.id +
        ": " +
        state.timeTronicsCompetition.name
      );
    }
    return "";
  });

  function showFinalsOnlyChanged(showFinalsOnly: boolean) {
    console.log(
      "useResultsImportController.showFinalsOnlyChanged",
      showFinalsOnly
    );
    importServiceController.showFinalsOnlyChanged(showFinalsOnly);
  }

  function filterTermChanged(filterTerm: string) {
    console.log("useResultsImportController.filterTermChanged", filterTerm);
    importServiceController.filterTermChanged(filterTerm);
  }

  const getSourceCompDisplayName = computed(() => {
    return importServiceController.getCompDisplayName("SOURCE");
  });

  const getTargetCompDisplayName = computed(() => {
    return importServiceController.getCompDisplayName("TARGET");
  });

  return {
    isReady,
    importServiceController,
    state,
    isTimeTronicsCompLoaded,
    getTimeTronicsDisplayName,
    showFinalsOnlyChanged,
    filterTermChanged,
    getSourceCompDisplayName,
    getTargetCompDisplayName,
  };
}
