<template>
  <div>
    <div
      class="
        e4s-flex-row e4s-justify-flex-space-between
        e4s-header--500
        e4s-justify-flex-row-vert-center
      "
    >
      <span v-text="getTitle"></span>
      <div
        class="e4s-flex-row e4s-gap--large e4s-justify-flex-row-vert-center"
        v-if="true"
      >
        <!--        <FormGenericInputTemplateV2 form-label="Show Rows">-->
        <!--         -->
        <!--        </FormGenericInputTemplateV2>-->

        <slot name="extra-filters"></slot>

        <span>Show Rows</span>
        <select
          slot="field"
          v-model="showTopXRows"
          class="browser-default e4s-input-field e4s-input-field--primary"
          style="width: 75px"
          v-on:change="createFlattenedParticipations"
        >
          <option
            v-for="n in timeTronicsHeatList.participations.length"
            v-text="n"
            :value="n"
          ></option>
        </select>
      </div>

      <!--      <a href="#" v-on:click.prevent="getOwners">Owners</a>-->
    </div>

    <table class="e4s-tt-import-results-grid">
      <thead>
        <tr>
          <td
            class="e4s-tt-import-results-grid-col-place"
            style="min-width: 60px"
          >
            Place
          </td>
          <td
            class="e4s-tt-import-results-grid-col-bib"
            style="min-width: 60px"
          >
            Bib
          </td>
          <td
            class="e4s-tt-import-results-grid-col-name"
            style="min-width: 200px"
          >
            Name
          </td>
          <td
            class="e4s-tt-import-results-grid-col-club"
            style="min-width: 200px"
          >
            <span
              v-text="
                timeTronicsEvent.eventtype.athletesquantity > 1
                  ? 'Team'
                  : 'Club'
              "
            ></span>
          </td>
          <td
            class="e4s-tt-import-results-grid-col-result"
            style="min-width: 60px"
          >
            Result
          </td>
          <td
            class="e4s-tt-import-results-grid-col-owner"
            style="min-width: 300px"
          >
            Owner/Payee
          </td>

          <td>
            <p v-if="false">
              <label>
                <input
                  class="e4s-checkbox"
                  type="checkbox"
                  v-model="selectAll"
                  v-on:change="createFlattenedParticipations"
                />
                <span class="e4s-bold"> </span>
              </label>
            </p>
          </td>
        </tr>
      </thead>
      <tbody>
        <!--      v-for="flattened in resultsImportHeatController.state-->
        <!--      .flattenedParticipationsDisplay"-->

        <ResultsImportEventListParticipation
          v-for="flattened in getParticipationsToDisplay"
          :key="flattenedParticipationKeyPred(flattened)"
          :flattened-participation="flattened"
          :participant-owner="
            resultsImportController.state.e4s.athletes[flattened.URN]
          "
          :participant-payee="
            resultsImportController.state.e4s.athletesPayees[flattened.URN]
          "
          :athlete-entry="getParticipationEntry(flattened)"
          :athlete-entry-key="getParticipationEntryKey(flattened)"
          :target-event-group-summary="resultsImportEventGroupSummary"
          :source-event-group-summary="sourceEventGroupSummary"
          v-on:onParticipationChanged="onParticipationChanged"
        />
        <!--        getEntriesForEvent[flattened.URN]-->
      </tbody>
    </table>
    <!--    <div>-->
    <!--      <FormGenericInputTemplateV2-->
    <!--        form-label="Show TargetEntries by Event Group and Urn"-->
    <!--      >-->
    <!--        <FieldCheckboxV2-->
    <!--          slot="field"-->
    <!--          style="align-self: flex-start"-->
    <!--          v-model="uiDebug.showTargetEntries"-->
    <!--        />-->
    <!--      </FormGenericInputTemplateV2>-->

    <!--      <div v-if="uiDebug.showTargetEntries">-->
    <!--        {{ getEntriesForEvent }}-->
    <!--      </div>-->
    <!--    </div>-->
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "vue";
import {
  ITimeTronicsHeatList,
  ITimeTronicsRoundList,
} from "./results-import-models-timetronics-list";
import * as ResultsImportService from "./results-import-service";
import {
  IFlattenedParticipation,
  IResultsImportEventGroupSummary,
  MapEntriesByAthleteUrnOrTeamAthleteUrns,
  MapFlattenedParticipations,
} from "./results-import-models";
import ResultsImportEventListParticipation from "./ResultsImportEventListParticipation.vue";
import {
  convertArrayToObject2,
  simpleClone,
} from "../../common/common-service-utils";
import { useResultsImportController } from "./useResultsImportController";
import { ITimeTronicsEvent } from "./results-import-models-timetronics";
import { useResultsImportHeatController } from "./useResultsImportHeatController";
import FormGenericInputTemplateV2 from "../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import { IR4sAthleteEntry } from "../scoreboard/rs4/rs4-scoreboard-models";
import FieldCheckboxV2 from "../../common/ui/layoutV2/fields/field-checkbox-v2.vue";

export default defineComponent({
  name: "results-import-heat",
  components: {
    FieldCheckboxV2,
    FormGenericInputTemplateV2,
    ResultsImportEventListParticipation,
  },
  props: {
    timeTronicsEvent: {
      type: Object as PropType<ITimeTronicsEvent>,
      required: true,
    },
    timeTronicsRoundList: {
      type: Object as PropType<ITimeTronicsRoundList>,
      required: true,
    },
    timeTronicsHeatList: {
      type: Object as PropType<ITimeTronicsHeatList>,
      required: true,
    },
    resultsImportEventGroupSummary: {
      type: Object as PropType<IResultsImportEventGroupSummary | undefined>,
    },
    sourceEventGroupSummary: {
      type: Object as PropType<IResultsImportEventGroupSummary | undefined>,
    },
  },
  setup(
    props: {
      timeTronicsEvent: ITimeTronicsEvent;
      timeTronicsRoundList: ITimeTronicsRoundList;
      timeTronicsHeatList: ITimeTronicsHeatList;
      resultsImportEventGroupSummary:
        | IResultsImportEventGroupSummary
        | undefined;
      sourceEventGroupSummary: IResultsImportEventGroupSummary | undefined;
    },
    context: SetupContext
  ) {
    const resultsImportHeatController = useResultsImportHeatController(
      props.timeTronicsHeatList
    );
    const resultsImportController = useResultsImportController();

    const selectAll = ref(false);

    const uiDebug = ref({
      showTargetEntries: false,
    });

    //  TODO wrong place
    const showTopXRows = ref<number>(4);
    // const flattenedParticipationDisplay = ref<IFlattenedParticipation[]>([]);

    // const resultsImportController = useResultsImportController();

    const mapFlattenedParticipations = ref<MapFlattenedParticipations>({});

    // createFlattenedParticipations();

    watch(
      () => props.timeTronicsHeatList,
      (newValue: ITimeTronicsHeatList, oldValue: any) => {
        createFlattenedParticipations();
        resultsImportHeatController.init(newValue);
      },
      {
        immediate: true,
      }
    );

    // watch(
    //   () => showTopXRows.value,
    //   (newValue: number, oldValue: any) => {
    //     if (newValue !== oldValue) {
    //       showTopXRowsChanged();
    //     }
    //   }
    // );

    function showTopXRowsChanged() {
      createFlattenedParticipations();
      getOwners();
    }

    const getParticipationsToDisplay = computed(() => {
      return resultsImportHeatController.state.flattenedParticipationsDisplay.slice(
        0,
        showTopXRows.value
      );
    });

    function createFlattenedParticipations() {
      const flatterned = ResultsImportService.mapHeatToFlattenedParticipants(
        props.timeTronicsHeatList
      ).map((flat) => {
        if (!selectAll.value) {
          //deselecting, so deselct everything
          flat.isSelected = false;
        } else {
          if (flat.owner.id > 0) {
            flat.isSelected = true;
          }
        }
        return flat;
      });
      // flattenedParticipationDisplay.value = simpleClone(flatterned);

      //  TT not sending position, so we need to generate a key so we can v-for.
      function getPositionKey(
        flattenedParticipation: IFlattenedParticipation,
        index?: number
      ): number {
        return flattenedParticipation.resultPosition === 0
          ? index
            ? index
            : Math.random() * 1000
          : flattenedParticipation.resultPosition;
      }

      mapFlattenedParticipations.value = convertArrayToObject2<
        number,
        IFlattenedParticipation
      >(getPositionKey, flatterned);
    }

    function flattenedParticipationKeyPred(
      flattenedParticipation: IFlattenedParticipation
    ) {
      return ResultsImportService.flattenedParticipationKey(
        flattenedParticipation
      );
    }

    function getOwners() {
      console.log("ResultsImportHeat.getOwners");
      context.emit("getOwners", props.timeTronicsHeatList);
    }

    // const getParticipantOwner = computed(() => {
    //   return resultsImportController.state.timeTronics.athletes[]
    // })

    const getEntriesForEvent =
      computed<MapEntriesByAthleteUrnOrTeamAthleteUrns>(() => {
        if (props.resultsImportEventGroupSummary) {
          const entriesForEventGroupByUrnOrName =
            resultsImportController.state.targetComp.entriesEventGroupUrnMap[
              props.resultsImportEventGroupSummary.id
            ];
          if (entriesForEventGroupByUrnOrName) {
            return entriesForEventGroupByUrnOrName;
          }
        }
        return {};
      });

    /**
     * Passing back up to parent because, user might select users from diff "heats", so
     * let them submit once.
     * @param flattenedParticipation
     */
    function onParticipationChanged(
      flattenedParticipation: IFlattenedParticipation
    ) {
      mapFlattenedParticipations.value[flattenedParticipation.resultPosition] =
        flattenedParticipation;

      console.log(
        "ResultsImportHeat.onParticipationChanged",
        simpleClone(mapFlattenedParticipations.value)
      );
      context.emit("onChanged", simpleClone(mapFlattenedParticipations.value));
    }

    const getTitle = computed(() => {
      //  timeTronicsRoundList.name + ', Heat: ' + timeTronicsHeatList.seqno
      return (
        props.timeTronicsRoundList.name +
        (props.timeTronicsRoundList.info.length > 0
          ? ", " + props.timeTronicsRoundList.info
          : "") +
        ", Heat: " +
        props.timeTronicsHeatList.seqno
      );
    });

    function getParticipationEntryKey(
      flattenedParticipation: IFlattenedParticipation
    ): string {
      return ResultsImportService.getParticipationEntryKey(
        flattenedParticipation,
        getEntriesForEvent.value
      );
    }

    function getParticipationEntry(
      flattenedParticipation: IFlattenedParticipation
    ): IR4sAthleteEntry | undefined {
      const eventGroupUrnOrUrns = getParticipationEntryKey(
        flattenedParticipation
      );
      if (eventGroupUrnOrUrns.length === 0) {
        return undefined;
      }
      return getEntriesForEvent.value[eventGroupUrnOrUrns];
    }

    // flattenedParticipationDisplay,
    return {
      resultsImportHeatController,
      getParticipationsToDisplay,
      showTopXRows,
      createFlattenedParticipations,
      flattenedParticipationKeyPred,
      selectAll,
      getOwners,
      resultsImportController,
      getEntriesForEvent,
      onParticipationChanged,
      showTopXRowsChanged,
      getTitle,
      getParticipationEntry,
      uiDebug,
      getParticipationEntryKey,
    };
  },
});
</script>
