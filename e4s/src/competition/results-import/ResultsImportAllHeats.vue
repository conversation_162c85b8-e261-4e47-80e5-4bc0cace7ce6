<template>
  <div>
    <div>Showing top {{ showTopX }} rows.</div>
    <table class="e4s-tt-import-results-grid">
      <thead>
        <tr>
          <td
            class="e4s-tt-import-results-grid-col-place"
            style="min-width: 60px"
          >
            Place
          </td>
          <td
            class="e4s-tt-import-results-grid-col-bib"
            style="min-width: 60px"
          >
            Bib
          </td>
          <td
            class="e4s-tt-import-results-grid-col-name"
            style="min-width: 200px"
          >
            Name
          </td>
          <td
            class="e4s-tt-import-results-grid-col-club"
            style="min-width: 200px"
          >
            <!--            <span-->
            <!--              v-text="-->
            <!--                timeTronicsEvent.eventtype.athletesquantity > 1-->
            <!--                  ? 'Team'-->
            <!--                  : 'Club'-->
            <!--              "-->
            <!--            ></span>-->
          </td>
          <td
            class="e4s-tt-import-results-grid-col-result"
            style="min-width: 60px"
          >
            Result
          </td>
          <td
            class="e4s-tt-import-results-grid-col-owner"
            style="min-width: 300px"
          >
            Owner/Payee
          </td>

          <td>
            <!--            <p v-if="false">-->
            <!--              <label>-->
            <!--                <input-->
            <!--                  class="e4s-checkbox"-->
            <!--                  type="checkbox"-->
            <!--                  v-model="selectAll"-->
            <!--                  v-on:change="setUpMapFlattenedParticipationsAllHeats"-->
            <!--                />-->
            <!--                <span class="e4s-bold"> </span>-->
            <!--              </label>-->
            <!--            </p>-->
          </td>
        </tr>
      </thead>
      <tbody>
        <ResultsImportEventListParticipation
          v-for="flattened in flattenedParticipationsAllHeats"
          :key="flattenedParticipationKeyPred(flattened)"
          :flattened-participation="flattened"
          :participant-owner="
            resultsImportController.state.e4s.athletes[flattened.URN]
          "
          :participant-payee="
            resultsImportController.state.e4s.athletesPayees[flattened.URN]
          "
          :athlete-entry="getParticipationEntry(flattened)"
          :athlete-entry-key="getParticipationEntryKey(flattened)"
          :target-event-group-summary="resultsImportEventGroupSummary"
          :source-event-group-summary="sourceEventGroupSummary"
          v-on:onParticipationChanged="onParticipationChanged"
        />
      </tbody>
    </table>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "vue";
import {
  IFlattenedParticipation,
  IResultsImportEventGroupSummary,
  MapEntriesByAthleteUrnOrTeamAthleteUrns,
  MapFlattenedParticipations,
} from "./results-import-models";
import * as ResultsImportService from "./results-import-service";
import {
  ITimeTronicsEventList,
  ITimeTronicsHeatListParticipation,
} from "./results-import-models-timetronics-list";
import ResultsImportEventListParticipation from "./ResultsImportEventListParticipation.vue";
import { useResultsImportController } from "./useResultsImportController";
import { IR4sAthleteEntry } from "../scoreboard/rs4/rs4-scoreboard-models";
import {
  convertArrayToObject,
  isEqual,
  simpleClone,
  uniqueBy,
} from "../../common/common-service-utils";
import { RoundType } from "./results-import-service";

export default defineComponent({
  name: "ResultsImportAllHeats",
  components: { ResultsImportEventListParticipation },
  props: {
    timeTronicsEventList: {
      type: Object as PropType<ITimeTronicsEventList>,
      required: true,
    },
    resultsImportEventGroupSummary: {
      type: Object as PropType<IResultsImportEventGroupSummary | undefined>,
    },
    sourceEventGroupSummary: {
      type: Object as PropType<IResultsImportEventGroupSummary | undefined>,
    },
    showTopX: {
      type: Number,
      default: 10,
    },
    autoSelectTopX: {
      type: Boolean,
      default: true,
    },
    numberToAutoSelect: {
      type: Number,
      default: 4,
    },
    filterRoundType: {
      type: String as PropType<RoundType>,
      required: true,
    },
  },
  setup(
    props: {
      timeTronicsEventList: ITimeTronicsEventList;
      resultsImportEventGroupSummary:
        | IResultsImportEventGroupSummary
        | undefined;
      sourceEventGroupSummary: IResultsImportEventGroupSummary | undefined;
      showTopX: number;
      autoSelectTopX: boolean;
      numberToAutoSelect: number;
      filterRoundType: RoundType;
    },
    context: SetupContext
  ) {
    const flattenedParticipationsAllHeats = ref<IFlattenedParticipation[]>([]);

    const resultsImportController = useResultsImportController();

    setUpMapFlattenedParticipationsAllHeats();

    watch(
      () => props.timeTronicsEventList,
      () => {
        setUpMapFlattenedParticipationsAllHeats();
      }
    );

    watch(
      () => props.filterRoundType,
      () => {
        setUpMapFlattenedParticipationsAllHeats();
      }
    );

    watch(
      () => resultsImportController.state.e4s.athletesPayees,
      (newValue, oldValue) => {
        console.warn(
          "ResultsImportAllHeats.watch athletesPayees",
          simpleClone({ newValue, oldValue })
        );
        if (isEqual(newValue, oldValue)) {
          console.warn(
            "ResultsImportAllHeats.watch athletesPayees the SAME, exiting.",
            simpleClone(resultsImportController.state.e4s.athletesPayees)
          );
          return;
        }

        // setUpMapFlattenedParticipationsAllHeats();
      }
    );

    function setUpMapFlattenedParticipationsAllHeats() {
      const res: ITimeTronicsHeatListParticipation[] =
        ResultsImportService.extractParticipationsFromTimeTronicsEventList(
          props.timeTronicsEventList,
          props.filterRoundType
        );

      let mapFlattened: IFlattenedParticipation[] = res.map((part, index) => {
        const flat = ResultsImportService.mapToFlattenedParticipation(part);
        if (props.autoSelectTopX) {
          const isOwnerSelected = flat.owner.id > 0;
          console.log(
            "ResultsImportAllHeats.setUpMapFlattenedParticipationsAllHeats isOwnerSelected: " +
              isOwnerSelected,
            flat
          );

          flat.isSelected = index < props.numberToAutoSelect && isOwnerSelected;
        }
        return flat;
      });

      console.log(
        "ResultsImportAllHeats.setUpMapFlattenedParticipationsAllHeats A: ",
        mapFlattened
      );

      mapFlattened = uniqueBy(mapFlattened, "bib");
      console.log(
        "ResultsImportAllHeats.setUpMapFlattenedParticipationsAllHeats uniqueBy: ",
        mapFlattened
      );

      mapFlattened = mapFlattened.sort(
        (a, b) => a.resultPosition - b.resultPosition
      );
      console.log(
        "ResultsImportAllHeats.setUpMapFlattenedParticipationsAllHeats sort: ",
        mapFlattened
      );

      mapFlattened = mapFlattened.slice(0, props.showTopX);
      console.log(
        "ResultsImportAllHeats.setUpMapFlattenedParticipationsAllHeats slice: ",
        mapFlattened
      );

      flattenedParticipationsAllHeats.value = mapFlattened;

      context.emit(
        "getOwners",
        simpleClone(flattenedParticipationsAllHeats.value)
      );
    }

    function flattenedParticipationKeyPred(
      flattenedParticipation: IFlattenedParticipation
    ) {
      return ResultsImportService.flattenedParticipationKey(
        flattenedParticipation
      );
    }

    function getParticipationEntryKey(
      flattenedParticipation: IFlattenedParticipation
    ): string {
      return ResultsImportService.getParticipationEntryKey(
        flattenedParticipation,
        getEntriesForEvent.value
      );
    }

    function getParticipationEntry(
      flattenedParticipation: IFlattenedParticipation
    ): IR4sAthleteEntry | undefined {
      const eventGroupUrnOrUrns = getParticipationEntryKey(
        flattenedParticipation
      );
      if (eventGroupUrnOrUrns.length === 0) {
        return undefined;
      }
      return getEntriesForEvent.value[eventGroupUrnOrUrns];
    }

    function onParticipationChanged(
      flattenedParticipation: IFlattenedParticipation
    ) {
      flattenedParticipationsAllHeats.value[
        flattenedParticipation.resultPosition
      ] = flattenedParticipation;

      console.log(
        "ResultsImportAllHeatsHeat.onParticipationChanged",
        simpleClone(flattenedParticipationsAllHeats.value)
      );

      const flattenedParticipationsMap: MapFlattenedParticipations =
        convertArrayToObject(
          "resultPosition",
          flattenedParticipationsAllHeats.value
        );
      console.log(
        "ResultsImportAllHeatsHeat.onParticipationChanged emit:",
        simpleClone(flattenedParticipationsMap)
      );
      context.emit("onChanged", simpleClone(flattenedParticipationsMap));

      // context.emit(
      //   "onChanged",
      //   simpleClone(flattenedParticipationsAllHeats.value)
      // );
    }

    const getEntriesForEvent =
      computed<MapEntriesByAthleteUrnOrTeamAthleteUrns>(() => {
        if (props.resultsImportEventGroupSummary) {
          const entriesForEventGroupByUrnOrName =
            resultsImportController.state.targetComp.entriesEventGroupUrnMap[
              props.resultsImportEventGroupSummary.id
            ];
          if (entriesForEventGroupByUrnOrName) {
            return entriesForEventGroupByUrnOrName;
          }
        }
        return {};
      });

    return {
      flattenedParticipationsAllHeats,
      flattenedParticipationKeyPred,
      setUpMapFlattenedParticipationsAllHeats,
      resultsImportController,
      getParticipationEntry,
      getParticipationEntryKey,
      onParticipationChanged,
    };
  },
});
</script>
