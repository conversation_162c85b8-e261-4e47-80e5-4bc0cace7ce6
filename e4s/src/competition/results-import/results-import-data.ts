import { ITimeTronicsCompetitionServerResponse } from "./results-import-models-timetronics";
import https from "../../common/https";
import { CONFIG } from "../../common/config";
import {
  AthleteUrn,
  EventGroupIdString,
  IBaseConcrete,
  IServerResponse,
} from "../../common/common-models";
import { ITimeTronicsEventList } from "./results-import-models-timetronics-list";
import { ISubmitEntriesPayload } from "./results-import-models";
// import { feed1094 } from "./mock/time-tronics-feed-1094";
// import { leinsterEventList600mU12Girls } from "./mock/dublin/leinsterEventList600mU12Girls";
// import { leinsterEventList600mU12Boys } from "./mock/dublin/leinsterEventList600mU12Boys";

// export class ResultsImportData {
//   public getTimeTronicsCompetition(
//     url: string
//   ): Promise<IServerResponse<ITimeTronicsCompetitionServerResponse>> {
//     return https.get("/wp-json/e4s/v5/relay");
//   }
// }

export function getRelayServer(url: string, useLocalServer: boolean): string {
  if (useLocalServer) {
    const localHostRelayUrl = "http://localhost:3000/relay?url=" + url;
    return localHostRelayUrl;
  }

  // const e4sServerRelayUrl =
  //   CONFIG.E4S_HOST + "/wp-json/e4s/v5/relay?url=" + url;
  // return e4sServerRelayUrl;
  const localHostRelayUrl = "http://localhost:3000/relay?url=" + url;
  return localHostRelayUrl;
}

export function getTimeTronicsCompetition(
  url: string,
  useLocalServer: boolean
): Promise<IServerResponse<ITimeTronicsCompetitionServerResponse>> {
  // http://results.athleticsireland.ie/results_19022023/menu.json

  //  https://${"domain"}/wp-json/e4s/v5/relay?url=http%3A%2F%2Fresults.athleticsireland.ie%2Fresults_19022023%2Fmenu.json

  url = url.replace(".html", ".json");
  url = encodeURIComponent(url);

  // const prom = https.get("/v5/relay?url=" + url);

  // const e4sServerRelayUrl =
  //   CONFIG.E4S_HOST + "/wp-json/e4s/v5/relay?url=" + url;
  // const localHostRelayUrl = "http://localhost:3000/relay?url=" + url;

  const prom = https.get(getRelayServer(url, useLocalServer));

  prom
    .then((res) => {
      console.warn("getTimeTronicsCompetition", res);
    })
    .catch((err) => {
      console.error("getTimeTronicsCompetition", err);
    })
    .finally(() => {
      console.log("getTimeTronicsCompetition", "finally");
    });

  return prom as any as Promise<
    IServerResponse<ITimeTronicsCompetitionServerResponse>
  >;

  // return https.get("https://dev.entry4sports.co.uk/wp-json/e4s/v5/builder/424");
  // return Promise.resolve({ errNo: 0, data: feed1094 }) as any as Promise<
  //   IServerResponse<ITimeTronicsCompetitionServerResponse>
  // >;
}

export function getTimeTronicsEventList(
  url: string,
  useLocalServer: boolean
): Promise<IServerResponse<ITimeTronicsEventList[]>> {
  // http://results.athleticsireland.ie/results_19022023/menu.json
  // http://results.athleticsireland.ie/results_19022023/5_list.json
  //
  //  https://${"domain"}/wp-json/e4s/v5/relay?url=http%3A%2F%2Fresults.athleticsireland.ie%2Fresults_19022023%2Fmenu.json

  url = url.replace(".html", ".json");
  url = encodeURIComponent(url);
  // return https.get(CONFIG.E4S_HOST + "/wp-json/e4s/v5/relay?url=" + url);

  return https.get(getRelayServer(url, useLocalServer));

  // return Promise.resolve({
  //   errNo: 0,
  //   data: leinsterEventList600mU12Boys,
  // }) as any as Promise<IServerResponse<ITimeTronicsEventList[]>>;
}

export interface IParticipantOwnersPayload {
  sourceEventGroupSummaryIds: number[];
  urns: number[];
}

export interface IParticipantPayeesPayload {
  sourceEventGroupSummaryId: number;
  urns: number[];
}

export type OwnerType = "Owner" | "Club" | "County" | "Region" | "Payee";

export interface IOwnerType {
  type: OwnerType;
  entityId: number;
  entityName: string;
}

export interface IOwner {
  id: number;
  name: string;
  types: IOwnerType[] | OwnerType[];
}

export interface IParticipantEntry {
  entryId: number;
  ownerId: number;
}

export interface IParticipantOwner {
  id: number; //  the owner id
  urn: number; //  the owner is the "owner" of this athlete.
  club: IBaseConcrete;
  county: IBaseConcrete;
  region: IBaseConcrete;
  entries: Record<EventGroupIdString, IParticipantEntry>;
  owners: Record<number | string, IOwner>;
}

export interface IParticipantsResponse {
  athletes: Record<AthleteUrn, IParticipantOwner>;
  teams: unknown[];
}

export type ParticipantOwnerType = "Owner" | "Club" | "Region";

export function getParticipantOwnersApi(
  participantOwnersPayload: IParticipantOwnersPayload,
  _userEntityId?: number
): Promise<IServerResponse<IParticipantsResponse>> {
  console.log(
    "ResultImportData.getParticipantOwners",
    participantOwnersPayload
  );
  return https.post(
    CONFIG.E4S_HOST + "/wp-json/e4s/v5/results/owners",
    participantOwnersPayload
  );
}

export interface IParticipantPayee {
  athleteId: number;
  entryId: number;
  urn: number;
  user: {
    id: number;
    name: string;
    email: string;
    displayName: string;
  };
}

export function getParticipantPayeesApi(
  participantPayeesPayload: IParticipantPayeesPayload
): Promise<IServerResponse<IParticipantPayee[]>> {
  return https.post(
    CONFIG.E4S_HOST + "/wp-json/e4s/v5/results/payees",
    participantPayeesPayload
  );
}

export function submitEntries(
  submitEntriesPayload: ISubmitEntriesPayload
): Promise<IServerResponse<unknown>> {
  return https.post(
    CONFIG.E4S_HOST + "/wp-json/e4s/v5/results/entries",
    submitEntriesPayload
  );
}
