<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <!--    <p>-->
    <!--      <label>-->
    <!--        <input class="e4s-checkbox" type="checkbox" v-model="showOnlyFinals" />-->
    <!--        <span class="e4s-bold"> Show Only Finals </span>-->
    <!--      </label>-->
    <!--    </p>-->

    <div class="e4s-flex-row e4s-gap--standard">
      <div class="e4s-flex-row e4s-gap--standard">
        <FieldRadioV2
          v-model="showByBestResultFromHeatOrFinal"
          option-value="HEAT"
          :is-disabled="rounds.heats.length === 0"
          :label="
            'Heat' +
            (rounds.heats.length > 1 ? 's' : '') +
            ' (' +
            rounds.heats.length +
            ')'
          "
        />
        <FieldRadioV2
          v-model="showByBestResultFromHeatOrFinal"
          option-value="FINAL"
          :is-disabled="rounds.finals.length === 0"
          :label="
            'Final' +
            (rounds.finals.length > 1 ? 's' : '') +
            ' (' +
            rounds.finals.length +
            ')'
          "
        />

        <!--        <span>Heats: {{ rounds.heats.length }}</span>-->
        <!--        <span>Finals: {{ rounds.finals.length }}</span>-->
      </div>

      <!--      <InputCheckboxV2-->
      <!--        v-if="rounds.finals.length > 0"-->
      <!--        v-model="showOnlyFinals"-->
      <!--        value-label="Show Only Finals"-->
      <!--        style="align-self: flex-start"-->
      <!--      />-->

      <!--      <span v-text="roundsMessage"></span>-->
    </div>

    <!--    <div-->
    <!--      class="e4s-flex-column e4s-gap&#45;&#45;standard"-->
    <!--      v-for="timeTronicsRoundList in getRoundsToDisplay"-->
    <!--      :key="getRoundKey(timeTronicsRoundList)"-->
    <!--    >-->
    <!--      <div v-if="showOnlyFinals">-->
    <!--        <ResultsImportHeat-->
    <!--          class="e4s-card-tt e4s-card-tt&#45;&#45;generic"-->
    <!--          v-for="heat in timeTronicsRoundList.heats"-->
    <!--          :time-tronics-event="timeTronicsEvent"-->
    <!--          :time-tronics-round-list="timeTronicsRoundList"-->
    <!--          :results-import-event-group-summary="resultsImportEventGroupSummary"-->
    <!--          :source-event-group-summary="sourceEventGroupSummary"-->
    <!--          :key="heat.id"-->
    <!--          :time-tronics-heat-list="heat"-->
    <!--          v-on:getOwners="getOwners"-->
    <!--          v-on:onChanged="onChanged"-->
    <!--        >-->
    <!--          <InputCheckboxV2-->
    <!--            slot="extra-filters"-->
    <!--            v-model="showOnlyFinals"-->
    <!--            value-label="Show Only Finals"-->
    <!--            style="align-self: flex-start"-->
    <!--          />-->
    <!--        </ResultsImportHeat>-->
    <!--      </div>-->

    <!--      <ResultsImportAllHeats-->
    <!--        v-if="!showOnlyFinals"-->
    <!--        :time-tronics-event-list="timeTronicsEventList"-->
    <!--        :results-import-event-group-summary="resultsImportEventGroupSummary"-->
    <!--        :source-event-group-summary="sourceEventGroupSummary"-->
    <!--        v-on:onChanged="onChanged"-->
    <!--        @getOwners="getOwnersForParticipants"-->
    <!--      />-->
    <!--    </div>-->

    <ResultsImportAllHeats
      :filter-round-type="showByBestResultFromHeatOrFinal"
      :time-tronics-event-list="timeTronicsEventList"
      :results-import-event-group-summary="resultsImportEventGroupSummary"
      :source-event-group-summary="sourceEventGroupSummary"
      v-on:onChanged="onChanged"
      @getOwners="getOwnersForParticipants"
    />

    <div class="e4s-flex-row e4s-justify-flex-end e4s-gap--standard">
      <ButtonGenericV2
        text="Submit"
        v-if="!showAskConfirmation"
        v-on:click="showAskConfirmation = true"
      />
      <ButtonGenericV2
        text="Confirm"
        v-if="showAskConfirmation"
        v-on:click="doSubmit"
      />
      <ButtonGenericV2
        text="Cancel"
        button-type="tertiary"
        v-if="showAskConfirmation"
        v-on:click="showAskConfirmation = false"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "vue";
import {
  ITimeTronicsEventList,
  ITimeTronicsHeatList,
  ITimeTronicsRoundList,
} from "./results-import-models-timetronics-list";
import * as ResultsImportService from "./results-import-service";
import ResultsImportHeat from "./ResultsImportHeat.vue";
import {
  IFlattenedParticipation,
  IResultsImportEventGroupSummary,
  MapFlattenedParticipations,
} from "./results-import-models";
import { ITimeTronicsEvent } from "./results-import-models-timetronics";
import { simpleClone } from "../../common/common-service-utils";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import InputCheckboxV2 from "../../common/ui/layoutV2/fields/input-checkbox-v2.vue";
import ResultsImportAllHeats from "./ResultsImportAllHeats.vue";
import { IFilterRoundsOutput, RoundType } from "./results-import-service";
import FieldRadioV2 from "../../common/ui/layoutV2/fields/field-radio-v2.vue";

export default defineComponent({
  name: "results-import-event-list",
  components: {
    FieldRadioV2,
    ResultsImportAllHeats,
    InputCheckboxV2,
    ButtonGenericV2,
    ResultsImportHeat,
  },
  props: {
    timeTronicsEvent: {
      type: Object as PropType<ITimeTronicsEvent>,
      required: true,
    },
    timeTronicsEventList: {
      type: Object as PropType<ITimeTronicsEventList>,
      required: true,
    },
    resultsImportEventGroupSummary: {
      type: Object as PropType<IResultsImportEventGroupSummary | undefined>,
    },
    sourceEventGroupSummary: {
      type: Object as PropType<IResultsImportEventGroupSummary | undefined>,
    },
  },
  setup(
    props: {
      timeTronicsEvent: ITimeTronicsEvent;
      timeTronicsEventList: ITimeTronicsEventList;
      resultsImportEventGroupSummary:
        | IResultsImportEventGroupSummary
        | undefined;
      sourceEventGroupSummary: IResultsImportEventGroupSummary | undefined;
    },
    context: SetupContext
  ) {
    const showOnlyFinals = ref(true);
    const showByBestResultFromHeatOrFinal = ref<RoundType>("FINAL");
    const showAskConfirmation = ref(false);
    const roundsToDisplay = ref<ITimeTronicsRoundList[]>([]);
    const rounds = ref<IFilterRoundsOutput>({
      finals: [],
      heats: [],
    });
    const roundsMessage = ref("");
    const mapFlattenedParticipations = ref<MapFlattenedParticipations>({});

    setUp(props.timeTronicsEventList);

    watch(
      () => props.timeTronicsEventList,
      (newValue: ITimeTronicsEventList) => {
        setUp(newValue);
      }
    );

    function setUp(timeTronicsEventList: ITimeTronicsEventList) {
      roundsMessage.value = "";
      rounds.value = ResultsImportService.filterRounds(timeTronicsEventList);

      //  specific case where no heats are present, there is ONE final but it has multiple "final heats".
      if (rounds.value.heats.length === 0 && rounds.value.finals.length === 1) {
        if (rounds.value.finals[0].heats.length > 1) {
          //  use the final heats as get best scores.
          showOnlyFinals.value = false;
          roundsMessage.value =
            "No heats, multiple final heats. Showing all final heats sorted.";
        }
      }

      showOnlyFinals.value = rounds.value.finals.length > 0;

      showByBestResultFromHeatOrFinal.value =
        rounds.value.finals.length > 0 ? "FINAL" : "HEAT";
    }

    function setRoundsToDisplay() {
      roundsToDisplay.value = showOnlyFinals.value
        ? rounds.value.finals
        : rounds.value.heats;
    }

    const getRoundsToDisplay = computed(() => {
      //  If heats, only need [0] as it has concatenated and sorted athletes.

      const roundsForDisplay = showOnlyFinals.value
        ? rounds.value.finals
        : rounds.value.heats.slice(0, 1);
      console.log(
        "ResultsImportEventList.getRoundsToDisplay",
        roundsForDisplay
      );
      return roundsForDisplay;
    });

    function getOwners(timeTronicsHeatList: ITimeTronicsHeatList) {
      console.log("ResultsImportEventList.getOwners", timeTronicsHeatList);
      context.emit("getOwners", timeTronicsHeatList);
    }

    function getOwnersForParticipants(
      flattenedParticipations: IFlattenedParticipation[]
    ) {
      console.log(
        "ResultsImportEventList.getOwnersForParticipants",
        flattenedParticipations
      );
      context.emit("getOwnersForParticipants", flattenedParticipations);
    }

    function onChanged(flattenedParticipations: MapFlattenedParticipations) {
      console.log("ResultsImportEventList.onChanged", flattenedParticipations);
      mapFlattenedParticipations.value = simpleClone(flattenedParticipations);
    }

    function doSubmit() {
      showAskConfirmation.value = false;
      console.log(
        "ResultsImportEventList.doSubmit",
        mapFlattenedParticipations.value
      );
      context.emit("doSubmit", simpleClone(mapFlattenedParticipations.value));
    }

    function getRoundKey(timeTronicsRoundList: ITimeTronicsRoundList) {
      const key =
        timeTronicsRoundList.id + ":" + timeTronicsRoundList.scheduled;
      console.log(
        "ResultsImportEventList.getRoundKey: " + key,
        timeTronicsRoundList
      );
      return key;
    }

    return {
      rounds,
      showOnlyFinals,
      roundsToDisplay,
      getOwners,
      getOwnersForParticipants,
      showAskConfirmation,
      onChanged,
      doSubmit,
      setRoundsToDisplay,
      getRoundsToDisplay,
      mapFlattenedParticipations,
      getRoundKey,
      roundsMessage,
      showByBestResultFromHeatOrFinal,
    };
  },
});
</script>
