import { IsoDate, IsoTime, Opaque } from "../../common/common-models";
import { TimeTronicsEventName } from "./results-import-models";

export type TimeTronicsEventKey = Opaque<"TimeTronicsEventKey", number>; //  E.g. 264 = 1500m
export type TimeTronicsEventSequence = Opaque<
  "TimeTronicsEventSequence",
  number
>; //  E.g. 1  that's our MaxGroup
export type TimeTronicsResultPosition = number;

export type ITimeTronicsCompetitionServerResponse = {
  competitions: ITimeTronicsCompetition[];
}[];

export interface ITimeTronicsCompetition {
  enddate: string; //    DD-MM-YYYY
  events: ITimeTronicsEvent[];
  federation: ITimeTronicsFederation;
  id: number;
  indoor: 1; //  ?
  info: string;
  location: string;
  name: string; //  "123.ie National Senior Indoor Championships";
  nationalcode: string; //  ""
  nonstadium: number; //  0;
  outdoor: 1 | 0;
  sessions: ITimeTronicsSession[];
  startdate: string; //    DD-MM-YYYY
}

export interface ITimeTronicsFederation {
  code: "AAI";
  country: "Ireland";
  id: number;
  language: "English";
  name: string; //  "Athletic Association of Ireland"
}

export interface ITimeTronicsSession {
  data: IsoDate; //  "2023-02-19",
  id: number;
  name: string; //  "Sprints Day 2",
  seqno: number; //  8
  time: IsoTime; //  "10:25:00",
}

export interface ITimeTronicsEventBase {
  abbreviation: string; //   e.g. 1500m
  categories: ITimeTronicsEventCategory[];
  eventtype: ITimeTronicsEventType;
  id: number;
  info: string; //  NR: 4.06.42 - Ciara Mageean (2020)  CR: 4.13.96 - Fionnuala Britton, Sli Cualann (2013)  NL: 4:18.85 - Georgia Hartigan
  name: string; //  "Senior Women 1500m",
  rounds: unknown[];
  seqno: number;
  status: number; //  5 ???
}

export interface ITimeTronicsEvent extends ITimeTronicsEventBase {
  rounds: ITimeTronicsRound[];
}

export interface ITimeTronicsEventCategory {
  abbreviation: string; //   e.g. SEN W
  id: number;
  name: string; //  e.g. Senior Women
}

export const TimeTronicsFieldTypes = [" ", "V", "H"] as const;

export type TimeTronicsFieldType = typeof TimeTronicsFieldTypes[number];

export interface ITimeTronicsEventType {
  abbreviation: string;
  athletesquantity: number; //  1
  code: string; //  "1500m";
  combinedeventsquantity: number;
  distance: number; //  1500
  fieldtype: TimeTronicsFieldType;
  id: number;
  implement: number; //  0.0;
  name: string;
  shortcode: string; //  "1500m";
  windmode: "N" | string; //  ??  N E S W
  windtime: string; //  ""; ??
}

export const TimeTronicsRoundHeatNames = [
  "Heats",
  "Heat",
  "Semi",
  " H e a t ",
] as const;

export const TimeTronicsRoundFinalNames = ["Final", "FINAL", "*"] as const;

export const TimeTronicsRoundNames = [
  ...TimeTronicsRoundHeatNames,
  ...TimeTronicsRoundFinalNames,
] as const;

export type TimeTronicsRoundName = typeof TimeTronicsRoundNames[number];
export type TimeTronicsRoundHeatName = typeof TimeTronicsRoundHeatNames[number];
export type TimeTronicsRoundFinalName =
  typeof TimeTronicsRoundFinalNames[number];

export interface ITimeTronicsRoundBase {
  abbreviation: string | "H" | "F"; //  "";
  datescheduled: IsoDate; //  "2023-02-18";
  id: number;
  info: string; //  "First 5 + 2 Fastest";
  name: TimeTronicsRoundName | TimeTronicsEventName;
  scheduled: IsoTime; // "15:40:00";
  seqno: number;
  status: number; //   5; ???
  official: IsoTime; //  "16:01:14";
  heats: unknown[];
}

export interface ITimeTronicsRound extends ITimeTronicsRoundBase {
  heats: ITimeTronicsHeat[];
  numberofparticipationsround: number;
  session: string; //  "Distance Day 1";
}

export interface ITimeTronicsHeatBase {
  attempts: number;
  // height: number;
  id: number;
  info: string; //  "0";
  official: IsoTime; //  "16:01:13";
  scheduled: IsoTime; //  "15:40:00";
  seqno: number;
  status: number; //  5 ???
}

export interface ITimeTronicsHeat extends ITimeTronicsHeatBase {
  height: number;
  numberofparticipations: number;
}
