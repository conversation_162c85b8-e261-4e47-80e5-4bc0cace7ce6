import {
  TimeTronicsEventsGroupBy,
  IResultsImportState,
  IFlattenedParticipation,
  IResultsImportEventGroupSummary,
  E4sResultImportSeqEventKey,
  MapFlattenedParticipations,
  ISubmitEntriesPayload,
  ISubmitEntry,
  ISubmitEntryAthlete,
  ISubmitEntryTeam,
  ISubmitEntryTeamAthlete,
  IEntityEntry,
  ResultsImportWhichComp,
  IResultsImportEventOutput,
  ITimeTronicsState,
  TimeTronicsEventGroupByKey,
  MapEntriesByEventGroupIdByAthleteUrn,
  MapEntriesByAthleteUrnOrTeamAthleteUrns,
} from "./results-import-models";
import {
  ITimeTronicsCompetition,
  ITimeTronicsCompetitionServerResponse,
  ITimeTronicsEvent,
  ITimeTronicsEventBase,
  ITimeTronicsRoundBase,
  TimeTronicsFieldType,
  TimeTronicsResultPosition,
} from "./results-import-models-timetronics";
import * as CommonServiceUtils from "../../common/common-service-utils";
import { BuilderService } from "../../builder/builder-service";
import { ICompEvent } from "../../compevent/compevent-models";
import {
  ITimeTronicsEventList,
  ITimeTronicsHeatList,
  ITimeTronicsHeatListParticipant,
  ITimeTronicsHeatListParticipation,
  ITimeTronicsHeatListResult,
  ITimeTronicsRoundList,
} from "./results-import-models-timetronics-list";
import {
  IOwner,
  IOwnerType,
  IParticipantOwner,
  IParticipantOwnersPayload,
  IParticipantPayee,
  OwnerType,
} from "./results-import-data";
import {
  EventType,
  EventTypeNameGeneric,
  ISecurity,
} from "../../athleteCompSched/athletecompsched-models";
import { ConfigService } from "../../config/config-service";
import { ENTITY_LEVEL_NUMBER } from "../../config/config-app-models";
import {
  IR4sAccountUser,
  IR4sAthleteEntry,
} from "../scoreboard/rs4/rs4-scoreboard-models";
import {
  AthleteUrnOrUrns,
  EntryIdString,
  EventGroupIdNumber,
  EventGroupIdString,
  GENDER,
  IBase,
} from "../../common/common-models";
import {
  IAthleteEntryFeeder,
  IFeederAllEntriesByEntryId,
  IFeederAllEntriesForCompByEventGroupIdByEntryId,
  ITeamEntryFeeder,
} from "../scoreboard/scoreboard-data";
import {
  convertArrayToObject,
  sortArray,
  uniqueBy,
} from "../../common/common-service-utils";

const builderService = new BuilderService();
const configService = new ConfigService();

export type RoundType = "FINAL" | "HEAT"; //"ALL" Leaving out for now.  @See extractParticipationsFromTimeTronicsEventList()

export function factoryResultsImportState(): IResultsImportState {
  return {
    ...factoryTimeTronicsState(),
    isLoading: false,
    isLoadingGlobal: false,
    useLocalServer: false,
    userEtity: configService.factoryEntity(),
    userEntities: [],
    showOnlyFinals: false,
    filterTerm: "",
    sourceComp: {
      id: 0,
      builderCompetition: builderService.factoryGetBuilder({}),
      eventGroupSummaries: [],
      eventGroupSummariesMap: {},
      entries: {},
      entriesEventGroupUrnMap: {},
      entriesE4sResultImportSeqEventKey: {},
    },
    targetComp: {
      id: 0,
      builderCompetition: builderService.factoryGetBuilder({}),
      eventGroupSummaries: [],
      eventGroupSummariesMap: {},
      entries: {},
      entriesEventGroupUrnMap: {},
      entriesE4sResultImportSeqEventKey: {},
    },
    e4s: {
      athletes: {},
      athletesPayees: {},
    },
    onResultsImportEventExpanded: {
      isExpanded: false,
      timeTronicsEvent: null,
    },
  };
}

export function factoryTimeTronicsState(): ITimeTronicsState {
  return {
    timeTronicsCompetitionServerUrl: "",
    timeTronicsCompetitionServerResponse: null,
    timeTronicsCompetition: null,
    timeTronicsEventsGroupBy: "SEQNO",
    timeTronicsEventsGrouped: {},
    timeTronicsEventsGroupedFilteredByEventName: {},
    timeTronicsFilterTerm: "",
    timeTronics: {
      autoLoad: false,
      timeTronicsEventMap: {},
    },
  };
}

// eventRounds: Record<TimeTronicsEventKey, >

export function extractCompetitionFromResponse(
  timeTronicsCompetitionServerResponse: ITimeTronicsCompetitionServerResponse
): ITimeTronicsCompetition | null {
  if (timeTronicsCompetitionServerResponse.length === 0) {
    return null;
  }

  //  what is that first array???
  const competitions = timeTronicsCompetitionServerResponse[0].competitions;
  if (competitions.length === 0) {
    return null;
  }
  return competitions[0];
}

export function removeEventsNoFinalEvents(
  timeTronicsCompetition: ITimeTronicsCompetition
): ITimeTronicsCompetition {
  const timeTronicsCompetitionInternal = CommonServiceUtils.simpleClone(
    timeTronicsCompetition
  );
  const timeTronicsEventInternal: ITimeTronicsEvent[] =
    timeTronicsCompetitionInternal.events.reduce<ITimeTronicsEvent[]>(
      (accum, timeTronicsEvent) => {
        const finalRounds = removeNoFinalRounds(timeTronicsEvent);
        if (finalRounds.rounds.length > 0) {
          accum.push(timeTronicsEvent);
        }
        return accum;
      },
      []
    );

  timeTronicsCompetitionInternal.events = timeTronicsEventInternal;
  return timeTronicsCompetitionInternal;
}

export function hasFinalRounds(timeTronicsEvent: ITimeTronicsEvent): boolean {
  return filterOnlyFinalRounds(timeTronicsEvent.rounds).length > 0;
}

export function removeNoFinalRounds(
  timeTronicsEvent: ITimeTronicsEventBase
): ITimeTronicsEventBase {
  const timeTronicsEventInternal =
    CommonServiceUtils.simpleClone(timeTronicsEvent);
  timeTronicsEventInternal.rounds = filterOnlyFinalRounds(
    timeTronicsEventInternal.rounds as ITimeTronicsRoundBase[]
  );
  return timeTronicsEventInternal;
}

/**
 *  This is not strictly correct.
 * @param rounds
 */
export function filterOnlyFinalRounds(
  rounds: ITimeTronicsRoundBase[]
): ITimeTronicsRoundBase[] {
  return rounds.filter((round) => {
    //  TODO  how do you say... Maybe<TimeTronicsRoundFinalName>("Some Value")
    // return (
    //   TimeTronicsRoundFinalNames.indexOf(
    //     round.name as any as TimeTronicsRoundFinalName
    //   ) > -1
    // );
    return isFinalRound(round);
  });
}

export interface IFilterRoundsOutput {
  finals: ITimeTronicsRoundList[];
  heats: ITimeTronicsRoundList[];
}

export function isFinalRound(round: ITimeTronicsRoundBase): boolean {
  // trim leading and trailing spaces off round Name
  // console.log("round name: " + round.name, round);

  const roundName: string = round.name.trim().toUpperCase();

  if (round.heats.length === 0) {
    return false;
  }

  if (round.abbreviation === "F") {
    return true;
  }
  return roundName === "FINAL" || roundName === "F I N A L";
}

export function filterRounds(
  timeTronicsEventList: ITimeTronicsEventList
): IFilterRoundsOutput {
  const filterRoundsOutput: IFilterRoundsOutput = {
    finals: [],
    heats: [],
  };

  const rounds = timeTronicsEventList.rounds;

  filterRoundsOutput.finals = rounds.filter((round) => {
    return isFinalRound(round);
  });

  filterRoundsOutput.heats = rounds.filter((round) => {
    return !isFinalRound(round);
  });

  return filterRoundsOutput;
}

export function groupEventsBy(
  timeTronicsEvents: ITimeTronicsEvent[],
  timeTronicsEventsGroupBy: TimeTronicsEventsGroupBy
): Record<string, ITimeTronicsEvent[]> {
  const mapPredsKey = {
    SESSION: (timeTronicsEvent: ITimeTronicsEvent): string => {
      // const finals = timeTronicsEvent.rounds.filter((round) => {
      //   return round.name === "Final";
      // });

      const finals = filterOnlyFinalRounds(timeTronicsEvent.rounds);

      if (finals.length === 0) {
        return "?0";
      }

      if (finals.length > 1) {
        return "?>1";
      }

      return timeTronicsEvent.name;
    },
    EVENT_TYPE: (timeTronicsEvent: ITimeTronicsEvent): string => {
      return timeTronicsEvent.eventtype.code;
    },
    SEQNO: (timeTronicsEvent: ITimeTronicsEvent): string => {
      return timeTronicsEvent.seqno.toString();
    },
  };

  return CommonServiceUtils.convertArrayToObjectArray(
    mapPredsKey[timeTronicsEventsGroupBy],
    timeTronicsEvents
  );
}

export function hasEventMultipleAgeGroups(
  timeTronicsEvent: ITimeTronicsEvent
): boolean {
  return timeTronicsEvent.categories.length > 1;
}

export function isFieldEvent(timeTronicsEvent: ITimeTronicsEvent): boolean {
  const fieldTypes: TimeTronicsFieldType[] = ["V", "H"];
  return fieldTypes.indexOf(timeTronicsEvent.eventtype.fieldtype) > -1;
}

export function getEventGroupSummaries(
  compEvents: ICompEvent[]
): IResultsImportEventGroupSummary[] {
  const eventGroupIdGenders: Record<
    string,
    Partial<Record<GENDER, GENDER>>
  > = {};

  const eventGroupIdEventName: Record<
    string,
    Partial<Record<string, string>>
  > = {};

  const eventGroupSummaries = compEvents.reduce<
    Record<string, IResultsImportEventGroupSummary>
  >((accum, compEvent) => {
    const id = compEvent.eventGroupSummary.id.toString();

    if (!eventGroupIdGenders[id]) {
      eventGroupIdGenders[id] = {};
    }
    eventGroupIdGenders[id][compEvent.event.gender] = compEvent.event.gender;

    if (!eventGroupIdEventName[id]) {
      eventGroupIdEventName[id] = {};
    }
    eventGroupIdEventName[id][compEvent.event.name] = compEvent.event.name;

    if (!accum[id]) {
      // accum[id] = simpleClone(compEvent.eventGroupSummary);
      const resultsImportEventGroupSummary: IResultsImportEventGroupSummary = {
        ...CommonServiceUtils.simpleClone(compEvent.eventGroupSummary),
        security: compEvent.options.security,
        genders: [],
        eventName: "",
        ageGroup: compEvent.ageGroup,
        isTeam: compEvent.options.isTeamEvent,
      };
      accum[id] = resultsImportEventGroupSummary;
    }
    return accum;
  }, {});
  const evtGroupSummaries: IResultsImportEventGroupSummary[] =
    Object.values(eventGroupSummaries);
  evtGroupSummaries.map((evt) => {
    const genders = eventGroupIdGenders[evt.id.toString()];
    evt.genders = genders ? Object.keys(genders) : [];

    const eventNames = eventGroupIdEventName[evt.id.toString()];
    evt.eventName = eventNames ? Object.keys(eventNames).join("~") : "";

    return evt;
  });
  return evtGroupSummaries;
}

export function timeTronicsEventListFinalOnly(
  timeTronicsEventList: ITimeTronicsEventList
): ITimeTronicsEventList {
  let timeTronicsEventListInternal =
    CommonServiceUtils.simpleClone(timeTronicsEventList);
  timeTronicsEventListInternal = removeNoFinalRounds(
    timeTronicsEventListInternal as ITimeTronicsEventBase
  ) as ITimeTronicsEventList;
  return timeTronicsEventListInternal;
}

export function mapToFlattenedParticipation(
  timeTronicsHeatListParticipation: ITimeTronicsHeatListParticipation
): IFlattenedParticipation {
  const participant = timeTronicsHeatListParticipation.participants[0];

  const result = getBestResult(timeTronicsHeatListParticipation);

  return {
    URN: participant.competitor.license.trim(),
    bib: participant.competitor.bib.trim(),
    birthdate: participant.competitor.athlete.birthdate,
    firstName: participant.competitor.athlete.firstname,
    lastName: participant.competitor.athlete.lastname,
    gender: "M",
    club: participant.competitor.team.name,
    competitionteam: participant.competitor.competitionteam.name
      ? participant.competitor.competitionteam.name
      : "",
    team: participant.competitor.team.name,
    resultPosition:
      timeTronicsHeatListParticipation.currentorder_round as TimeTronicsResultPosition,
    result: result ? result.value : "",
    resultValue: result ? result.result_value : 0,
    owner: {
      id: 0,
      name: "",
    },
    isSelected: false,
    isTeam: isTeamParticipation(timeTronicsHeatListParticipation),
    timeTronicsHeatListParticipation,
  };
}

export function getBestResult(
  timeTronicsHeatListParticipation: ITimeTronicsHeatListParticipation
): ITimeTronicsHeatListResult | undefined {
  return timeTronicsHeatListParticipation.results.find((res) => {
    return res.bestresult;
  });
}

export function flattenedParticipationKey(
  flattenedParticipation: IFlattenedParticipation
): string {
  return flattenedParticipation.URN.length === 0
    ? flattenedParticipation.firstName +
        "-" +
        flattenedParticipation.lastName +
        "-" +
        flattenedParticipation.birthdate
    : flattenedParticipation.URN;
}

export function getAthleteNameFromFlattenedParticipation(
  flattenedParticipation: IFlattenedParticipation
) {
  return (
    flattenedParticipation.firstName +
    " " +
    flattenedParticipation.lastName +
    " - " +
    flattenedParticipation.URN
  );
}

export function getNameFromFlattenedParticipation(
  flattenedParticipation: IFlattenedParticipation
): string {
  return isTeamParticipation(
    flattenedParticipation.timeTronicsHeatListParticipation
  )
    ? flattenedParticipation.timeTronicsHeatListParticipation.teamname!
    : getAthleteNameFromFlattenedParticipation(flattenedParticipation);
}

export function getClubOrTeamAthletesFromFlattenedParticipation(
  flattenedParticipation: IFlattenedParticipation
): string {
  return isTeamParticipation(
    flattenedParticipation.timeTronicsHeatListParticipation
  )
    ? getTeamAthleteNames(
        flattenedParticipation.timeTronicsHeatListParticipation
      ).join(", ")
    : flattenedParticipation.club;
}

export function getTeamAthleteNames(
  timeTronicsHeatListParticipation: ITimeTronicsHeatListParticipation
): string[] {
  if (!isTeamParticipation(timeTronicsHeatListParticipation)) {
    return [];
  }
  return timeTronicsHeatListParticipation.participants.map((participant) => {
    const athlete = participant.competitor.athlete;
    return (
      athlete.firstname.trim() +
      " " +
      athlete.lastname.trim() +
      " - " +
      getLicenseFromTeamParticipant(participant)
    );
  });
}

export function getLicenseFromTeamParticipant(
  timeTronicsHeatListParticipant: ITimeTronicsHeatListParticipant
): string {
  //  E.g. 1919_179678
  const licenseParts =
    timeTronicsHeatListParticipant.competitor.license.split("_");
  return licenseParts.length > 1 ? licenseParts[1] : licenseParts[0];
}

export function arePositionsAvailable(
  timeTronicsHeatList: ITimeTronicsHeatList
): boolean {
  return timeTronicsHeatList.participations.reduce<boolean>((accum, part) => {
    if (part.currentorder > 0) {
      accum = true;
    }
    return accum;
  }, false);
}

export function mapHeatToFlattenedParticipants(
  timeTronicsHeatList: ITimeTronicsHeatList
): IFlattenedParticipation[] {
  //  showTopXRows: number
  // const atLeastOnePositionAvailable =
  //   arePositionsAvailable(timeTronicsHeatList);

  //  If Track ort Field ASC, DESC.  Sorting should be on ui, not here...???
  //  Should just default to currentorder.
  // const participations = sortArray(
  //   atLeastOnePositionAvailable
  //     ? "currentorder"
  //     : (part) => {
  //         return part.results[0].result_value;
  //       },
  //   timeTronicsHeatList.participations
  // );

  //  Do all the filtering in ui...not here.
  const flatterned = timeTronicsHeatList.participations
    // .filter((part, index) => {
    //   return part.currentorder >= 0; //  changed from just checking above zero
    // })
    // .slice(
    //   0,
    //   atLeastOnePositionAvailable
    //     ? showTopXRows
    //     : timeTronicsHeatList.participations.length
    // )
    .map((timeTronicsHeatListParticipation) => {
      return mapToFlattenedParticipation(timeTronicsHeatListParticipation);
    })
    .map((flattenedParticipation) => {
      return flattenedParticipation;
    });
  return flatterned;
}

export function getUrnsFromHeat(
  timeTronicsHeatList: ITimeTronicsHeatList,
  topX: number
): string[] {
  const participations =
    timeTronicsHeatList.participations.length > topX
      ? timeTronicsHeatList.participations.slice(0, topX)
      : timeTronicsHeatList.participations;

  return participations.reduce<string[]>((accum, participation) => {
    participation.participants.forEach((participant) => {
      accum.push(participant.competitor.license);
    });
    return accum;
  }, []);
}

export interface IBestMatchOwnerResult {
  owner: IOwner | null;
  message: string;
  code:
    | "PAYEE"
    | "1_OWNER"
    | "1_ACTUAL_OWNER"
    | "MULTIPLE_OWNERS"
    | "MULTIPLE"
    | "SOURCE_COMP_PAID"
    | "TARGET_BEST_MATCH"
    | "NA";
}

export function getBestOwnerMatch2(
  participantOwner: IParticipantOwner,
  participantPayee: IParticipantPayee | undefined,
  targetResultsImportEventGroupSummary:
    | IResultsImportEventGroupSummary
    | undefined,
  sourceAccountUser: IR4sAccountUser | undefined,
  sourceResultsImportEventGroupSummary:
    | IResultsImportEventGroupSummary
    | undefined
): IBestMatchOwnerResult {
  const result: IBestMatchOwnerResult = {
    owner: null,
    message: "",
    code: "NA",
  };

  //  1.  We currently have that target group is minimum for making a match
  if (!targetResultsImportEventGroupSummary) {
    //  whatever owners we have, we can't default.
    result.message = "No target event.";
    return result;
  }

  if (participantPayee) {
    // const owner = getSourceAccountUserMatch(participantOwner, participantPayee);
    // if (owner) {
    //   result.owner = owner;
    //   result.message = "Payee found";
    //   return result;
    // }
    result.owner = {
      id: participantPayee.user.id,
      name: participantPayee.user.email,
      types: ["Payee"],
    };
    result.message = "Payee found";
    result.code = "PAYEE";
    return result;
  }

  const owners: IOwner[] = Object.values(participantOwner.owners);
  //  2.  If only got 1 owner...default to that....!?!?!  Surely that's correct??
  if (owners.length === 1) {
    result.message = "Only 1 owner";
    result.owner = owners[0];
    result.code = "1_OWNER";
    return result;
  }

  //  2.1
  if (owners.length > 1) {
    const actualOwners = owners.reduce<IOwner[]>((accum, owner) => {
      const isOwner = getOwnerTypes(owner).indexOf("Owner") > -1;
      if (isOwner) {
        accum.push(owner);
      }
      return accum;
    }, []);

    if (actualOwners.length === 1) {
      result.message = "Only 1 actual owner";
      result.owner = actualOwners[0];
      result.code = "1_ACTUAL_OWNER";
      return result;
    }

    if (actualOwners.length > 1) {
      //  See if we can filter out owners where it's not a club, county or region as well.
      const actualOnlyOwners = owners.reduce<IOwner[]>((accum, owner) => {
        const isOwnerTypes = getOwnerTypes(owner);
        const isOwner =
          isOwnerTypes.length === 1 && isOwnerTypes[0] === "Owner";
        if (isOwner) {
          accum.push(owner);
        }
        return accum;
      }, []);
      if (actualOnlyOwners.length === 1) {
        result.owner = actualOnlyOwners[0];
        result.message = "Found multiple owners, but 1 that is only 'owner'";
        result.code = "MULTIPLE_OWNERS";
        return result;
      }
    }
  }

  //  3.  Got a source event group, if this match, then unless you want
  //  to switch and get another user to pay...
  if (
    sourceResultsImportEventGroupSummary &&
    participantOwner.entries[sourceResultsImportEventGroupSummary.id]
  ) {
    //  get from participants entries the source event group match
    const sourceOwnerId =
      participantOwner.entries[sourceResultsImportEventGroupSummary.id].ownerId;
    const sourceCompEntryUser: IR4sAccountUser = {
      id: sourceOwnerId,
      name: "",
      email: "",
    };
    const sourceOwnerMatch = getSourceAccountUserMatch(
      participantOwner,
      sourceCompEntryUser
    );
    if (sourceOwnerMatch) {
      result.owner = sourceOwnerMatch;
      result.message = "Entry in source comp paid by this user.";
      result.code = "SOURCE_COMP_PAID";
    }
  }

  //  4. Source comp has security on it, guess the best "club" rep.
  const ownerSecurity = getBestOwnerMatchWithSecurity(
    participantOwner,
    targetResultsImportEventGroupSummary.id,
    targetResultsImportEventGroupSummary
  );
  if (ownerSecurity) {
    result.owner = ownerSecurity;
    result.message = "Target comp has security, best match.";
    result.code = "TARGET_BEST_MATCH";
  }

  //
  // let owner = null;
  // let entry = participantOwner.entries[targetResultsImportEventGroupSummary.id];
  // if (!entry) {
  //   entry =
  //     participantOwner.entries[
  //       targetResultsImportEventGroupSummary.id.toString()
  //     ];
  // }
  // if (entry) {
  //   owner = [entry.ownerId, entry.ownerId.toString()].reduce<IOwner | null>(
  //     (accum, id) => {
  //       if (participantOwner.owners[id]) {
  //         accum = participantOwner.owners[id];
  //       }
  //       return accum;
  //     },
  //     null
  //   );
  //
  //   result.owner = owner;
  //   if (owner) {
  //     result.message = "Owner found from source comp";
  //   }
  // }
  if (!result.owner) {
    result.message = "Has " + owners.length + " owners and no rules applied.";
  }
  return result;
}

/**
 * @param participantOwner
 * @param eventGroupId Needs deleting......!!!!!!!!!!!!
 * @param targetResultsImportEventGroupSummary
 * @param sourceAccountUser
 * @param sourceResultsImportEventGroupSummary
 */
export function getBestOwnerMatch(
  participantOwner: IParticipantOwner,
  eventGroupId: number,
  targetResultsImportEventGroupSummary:
    | IResultsImportEventGroupSummary
    | undefined,
  sourceAccountUser: IR4sAccountUser | undefined,
  sourceResultsImportEventGroupSummary:
    | IResultsImportEventGroupSummary
    | undefined
): IOwner | null {
  if (eventGroupId === 0) {
    //  whatever owners we have, we can't default.
    return null;
  }

  // const owners: IOwner[] = Object.values(participantOwner.owners);
  //  If only got 1 owner...default to that....!?!?!  Surely that's correct??
  // if (owners.length === 1) {
  //   return owners[0];
  // }
  // if (sourceAccountUser) {
  if (
    sourceResultsImportEventGroupSummary &&
    participantOwner.entries[sourceResultsImportEventGroupSummary.id]
  ) {
    const sourceOwnerId =
      participantOwner.entries[sourceResultsImportEventGroupSummary.id].ownerId;
    const sourceCompEntryUser: IR4sAccountUser = {
      id: sourceOwnerId,
      name: "",
      email: "",
    };
    const sourceOwnerMatch = getSourceAccountUserMatch(
      participantOwner,
      sourceCompEntryUser
    );
    if (sourceOwnerMatch) {
      return sourceOwnerMatch;
    }
  }

  // const ownerSecurity = getBestOwnerMatchWithSecurity(
  //   participantOwner,
  //   eventGroupId,
  //   targetResultsImportEventGroupSummary
  // );
  // if (ownerSecurity) {
  //   return ownerSecurity;
  // }

  // let owner = null;
  // let entry = participantOwner.entries[eventGroupId];
  // if (!entry) {
  //   entry = participantOwner.entries[eventGroupId.toString()];
  // }
  // if (entry) {
  //   owner = [entry.ownerId, entry.ownerId.toString()].reduce<IOwner | null>(
  //     (accum, id) => {
  //       if (participantOwner.owners[id]) {
  //         accum = participantOwner.owners[id];
  //       }
  //       return accum;
  //     },
  //     null
  //   );
  //
  //   return owner;
  // }
  return null;
}

export function getSourceAccountUserMatch(
  participantOwner: IParticipantOwner,
  sourceAccountUser: IBase
): IOwner | undefined {
  return Object.values(participantOwner.owners).find((own) => {
    return own.id === sourceAccountUser.id;
  });
}

/**
 *
 * @param participantOwner
 * @param eventGroupId        Most likely "SOURCE" competition event group id...but doesn't have to be.
 */
export function getBestOwnerMatchWithSecurity(
  participantOwner: IParticipantOwner,
  eventGroupId: number,
  targetResultsImportEventGroupSummary?: IResultsImportEventGroupSummary
): IOwner | undefined {
  if (!targetResultsImportEventGroupSummary) {
    //  We can't default.
    return;
  }

  let owner: IOwner | undefined;

  const hasSecurity = {
    clubs: false,
    county: false,
    region: false,
  };
  if (
    targetResultsImportEventGroupSummary &&
    targetResultsImportEventGroupSummary.security
  ) {
    const security = targetResultsImportEventGroupSummary.security;
    hasSecurity.clubs = !!(security.clubs && security.clubs.length > 0);
    hasSecurity.county = !!(security.counties && security.counties.length > 0);
    hasSecurity.region = !!(security.regions && security.regions.length > 0);
    if (hasSecurity.clubs || hasSecurity.county || hasSecurity.region) {
      //  It's VERY rare...never??? that you get security on more than one entity
      let matchKey: OwnerType | "" = "";
      if (hasSecurity.clubs) {
        matchKey = "Club";
      }

      if (hasSecurity.county) {
        matchKey = "County";
      }

      if (hasSecurity.region) {
        matchKey = "Region";
      }
      if (matchKey.length > 0) {
        owner = Object.values(participantOwner.owners).find((own) => {
          const ownerTypes = getOwnerTypes(own);
          return ownerTypes.indexOf(matchKey as OwnerType) > -1;
        });
      }
      return owner ? owner : undefined;
    }
  }

  return owner;
}

export function getOwnerTypes(owner: IOwner): OwnerType[] {
  //  @ts-ignore  my TS foo is not good enough, wasted enough time on it.
  return owner.types.reduce<OwnerType[]>(
    (accum: OwnerType[], ty: IOwnerType | OwnerType) => {
      if (typeof ty === "string") {
        accum.push(ty);
      } else {
        accum.push(ty.type);
      }
      return accum;
    },
    []
  );
}

export function getEventGroupSecuritySummary(
  resultsImportEventGroupSummary: IResultsImportEventGroupSummary
): (keyof ISecurity)[] {
  const hasSecurityTypes: (keyof ISecurity)[] = [];
  const security = resultsImportEventGroupSummary.security;
  if (!!(security.clubs && security.clubs.length > 0)) {
    hasSecurityTypes.push("clubs");
  }
  if (!!(security.counties && security.counties.length > 0)) {
    hasSecurityTypes.push("counties");
  }
  if (!!(security.regions && security.regions.length > 0)) {
    hasSecurityTypes.push("regions");
  }
  return hasSecurityTypes;
}

export function getEventGroupSecuritySummaryText(
  resultsImportEventGroupSummary: IResultsImportEventGroupSummary
): string {
  return getEventGroupSecuritySummary(resultsImportEventGroupSummary).join(
    ", "
  );
}

export function getUrnsFromTimeTronicsEventList(
  timeTronicsEventListMock: ITimeTronicsEventList,
  showTopX: number
): number[] {
  const urnMap = timeTronicsEventListMock.rounds.reduce<Record<number, number>>(
    (accum, round) => {
      const heats = round.heats;

      heats.forEach((heat) => {
        getUrnsFromHeat(heat, showTopX).forEach((urn) => {
          const urnNumber = Number(urn);
          if (!accum[urnNumber]) {
            accum[urnNumber] = urnNumber;
          }
        });
      });
      return accum;
    },
    {}
  );
  return Object.values(urnMap);
}

export function isTeamParticipation(
  timeTronicsHeatListParticipation: ITimeTronicsHeatListParticipation
): boolean {
  return !!(
    timeTronicsHeatListParticipation.teamname &&
    timeTronicsHeatListParticipation.teamname.length > 0
  );
}

/**
 *
 * @param timeTronicsEvent
 * @return E.g. 1:264  sequence one, event 264
 */
export function getE4sResultImportSeqEventKey(
  timeTronicsEvent: ITimeTronicsEvent
): E4sResultImportSeqEventKey {
  return (timeTronicsEvent.seqno +
    ":" +
    timeTronicsEvent.eventtype.id) as E4sResultImportSeqEventKey;
}

export function getTimeTronicsEventEntries(
  resultsImportState: IResultsImportState,
  timeTronicsEvent: ITimeTronicsEvent,
  whichComp: ResultsImportWhichComp
): IR4sAthleteEntry[] {
  const eventKey = getE4sResultImportSeqEventKey(timeTronicsEvent);

  const comp =
    whichComp === "source"
      ? resultsImportState.sourceComp
      : resultsImportState.targetComp;
  const entriesObject = comp.entriesE4sResultImportSeqEventKey[eventKey];
  return entriesObject ? entriesObject : [];
}

export function getResultsImportEventGroupSummaryFor(
  resultsImportState: IResultsImportState,
  timeTronicsEvent: ITimeTronicsEvent,
  whichComp: ResultsImportWhichComp
): IResultsImportEventGroupSummary | null {
  const entries = getTimeTronicsEventEntries(
    resultsImportState,
    timeTronicsEvent,
    whichComp
  );
  if (entries.length > 0) {
    const eventGroupSummary: IResultsImportEventGroupSummary =
      resultsImportState.targetComp.eventGroupSummariesMap[entries[0].egId];
    return eventGroupSummary;
  }
  return null;
}

export function createSubmitEntryPayload(
  entity: IEntityEntry,
  timeTronicsEvent: ITimeTronicsEvent,
  resultsImportEventGroupSummary: IResultsImportEventGroupSummary,
  mapFlattenedParticipations: MapFlattenedParticipations
): ISubmitEntriesPayload {
  const flats = filterFlattenedParticipationsToSubmit(
    Object.values(mapFlattenedParticipations)
  );
  const entries: ISubmitEntry[] = flats.map((flat) => {
    return createSubmitEntry(
      timeTronicsEvent,
      resultsImportEventGroupSummary,
      flat
    );
  });

  const entityLevel: ENTITY_LEVEL_NUMBER = entity.level;

  return {
    entity: {
      id: entity.id,
      level: entityLevel,
    },
    entries,
  };
}

export function filterFlattenedParticipationsToSubmit(
  flattenedParticipations: IFlattenedParticipation[]
): IFlattenedParticipation[] {
  return flattenedParticipations.filter((flattenedParticipation) => {
    return (
      flattenedParticipation.isSelected && flattenedParticipation.owner.id > 0
    );
  });
}

export function createSubmitEntry(
  timeTronicsEvent: ITimeTronicsEvent,
  resultsImportEventGroupSummary: IResultsImportEventGroupSummary,
  flattenedParticipation: IFlattenedParticipation
): ISubmitEntry {
  const result = getBestResult(
    flattenedParticipation.timeTronicsHeatListParticipation
  );

  const timeTronicsHeatListParticipation: ITimeTronicsHeatListParticipation =
    flattenedParticipation.timeTronicsHeatListParticipation;

  const payload: ISubmitEntry = {
    targetEgId: resultsImportEventGroupSummary.id,
    ownerId: flattenedParticipation.owner.id,
    result: result ? result.result_value : 0,
    resultsKey: getE4sResultImportSeqEventKey(timeTronicsEvent),
  };

  if (isTeamParticipation(timeTronicsHeatListParticipation)) {
    const athletes: ISubmitEntryTeamAthlete[] =
      timeTronicsHeatListParticipation.participants.map((part, index) => {
        const athlete: ISubmitEntryTeamAthlete = {
          urn: Number(getLicenseFromTeamParticipant(part)),
          seqno: part.seqno,
        };
        return athlete;
      });
    (payload as any as ISubmitEntryTeam).team = {
      teamName: timeTronicsHeatListParticipation.teamname!,
      athletes,
    };
  } else {
    (payload as ISubmitEntryAthlete).urn = Number(flattenedParticipation.URN);
  }
  return payload;
}

export type EventNameMatchResult =
  | "Exact Match"
  | "Partial Match E4S"
  | "Partial Match TT"
  | "No Match";

export interface IBestGuessResultsImportEventGroupSummary {
  timeTronicsEventName: string;
  eventGroupMatches: IResultsImportEventGroupSummary[];
  closeMatches: IResultsImportEventGroupSummary[];
  eventGroupMatchNames: string[];
  eventGroupMatchMessage: string[];
  allEventGroupNames: string[];
  eventTypeNameGeneric: EventTypeNameGeneric;
  eventTypeNameCount: number;
  genderMatchCount: number;
  ageGroup: {
    matchCount: number;
    messages: string[];
  };
  message: string;
}

export function factoryBestGuessResultsImportEventGroupSummary(): IBestGuessResultsImportEventGroupSummary {
  return {
    timeTronicsEventName: "",
    eventGroupMatches: [],
    closeMatches: [],
    eventGroupMatchNames: [],
    eventGroupMatchMessage: [],
    allEventGroupNames: [],
    eventTypeNameGeneric: "Track",
    eventTypeNameCount: 0,
    genderMatchCount: 0,
    ageGroup: {
      matchCount: 0,
      messages: [],
    },
    message: "",
  };
}

export function bestGuessResultsImportEventGroupSummary(
  resultsImportEventGroupSummaries: IResultsImportEventGroupSummary[],
  timeTronicsEvent: ITimeTronicsEvent
): IBestGuessResultsImportEventGroupSummary {
  // const timeTronicsEventName = timeTronicsEvent.eventtype.code;

  const eventTypeNameGeneric: EventTypeNameGeneric = isFieldEvent(
    timeTronicsEvent
  )
    ? "Field"
    : "Track";

  const result = factoryBestGuessResultsImportEventGroupSummary();
  result.timeTronicsEventName = timeTronicsEvent.eventtype.code;
  result.allEventGroupNames = resultsImportEventGroupSummaries.map(
    (evt) => evt.name
  );

  if (resultsImportEventGroupSummaries.length === 0) {
    result.message = "No event groups provided.";
    return result;
  }

  //  Filter track\field
  let eventsFiltered = filterEventGroupSummaries(
    resultsImportEventGroupSummaries,
    eventTypeNameGeneric
  );
  result.eventTypeNameCount = eventsFiltered.length;

  //  filter gender
  eventsFiltered = eventsFiltered.filter((evtGroup) => {
    const res = eventGenderMatch(evtGroup, timeTronicsEvent);
    return res.genderMatchResult !== "No Match";
  });
  result.genderMatchCount = eventsFiltered.length;

  /*
  //  Filter on event name
  eventsFiltered.forEach((evt) => {
    const matchType: EventNameMatchResult = eventNameMatch(
      evt,
      timeTronicsEvent
    );
    if (matchType !== "No Match") {
      result.eventGroupMatches.push(evt);
      result.eventGroupMatchNames.push(evt.name);
    }
    result.eventGroupMatchMessage.push(matchType);
  });

  // try age group match
  const ageGroupsTimeTronics: string[] = timeTronicsEvent.categories.map(
    (cat) => {
      return CommonServiceUtils.extractNumericChars(cat.abbreviation);
    }
  );

  eventsFiltered.forEach((evt) => {
    if (evt.ageGroup && evt.ageGroup.shortName) {
      const ageGroupsE4S: string[] = resultsImportEventGroupSummaries.map(
        (evtSummary) => {
          return CommonServiceUtils.extractNumericChars(evt.ageGroup.shortName);
        }
      );

      const ageGroupOverlap = CommonServiceUtils.intersection(
        ageGroupsTimeTronics,
        ageGroupsE4S
      );

      if (ageGroupOverlap.length > 0) {
        result.ageGroup.matchCount++;
        result.ageGroup.messages.push(
          "Age group overlap: " + ageGroupOverlap.join(", ")
        );
      }
    }
  });

  result.message = "Found " + result.eventGroupMatches.length + " matches.";
  */

  //  Filter on event name
  eventsFiltered = eventsFiltered.filter((evt) => {
    const matchType: EventNameMatchResult = eventNameMatch(
      evt,
      timeTronicsEvent
    );
    if (matchType !== "No Match") {
      // result.eventGroupMatches.push(evt);
      result.eventGroupMatchNames.push(evt.name);
    }
    result.eventGroupMatchMessage.push("Event name " + matchType);
    return matchType !== "No Match";
  });

  //  Stuff that is fairly close.
  result.closeMatches = CommonServiceUtils.simpleClone(eventsFiltered);

  // try age group match
  const ageGroupsTimeTronics: string[] = timeTronicsEvent.categories.map(
    (cat) => {
      return CommonServiceUtils.extractNumericChars(cat.abbreviation);
    }
  );

  eventsFiltered = eventsFiltered.filter((evt) => {
    if (evt.ageGroup && evt.ageGroup.shortName) {
      const ageGroupE4S: string = CommonServiceUtils.extractNumericChars(
        evt.ageGroup.shortName
      );

      const ageGroupOverlap = CommonServiceUtils.intersection(
        ageGroupsTimeTronics,
        [ageGroupE4S]
      );

      if (ageGroupOverlap.length > 0) {
        result.ageGroup.matchCount++;
        result.ageGroup.messages.push(
          "Age group overlap: " + ageGroupOverlap.join(", ")
        );
      }
      return ageGroupOverlap.length > 0;
    }
    return true;
  });

  result.eventGroupMatches = eventsFiltered;

  return result;
}

export function filterEventGroupSummaries(
  resultsImportEventGroupSummaries: IResultsImportEventGroupSummary[],
  eventType: EventTypeNameGeneric
): IResultsImportEventGroupSummary[] {
  return resultsImportEventGroupSummaries.filter((evt) => {
    const e4sTrackTypes: EventType[] = ["T", "R", "X"];
    const isEventTrack = e4sTrackTypes.indexOf(evt.type) > -1;

    return eventType === "Track" ? isEventTrack : !isEventTrack;
  });
}

export function eventNameMatch(
  resultsImportEventGroupSummary: IResultsImportEventGroupSummary,
  timeTronicsEvent: ITimeTronicsEvent
): EventNameMatchResult {
  let e4sName = resultsImportEventGroupSummary.name.toLowerCase();
  let timeTronicsEventName = timeTronicsEvent.eventtype.code.toLowerCase();

  e4sName = e4sName.replace("hurdles", "h");

  if (e4sName.indexOf(timeTronicsEventName) > -1) {
    return "Exact Match";
  }

  e4sName = resultsImportEventGroupSummary.name
    .toLowerCase()
    .replace(/\s/g, "");
  timeTronicsEventName = timeTronicsEvent.eventtype.code
    .toLowerCase()
    .replace(/\s/g, "");
  if (e4sName.indexOf(timeTronicsEventName) > -1) {
    return "Partial Match E4S";
  }

  e4sName = resultsImportEventGroupSummary.eventName
    .toLowerCase()
    .replace(/\s/g, "");
  if (e4sName.indexOf(timeTronicsEventName) > -1) {
    return "Partial Match E4S";
  }

  if (timeTronicsEventName.indexOf(e4sName) > -1) {
    return "Partial Match TT";
  }

  return "No Match";
}

export function eventNameMatch2(
  resultsImportEventGroupSummary: IResultsImportEventGroupSummary,
  timeTronicsEvent: ITimeTronicsEvent
): any {
  let e4sName = resultsImportEventGroupSummary.name.toLowerCase();
  let timeTronicsEventName = timeTronicsEvent.eventtype.code.toLowerCase();

  e4sName = e4sName.replace("hurdles", "h");

  if (e4sName.indexOf(timeTronicsEventName) > -1) {
    return "Exact Match";
  }

  e4sName = resultsImportEventGroupSummary.name
    .toLowerCase()
    .replace(/\s/g, "");
  timeTronicsEventName = timeTronicsEvent.eventtype.code
    .toLowerCase()
    .replace(/\s/g, "");
  if (e4sName.indexOf(timeTronicsEventName) > -1) {
    return "Partial Match E4S";
  }

  e4sName = resultsImportEventGroupSummary.eventName
    .toLowerCase()
    .replace(/\s/g, "");
  if (e4sName.indexOf(timeTronicsEventName) > -1) {
    return "Partial Match E4S";
  }

  if (timeTronicsEventName.indexOf(e4sName) > -1) {
    return "Partial Match TT";
  }

  return "No Match";
}

export type GenderMatchResultType =
  | "Exact Match"
  | "Partial Match E4S"
  | "Partial Match TT"
  | "No Match";

export interface GenderMatchResult {
  e4sGenders: string[];
  timeTronicsGender: string[];
  genderOverlap: string[];
  genderMatchResult: GenderMatchResultType;
}

export function eventGenderMatch(
  resultsImportEventGroupSummary: IResultsImportEventGroupSummary,
  timeTronicsEvent: ITimeTronicsEvent
): GenderMatchResult {
  const e4sGenders: string[] = resultsImportEventGroupSummary.genders;
  //  categories: [{ abbreviation: "SEN M", id: 63, name: "Senior Men" }],
  const timeTronicsGender: string[] = timeTronicsEvent.categories.map(
    (ttGender) => {
      const abbreviation = ttGender.abbreviation;

      const firstChar = abbreviation.slice(0, 1);
      const lastChar = abbreviation.slice(-1);

      //  E.g. BU17
      if (firstChar === "B") {
        return "M";
      }

      if (firstChar === "G") {
        return "F";
      }

      if (firstChar === "M") {
        return "M";
      }

      if (firstChar === "W") {
        return "F";
      }

      /////////////
      if (lastChar === "B") {
        return "M";
      }

      if (lastChar === "G") {
        return "F";
      }

      if (lastChar === "M") {
        return "M";
      }

      if (lastChar === "W") {
        return "F";
      }

      //  TT uses: W, F, M....and others?
      return abbreviation.slice(-1).replace("W", "F");
    }
  );

  const result: GenderMatchResult = {
    e4sGenders,
    timeTronicsGender,
    genderOverlap: [] as string[],
    genderMatchResult: "No Match",
  };

  const genderOverlap = CommonServiceUtils.intersection(
    e4sGenders,
    timeTronicsGender
  );
  if (genderOverlap.length > 0) {
    result.genderMatchResult = "Exact Match";
  }

  return result;
}

export function createParticipantOwnersPayload(
  resultsImportEventOutput: IResultsImportEventOutput
): IParticipantOwnersPayload {
  const urns = getUrnsFromTimeTronicsEventList(
    resultsImportEventOutput.timeTronicsEventList,
    resultsImportEventOutput.showTopXRows
  );

  const participantOwnersPayload: IParticipantOwnersPayload = {
    sourceEventGroupSummaryIds: [],
    urns,
  };

  let srcId = resultsImportEventOutput.sourceEventGroupSummary.id;
  if (srcId > 0) {
    participantOwnersPayload.sourceEventGroupSummaryIds.push(srcId);
  }
  srcId = resultsImportEventOutput.targetEventGroupSummary.id;
  if (srcId > 0) {
    participantOwnersPayload.sourceEventGroupSummaryIds.push(srcId);
  }

  return participantOwnersPayload;
}

export function getOwnerDisplayName(owner: IOwner): string {
  const ownerTypes = getOwnerTypes(owner);
  return (
    owner.name +
    ": " +
    owner.id +
    (ownerTypes.length > 0
      ? "  [" +
        ownerTypes
          .map((o) => {
            return o.slice(0, 2);
          })
          .join(", ") +
        "]"
      : "")
  );
}

export function filterTimeTronicsEventGroupResult(
  timeTronicsEventGroupByEventName: TimeTronicsEventGroupByKey,
  searchTerm: string
): TimeTronicsEventGroupByKey {
  const timeTronicsEventGroupByEventNameInternal =
    CommonServiceUtils.simpleClone(timeTronicsEventGroupByEventName);

  const eventNames = Object.keys(timeTronicsEventGroupByEventName);

  return eventNames.reduce<TimeTronicsEventGroupByKey>((accum, eventName) => {
    const timeTronicsEvents =
      timeTronicsEventGroupByEventNameInternal[eventName];

    const isSearchTermMatch =
      eventName.toLowerCase().indexOf(searchTerm.toLowerCase()) > -1;

    if (isSearchTermMatch) {
      accum[eventName] = timeTronicsEvents;
    }
    return accum;
  }, {});
}

export function convertFlattenedParticipationsToMap(
  flattenedParticipations: IFlattenedParticipation[]
): MapFlattenedParticipations {
  //  TT not sending position, so we need to generate a key so we can v-for.
  function getPositionKey(
    flattenedParticipation: IFlattenedParticipation,
    index?: number
  ): number {
    return flattenedParticipation.resultPosition === 0
      ? index
        ? index
        : Math.random() * 1000
      : flattenedParticipation.resultPosition;
  }

  return CommonServiceUtils.convertArrayToObject2<
    number,
    IFlattenedParticipation
  >(getPositionKey, flattenedParticipations);
}

export function mapAllEntriesByResultKey(
  compEntries: Record<
    EventGroupIdString,
    Record<EntryIdString, IR4sAthleteEntry>
  >
): Record<E4sResultImportSeqEventKey, IR4sAthleteEntry[]> {
  const eventGroupIdKeys: EventGroupIdString[] = Object.keys(
    compEntries
  ) as any as string[];

  return eventGroupIdKeys.reduce<
    Record<E4sResultImportSeqEventKey, IR4sAthleteEntry[]>
  >((accum, eventGroupId: EventGroupIdString) => {
    const entryIdValues: IR4sAthleteEntry[] = Object.values(
      compEntries[eventGroupId]
    );
    const seqKey: string = entryIdValues[0].entryOptions.resultsKey!;

    if (!accum[seqKey]) {
      accum[seqKey] = [];
    }

    accum[seqKey] = accum[seqKey].concat(entryIdValues);
    return accum;
  }, {});
}

export function mapEntriesEventGroupUrnMap(
  entries: (ITeamEntryFeeder | IAthleteEntryFeeder)[]
): Record<AthleteUrnOrUrns, IR4sAthleteEntry> {
  if (entries.length === 0) {
    return {};
  }
  return CommonServiceUtils.convertArrayToObject(
    getKeyForEntriesMap,
    entries
  ) as Record<AthleteUrnOrUrns, IR4sAthleteEntry>;
}

export function getTimeTronicsTeamUrnsKey(
  timeTronicsHeatListParticipation: ITimeTronicsHeatListParticipation
): string {
  const participants = timeTronicsHeatListParticipation.participants;
  return participants
    .map((part) => {
      return getLicenseFromTeamParticipant(part);
    })
    .sort((a, b) => {
      return a.localeCompare(b);
    })
    .join("~");
}

/*
 *  @returns urn or if team a concatenation of all athletes urns.
 */
export function getKeyForEntriesMap(
  entryFeeder: ITeamEntryFeeder | IAthleteEntryFeeder
): string {
  if ((entryFeeder as ITeamEntryFeeder).teamName) {
    const teamEntryFeeder: ITeamEntryFeeder = entryFeeder as ITeamEntryFeeder;
    return teamEntryFeeder.athletes
      .map((ath) => {
        return ath.urn.toString();
      })
      .sort((a, b) => {
        return a.localeCompare(b);
      })
      .join("~");
  }
  const entryFeederAthlete: IAthleteEntryFeeder =
    entryFeeder as IAthleteEntryFeeder;
  if (
    !entryFeederAthlete.urn ||
    entryFeederAthlete.urn === undefined ||
    entryFeederAthlete.urn === null ||
    entryFeederAthlete.urn === 0
  ) {
    return entryFeederAthlete.athleteId + "-1";
  }
  return entryFeederAthlete.urn.toString();
}

export function getParticipationEntryKey(
  flattenedParticipation: IFlattenedParticipation,
  entriesForEvent: MapEntriesByAthleteUrnOrTeamAthleteUrns
): string {
  if (!entriesForEvent) {
    return "";
  }

  const isTeamEvent = isTeamParticipation(
    flattenedParticipation.timeTronicsHeatListParticipation
  );
  let eventGroupUrnOrUrns = "";
  if (isTeamEvent) {
    eventGroupUrnOrUrns = getTimeTronicsTeamUrnsKey(
      flattenedParticipation.timeTronicsHeatListParticipation
    );
  } else {
    eventGroupUrnOrUrns = flattenedParticipation.URN;
  }
  return eventGroupUrnOrUrns;
}

export function convertAllEntriesForCompToEventGroupUrns(
  feederAllEntriesForComp: IFeederAllEntriesForCompByEventGroupIdByEntryId
): MapEntriesByEventGroupIdByAthleteUrn {
  const eventGroupIds: (EventGroupIdNumber | EventGroupIdString)[] =
    Object.keys(feederAllEntriesForComp);

  const entriesByEventGroupIdByAthleteUrn: MapEntriesByEventGroupIdByAthleteUrn =
    eventGroupIds.reduce<MapEntriesByEventGroupIdByAthleteUrn>(
      (accum, eventGroupId) => {
        const eventGroupIdString = eventGroupId.toString();

        //  This is how we map the entries in the event group to an athlete or team.
        //  Add the event group id as key, then add athlete urn(s) as key in the event group.
        const feederAllEntriesByEntryId: IFeederAllEntriesByEntryId =
          feederAllEntriesForComp[eventGroupId as any as number];

        const athleteEntriesWithinEventGroup: IR4sAthleteEntry[] =
          Object.values(feederAllEntriesByEntryId);

        //  Set up an object with event group and in it all athletes by urn.
        accum[eventGroupIdString] = convertArrayToObject((athleteEntry) => {
          const athleteEntryTemp: ITeamEntryFeeder | IAthleteEntryFeeder =
            athleteEntry as any as IAthleteEntryFeeder;

          //  If athlete, use the urn, if team, use concatentation of athlete urns.
          const entryKey = getKeyForEntriesMap(athleteEntryTemp);

          // return athleteEntry.urn ? athleteEntry.urn : -1;
          return entryKey;
        }, athleteEntriesWithinEventGroup);

        return accum;
      },
      {}
    );
  return entriesByEventGroupIdByAthleteUrn;
}

/**
 * Originasl intent was to get the best result from heats, as no final run. But
 * this is not always the case, so sometimes need to get best result from final round that has heats.
 * @param timeTronicsEventList
 * @param filterRounds
 */
export function extractParticipationsFromTimeTronicsEventList(
  timeTronicsEventList: ITimeTronicsEventList,
  filterRounds: RoundType = "HEAT"
): ITimeTronicsHeatListParticipation[] {
  //  Filter out finals.
  const rounds: ITimeTronicsRoundList[] = timeTronicsEventList.rounds.filter(
    (round) => {
      // if (filterRounds === "ALL") {
      //   return true;
      // }

      const isFinal = isFinalRound(round);
      //  Looking for best score from finals, so need to filter out heats.
      if (filterRounds === "FINAL") {
        return isFinal;
      }
      //  Looking for best score from heats, so need to filter out finals.
      return !isFinal;
    }
  );

  let participations = rounds
    .reduce<ITimeTronicsHeatListParticipation[]>((accum, round) => {
      const heats = round.heats;
      heats.forEach((heat) => {
        accum = accum.concat(heat.participations);
      });
      return accum;
    }, [])
    .filter((part) => {
      return part.currentorder_round > 0;
    });

  //  This screws up when you get 2 athletes with same "joint" place.  E.g. 1, 2, 3, 3, 5
  // participations = uniqueBy(participations, "currentorder_round");
  participations = uniqueBy(participations, "id");

  //  How do we sort accurately if RoundType is "ALL"? WE would have to use result Value.  Seen
  //  some events where all scores are zero.
  const participationsSorted = sortArray("currentorder_round", participations);

  return participationsSorted;
}
