import {doJwtAuth} from "../common/common.ispec";
import {CompetitionData} from "./competition-data";
import {IServerCompetitionLookupResponse} from "./competition-models";
// import {IClub} from "../club/club-models";

const competitionData: CompetitionData = new CompetitionData();

beforeAll((done) => {
    doJwtAuth(done);
});

describe("Competition", () => {
    test("getCompetitions", async () => {
        return competitionData.getCompetitionsByid(1)
        .then((response: IServerCompetitionLookupResponse) => {
            //  console.log("...................getCompetitionsByid response", response);
            expect(response.errNo === 0).toBe(true);
            expect(response.data.compDates.length > 0).toBe(true);
        });
    });
});
