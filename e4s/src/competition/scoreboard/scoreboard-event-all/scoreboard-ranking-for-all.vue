<template>
    <div class="ranking--wrapper">

<!--        name: {{scoreboardEvent.eventGroup.name}} pageCycleTime: {{pageCycleTime}} numberOfPages: {{numberOfPages}} displayingPage: {{displayingPage}}-->

        <div v-show="showPicture">
            <img :src="scoreboardEvent.picture" class="ranking--picture">
        </div>

        <div v-show="!showPicture">
            <table class="ranking--table">

                <tr class="ranking--table-header">
                    <th class="ranking--table-pos">Position</th>
                    <th class="ranking--table-bib">Bib</th>
                    <th class="ranking--table-th-ath">Athlete</th>
                    <th class="ranking--table-th-sco">Score</th>
                </tr>

                <tr v-for="(ranking, index) in scoreboardRanksDisplay" :style="rowStyle(ranking, index)">
                    <td class="ranking--table-pos"><span v-text="ranking.position"></span></td>
                    <td class="ranking--table-bib"><span v-text="ranking.bibno"></span></td>
                    <td><span v-text="ranking.athleteName"></span></td>
                    <td class="ranking--table-td-sco"><span v-text="getScore(ranking)"></span></td>
                </tr>
            </table>

            <div class="ranking--table-under">
                <span v-text="scoreboardEventDisplay.text"></span>
                <div class="ranking--table-page">
                    Page <span v-text="displayingPage - 1"></span> of <span v-text="numberOfPages"></span>
                </div>
            </div>
        </div>



        <div style="visibility: hidden" :id="PREFIX">window.setTimeout() looking for this div id: {{PREFIX}}</div>

<!--        <div class="debug">-->
<!--            DEBUG SECTION-->
<!--            ranksPerPage <input v-model="ranksPerPage">-->
<!--            rankPageLoopMs <input v-model="rankPageLoopMs">-->

<!--            public ranksPageTime:{{scoreboardEvent.config.ranksPageTime}}<br/>-->
<!--            public ranksPerPage:{{scoreboardEvent.config.ranksPerPage}}<br/>-->
<!--            public numberOfPages: {{numberOfPages}}<br/>-->
<!--            public displayingPage: {{displayingPage}}<br/>-->
<!--            public rowStart: number {{rowStart}}<br/>-->
<!--            public rowEnd: {{rowEnd}}<br/>-->
<!--        </div>-->

    </div>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop, Watch} from "vue-property-decorator"
    import {IScoreboardEvent, IScoreboardRank} from "../scoreboard-models"
    import {ScoreboardService} from "../scoreboard-service"

    const scoreboardService: ScoreboardService = new ScoreboardService();

    @Component({
        name: "scoreboard-ranking-for-all"
    })
    export default class ScoreboardRankingForAll extends Vue {
        @Prop({
            default: () => {
                return scoreboardService.factoryScoreboardEvent();
            }
        }) public readonly scoreboardEvent: IScoreboardEvent;

        // @Prop({default: 5000}) public readonly pageCycleTime: number;
        // @Prop({default: 8}) public readonly ranksPerPage: number;

        public PREFIX = Math.random().toString(36).substring(2);
        public scoreboardEventDisplay: IScoreboardEvent = scoreboardService.factoryScoreboardEvent();

        public timerLoopPresentation: number;

        public numberOfPages: number = 0;
        public displayingPage: number = 1;
        public rowStart: number = 0;
        public rowEnd: number = 0;
        public scoreboardRanksDisplay: IScoreboardRank[] = []
        public showPicture: boolean = false;

        public created() {
            this.startLoopPresentation();
        }

        @Watch("scoreboardEvent")
        public onScoreboardEventChanged(newValue: IScoreboardEvent) {
            this.stopLoopPresentation();
            this.scoreboardEventDisplay = R.clone(newValue);

            this.showPicture = false;
            this.displayingPage = 1;
            this.rowStart = 0;
            this.rowEnd = 0;
            this.scoreboardRanksDisplay = [];
            this.renderRankings();
        }

        public getScore(scoreboardRank: IScoreboardRank) {
            if (scoreboardRank.score === 0) {
                return "-";
            }

            return scoreboardRank.score;
        }

        public startLoopPresentation() {
            console.log("ScoreboardRanking.startLoopPresentation");
            this.displayingPage = 1;
            this.doPresentation();
        }

        public doPresentation() {

            console.log("ScoreboardRankingForAll.doPresentation");

            const timerDiv = document.getElementById(this.PREFIX);

            if (!timerDiv) {
                console.log("ScoreboardRankingForAll.doPresentation can not find div: " + this.PREFIX + ", stop loop.");
                this.stopLoopPresentation();
                return;
            }

            const pageCycleTime = this.scoreboardEventDisplay.config.ranksPageTime;
            // const pageCycleTime = this.pageCycleTime;

            this.timerLoopPresentation = window.setTimeout( () => {
                this.renderRankings();
            }, (pageCycleTime));

        }

        public renderRankings() {
            this.showPicture = false;
            if (this.scoreboardEvent.ranking.length === 0) {
                console.log("ScoreboardRankingForAll.startLoopPresentation ranking.length === 0, exit");
                const pageCycleTime = this.scoreboardEventDisplay.config.ranksPageTime;
                // const pageCycleTime = this.pageCycleTime;
                window.setTimeout( () => {
                    this.$emit("atLastPage");
                }, (pageCycleTime));
                return;

            } else {
                if (this.displayingPage > this.numberOfPages) {

                    if (this.scoreboardEventDisplay.picture.length > 0 && this.numberOfPages > 0) {
                        this.showPicture = true;
                        window.setTimeout( () => {
                            this.$emit("atLastPage");
                            this.displayingPage = 1;
                            this.showPicture = false;
                            this.scoreboardRanksDisplay = [];
                        }, (this.scoreboardEventDisplay.config.showPicture));
                        return;
                    }

                    this.$emit("atLastPage");
                    this.displayingPage = 1;
                }
                // TODO
                const perPage = this.scoreboardEventDisplay.config.ranksPerPage;

                this.numberOfPages = Math.ceil( this.scoreboardEvent.ranking.length / perPage );
                this.rowStart = this.displayingPage === 1 ? 0 : ((this.displayingPage -1) * perPage);
                this.rowEnd = this.rowStart + perPage;

                const rankings = R.clone(this.scoreboardEvent.ranking);
                const rankingsForPage = rankings.slice(this.rowStart, this.rowEnd);
                this.scoreboardRanksDisplay = rankingsForPage;

                console.log("ScoreboardRankingForAll.render  numberOfPages: " + this.numberOfPages +
                    ", displayingPage: " + this.displayingPage +
                    ", rowStart: " + this.rowStart +
                    ", rowEnd: " + this.rowEnd,
                    R.clone(this.scoreboardEventDisplay)
                );

                this.displayingPage++;
            }
            this.doPresentation();
        }


        public stopLoopPresentation() {
            console.log("ScoreboardRankingForAll.stopLoopPresentation...clearTimeout: " + this.timerLoopPresentation);
            clearTimeout(this.timerLoopPresentation);
        }

        public doesAthleteMatchScore(scoreboardRank: IScoreboardRank): boolean {
            return this.scoreboardEvent.athlete.bibNo === scoreboardRank.bibno;
        }

        public rowStyle(scoreboardRank: IScoreboardRank, index: number) {
            const styles: Record<string, string> = {
                // lineHeight: "3vh"
            };

            if (this.doesAthleteMatchScore(scoreboardRank)) {
                styles["backgroundColor"] = "#c5f1da";
            } else {
                if (index % 2 === 0) {
                    styles["backgroundColor"] = "#fafafa";
                }
            }

            return styles;
        }

        public destroy() {
            console.log("ScoreboardRankingForAll.destroy...");
            this.stopLoopPresentation();
        }

        @Watch("$route")
        public onRouteChanged(newValue: any, oldValue: any) {
            console.log("ScoreboardRankingForAll.onRouteChanged...");
        }

        public beforeRouteLeave(to: any, from: any, next: any) {
            console.warn("ScoreboardRankingForAll.beforeRouteLeave", {to, from});
            this.stopLoopPresentation();
            next();
        }

        public beforeRouteUpdate (to: any, from: any, next: any) {
            console.warn("ScoreboardRankingForAll.beforeRouteUpdate", {to, from});
            this.stopLoopPresentation();
            next()
        }

    }
</script>

<style scoped>

    .debug {
        font-size: 1vh;
    }

    .ranking--wrapper {
        margin: 0 1vh 0 1vh;
        font-size: 4vh;
        height: 85vh;
    }

    .ranking--table {

    }

    .ranking--table-header {
        /*font-size: 3vh;*/
        /*font-weight: normal;*/
        color: #da7500;
    }

    .ranking--table-row {
        /*border-top: 0.5vh solid #cddae6;*/
        /*border-bottom: 1vh solid #cddae6;*/
    }

    .ranking--table tr {
        line-height: 4vh;
    }

    .ranking--table th {
        font-weight: normal;
        text-align: center;
    }

    .ranking--table-th-ath {
        text-align: left !important;
    }

    .ranking--table td {
        border: solid 1px #eae7e7;
    }

    .ranking--table-pos {
        width: 15%;
        text-align: center;
    }

    .ranking--table-bib {
        width: 15%;
        text-align: center;
    }

    .ranking--table-th-ath {
        /*text-align: left;*/
    }

    .ranking--table-th-sco {
        width: 15%;
    }

    .ranking--table-td-sco {
        width: 15%;
        text-align: right;
        padding-right: 3vw;
    }

    .ranking--table-under {
        font-size: 2vh;
        color: #da7500;
    }

    .ranking--table-page {
        float: right;
    }

    .ranking--picture {
        height: 75vh;
        float: left;
        margin-top: 1vh;
        width: 100%;
    }

    @media only screen and (max-height: 700px) {
        .ranking--wrapper {
            margin: 0 1vh 0 1vh;
            font-size: 2vh;
            height: 85vh;
        }

        .ranking--table tr {
            line-height: 2vh;
        }
    }


</style>
