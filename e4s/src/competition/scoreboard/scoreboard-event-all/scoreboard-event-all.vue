<template>
    <div>
<!--        All Events eventCounterPosition{{eventCounterPosition}}  scoreboardEvents.length{{scoreboardEvents}}-->

<!--        globalPageCycleTime{{globalPageCycleTime}}<br>-->
<!--        globalRanksPerPage{{globalRanksPerPage}}<br>-->
<!--        currentEventPageCount{{currentEventPageCount}}<br>-->
<!--        pageDisplayTimeForEvent{{displayTimeForEvent}}<br>-->

<!--        scoreboardsEventQuery {{scoreboardsEventQuery}}-->

        <div v-if="scoreboardEvents.length === 0" class="scoreboard--no-scores">
            No scores available.
        </div>

        <div v-if="scoreboardEvents.length > 0 && isRouteOK">
            <ScoreboardRankingForAll :scoreboard-event="scoreboardEventToDisplay"
                                     v-on:noRankings="noRankings"
                                     v-on:atLastPage="atLastPage">
            </ScoreboardRankingForAll>
        </div>

        <div style="visibility: hidden" id="scoreboard-event-all-timer-dev">
            window.setTimeout() looking for this div id: scoreboard-event-route-timer-dev
        </div>

    </div>
</template>


<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import { IScoreboardStoreState, SCOREBOARD_STORE_CONST } from "../scorboard-store";
    import { mapState } from "vuex";
    import {IScoreboardEvent} from "../scoreboard-models"
    import {ScoreboardData, IScoreboardsEventQuery} from "../scoreboard-data"
    import {messageDispatchHelper} from "../../../user-message/user-message-store"
    import {USER_MESSAGE_LEVEL} from "../../../user-message/user-message-models"
    import ScoreboardRankingForAll from "./scoreboard-ranking-for-all.vue"
    import {ScoreboardService} from "../scoreboard-service"
    import {Watch} from "vue-property-decorator"

    @Component({
        name: "scoreboard-event-all",
        components: {ScoreboardRankingForAll},
        computed: {
            ...mapState(SCOREBOARD_STORE_CONST.SCOREBOARD_CONST_MODULE_NAME, {
                scoreboardEventsAllState: (state: any) => state.scoreboardEventsAll,
                scoreboardsEventQuery: (state: any) => state.scoreboardsEventQuery
            })
        }
    })
    export default class ScoreboardEventAll extends Vue{
        public readonly scoreboardsEventQuery: IScoreboardsEventQuery[]

        public compId: number = 0;
        public isLoading: boolean = false;

        public scoreboardService: ScoreboardService = new ScoreboardService();

        public eventCounterPosition: number = 0
        public scoreboardEventToDisplay: IScoreboardEvent = this.scoreboardService.factoryScoreboardEvent();
        public scoreboardEvents: IScoreboardEvent[] = [];

        public globalPageCycleTime: number = 5000;
        public globalRanksPerPage: number = 8;

        public displayTimeForEvent: number = 8000;
        public currentEventPageCount: number = 0

        public isRouteOK: boolean = true;

        public getDataLoop: number;

        public mounted() {
            this.$store.commit(SCOREBOARD_STORE_CONST.SCOREBOARD_CONST_MODULE_NAME + "/" +
                SCOREBOARD_STORE_CONST.SCOREBOARD_MUTATIONS_SET_SCOREBOARD, this.scoreboardService.factoryScoreboardEvent());

            this.compId = isNaN(Number(this.$route.params.compid)) ? 0 : parseInt(this.$route.params.compid, 0);

            //  @ts-ignore
            this.getCompetitionEventGroupsAll(this.compId).then(() => {
               this.cycleThroughEvents();
            });

        }

        public getCompetitionEventGroupsAll(compId: number) {


            const eventNos = this.scoreboardsEventQuery;


            const timerDiv = document.getElementById("scoreboard-event-all-timer-dev");

            if (!timerDiv) {
                console.log("ScoreboardRankingForAll.getCompetitionEventGroupsAll() can not find div: " + "scoreboard-event-all-timer-dev" + ", stop loop.");
                this.stopGetData();
                return;
            }

            if (this.isLoading) {
                console.log("ScoreboardRankingForAll.getCompetitionEventGroupsAll() isLoading: " + this.isLoading + " already running");
                return;
            }
            this.isLoading = true;

            return new ScoreboardData().getScoreboards(compId, eventNos)
                .then( (response) => {
                    if (response.errNo > 0) {
                        messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
                        return;
                    }
                    // this.$store.commit(SCOREBOARD_STORE_CONST.SCOREBOARD_CONST_MODULE_NAME + "/" +
                    //     SCOREBOARD_STORE_CONST.SCOREBOARD_MUTATIONS_SET_SCOREBOARD_ALL, response.data);

                    //  @ts-ignore
                    this.scoreboardEvents = response.data.scoreboards;

                    if (!this.scoreboardEvents || this.scoreboardEvents.length === 0) {
                        const tryAgainIn = response.data.config.polling ? response.data.config.polling : 5000;
                        console.log("ScoreboardRankingForAll.getCompetitionEventGroupsAll() tryAgainIn: " + tryAgainIn);
                        this.getDataLoop = window.setTimeout( () => {
                            this.getCompetitionEventGroupsAll(this.compId);
                        }, tryAgainIn);
                        return;
                    }

                    if (response.data.config) {
                        if(response.data.config.ranksPageTime && response.data.config.ranksPageTime > 1000) {
                            this.globalPageCycleTime = response.data.config.ranksPageTime;
                        }
                        if(response.data.config.ranksPerPage && response.data.config.ranksPerPage > 1) {
                            this.globalRanksPerPage = response.data.config.ranksPerPage;
                        }
                    }

                    return;
                })
                .catch((error) => {
                    messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
                    return;
                })
                .finally(() => {
                    this.isLoading = false;
                })
        }


        public stopGetData() {
            console.log("ScoreboardEventAll.stopGetData...clearTimeout: " + this.getDataLoop);
            clearTimeout(this.getDataLoop);
        }

        public noRankings() {
            this.cycleThroughEvents();
        }
        public atLastPage() {

            const timerDiv = document.getElementById("scoreboard-event-all-timer-dev");

            if (!timerDiv) {
                console.log("ScoreboardEventAll.atLastPage can not find div: " + "scoreboard-event-all-timer-dev" + ", stop loop.");
                return;
            }
            this.cycleThroughEvents();
        }

        public cycleThroughEvents() {
            if (this.eventCounterPosition >= this.scoreboardEvents.length) {
                this.eventCounterPosition = 0;
                //  we have cycled through all the events, get data again.
                // @ts-ignore
                this.getCompetitionEventGroupsAll(this.compId)
                    .then(() => {
                        this.setLocalState();
                    })
            } else {
                this.setLocalState();
            }
        }

        public setLocalState() {
            const scoreboardEvent: IScoreboardEvent = this.scoreboardEvents[this.eventCounterPosition];

            const numberOfPages = Math.ceil( (scoreboardEvent.ranking.length === 0 ? 1 : scoreboardEvent.ranking.length) / this.globalRanksPerPage );
            this.currentEventPageCount = numberOfPages;

            this.displayTimeForEvent = (this.globalPageCycleTime * numberOfPages ) + this.globalPageCycleTime;

            this.scoreboardEventToDisplay = R.clone(scoreboardEvent);

            this.$store.commit(SCOREBOARD_STORE_CONST.SCOREBOARD_CONST_MODULE_NAME + "/" +
                SCOREBOARD_STORE_CONST.SCOREBOARD_MUTATIONS_SET_SCOREBOARD, scoreboardEvent);

            this.eventCounterPosition++;
        }

        public destroy() {
            console.log("ScoreboardEventAll.destroy...");
            this.stopGetData();
        }

        @Watch("$route")
        public onRouteChanged(newValue: any, oldValue: any) {
            console.log("ScoreboardEventAll.onRouteChanged...");
        }

        public beforeRouteLeave(to: any, from: any, next: any) {
            console.warn("ScoreboardEventAll.beforeRouteLeave", {to, from});
            this.stopGetData();
            next();
        }

        public beforeRouteUpdate (to: any, from: any, next: any) {
            console.warn("ScoreboardEventAll.beforeRouteUpdate", {to, from});
            this.stopGetData();
            next()
        }

    }
</script>
