import { IBaseConcrete } from "../../common/common-models";

export interface IScoreboardCompEventGroup extends IBaseConcrete {
  compId: number;
  eventNo: number;
  startDateIso: string;
}

export interface IScoreboardAthlete extends IBaseConcrete {
  bibNo: number;
  pb: number;
}

export interface IScoreboardScore {
  score: number;
  scoreUom: string;
  modified: string;
}

export interface IScoreboardComp extends IBaseConcrete {
  logo: string; //  uri to image file.
}

export interface IScoreboardEventGroup extends IBaseConcrete {
  // eventNo: number;
  startDateIso: string;
}

export interface IScoreboardRank {
  bibno: number;
  athleteId: number;
  athleteName: string;
  position: string; //  1, 1st equal, etc.
  score: number; //  Best score to date.
}

export interface IScoreboardEvent {
  comp: IScoreboardComp;
  eventGroup: IScoreboardEventGroup;
  athlete: IScoreboardAthlete;
  score: IScoreboardScore;
  text: string;
  picture: string; //  url to video/picture
  ranking: IScoreboardRank[];
  config: {
    polling: number; //  (ms)
    showScore: number; //  if 0, never show the score (ms)
    showVideo: number; //  if 0, never show the video (ms)
    showPicture: number;

    ranksPerPage: number;
    ranksPageTime: number;

    // showElement: "all" | "rank" | "score";
  };
}
