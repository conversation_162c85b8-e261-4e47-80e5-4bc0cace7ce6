<template>
    <div class="ranking--wrapper">

        <table class="ranking--table">

            <tr class="ranking--table-header">
                <th class="ranking--table-pos">Position</th>
                <th class="ranking--table-bib">Bib</th>
                <th class="ranking--table-th-ath">Athlete</th>
                <th class="ranking--table-th-sco">Score</th>
            </tr>

            <tr v-for="(ranking, index) in scoreboardRanksDisplay" :style="rowStyle(ranking, index)">
                <td class="ranking--table-pos"><span v-text="ranking.position"></span></td>
                <td class="ranking--table-bib"><span v-text="ranking.bibno"></span></td>
                <td><span v-text="ranking.athleteName"></span></td>
                <td class="ranking--table-td-sco"><span v-text="getScore(ranking)"></span></td>
            </tr>
        </table>
        <div class="ranking--table-under">
            <span v-text="scoreboardEventDisplay.text"></span>
            <div class="ranking--table-page">
                Page <span v-text="displayingPage - 1"></span> of <span v-text="numberOfPages"></span>
            </div>
        </div>

        <div style="visibility: hidden" id="scoreboard-ranking-timer-dev">
            window.setTimeout() looking for this div id: scoreboard-ranking-timer-dev
        </div>

    </div>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop, Watch} from "vue-property-decorator"
    import {IScoreboardEvent, IScoreboardRank} from "../scoreboard-models"
    import {ScoreboardService} from "../scoreboard-service"
    import {parse, format} from "date-fns"

    const scoreboardService: ScoreboardService = new ScoreboardService();

    @Component({
        name: "scoreboard-ranking"
    })
    export default class ScoreboardRanking extends Vue {
        @Prop({
            default: () => {
                return scoreboardService.factoryScoreboardEvent();
            }
        }) public readonly scoreboardEvent: IScoreboardEvent;

        // public PREFIX = Math.random().toString(36).substring(2);
        public scoreboardEventDisplay: IScoreboardEvent = scoreboardService.factoryScoreboardEvent();

        public timerLoopPresentation: number;

        public rankPageLoopMs: number = 2000;
        public ranksPerPage: number  = 12;

        public numberOfPages: number = 0;
        public displayingPage: number = 1;
        public rowStart: number = 0;
        public rowEnd: number = 0;
        public scoreboardRanksDisplay: IScoreboardRank[] = []

        // public created() {
        //
        // }

        public mounted() {
            this.startLoopPresentation();
        }

        @Watch("scoreboardEvent")
        public onScoreboardEventChanged(newValue: IScoreboardEvent) {
            // const areRankingsSame = R.equals(newValue.ranking, this.scoreboardEventDisplay.ranking);
            this.scoreboardEventDisplay = R.clone(newValue);
        }

        public getScore(scoreboardRank: IScoreboardRank) {
            if (scoreboardRank.score === 0) {
                return "-";
            }

            return scoreboardRank.score;
        }

        public get getStartTime() {
            return format(parse(this.scoreboardEvent.eventGroup.startDateIso), "hh:mm");
        }

        public startLoopPresentation() {
            console.log("ScoreboardRanking.startLoopPresentation");
            this.displayingPage = 1;
            this.doPresentation();
        }

        public doPresentation() {
            console.log("ScoreboardRanking.doPresentation");

            // const timerDiv = document.getElementById(this.PREFIX);
            const timerDiv = document.getElementById("scoreboard-ranking-timer-dev");


            if (!timerDiv) {
                // console.log("ScoreboardRanking.doPresentation can not find div: " + this.PREFIX + ", stop loop.");
                console.log("ScoreboardRanking.doPresentation can not find div: " + "scoreboard-ranking-timer-dev" + ", stop loop.");
                // this.stopLoopPresentation();
                return;
            }

            this.timerLoopPresentation = window.setTimeout( () => {
                this.renderRankings();
            }, (this.scoreboardEvent.config.ranksPageTime >= this.rankPageLoopMs ? this.scoreboardEvent.config.ranksPageTime : this.rankPageLoopMs));
        }


        public renderRankings() {

            if (this.scoreboardEvent.ranking.length === 0) {
                console.log("ScoreboardRanking.startLoopPresentation ranking.length === 0, exit");

            } else {
                if (this.displayingPage > this.numberOfPages) {
                    this.displayingPage = 1;
                }
                // TODO
                const perPage = this.scoreboardEvent.config.ranksPerPage > 0 && this.scoreboardEvent.config.ranksPerPage <= 12  ?
                    this.scoreboardEvent.config.ranksPerPage :
                    this.ranksPerPage;
                // const ranksPerPage = this.ranksPerPage;

                this.numberOfPages = Math.ceil( this.scoreboardEvent.ranking.length / perPage );
                this.rowStart = this.displayingPage === 1 ? 0 : ((this.displayingPage -1) * perPage);
                this.rowEnd = this.rowStart + perPage;

                const rankings = R.clone(this.scoreboardEvent.ranking);
                const rankingsForPage = rankings.slice(this.rowStart, this.rowEnd);
                this.scoreboardRanksDisplay = rankingsForPage;

                console.log("ScoreboardRanking.render  numberOfPages: " + this.numberOfPages +
                    ", displayingPage: " + this.displayingPage +
                    ", rowStart: " + this.rowStart +
                    ", rowEnd: " + this.rowEnd,
                    R.clone(this.scoreboardEventDisplay)
                );

                this.displayingPage++;
            }
            this.doPresentation();
        }

        public stopLoopPresentation() {
            clearTimeout(this.timerLoopPresentation);
        }

        public doesAthleteMatchScore(scoreboardRank: IScoreboardRank): boolean {
            return this.scoreboardEvent.athlete.bibNo === scoreboardRank.bibno;
        }

        public rowStyle(scoreboardRank: IScoreboardRank, index: number) {
            const styles: Record<string, string> = {
                // lineHeight: "3vh"
            };

            if (this.doesAthleteMatchScore(scoreboardRank)) {
                styles["backgroundColor"] = "#c5f1da";
            } else {
                if (index % 2 === 0) {
                    styles["backgroundColor"] = "#fafafa";
                }
            }

            return styles;
        }

        public destroy() {
            this.stopLoopPresentation();
        }

        public beforeRouteLeave(to: any, from: any, next: any) {
            console.warn("ScoreboardRanking.beforeRouteLeave", {to, from});
            this.stopLoopPresentation();
            next();
        }

    }
</script>

<style scoped>

    .debug {
        font-size: 1vh;
    }

    .ranking--wrapper {
        margin: 0 1vh 0 1vh;
        font-size: 3vh;
        height: 85vh;
    }

    .ranking--table {

    }

    .ranking--table-header {
        /*font-size: 3vh;*/
        /*font-weight: normal;*/
        color: #da7500;
    }

    .ranking--table-row {
        /*border-top: 0.5vh solid #cddae6;*/
        /*border-bottom: 1vh solid #cddae6;*/
    }

    .ranking--table tr {
        line-height: 3vh;
    }

    .ranking--table th {
        font-weight: normal;
        text-align: center;
    }

    .ranking--table td {
        border: solid 1px #eae7e7;
    }

    .ranking--table-pos {
        width: 15%;
        text-align: center;
    }

    .ranking--table-bib {
        width: 15%;
        text-align: center;
    }

    .ranking--table-th-ath {
        text-align: left !important;
    }

    .ranking--table-th-sco {
        width: 15%;
    }

    .ranking--table-td-sco {
        width: 15%;
        text-align: right;
        padding-right: 3vw;
    }

    .ranking--table-under {
        font-size: 2vh;
        color: #da7500;
    }

    .ranking--table-page {
        float: right;
    }

    @media only screen and (max-height: 700px) {
        .ranking--wrapper {
            margin: 0 1vh 0 1vh;
            font-size: 2vh;
            height: 85vh;
        }

        .ranking--table tr {
            line-height: 2vh;
        }
    }


</style>
