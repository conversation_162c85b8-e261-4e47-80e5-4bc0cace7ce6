<template>
    <div>

        <!--        scoreboardEvent.link{{scoreboardEvent.link}}-->
        <!--        <div>-->
        <!--            <video width="320" height="240" autoplay>-->
        <!--                <source :src="scoreboardEvent.link" type="video/avi">-->
        <!--                Your browser does not support the video tag.-->
        <!--            </video>-->
        <!--        </div>-->

        <!--        <div>-->
        <!--            showRanking: {{showRanking}}<br/>-->
        <!--            showNewScore: {{showNewScore}}<br/>-->
        <!--            showVideo: {{showVideo}}<br/>-->
        <!--            showElement: {{scoreboardEvent.config.showElement}}-->
        <!--        </div>-->
        <div v-if="scoreboardEventStoreState.ranking.length === 0" class="scoreboard--no-scores">
            No scores available.
        </div>

        <div v-if="scoreboardEventStoreState.ranking.length > 0">
            <ScoreboardRanking v-show="showRanking" :scoreboard-event="scoreboardEvent"></ScoreboardRanking>

            <ScoreboardEvent v-if="showNewScore" :scoreboard-event="scoreboardEvent"></ScoreboardEvent>

            <div v-if="showVideo">
                <div>
                    <img :src="scoreboardEvent.picture">
                </div>
            </div>
        </div>

        <div style="visibility: hidden" id="scoreboard-event-route-timer-dev">
            window.setTimeout() looking for this div id: scoreboard-event-route-timer-dev
        </div>

    </div>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import ScoreboardEvent from "./scoreboard-event.vue";
    import {IScoreboardEvent} from "../scoreboard-models"
    import {ScoreboardService} from "../scoreboard-service";
    import {ScoreboardData} from "../scoreboard-data"
    import {messageDispatchHelper} from "../../../user-message/user-message-store"
    import {USER_MESSAGE_LEVEL} from "../../../user-message/user-message-models"
    import ScoreboardRanking from "./scoreboard-ranking.vue"
    import {mapState} from "vuex"
    import {IScoreboardStoreState, SCOREBOARD_STORE_CONST} from "../scorboard-store"

    @Component({
        name: "scoreboard-event-route",
        components: {
            ScoreboardRanking,
            ScoreboardEvent
        },
        computed: {
            ...mapState(SCOREBOARD_STORE_CONST.SCOREBOARD_CONST_MODULE_NAME, {
                scoreboardEventStoreState: (state: any) => state.scoreboardEvent
            })
        }
    })
    export default class ScoreboardEventRoute extends Vue {
        public scoreboardEventStoreState: IScoreboardEvent;

        public scoreboardService: ScoreboardService = new ScoreboardService();
        public scoreboardEvent: IScoreboardEvent = this.scoreboardService.factoryScoreboardEvent();

        public compId: number = 0;
        public eventNo: number = 0;
        public isLoading = false;

        public timerLoop: number;
        public timerLoopTransition: number;

        public showRanking: boolean = true;
        public showNewScore: boolean = false;
        public showVideo: boolean = false;

        public hasFirstRequestRun: boolean = false;
        public showElementParam: string = "all";

        public created() {
            this.compId = isNaN(Number(this.$route.params.compid)) ? 0 : parseInt(this.$route.params.compid, 0);
            this.eventNo = isNaN(Number(this.$route.params.eventno)) ? 0 : parseInt(this.$route.params.eventno, 0);

            const show = this.$route.query.show ? this.$route.query.show : "all" as string;
            console.log("ScoreboardEventRoute.created() show: " + show);
            this.showElementParam = show.toString();
        }

        public mounted() {
            this.getEventLatestScore().then( () => {
                this.startLoopGetData()
            })
        }

        public startLoopGetData() {

            const timerDiv = document.getElementById("scoreboard-event-route-timer-dev");

            if (!timerDiv) {
                console.log("ScoreboardEventRoute.startLoopGetData can not find div: " + "scoreboard-event-route-timer-dev" + ", stop loop.");
                return;
            }

            this.timerLoop = window.setTimeout( () => {
                this.getEventLatestScore();
            }, this.scoreboardEvent.config.polling);
        }


        public stopLoopGetData() {
            clearTimeout(this.timerLoop);
        }

        public getEventLatestScore() {
            this.isLoading = true;
            return new ScoreboardData().getEventLatestScore(this.compId, this.eventNo)
                .then( (response) => {
                    if (response.errNo > 0) {
                        messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
                        return;
                    }

                    // if (!this.hasFirstRequestRun) {
                    //     const showElement = this.scoreboardEvent.config.showElement;
                        const showElement = this.showElementParam;
                        this.showRanking = showElement === "all" || showElement === "rank";

                        //  ...else show the new score
                        this.showNewScore = showElement === "score";

                        //  ...but always hide the video
                        this.showVideo = false;
                    // }

                    this.hasFirstRequestRun = true;

                    if (this.hasScoreChanged(response.data, this.scoreboardEvent)) {
                        this.transitionNewScore();
                    } else {
                        this.startLoopGetData();
                    }

                    const scoreboardEvent = R.clone(response.data);
                    //  TODO
                    // scoreboardEvent.config.showElement = "score";
                    this.scoreboardEvent = scoreboardEvent;

                    this.$store.commit(SCOREBOARD_STORE_CONST.SCOREBOARD_CONST_MODULE_NAME + "/" +
                        SCOREBOARD_STORE_CONST.SCOREBOARD_MUTATIONS_SET_SCOREBOARD, response.data);

                    return;
                })
                .catch((error) => {
                    messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
                    return;
                })
                .finally( () => {
                    this.isLoading = false;
                })
        }

        public transitionNewScore() {

            console.warn("ScoreboardEventRoute.transitionNewScore....start...");

            //  Not quite sure what this does...!?!?!
            if (this.scoreboardEvent.config.showScore === 0) {
                this.showRanking = true;
                this.showNewScore = false;
                this.showVideo = false;
                console.warn("ScoreboardEventRoute.transitionNewScore....eh?...");
                return;
            }

            this.stopLoopGetData();


            // if (this.scoreboardEvent.config.showElement === "all") {
            //     this.showRanking = false;
            //     this.showNewScore = true;   //  <-----
            // }
            //
            // if (this.scoreboardEvent.config.showElement === "rank") {
            //     this.showRanking = true;   //  <-----
            //     this.showNewScore = false;
            // }
            //
            // if (this.scoreboardEvent.config.showElement === "score") {
            //     this.showRanking = true;
            //     this.showNewScore = false;  //  <-----
            // }

            // const showElement = this.scoreboardEvent.config.showElement;
            const showElement = this.showElementParam;
            //  only show "rank" if we are ONLY showing ranks...
            this.showRanking = showElement === "rank";

            //  ...else show the new score
            this.showNewScore = showElement === "all" || showElement === "score";

            //  ...but always hide the video
            this.showVideo = false;

            //  ...for this long
            this.timerLoopTransition = window.setTimeout( () => {

                console.warn("ScoreboardEventRoute.transitionNewScore...setTimeout...");

                // const showElement = this.scoreboardEvent.config.showElement;
                const showElement = this.showElementParam;

                //  Only show video if...
                if ((showElement === "all" || showElement === "score") &&
                    this.scoreboardEvent.picture.length > 0 &&
                    this.scoreboardEvent.config.showVideo > 0) {

                    console.warn("ScoreboardEventRoute.transitionNewScore...showVideo...");

                    //  only show the video
                    this.showRanking = false;
                    this.showNewScore = false;
                    this.showVideo = true;   //  <-----

                    window.setTimeout( () => {

                        console.warn("ScoreboardEventRoute.transitionNewScore...back to rank/score (a)...");

                        // const showElement = this.scoreboardEvent.config.showElement;
                        const showElement = this.showElementParam;
                        this.showRanking = showElement === "all" || showElement === "rank";
                        this.showNewScore = showElement === "score";
                        this.showVideo = false;

                        this.startLoopGetData();

                    }, this.scoreboardEvent.config.showVideo);
                    return;
                }

                // if (this.scoreboardEvent.config.r)
                console.warn("ScoreboardEventRoute.transitionNewScore...back to rank/score (b)...");
                this.showRanking = showElement === "all" || showElement === "rank";
                this.showNewScore = showElement === "score";
                this.showVideo = false;

                this.startLoopGetData();

            }, this.scoreboardEvent.config.showScore);
        }

        public hasScoreChanged(scoreboardEventNew: IScoreboardEvent, scoreboardEventOld: IScoreboardEvent) {
            if (scoreboardEventNew.athlete.id === 0 || scoreboardEventOld.athlete.id === 0) {
                return false;
            }
            console.log("ScoreboardEventRoute.hasScoreChanged() modified old: " + scoreboardEventOld.score.modified +
                ", modified new: " + scoreboardEventNew.score.modified);
            return scoreboardEventNew.score.modified !== scoreboardEventOld.score.modified;
        }

        public destroy() {
            this.stopLoopGetData();
            clearTimeout(this.timerLoopTransition);
        }

        public beforeRouteLeave(to: any, from: any, next: any) {
            console.warn("ScoreboardEventRoute.beforeRouteLeave", {to, from});
            this.stopLoopGetData();
            clearTimeout(this.timerLoopTransition);
            next();
        }
    }

</script>


<style scoped>
    .scoreboard--no-scores {
        height: 30vh;
        line-height: 30vh;
        font-size: 10vh;
        padding-left: 5vw;
    }
</style>
