import {
  HEAT_LABEL,
  IPublicHeatGroupMeta,
  IR4sAthlete,
  IR4sAthleteEntry,
  IR4sCompSchedule,
  IR4sEntry,
  IR4sPayloadTrack,
  IR4sPhotoFinishResult,
  IR4sScheduledEvent,
  IR4sSocketDataMessage,
  IResultRow,
  IResultRowTrack,
  IRs4Score,
  IScheduleTableRow,
  R4S_DESIGN_TYPE,
  R4SEventType,
} from "./rs4-scoreboard-models";
import { IScoreboardSchedule } from "../scoreboard-schedule/scoreboard-schedule-models";
import { CommonService } from "../../../common/common-service";
import { isAfter, isBefore, parse } from "date-fns";
import * as R from "ramda";
import { BuilderService } from "../../../builder/builder-service";
import {
  EventType,
  IEventSeed,
} from "../../../athleteCompSched/athletecompsched-models";
import * as CommonServiceUtils from "../../../common/common-service-utils";
import { sortArray } from "../../../common/common-service-utils";

const commonService: CommonService = new CommonService();
const builderService = new BuilderService();

export class Rs4Service {
  public factoryR4sCompSchedule(): IR4sCompSchedule {
    return {
      compId: 0,
      name: "",
      date: "",
      location: "",
      club: "",
      logo: "",
      org: {
        id: 0,
        name: "",
      },
      schedule: [],
      autoEntries: builderService.factoryAutoEntries(),
    };
  }

  public factoryR4sScheduledEvent(): IR4sScheduledEvent {
    return {
      id: 0,
      eventNo: 0,
      eventName: "",
      name: "",
      type: "T",
      typeNo: "T1",
      isTeamEvent: false,
      startDate: "",
      counts: {
        entries: 0,
        maxAthletes: 0,
        maxInHeat: 0,
      },
      wind: "",
      results: false,
      waiting: false,
      seed: this.factoryEventSeed(),
      entries: [],
      teamEntries: [],
      resultsPossible: true,
    };
  }

  public factoryEventSeed(): IEventSeed {
    return {
      gender: false,
      age: false,
      type: "O",
      qualifyToEg: {
        id: 0,
        name: "",
        eventNo: 0,
        isMultiEvent: false,
      },
      laneCount: 8,
      firstLane: 1,
      waiting: false,
    };
  }

  public factoryR4sAthlete(): IR4sAthlete {
    return {
      id: 0,
      club: "",
      gender: "M",
      name: "",
      pb: 0,
      sb: 0,
      aocode: "",
      urn: "",
    };
  }

  public factoryPublicEventGroupMeta(): Record<number, IPublicHeatGroupMeta> {
    // return {
    //     eventTime: "",
    //     heatNo: 0,
    //     image: "",
    //     ws: ""
    // }
    return {};
  }

  // factoryRs4ScoreboardOutputOptions(): IRs4ScoreboardOutputOptions {
  //     return   {
  //         timings: {
  //             ranking: {
  //                 pageSize: 10,
  //                 pageCycleMs: 4000
  //             },
  //             scoreboard: {
  //                 displayForMs: 5000
  //             }
  //         }
  //     }
  // }
  //
  // factoryRs4ScoreboardOutput(): IRs4ScoreboardOutput {
  //     return {
  //         id: 0,
  //         compid: 0,
  //         output: 0,
  //         description: "",
  //         options: this.factoryRs4ScoreboardOutputOptions()
  //     }
  // }

  public getEventGroupIdFromMessage(
    r4sSocketDataMessage: IR4sSocketDataMessage
  ): number {
    return this.getEventGroupIdFromEntries(
      r4sSocketDataMessage.data.payload.entries
    );
    // const entriesArray = commonService.convertObjectToArray(r4sSocketDataMessage.data.payload.entries);
    // if (entriesArray.length === 0 ) {
    //    return 0;
    // }
    // return entriesArray[0].eventGroup.id;
  }

  public getEventGroupIdFromEntries(
    entries: Record<string, IR4sEntry>
  ): number {
    const entriesArray = commonService.convertObjectToArray(entries);
    if (entriesArray.length === 0) {
      return 0;
    }
    return entriesArray[0].eventGroup.id;
  }

  public shouldDisplayScore(
    r4sSocketDataMessage: IR4sSocketDataMessage,
    scoreboardSchedules: IScoreboardSchedule[],
    pointInTime: Date = new Date()
  ): {
    result: boolean;
    message: string;
    code:
      | "NO_ENTRIES"
      | "NO_EVENT_GROUP_MATCH"
      | "POINT_IN_TIME_WRONG"
      | "NO_ATHLETE_MATCH"
      | "SUCCESS";
  } {
    const entriesArray = commonService.convertObjectToArray(
      r4sSocketDataMessage.data.payload.entries
    );
    if (entriesArray.length === 0) {
      return {
        result: false,
        message: "No entries",
        code: "NO_ENTRIES",
      };
    }
    const eventGroupId = this.getEventGroupIdFromMessage(r4sSocketDataMessage);

    const scoreboardSchedulesWithEventGroupId: IScoreboardSchedule[] =
      this.getSchedulesContainingEventGroupId(
        eventGroupId,
        scoreboardSchedules
      );
    if (scoreboardSchedulesWithEventGroupId.length === 0) {
      return {
        result: false,
        message: "No schedules contain event Group id: " + eventGroupId,
        code: "NO_EVENT_GROUP_MATCH",
      };
    }
    const scoreboardSchedulesInTimeRange =
      scoreboardSchedulesWithEventGroupId.filter((sched) => {
        return this.isEventInScheduleTimeRange(sched, pointInTime);
      });
    if (scoreboardSchedulesInTimeRange.length === 0) {
      return {
        result: false,
        message:
          "Point in time " + pointInTime + " is not within any schedules",
        code: "POINT_IN_TIME_WRONG",
      };
    }

    // const validSchedules = this.getValidSchedules(eventGroupId, scoreboardSchedules, pointInTime);
    // if (validSchedules.length === 0) {
    //     return {
    //         result: false,
    //         message: "No valid schedules"
    //     };
    // }

    const r4sEntry: IR4sEntry | null =
      this.getAthleteEntryFromScore(r4sSocketDataMessage);
    if (!r4sEntry) {
      return {
        result: false,
        message: "Can't find matching athlete",
        code: "NO_ATHLETE_MATCH",
      };
    }

    return {
      result: true,
      message: "Success",
      code: "SUCCESS",
    };
  }

  public getValidSchedules(
    eventGroupId: number,
    scoreboardSchedules: IScoreboardSchedule[],
    pointInTime: Date = new Date()
  ): IScoreboardSchedule[] {
    const scoreboardSchedulesWithEventGroupId: IScoreboardSchedule[] =
      this.getSchedulesContainingEventGroupId(
        eventGroupId,
        scoreboardSchedules
      );
    const scoreboardSchedulesInTimeRange =
      scoreboardSchedulesWithEventGroupId.filter((sched) => {
        return this.isEventInScheduleTimeRange(sched, pointInTime);
      });
    return scoreboardSchedulesInTimeRange;
  }

  public getSchedulesContainingEventGroupId(
    eventGroupId: number,
    scoreboardSchedules: IScoreboardSchedule[]
  ): IScoreboardSchedule[] {
    return scoreboardSchedules.reduce((accum, scoreboardSchedule) => {
      if (
        this.doesScheduleContainEventGroupId(eventGroupId, scoreboardSchedule)
      ) {
        accum.push(scoreboardSchedule);
      }
      return accum;
    }, [] as IScoreboardSchedule[]);
  }

  public doesScheduleContainEventGroupId(
    eventGroupId: number,
    scoreboardSchedule: IScoreboardSchedule
  ): boolean {
    return scoreboardSchedule.events.reduce((accum, scheduleEvent) => {
      if (scheduleEvent.id === eventGroupId) {
        accum = true;
      }
      return accum;
    }, false as boolean);
  }

  public anyEventsInScheduleTimeRange(
    scoreboardSchedules: IScoreboardSchedule[],
    pointInTime: Date = new Date()
  ): IScoreboardSchedule[] {
    return scoreboardSchedules.filter((sched) => {
      return this.isEventInScheduleTimeRange(sched, pointInTime);
    });
  }

  public isEventInScheduleTimeRange(
    scoreboardSchedule: IScoreboardSchedule,
    pointInTime: Date = new Date()
  ): boolean {
    const isAfterStart = isAfter(
      pointInTime,
      parse(scoreboardSchedule.startdt)
    );
    const isBeforeEnd = isBefore(pointInTime, parse(scoreboardSchedule.enddt));

    return isAfterStart && isBeforeEnd;
  }

  public getAthleteEntryFromScore(
    r4sSocketDataMessage: IR4sSocketDataMessage
  ): IR4sEntry | null {
    const r4sEntry: IR4sEntry | null =
      r4sSocketDataMessage.data.payload.entries[
        r4sSocketDataMessage.data.payload.score.athleteId.toString()
      ];
    if (r4sEntry) {
      return r4sEntry;
    }
    return null;
  }

  public getAthleteFromScore(
    r4sSocketDataMessage: IR4sSocketDataMessage
  ): IR4sAthlete | null {
    const r4sEntry: IR4sEntry | null =
      this.getAthleteEntryFromScore(r4sSocketDataMessage);
    if (r4sEntry) {
      return r4sEntry.athlete;
    }
    return null;
  }

  public howManyEntriesPages(
    r4sSocketDataMessage: IR4sSocketDataMessage,
    pageSize: number
  ): number {
    // const entriesArray = commonService.convertObjectToArray(r4sSocketDataMessage.data.payload.entries);
    const entriesArray = this.getEntriesArray(r4sSocketDataMessage);
    return Math.ceil(entriesArray.length / pageSize);
  }

  public getEntriesArray(
    r4sSocketDataMessage: IR4sSocketDataMessage
  ): IR4sEntry[] {
    return this.filterEntries(
      commonService.convertObjectToArray(
        r4sSocketDataMessage.data.payload.entries
      )
    );
  }

  public sortAndFilterEntries(
    entries: IR4sEntry[],
    eventType?: R4SEventType,
    includeZeroAndDnsDnf?: boolean
  ): IR4sEntry[] {
    const sortEntries = (entriesSort: IR4sEntry[]) => {
      return this.sortEntries(entriesSort, eventType);
    };

    const filterIncludeZeroAndDnsDnf = (entriesSort: IR4sEntry[]) => {
      if (includeZeroAndDnsDnf) {
        return entriesSort;
      }
      return this.filterEntries(entriesSort);
    };

    return R.compose(sortEntries, filterIncludeZeroAndDnsDnf)(R.clone(entries));
  }

  public filterEntries(entries: IR4sEntry[]): IR4sEntry[] {
    return entries.filter((r4sEntry) => {
      //  TODO  use -1 for testing if data a hassle.
      return r4sEntry.results.currentPosition > 0;
    });
  }

  public sortAndFilterResultEntries(
    entries: IR4sEntry[],
    eventType?: R4SEventType
  ): IR4sEntry[] {
    const sortEntries = (entriesSort: IR4sEntry[]) => {
      return this.sortEntries(entriesSort, eventType);
    };

    const filterIncludeZeroAndDnsDnf = (entriesFilter: IR4sEntry[]) => {
      return entriesFilter.filter((r4sEntry) => {
        return r4sEntry.results.data;
      });
    };

    return R.compose(sortEntries, filterIncludeZeroAndDnsDnf)(R.clone(entries));
  }

  /**
   * put current position zero at bottom
   * @param entries
   */
  public sortEntries(
    entries: IR4sEntry[],
    eventType?: R4SEventType
  ): IR4sEntry[] {
    const splitIntoWithAndWithOutScore = entries.reduce(
      (accum, entry) => {
        if (entry.results.currentPosition > 0) {
          accum.withScore.push(entry);
        } else {
          accum.noScore.push(entry);
        }

        return accum;
      },
      {
        withScore: [] as IR4sEntry[],
        noScore: [] as IR4sEntry[],
      }
    );
    return [
      ...splitIntoWithAndWithOutScore.withScore.sort(function (a, b) {
        //  Track: lowest time is better, Field: highest score is better..
        // return eventType === 'T' ?
        //     b.results.currentPosition - a.results.currentPosition :
        //     a.results.currentPosition - b.results.currentPosition;
        return a.results.heatPosition - b.results.heatPosition;
      }),
      ...splitIntoWithAndWithOutScore.noScore,
    ];
  }

  public sortScheduleMap(
    scheduledEvents: IR4sScheduledEvent[]
  ): Record<EventType, IR4sScheduledEvent[]> {
    const mapByType: Record<EventType, IR4sScheduledEvent[]> =
      scheduledEvents.reduce<Record<EventType, IR4sScheduledEvent[]>>(
        (accum, scheduledEvent) => {
          const eventType: EventType = scheduledEvent.typeNo.slice(
            0,
            1
          ) as EventType;

          if (!accum[eventType]) {
            accum[eventType] = [];
          }
          accum[eventType]!.push(scheduledEvent);
          return accum;
        },
        {
          T: [],
          F: [],
          M: [],
          R: [],
          X: [],
        }
      );

    (Object.keys(mapByType) as EventType[]).forEach((eventTypeKey) => {
      mapByType[eventTypeKey] = sortArray("eventNo", mapByType[eventTypeKey]);
    });

    // let sortedTrack: IR4sScheduledEvent[] = [];
    // if (mapByType.T) {
    //   mapByType.T = sortArray("eventNo", mapByType.T);
    // }
    //
    // if (mapByType.F) {
    //   mapByType.F = sortArray("eventNo", mapByType.F);
    // }

    return mapByType;
  }

  public getEventName(r4sSocketDataMessage: IR4sSocketDataMessage): string {
    const entriesArray = this.getEntriesArray(r4sSocketDataMessage);
    if (entriesArray.length > 0) {
      return entriesArray[0].eventGroup.groupName;
    }
    return "";
  }

  public getEventNameFromSchedulesByEventGroupId(
    eventGroupId: number,
    scoreboardSchedules: IScoreboardSchedule[]
  ): string {
    return scoreboardSchedules.reduce((accum, schedule) => {
      schedule.events.forEach((eventGroup) => {
        if (eventGroup.id === eventGroupId) {
          accum = eventGroup.name;
        }
      });
      return accum;
    }, "NA");
  }

  /**
   * TODO
   * //    1 = Scoreboard, 2 = Ranking, 3 = Scoreboard + Ranking, 4 = Picture
   * @param scoreboardSchedule
   */
  public getDesignTypeForSchedule(
    scoreboardSchedule: IScoreboardSchedule
  ): R4S_DESIGN_TYPE {
    return scoreboardSchedule.design.designNo;
  }

  public getScoreFromScore(
    rs4Score: IRs4Score,
    appendScoreText: boolean = true
  ): string {
    let score = "";
    if (typeof rs4Score.score === "number") {
      //  TODO what to do with zero
      score = commonService
        .roundNumberToDecimalPlaces(rs4Score.score, 2, false)
        .toString();
    } else {
      score = rs4Score.score;
    }
    return score; // + (appendScoreText ? " " + rs4Score.scoreText : "" );
  }

  public getScoreFromEntry(r4sEntry: IR4sEntry): string {
    if (typeof r4sEntry.results.highScore === "string") {
      return r4sEntry.results.highScore;
    }

    //  TODO what to do with zero
    if (r4sEntry.results.highScore === 0) {
      return "-";
    }
    const score = commonService
      .roundNumberToDecimalPlaces(r4sEntry.results.highScore, 2, false)
      .toString();
    // return score + " " + r4sEntry.results.scoreText;
    return score;
  }

  public getPbFromEntry(r4sEntry: IR4sEntry): string {
    if (typeof r4sEntry.results.highScore === "string") {
      return r4sEntry.results.highScore;
    }

    //  TODO what to do with zero
    if (r4sEntry.results.highScore === 0) {
      return "-";
    }
    const score = commonService
      .roundNumberToDecimalPlaces(r4sEntry.athlete.pb, 2, false)
      .toString();
    // return score + " " + r4sEntry.results.scoreText;
    return score;
  }

  public shouldDisplayPhotoFinish(
    socketPhotoFinishMessage: IR4sSocketDataMessage,
    scoreboardSchedules: IScoreboardSchedule[],
    pointInTime: Date = new Date()
  ): {
    result: boolean;
    message: string;
    code:
      | "NO_ENTRIES"
      | "NO_EVENT_GROUP_MATCH"
      | "POINT_IN_TIME_WRONG"
      | "NO_ATHLETE_MATCH"
      | "SUCCESS";
  } {
    // const scoreboardSchedulesWithEventGroupId: IScoreboardSchedule[] = scoreboardSchedules;

    //  TODO
    // const socketData: IR4sSocketPhotoFinishMessage = socketPhotoFinishMessage.data as unknown as IR4sSocketPhotoFinishMessage;
    const scoreboardSchedulesWithEventGroupId: IScoreboardSchedule[] =
      scoreboardSchedules;
    // const eventGroupId =  socketData.payload.eventNo;
    // const scoreboardSchedulesWithEventGroupId: IScoreboardSchedule[] = this.getSchedulesContainingEventGroupId(eventGroupId, scoreboardSchedules);
    // if (scoreboardSchedulesWithEventGroupId.length === 0) {
    //     return {
    //         result: false,
    //         message: "No schedules contain event Group id: " + eventGroupId,
    //         code: "NO_EVENT_GROUP_MATCH"
    //     };
    // }
    const scoreboardSchedulesInTimeRange =
      scoreboardSchedulesWithEventGroupId.filter((sched) => {
        return this.isEventInScheduleTimeRange(sched, pointInTime);
      });
    if (scoreboardSchedulesInTimeRange.length === 0) {
      return {
        result: false,
        message:
          "Point in time " + pointInTime + " is not within any schedules.",
        code: "POINT_IN_TIME_WRONG",
      };
    }

    return {
      result: true,
      message: "Success",
      code: "SUCCESS",
    };
  }

  public sortPhotoFinishResults(
    photoFinishResults: IR4sPhotoFinishResult[]
  ): IR4sPhotoFinishResult[] {
    const realAndDnfs = photoFinishResults.reduce<
      Record<"real" | "dnf", IR4sPhotoFinishResult[]>
    >(
      (accum, photoFinishResult) => {
        const isNumeric: boolean = CommonServiceUtils.isNumeric(
          photoFinishResult.timeInSeconds
        );
        // !isNumeric || Number(photoFinishResult.resultvalue) === 0

        !isNumeric || photoFinishResult.timeInSeconds === 0
          ? accum.dnf.push(photoFinishResult)
          : accum.real.push(photoFinishResult);
        return accum;
      },
      {
        real: [],
        dnf: [],
      }
    );

    const pred = (result: any) => {
      return result.options.place;
    };
    const realSorted = commonService.sortArray(pred, realAndDnfs.real);

    return [...realSorted, ...realAndDnfs.dnf];
  }

  public getPayloadTrackKey(payloadTrack: IR4sPayloadTrack): string {
    return payloadTrack.eventName + "-" + payloadTrack.heatNo;
  }

  public mapTrackToScheduleTableRow(
    payloadTrack: IR4sPayloadTrack
  ): IScheduleTableRow {
    return {
      scheduledEvent: payloadTrack,
      eventGroupId: -111,
      eventKey: this.getPayloadTrackKey(payloadTrack),
      startTime: payloadTrack.scheduleTime,
      eventName: payloadTrack.eventName + " - " + payloadTrack.heatNo,
      entries: -1,
      heats: -1,
      status: "unchecked",
      hasResults: false,
      resultsPossible: true,
      waiting: payloadTrack.waiting,
      counts: {
        entries: -1,
        maxAthletes: -1,
        maxInHeat: -1,
      },
    };
  }

  public mapScheduleEventToScheduleTableRow(
    scheduledEvent: IR4sScheduledEvent
  ): IScheduleTableRow {
    let entries: IR4sAthleteEntry[] = [];
    if (scheduledEvent.entries) {
      entries =
        scheduledEvent.entries && scheduledEvent.entries.length > 0
          ? scheduledEvent.entries
          : [];
    }

    return {
      scheduledEvent,
      eventGroupId: scheduledEvent.id,
      eventKey: scheduledEvent.id.toString(),
      startTime: scheduledEvent.startDate,
      eventName: scheduledEvent.name,
      entries: scheduledEvent.counts.entries,
      heats: Math.ceil(
        scheduledEvent.counts.entries / scheduledEvent.counts.maxInHeat
      ),
      status: "unchecked",
      hasResults: scheduledEvent.results,
      resultsPossible: scheduledEvent.resultsPossible,
      waiting: scheduledEvent.waiting,
      athleteEntries: entries,
      counts: scheduledEvent.counts,
    };
  }

  public mapR4sEntryToIResultRow(r4sEntry: IR4sEntry): IResultRow {
    return {
      bibNo: r4sEntry.entry.bibNo,
      athlete: {
        id: r4sEntry.athlete.id,
        name: r4sEntry.athlete.name,
        urn: r4sEntry.athlete.urn,
        aocode: r4sEntry.athlete.aocode,
      },
      club: r4sEntry.athlete.club,
      result: this.getScoreFromEntry(r4sEntry),
      position: r4sEntry.results.currentPosition,
      heatPosition: r4sEntry.results.heatPosition,
      status: "unchecked",
    };
  }

  public getScoreFromTrackEntry(r4sEntry: IR4sEntry): string {
    //  TODO what to do with zero
    if (r4sEntry.results.highScore === 0) {
      return "-";
    }
    // const dataKeys = Object.keys(r4sEntry.results.data);
    // const dataKey = dataKeys[ dataKeys.length - 1];
    //  @ts
    // const score = r4sEntry.results.data[dataKey];

    if (typeof r4sEntry.results.highScore === "string") {
      return r4sEntry.results.highScore;
    }

    const score = commonService
      .roundNumberToDecimalPlaces(r4sEntry.results.highScore, 2, false)
      .toString();
    return score + " " + r4sEntry.results.scoreText;
  }

  public mapR4sEntriesToResultRowTracks(
    r4sEntries: IR4sEntry[]
  ): IResultRowTrack[] {
    return r4sEntries.reduce((accum, r4sEntry) => {
      const hack: IResultRowTrack[] =
        this.mapR4sEntryToResultRowTrack(r4sEntry);
      //  We have IE11 users...
      let i;
      const length = hack.length;
      for (i = 0; i < length; i++) {
        accum.push(hack[i]);
      }

      return accum;
    }, [] as IResultRowTrack[]);
  }

  public mapR4sEntryToResultRowTrack(r4sEntry: IR4sEntry): IResultRowTrack[] {
    const hackHeats = Object.keys(r4sEntry.results.data);
    const fTHis: boolean = r4sEntry.eventGroup.eventNo === 9;

    if (fTHis) {
      return hackHeats.map((hackHeatKey) => {
        const heatNumber: number = Number(hackHeatKey.replace("h", ""));
        const heatName = "Heat " + heatNumber;
        const score =
          r4sEntry.results.data[hackHeatKey as HEAT_LABEL].toString();

        //  @ts-ignore
        const scoreData = JSON.parse(
          //  @ts-ignore
          r4sEntry.results.dataOptions[hackHeatKey as HEAT_LABEL]
        );

        return {
          ...this.mapR4sEntryToIResultRow(r4sEntry),
          result: score,
          lane: 0,
          heat: heatName,
          heatNumber,
          needsSorting: true,
          hackScore: scoreData.place,
        };
      });
    } else {
      const heatName =
        "Heat " +
        (r4sEntry.heatInfo && r4sEntry.heatInfo.heatNo
          ? r4sEntry.heatInfo.heatNo.toString()
          : "NA");
      const heatNumber: number =
        r4sEntry.heatInfo && r4sEntry.heatInfo.heatNo
          ? r4sEntry.heatInfo.heatNo
          : 0;
      const score = this.getScoreFromTrackEntry(r4sEntry);
      return [
        {
          ...this.mapR4sEntryToIResultRow(r4sEntry),
          result: score,
          lane: 0,
          heat: heatName,
          heatNumber,
          needsSorting: false,
          hackScore: -1,
        },
      ];
    }
  }

  public isFeederComp(r4sCompSchedule: IR4sCompSchedule) {
    return r4sCompSchedule.autoEntries.selectedTargetComp.id > 0;
  }
}
