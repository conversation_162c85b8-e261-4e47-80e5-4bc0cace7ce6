<template>
  <div class="scoreboard--score-wrapper">
    <div class="scoreboard--score-header">
      <div
        class="scoreboard--score-header-event"
        v-text="r4sSocketDataMessage.data.payload.score.eventGroup.groupName"
      ></div>
      <!--      <div class="scoreboard&#45;&#45;score-header-e4s">-->
      <!--        entry4sports-->
      <!--      </div>-->
    </div>

    <div class="scoreboard--score-sep"></div>

    <div class="scoreboard--score-scoreboard-name">
      <span v-text="getTitle"></span>
    </div>

    <div class="scoreboard--score-sep"></div>

    <div class="scoreboard--score-scoreboard-scores">
      <div
        class="scoreboard--score-scoreboard-score-wrapper"
      >
        <div class="scoreboard--score-scoreboard-score-bib">Bib: <span v-text="getBib"></span></div>
        <div class="scoreboard--score-scoreboard-score-data" v-text="getScore"></div>
      </div>

      <div class="scoreboard--score-scoreboard-score-wrapper-right">
        <div class="scoreboard--score-scoreboard-bib-and-pb">
          <span class="scoreboard--score-scoreboard-bib-and-pb-label">Rank:</span
          ><span
            class="scoreboard--score-scoreboard-bib-and-pb-data"
            v-text="getPosition"
          ></span>
        </div>

        <div class="scoreboard--score-scoreboard-bib-and-pb">
          <span class="scoreboard--score-scoreboard-bib-and-pb-label"
            >Best:</span
          ><span
            class="scoreboard--score-scoreboard-bib-and-pb-data"
            v-text="getBest"
          ></span>
        </div>

        <div class="scoreboard--score-scoreboard-bib-and-pb">
          <span class="scoreboard--score-scoreboard-bib-and-pb-label">PB:</span
          ><span
            class="scoreboard--score-scoreboard-bib-and-pb-data"
            v-text="getPb"
          ></span>
        </div>

        <!--                <div class="scoreboard&#45;&#45;score-scoreboard-bib-and-pb score-scoreboard&#45;&#45;scoreboard-bib scoreboard&#45;&#45;border">-->
        <!--                    POS: <span v-text="getPosition"></span>-->
        <!--                </div>-->

        <!--                <div class="scoreboard&#45;&#45;score-sep-pb"></div>-->

        <!--                <div class="scoreboard&#45;&#45;score-scoreboard-bib-and-pb scoreboard&#45;&#45;border">-->
        <!--                    PB: <span v-text="getPb"></span>-->
        <!--                </div>-->
      </div>
    </div>

    <div class="scoreboard--score-scoreboard-footer-e4s">
      www.entry4sports.com
    </div>
  </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import {
  IR4sEntry,
  IR4sSocketDataMessage,
  IRs4Score,
} from "./rs4-scoreboard-models";
import { Rs4Service } from "./rs4-service";

@Component({
  name: "rs4-score-v2",
})
export default class Rs4ScoreV2 extends Vue {
  @Prop({ required: true })
  public readonly r4sSocketDataMessage: IR4sSocketDataMessage;

  public rs4Service: Rs4Service = new Rs4Service();

  public get getBib(): string {
    if (!this.doWeHaveData(this.r4sSocketDataMessage)) {
      return "";
    }
    const r4sEntry: IR4sEntry | null = this.rs4Service.getAthleteEntryFromScore(
      this.r4sSocketDataMessage
    );
    if (r4sEntry) {
      return r4sEntry.entry.bibNo.toString();
    }
    return "";
  }


  public get getTitle(): string {
    if (!this.doWeHaveData(this.r4sSocketDataMessage)) {
      return "";
    }
    const r4sEntry: IR4sEntry | null = this.rs4Service.getAthleteEntryFromScore(
      this.r4sSocketDataMessage
    );
    if (r4sEntry) {
      return r4sEntry.athlete.name;
    }
    return "";
  }

  public get getScore(): string {
    if (!this.doWeHaveData(this.r4sSocketDataMessage)) {
      return "";
    }
    // const rs4Score: IRs4Score | null = this.r4sSocketDataMessage.data.payload.score;
    const rs4Score: IRs4Score | null = this.getScoreFromData(
      this.r4sSocketDataMessage
    );
    if (!rs4Score) {
      return "-";
    }
    return this.rs4Service.getScoreFromScore(rs4Score, false);
  }

  public get getPosition(): string {
    if (!this.doWeHaveData(this.r4sSocketDataMessage)) {
      return "";
    }
    const r4sEntry: IR4sEntry | null = this.rs4Service.getAthleteEntryFromScore(
      this.r4sSocketDataMessage
    );
    if (r4sEntry) {
      return r4sEntry.results.currentPosition.toString();
    }
    return "";
  }

  public get getBest(): string {
    if (!this.doWeHaveData(this.r4sSocketDataMessage)) {
      return "";
    }
    const r4sEntry: IR4sEntry | null = this.rs4Service.getAthleteEntryFromScore(
      this.r4sSocketDataMessage
    );
    if (r4sEntry) {
      return this.rs4Service.getScoreFromEntry(r4sEntry);
    }
    return "";
  }

  public get getPbClass(): string {
    if (!this.doWeHaveData(this.r4sSocketDataMessage)) {
      return "";
    }
    // const rs4Score: IRs4Score = this.r4sSocketDataMessage.data.payload.score;
    const rs4Score: IRs4Score | null = this.getScoreFromData(
      this.r4sSocketDataMessage
    );
    if (!rs4Score) {
      return "";
    }
    const r4sEntry: IR4sEntry | null = this.rs4Service.getAthleteEntryFromScore(
      this.r4sSocketDataMessage
    );
    if (r4sEntry && rs4Score.score > r4sEntry.athlete.pb) {
      return "scoreboard--scoreboard-score-above-pb";
    }
    return "";
  }

  public get getPb(): string {
    if (!this.doWeHaveData(this.r4sSocketDataMessage)) {
      return "";
    }
    const r4sEntry: IR4sEntry | null = this.rs4Service.getAthleteEntryFromScore(
      this.r4sSocketDataMessage
    );
    if (r4sEntry) {
      return this.rs4Service.getPbFromEntry(r4sEntry);
      // return r4sEntry.athlete.pb.toString();
    }
    return "";
  }

  public doWeHaveData(r4sSocketDataMessage: IR4sSocketDataMessage): boolean {
    return r4sSocketDataMessage &&
      r4sSocketDataMessage.data &&
      r4sSocketDataMessage.data.payload
      ? true
      : false;
  }

  public getScoreFromData(
    r4sSocketDataMessage: IR4sSocketDataMessage
  ): IRs4Score | null {
    if (
      r4sSocketDataMessage &&
      r4sSocketDataMessage.data &&
      r4sSocketDataMessage.data.payload &&
      r4sSocketDataMessage.data.payload.score
    ) {
      return r4sSocketDataMessage.data.payload.score;
    }
    return null;
  }
}
</script>

<style scoped>
.scoreboard--score-wrapper {
  width: 100%;
  /*border: solid 1px black;*/
  /*height: 85vh;*/
  height: 100vh;
  padding: 1vw;
  background-color: black;
  color: white;
  border-right: 5px solid white;
  border-left: 5px solid white;
}

.scoreboard--score-header {
  height: 15vh;
  /*background-color: #0bbf0b;*/
  line-height: 15vh;
}

.scoreboard--score-header-event {
  height: 12vh;
  line-height: 12vh;
  font-size: 12vh;
  /*text-align: center;*/
}

.scoreboard--score-header-e4s {
  height: 12vh;
  /*background-color: #0bbf0b;*/
  line-height: 12vh;
  font-size: 12vh;
  float: right;
}

.scoreboard--score-scoreboard-bib {
  margin-bottom: 10vh;
}

/*.scoreboard--border {*/
/*  border: solid 0.5vh black;*/
/*}*/

/*.scoreboard--score-sep {*/
/*    height: 2vh;*/
/*}*/

.scoreboard--score-scoreboard-name {
  width: 100%;
  /*background-color: orange;*/
  height: 15vh;
  line-height: 15vh;
  font-size: 10vh;
  text-align: center;
  /*border: solid 2px black;*/
  margin-bottom: 5vh;
}

.scoreboard--score-scoreboard-scores {
  width: 100%;
  /*background-color: pink;*/
}

.scoreboard--score-scoreboard-score-wrapper {
  width: 46vw;
  height: 46vh;
  /*line-height: 45vh;*/
  /*background-color: yellow;*/
  display: inline-block;
  border: solid 5px white;
  vertical-align: top;
  text-align: center;
  vertical-align: middle;
  /*font-size: 20vh;*/
}

.scoreboard--score-scoreboard-score-bib {
  font-size: 10vh;
}

.scoreboard--score-scoreboard-score-data {
  font-size: 20vh;
}


.scoreboard--score-scoreboard-score-wrapper-right {
  width: 46vw;
  height: 46vh;
  /*background-color: green;*/
  display: inline-block;
  /*border: solid 2px black;*/
  vertical-align: top;
  float: right;
  padding-right: 5vw;
}

.scoreboard--score-scoreboard-bib-and-pb {
  /*background-color: blue;*/
  /*height: 23vh;*/
  /*line-height: 23vh;*/
  /*text-align: center;*/
  vertical-align: middle;
  /*border: solid 2px black;*/
  font-size: 10vh;
  /*float: right;*/
}

.scoreboard--score-scoreboard-bib-and-pb-label {
}

.scoreboard--score-scoreboard-bib-and-pb-data {
  float: right;
}

.scoreboard--score-scoreboard-footer-e4s {
  text-align: center;
  font-size: 10vh;
}

.scoreboard--scoreboard-score-above-pb {
  /*background-color: #00C853;*/
}
</style>
