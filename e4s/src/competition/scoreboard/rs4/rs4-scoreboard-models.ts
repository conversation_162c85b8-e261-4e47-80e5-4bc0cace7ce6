import {
  EventGroupIdN<PERSON>ber,
  GenderType,
  HtmlString,
  IBaseConcrete,
  IBaseRaw,
} from "../../../common/common-models";
import { AO_CODE } from "../../../common/ui/athletic-org/athletic-org-models";
import { IAutoEntries } from "../../../builder/builder-models";
import { IEventSeed } from "../../../athleteCompSched/athletecompsched-models";
import { E4sResultImportSeqEventKey } from "../../results-import/results-import-models";
import { ScoreboardTrackResultV3 } from "../scoreboard-output/display/v3/track/scoreboard-track-models-v3";
import { ISocketMetaV3 } from "../scoreboard-output/display/v3/useScoreboardOutputDisplayV3";
import { IPerfInfoBase } from "../../../athleteCompSched/pb/v3/edit-pb-v3-models";

export interface IR4sCompSchedule {
  compId: number;
  name: string;
  date: string;
  location: string;
  club: string;
  logo: string;
  org: IBaseConcrete;
  schedule: IR4sScheduledEvent[];
  autoEntries: IAutoEntries;
}

export type R4SEventType = "T" | "D" | "H"; //  Track (Time?), Distance, Height
export type R4SWindType = "" | "E" | "A";

//  TODO this is very similar to IResultEvent
export interface IR4sScheduledEvent extends IBaseRaw {
  eventNo: number;
  name: string;
  eventName: string;
  type: R4SEventType;
  typeNo: string;
  isTeamEvent: boolean;
  startDate: string;
  counts: {
    entries: number;
    maxAthletes: number;
    maxInHeat: number;
  };
  wind: R4SWindType; //  blank = don't show, E = "event", A = athlete
  results: boolean; //  do nay results exist for this event.
  waiting: boolean;
  seed: IEventSeed;
  entries?: IR4sAthleteEntry[];
  teamEntries?: IR4sTeamEntry[];
  resultsPossible: boolean;
}

export interface IR4sAccountUser {
  id: number;
  name: string;
  email: string;
  login?: string;
}

export interface IR4sBaseEntry {
  entryId: number;
  ageGroup: IR4sAthleteEntryAgeGroup;
  gender: GenderType;
  entryOptions: {
    autoEntries: IAutoEntryOptions;
    resultsKey?: E4sResultImportSeqEventKey;
    allowAreas?: boolean;
    otd?: boolean;
    forceBasket?: boolean;
    trackPB?: boolean;
    checkIn?: unknown;
  };
  user?: IR4sAccountUser;
}

export interface IR4sAthleteEntry extends IR4sBaseEntry {
  athleteId: number;
  firstname: string;
  surname: string;
  clubname: string;
  club: IBaseConcrete;
  urn: string | number | null;
  qualify?: "Q" | "q" | ""; //  Q = qualify, q = slowest qualify
  paid: boolean | 1 | 0;
  egId: EventGroupIdNumber;
  perf: IPerfInfoBase;
  editAccess: boolean;
}

export interface IR4sTeamEntryAthlete extends IBaseConcrete {
  pos: number;
}

export interface IR4sTeamEntryEntity {
  id: number;
  entityName: string;
  teamEntity: string;
}

export interface IR4sTeamEntry extends IR4sBaseEntry {
  teamName: string;
  entity: IR4sTeamEntryEntity;
  athletes: IR4sTeamEntryAthlete[];
}

export interface IAutoEntryOptions {
  targetEntry: {
    id: number; //  Entry Id in the target comp.
    paid: 0 | 1 | 2; //  0 = not paid, 1 = paid, 2 = cheque (awaiting payment)
    // user: {
    //     id: number;
    //     name: string;
    //     email: string
    // };
    orderId: number;
  };
  targetEventGroup: {
    id: number;
    name: string;
    ceId?: number;
  };
}

export interface IR4sAthleteEntryAgeGroup {
  name: string;
  shortName: string;
  currentAge: number;
  competitionAge: number;
}

export interface IRs4ScoreboardOutputOptions {
  requestScreen: boolean; //  Default false, if true, only listens for messages specific to that id.
  // type: R4S_DESIGN_TYPE;
  timings: {
    ranking: {
      pageSize: number;
      pageCycleMs: number;
    };
    scoreboard: {
      displayForMs: number;
    };
    photoFinish: {
      cycle: boolean; //  If false, only show latest
      displayForMs: number;
      maxCount: number; //  Max number to show
    };
    socketMetaMap: Partial<Record<SocketActionData, ISocketMetaV3>>;
  };
  ui: IRs4ScoreboardOutputOptionsUi;
  listenForOtherComps: string; //  Comma separated list of compIds to listen for.
  listenForOtherDomains: string; //  Comma separated list of compIds to listen for.
  listenForEventGroupIds: string; //  Comma separated list of compIds to listen for.
  inDebugMode: boolean;
}

export interface IRs4ScoreboardOutputOptionsUi {
  body: {
    main: string;
    all: string;
  };
  header: {
    body: string;
    defaultTitle: string;
    title: string;
    rightContent: string;
  };
  displayClock: {
    body: string;
    time: string;
  };
  confirmHeat: {
    header: string;
    row: string;
    paging: string;
  };
  photoFinish: {
    header: string;
    row: string;
    paging: string;
    athleteTableCell: string;
    image: string;
  };
  message: {
    body: string;
  };
}

export interface IRs4ScoreboardOutput extends IBaseRaw {
  compid: number;
  output: number;
  description: string; //  E.g. "Main Scoreboard", "By Long Jump Pit", etc.
  options: IRs4ScoreboardOutputOptions;
}

export interface IRs4ScoreboardComp {
  Date: string;
  ID: number;
  Name: string;
}

export interface IRs4ScoreboardOutputList {
  comp: IRs4ScoreboardComp;
  outputs: IRs4ScoreboardOutput[];
}

//    1 = Scoreboard, 2 = Ranking, 3 = Scoreboard + Ranking, 4 = Picture.
export type R4S_DESIGN_TYPE = 1 | 2 | 3 | 4;

//  TODO set upu types for 1, 2, etc.
export interface IRs4ScoreboardDesign extends IBaseRaw {
  design: R4S_DESIGN_TYPE; //    1 = Scoreboard, 2 = Ranking, 3 = Scoreboard + Ranking, 4 = Picture.
  description: string;
  // maxevents: number;
}

export interface IRs4ScoreboardEvent extends IBaseRaw {
  compid: number;
  output: number;
  description: string;
}

export interface IRs4EventGroup extends IBaseConcrete {
  compId: number;
  eventNo: number;
  startdate: string; //  TODO Not Iso... 2021-03-01 09:00:00+00:00
  startdateiso: string; //  TODO 2021-03-01T09:00:00
  typeNo: string; //  E.g. T4, F7  event "type" plus eventNo
}

export interface IRs4EventGroupScore extends IBaseConcrete {
  groupName: string; //  E.g. Shot put
  eventNo: number; //     ???
}

export interface IR4sAthlete {
  id: number;
  name: string;
  club: string;
  gender: "M" | "F";
  pb: number;
  sb: number;
  aocode: AO_CODE;
  urn: string;
}

export type HEAT_LABEL = "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "h7" | "h8";
// export type HEAT_LABEL = `h${number}`;
export type TRIAL_LABEL = "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "h7" | "h8";

export type HEAT_TRIAL_LABEL = HEAT_LABEL | TRIAL_LABEL;

export interface IR4sEntry {
  ageGroup: {
    ageGroup: string;
    ageGroupID: number;
  };
  athlete: IR4sAthlete;
  entry: {
    id: number;
    bibNo: number;
    checkedIn: boolean;
    collected: boolean;
    present: boolean;
  };
  eventGroup: {
    id: number;
    eventNo: number;
    groupName: string;
  };
  heatInfo: {
    heatNo: number;
    position: number;
    seededPosition: number;
  };
  results: {
    currentPosition: number;
    heatPosition: number;
    data: Record<HEAT_TRIAL_LABEL, number>;
    highScore: number;
    isPb: boolean;
    isSb: boolean;
    scoreText: string;
    resultInSeconds?: number; //  Track only
    trial: number;
  };
}

export interface IRs4Score {
  athleteId: number;
  athlete: IR4sAthlete;
  eventGroup: IRs4EventGroupScore;
  nextAthleteId: number;
  score: number | "x" | "-" | "r"; //  x = failed, - = passed, r = retired.
  scoreText: string; //  E.g. (pb), (sb)...
  trial: number;
}

export interface IR4sSocketPayload {
  competition: {
    id: number;
    logo: string;
    name: string;
  };
  entries: Record<string, IR4sEntry>;
  score: IRs4Score;
}

export type SocketActionMetaConfig =
  | "photofinish"
  | "confirmHeat"
  | "field-results"
  | "video";

export type SocketActionData =
  | SocketActionMetaConfig
  | "athlete-result"
  | "height-result"
  | "photofinish-request"
  | "message";

export type SocketAction =
  | "output-schedule--reload"
  | "output-schedule--refresh"
  | "output-schedule--reset"
  | "output-schedule--show-schedule"
  | "output-schedule--show-ranking"
  | "output-schedule--message-start"
  | "output-schedule--message-stop"
  | "output-schedule--message-relay"
  | "output-schedule--message-cancel";

export type R4S_SOCKET_ACTIONS = SocketAction | SocketActionData;

export interface IR4SSocketMessageBase {
  action: "sendmessage";
  data: IR4SSocketMessageBaseData;
}

export interface IR4sSocketDataMessage extends IR4SSocketMessageBase {
  data: IR4sSocketDataMessageData;
}

export interface IR4SSocketMessageBaseData {
  action: R4S_SOCKET_ACTIONS;
}

export interface IR4sSocketDataMessageData extends IR4SSocketMessageBaseData {
  key: string; //  E.g. "6-2"   , event no 6, heat 2.
  comp: {
    id: number;
    name: string;
    organiser: string;
    date: string;
  };
  action: R4S_SOCKET_ACTIONS;
  type?: "" | "request"; //  if request, only show that.
  outputNo: number; //  Future...only for screen #x
  deviceKey: string;
  securityKey: string;
  domain: string;
  payload: IR4sSocketPayload;
}

export interface IR4sSocketPhotoFinishMessage
  extends IR4SSocketMessageBaseData {
  action: "photofinish";
  comp: {
    id: number;
  };
  deviceKey: string;
  securityKey: string;
  payload: IR4sSocketPhotoFinishPayload;
}

export interface IR4sSocketVideoMessage {
  action: "video";
  comp: {
    id: number;
  };
  deviceKey: string;
  securityKey: string;
  payload: IR4sSocketVideoPayload;
}

export interface IR4sSocketAthleteFieldResults
  extends IR4SSocketMessageBaseData {
  action: "field-results";
  comp: {
    id: number;
  };
  key: string;
  outputNo: number;
  deviceKey: string;
  securityKey: string;
  domain: string;
  payload: {
    ranking: ScoreboardTrackResultV3;
  };
}

export interface IR4sSocketEventGroup extends IBaseConcrete {
  eventNo: number;
  typeNo: string;
  heatNo: number;
}

export interface IR4sSocketPhotoFinishPayload {
  comp: {
    id: number;
  };
  domain: string;
  picture: string;
  eventNo: number;
  typeNo: string;
  eventName: string;
  heatNo: number;
  eventGroup: IR4sSocketEventGroup;
  results: IR4sPhotoFinishResult[];
}

export interface IR4sSocketVideoPayload
  extends Omit<IR4sSocketPhotoFinishPayload, "picture"> {
  video: string;
}

//  "{"bibNo":327,"athlete":"Rufus Jones (Rugb)","time":"11/04/2021 - 10:15:46
// ↵","ws":"-0.9 m/s","lane":1,"place":3,"ageGroup":"U11,2011,
// ↵"}"
export interface IR4sPhotoFinishResult extends IBaseRaw {
  athleteid: number;
  compid: number;
  eventno: number;
  resultkey: string;
  resultvalue: string;
  timeInSeconds: number;
  options: {
    bibNo: number;
    athlete: string; //  Rufus Jones (Rugb)
    club: string;
    eventTime: string; //  11/04/2021 - 10:15:46
    time: string; //  athlete's "score"
    ws: string; //  -0.9 m/s
    lane: number;
    place: number;
    ageGroup: string; //  U11,2011
    scoreText: string; //  E.g. PB, etc.
    //  ...etc.
  };
}

export interface IRs4SocketMessageOutput extends IR4SSocketMessageBase {
  data: {
    compId: number;
    outputId: number;
    action:
      | "output-schedule--reload"
      | "output-schedule--refresh"
      | "output-schedule--show-schedule"
      | "output-schedule--show-ranking";
  };
}

//  NEW objects

export interface IR4sSocketRootMessage {
  action: "sendmessage";
  data: IR4sSocketRootMessageData;
}

export interface IR4sSocketRootMessageData {
  action: R4S_SOCKET_ACTIONS;
  deviceKey: string;
  securityKey: string;
  key: number;
  payload?: any;
}

//  <FIELD>
//  <FIELD>
//  <FIELD>
// export interface IR4sSocketMessageField extends IR4sSocketRootMessage {
//     data: IR4sSocketMessageDataField;
// }

export interface IR4sSocketMessageDataField extends IR4sSocketRootMessageData {
  comp: {
    id: string;
    name: string;
    organiser: string;
    date: string;
    location: string;
    options: {};
  };
  payload: IR4sPayloadField;
}

export interface IR4sPayloadField {
  competition: {
    id: number;
    logo: string;
    name: string;
  };
  entries: Record<string, IR4sEntry>;
  score: IRs4Score;
}

export interface IR4sSocketMessageDataTrack extends IR4sSocketRootMessageData {
  comp: {
    id: string;
  };
  payload: IR4sPayloadTrack;
}

export interface IR4sPayloadTrack {
  competition: {
    id: number;
  };
  domain: string;
  eventName: string;
  eventNo: number;
  heatNo: number;
  scheduleTime: string;
  resultKey: string; //  E.g. "h10"
  picture: string; //  E.g. /results/351/pf/E007h10,100m.png
  results: IR4sResultTrack[];
  waiting: boolean;
}

export interface IR4sResultTrack extends IBaseRaw {
  athleteid: number;
  compid: number;
  eventno: number;
  resultKey: string;
  resultValue: number;
  options: {
    place: number;
    lane: number;
    bibNo: number;
    athlete: string; //  E.g. Nick Wall
    club: string; //  E.g. (Rugb)
    ageGroup: string; //  E.g. 40,1978
    ws: string; //  E.g. -0.5 m/s
    eventTime: string; //  E.g. 11/04/2021 - 11:04:56
  };
}

export interface IScheduleTableRow {
  scheduledEvent: null | IR4sScheduledEvent | IR4sPayloadTrack;
  eventGroupId: number;
  eventKey: string;
  startTime: string;
  eventName: string;
  entries: number;
  heats: number;
  status: "unchecked" | "checked";
  hasResults: boolean; //  does event have results.
  resultsPossible: boolean;
  waiting: boolean;
  athleteEntries?: IR4sAthleteEntry[];
  counts: {
    entries: number;
    maxAthletes: number;
    maxInHeat: number;
  };
}

export interface IResultRow {
  bibNo: number;
  athlete: {
    id: number;
    name: string;
    urn: string;
    aocode: AO_CODE;
  };
  club: string;
  result: string;
  position: number;
  heatPosition: number;
  status: "unchecked" | "checked";
}

export interface IResultRowTrack extends IResultRow {
  heat: string;
  heatNumber: number;
  lane: number;
  needsSorting: boolean;
  hackScore: number;
}

export interface IPublicEventGroupMetaXXX {
  eventTime: string;
  heatNo: number;
  image: string;
  ws: string;
}

export interface IPublicHeatGroupMeta {
  eventTime: string;
  heatNo: number;
  image: string;
  ws: string;
}

export interface IScoreboardMessage {
  title: string;
  message: HtmlString;
}

//  </FIELD>
//  </FIELD>
//  </FIELD>
