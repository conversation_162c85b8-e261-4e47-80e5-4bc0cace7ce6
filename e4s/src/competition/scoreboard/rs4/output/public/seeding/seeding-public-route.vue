<template>
    <div>
        <div v-if="isLoading" class="row">
            <div class="col s12 m12 l12">
                Loading...<LoadingSpinner></LoadingSpinner>
            </div>
        </div>
        <SeedingPublic
            :r4s-comp-schedule="r4sCompSchedule"
            v-if="r4sCompSchedule.compId > 0"
        ></SeedingPublic>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import {handleResponseMessages} from "../../../../../../common/handle-http-reponse"
import {ScoreboardData} from "../../../../scoreboard-data"
import {IR4sCompSchedule} from "../../../rs4-scoreboard-models"
import {Rs4Service} from "../../../rs4-service"
import SeedingPublic from "./seeding-public.vue"


@Component({
    name: "seeding-public-route",
    components: {SeedingPublic}
})
export default class SeedingPublicRoute extends Vue {
    public compId: number = 0
    public isLoading: boolean = false;

    public scoreboardData: ScoreboardData = new ScoreboardData();
    public rs4Service: Rs4Service = new Rs4Service();

    public r4sCompSchedule: IR4sCompSchedule = this.rs4Service.factoryR4sCompSchedule();

    public created() {
        this.compId = isNaN(Number(this.$route.params.compId)) ? 0 : parseInt(this.$route.params.compId, 0);

        if (this.compId > 0) {
            this.getData();
        }
    }

    public getData() {
        this.isLoading = true;

        const prom = this.scoreboardData.getCompSchedule(this.compId);
        handleResponseMessages(prom);
        return prom
            .then((resp) => {
                if (resp.errNo === 0) {
                    this.r4sCompSchedule = resp.data;
                }
                return;
            })
            .finally(() => {
                this.isLoading = false;
            })

    }
}
</script>
