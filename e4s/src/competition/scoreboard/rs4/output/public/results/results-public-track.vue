<template>
  <div class="e4s-flex-column e4s-full-width e4s-gap--large">
    >
    <div v-show="showSection === sections.RESULTS">
      <!--      <div class="r4s-public-results&#45;&#45;event-name">-->
      <!--        <div class="row">-->
      <!--          <div class="col s12 m12 l12">-->
      <!--            <span v-text="getEventTitle"></span>-->
      <!--            -->
      <!--            <a href="#" v-on:click.prevent="close" class="right">Close</a>-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </div>-->

      <div class="e4s-flex-row e4s-justify-flex-space-between">
        <span v-text="getEventTitle" class="e4s-header--400"></span>
        <ButtonGenericBackV2 v-on:click="close" />
      </div>

      <div class="row">
        <div class="col s12 m12 l12">
          <div class="left">
            <!--                        <slot name="close"></slot>-->
            <a
              href="#"
              v-on:click.prevent="close"
              class="r4s-ranking--heat-link"
              >Schedule</a
            >
            <a
              href="#"
              v-for="heat in heats"
              :key="heat"
              class="r4s-ranking--heat-link"
              :class="
                isHeatSelected(heat) ? 'r4s-ranking--heat-link-selected' : ''
              "
              v-text="heat"
              v-on:click.prevent="changeHeat(heat)"
            ></a>

            <a
              href="#"
              v-on:click.prevent="showImage"
              v-if="getImagePath.length > 0"
            >
              <i class="material-icons" style="vertical-align: middle"
                >camera_alt</i
              >
            </a>
          </div>
          <!--                    <div class="right">-->
          <!--                        <slot name="close"></slot>-->
          <!--                    </div>-->
        </div>
      </div>

      <table class="r4s-public-results--table">
        <tr class="r4s-public--table-header">
          <th class="r4s-public-results--table-pos">Pos#</th>
          <th class="r4s-public-results--table-bib">Bib</th>
          <th class="r4s-public-results--table-th-ath">Athlete</th>
          <th class="r4s-public-results--table-th-club">Club</th>
          <th class="r4s-public-results--table-th-sco">Result</th>
        </tr>

        <tr
          v-for="(result, index) in resultsToDisplay"
          :key="result.athlete.id"
        >
          <td class="r4s-public-results--table-pos">
            <span v-text="index + 1"></span>
          </td>
          <td class="r4s-public-results--table-bib">
            <span v-text="result.bibNo"></span>
          </td>
          <td class="r4s-public-results--table-th-ath">
            <span v-text="trimColumnText(result.athlete.name)"></span>
          </td>
          <td class="r4s-public-results--table-th-club">
            <span v-text="trimColumnText(result.club)"></span>
          </td>
          <td class="r4s-public-results--table-td-sco">
            <PowerOfTenLinkV2
              :urn="result.athlete.urn"
              v-if="
                result.athlete.urn &&
                result.athlete.urn.toString().length > 0 &&
                result.athlete.aocode === 'EA'
              "
            />
            <!--                        <a v-if="result.athlete.urn && result.athlete.urn.toString().length > 0"-->
            <!--                           :href="'https://www.thepowerof10.info/athletes/profile.aspx?ukaurn=' + result.athlete.urn"-->
            <!--                           target="e4s-po10"-->
            <!--                           class="po10-link">-->
            <!--                            <img :src="require('../../../../../../images/po10.ico')"/>-->
            <!--                        </a>-->
            <span v-text="result.result"></span>
          </td>
        </tr>
      </table>
    </div>

    <div v-show="showSection === sections.IMAGE">
      <a href="#" v-on:click.prevent="showSection = sections.RESULTS">Close</a>
      <img class="r4s-public-results--photo" :src="getImagePath" />
    </div>
  </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import {
  IPublicHeatGroupMeta,
  IResultRowTrack,
} from "../../../rs4-scoreboard-models";
import { CONFIG } from "../../../../../../common/config";
import { format, parse } from "date-fns";
import { Rs4Service } from "../../../rs4-service";
import { CommonService } from "../../../../../../common/common-service";
import PowerOfTenLinkV2 from "../../../../../../common/ui/layoutV2/PowerOfTenLinkV2.vue";

@Component({
  name: "results-public-track",
  components: { PowerOfTenLinkV2 },
})
export default class ResultsPublicTrack extends Vue {
  @Prop({
    default: () => {
      return {};
    },
  })
  public readonly publicHeatGroupMeta!: Record<number, IPublicHeatGroupMeta>;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly results!: IResultRowTrack[];

  @Prop({
    default: "",
  })
  public readonly eventName!: string;

  public heats: string[] = [];
  public heatToDisplay: string = "";
  public resultsToDisplay: IResultRowTrack[] = [];
  public rs4Service: Rs4Service = new Rs4Service();
  public commonService: CommonService = new CommonService();

  public sections = {
    RESULTS: "RESULTS",
    IMAGE: "IMAGE",
  };
  public showSection: string = this.sections.RESULTS;

  public created() {
    const heats = this.getHeats();
    this.heatToDisplay = heats[0];
    this.heats = heats;
    this.setDisplayResults();
  }

  public getHeats(): string[] {
    return this.results
      .reduce((accum, result) => {
        if (accum.indexOf(result.heatNumber) === -1) {
          accum.push(result.heatNumber);
        }
        return accum;
      }, [] as number[])
      .sort((a, b) => {
        return a - b;
      })
      .map((heatNo) => "Heat " + heatNo);
  }

  public setDisplayResults(): void {
    let resultsToDisplay = this.results.filter((result) => {
      return result.heat === this.heatToDisplay;
    });
    //  hack
    const needsSorting = resultsToDisplay.reduce((accum, result) => {
      if (result.needsSorting) {
        accum = true;
      }
      return accum;
    }, false as boolean);

    if (needsSorting) {
      resultsToDisplay = this.commonService.sortArray(
        "hackScore",
        resultsToDisplay,
        "ASC"
      );
    }
    this.resultsToDisplay = resultsToDisplay;
  }

  public isHeatSelected(heat: string): boolean {
    return heat === this.heatToDisplay;
  }

  public changeHeat(heat: string) {
    this.heatToDisplay = heat;
    this.setDisplayResults();
  }

  public trimColumnText(content: string): string {
    if (content.length > 30) {
      return content.slice(0, 31) + "...";
    }
    return content;
  }

  public showImage() {
    this.showSection = this.sections.IMAGE;
  }

  public getHeatDataForHeat(heat: string): IPublicHeatGroupMeta {
    const heatToDisplay: number = Number(
      this.heatToDisplay.replace("Heat ", "")
    );
    return this.publicHeatGroupMeta[heatToDisplay];
  }

  public get getHeatTime() {
    const heatToDisplay = this.getHeatDataForHeat(this.heatToDisplay);
    if (heatToDisplay && heatToDisplay.eventTime) {
      return (
        "Time: " +
        format(parse(heatToDisplay.eventTime.replace(" ", "T")), "HH:mm")
      );
    }
    return "";
  }

  public get getWindSpeed() {
    const heatToDisplay = this.getHeatDataForHeat(this.heatToDisplay);
    if (heatToDisplay && heatToDisplay.ws && heatToDisplay.ws.length > 0) {
      return heatToDisplay.ws;
    }
    return "NA";
  }

  public get getImagePath(): string {
    const heatToDisplay = this.getHeatDataForHeat(this.heatToDisplay);
    if (
      heatToDisplay &&
      heatToDisplay.image &&
      heatToDisplay.image.length > 0
    ) {
      return CONFIG.E4S_HOST + heatToDisplay.image;
    }
    return "";
  }

  public get getEventTitle() {
    const heatToDisplay = this.getHeatDataForHeat(this.heatToDisplay);
    if (heatToDisplay && heatToDisplay.eventTime) {
      return (
        this.eventName +
        " : " +
        this.heatToDisplay +
        " at " +
        format(parse(heatToDisplay.eventTime.replace(" ", "T")), "HH:mma") +
        (heatToDisplay.ws && heatToDisplay.ws.length > 0
          ? " : Wind: " + heatToDisplay.ws
          : "")
      );
    }
    return this.eventName;
  }

  // public getTrackScore() {
  //     return
  // }

  public close() {
    this.$emit("close");
  }
}
</script>

<style>
.r4s-ranking--heat-link {
  padding: 0 0.5em;
  border-right: 1px solid #c5c5c5;
}

.r4s-public-results--event-name {
  margin: 0.5em 0;
  font-size: 1.25em;
}

.r4s-ranking--heat-link-selected {
  color: black;
}

.r4s-ranking--table {
  font-size: 1.4em;
}

.r4s-public-results--table-pos {
  text-align: center;
}

.r4s-public-results--photo {
  width: 100vw;
  height: 80vh;
}

.r4s-ranking--table-pos {
  width: 10vw;
  text-align: center;
}

.r4s-ranking--table-bib {
  width: 10vw;
}

.r4s-ranking--table-th-ath {
  width: 35vw;
}

.r4s-ranking--table-th-club {
  width: 35vw;
}

.r4s-ranking--table-td-sco {
  width: 10vw;
}

.r4s-ranking--page-count {
  float: right;
  margin-right: 1em;
  font-size: 1.25em;
  font-weight: 500;
}
</style>
