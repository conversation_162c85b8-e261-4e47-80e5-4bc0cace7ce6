<template>
    <div>
        <div class="row r4s-schedule-public--header">
            <div class="col s12 m12 l12">
                <div class="left">
                    <div class="r4s-schedule-public--comp-name" v-text="r4sCompSchedule.name"></div>

                    <div class="r4s-schedule-public--comp-date-section">
                        <div class="r4s-schedule-public--comp-date"
                             v-if="scheduleDates.length === 1"
                        >
                            <span v-text="getDate"></span>
                        </div>

                        <span
                            v-if="scheduleDates.length > 1"
                            v-for="startDate in scheduleDates"
                            :key="startDate.iso"
                        >
                            <a
                                class="r4s-schedule-public--comp-date-link"
                                :class="isDateSelected(startDate) ? 'r4s-schedule-public--comp-date-link-selected' : ''"
                                href="#"
                                v-on:click.prevent="selectedScheduleByDate(startDate)">
                                <span v-text="startDate.display"></span>
                            </a>
                        </span>
                    </div>


                </div>

                <div class="right">
                    <img :src="getLogoPath" class="r4s-schedule-public--logo"/>
                </div>
            </div>
        </div>

<!--      <div>-->
<!--        <div class="row">-->
<!--          <div class="col s12 m12 l12">-->
<!--          </div>-->
<!--          <br/>-->
<!--          <p><strong>We are updating the Results page, back up in the morning.</strong></p>-->
<!--          </div>-->
<!--      </div>-->


        <div v-show="showSection === sections.SCHEDULE">

            <div class="r4s-schedule-public--schedule-row-head">
                <div class="row">
                    <div class="col s2 m2 l2">
                        <span class="r4s-schedule-public--schedule-row-head-label">Time</span>
                    </div>
                    <div class="col s8 m8 l8">
                        <span class="r4s-schedule-public--schedule-row-head-label">Event Name</span>
                    </div>
                    <div class="col s2 m2 l2">
                        <div class="r4s-schedule-public--schedule-row-head-label center">Entries</div>
                    </div>
                </div>
            </div>


            <div v-for="(scheduleTableRow, index) in scheduleTableRows">
                <div class="row r4s-schedule-public--schedule-row" :class="index % 2 === 0 ? '' : 'r4s-schedule-public--schedule-row-odd'">
                    <div class="col s2 m2 l2">
                        <span :title="scheduleTableRow.startTime" v-text="getEventTimeForRow(scheduleTableRow)"></span>
                        <a
                          v-if="isAdmin"
                          :href="'#/results-entry/' + r4sCompSchedule.compId + '/' + scheduleTableRow.eventGroupId"
                        >Edit</a>
                    </div>
                    <div class="col s8 m8 l8">
                        <a href="#">
                            <span v-text="scheduleTableRow.eventName" v-on:click.prevent="displayRankings(scheduleTableRow.eventKey)"></span>
                        </a>
                    </div>
                    <div class="col s2 m2 l2">
                        <div v-text="scheduleTableRow.entries" class="center"></div>
                    </div>
                </div>
            </div>

        </div>


        <div v-if="showSection === sections.RANKING">
            <div class="row" v-if="isLoadingLatestScore || showTheseEntries.length === 0">
                <div class="col s12 m12 l12">
                    <div class="left">
                        <div v-if="isLoadingLatestScore"><LoadingSpinner></LoadingSpinner></div>
                        <div v-if="!isLoadingLatestScore && showTheseEntries.length === 0">
                            <a href="#" v-on:click.prevent="showSection = sections.SCHEDULE">Schedule</a> Nothing available.
                        </div>
                    </div>
                </div>
            </div>
<!--            <div class="r4s-schedule-public&#45;&#45;ranking-header">-->
<!--                <div class="row">-->
<!--                    <div class="col s12 m12 l12">-->
<!--                        <div class="left">-->
<!--                            <div v-if="isLoadingLatestScore"><LoadingSpinner></LoadingSpinner></div>-->
<!--                            <div v-if="!isLoadingLatestScore && showTheseEntries.length === 0">-->
<!--                                Nothing available.-->
<!--                            </div>-->
<!--                            <div v-if="showTheseEntries.length > 0">-->
<!--                                <span v-text="showTheseEntries[0].eventGroup.groupName"></span>-->
<!--&lt;!&ndash;                                <span v-if="showThisMeta.ws.length > 0">&ndash;&gt;-->
<!--&lt;!&ndash;                                    : Windspeed: <span v-text="showThisMeta.ws"></span>&ndash;&gt;-->
<!--&lt;!&ndash;                                </span>&ndash;&gt;-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="right">-->
<!--                            <a href="#"-->
<!--                               v-on:click.prevent="showSection = sections.SCHEDULE">-->
<!--                                Schedule-->
<!--                            </a>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->


            <div v-if="showTheseResultRows.length > 0">
                <ResultsPublicField
                    v-if="showTheseResultRowsType !== 'T'"
                    :event-name="showTheseEntries[0].eventGroup.groupName"
                    :public-heat-group-meta="showThisMeta"
                    :results="showTheseResultRows"
                    v-on:close="showSection = sections.SCHEDULE"
                >
<!--                    <div slot="close">-->
<!--                        <div class="col s12 m12 l12">-->
<!--                            <a href="#" v-on:click.prevent="showSection = sections.SCHEDULE">Schedule</a>-->
<!--                        </div>-->
<!--                    </div>-->
                </ResultsPublicField>
                <ResultsPublicTrack
                    v-if="showTheseResultRowsType === 'T'"
                    :event-name="showTheseEntries[0].eventGroup.groupName"
                    :public-heat-group-meta="showThisMeta"
                    :results="showTheseResultRows"
                    v-on:close="showSection = sections.SCHEDULE"
                >
<!--                    <template slot="close">-->
<!--                        <a href="#" v-on:click.prevent="showSection = sections.SCHEDULE">Schedule</a>-->
<!--                    </template>-->
                </ResultsPublicTrack>
            </div>

        </div>

    </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import {Prop, Watch} from "vue-property-decorator"
import {
    IPublicHeatGroupMeta,
    IR4sCompSchedule,
    IR4sEntry, IR4sPayloadTrack,
    IR4sScheduledEvent, IR4sSocketMessageDataField, IR4sSocketMessageDataTrack,
    IR4sSocketRootMessage, IR4sSocketRootMessageData, IResultRow, IScheduleTableRow, R4SEventType
} from "../../../rs4-scoreboard-models"
import {Rs4Service} from "../../../rs4-service"
import {CONFIG} from "../../../../../../common/config"
import {format, parse} from "date-fns"
import {CommonService} from "../../../../../../common/common-service"
import {handleResponseMessages} from "../../../../../../common/handle-http-reponse"
import {ScoreboardData} from "../../../../scoreboard-data"
import RankingsPublic from "../rankings/rankings-public.vue"
import * as R from "ramda"
import {E4SSocketProxy, getSocketInstance} from "../../../../../../socket/socket-controller"
import ResultsPublicField from "../results/results-public-field.vue"
import ResultsPublicTrack from "../results/results-public-track.vue"
import {ISimpleDateModel} from "../../../../../../common/common-models"
import {mapGetters} from "vuex";
import {CONFIG_STORE_CONST} from "../../../../../../config/config-store";

const rs4Service: Rs4Service = new Rs4Service();

@Component({
    name: "schedule-public",
    components: {ResultsPublicTrack, ResultsPublicField, RankingsPublic},
    computed: {
        ...mapGetters(
          {
              isAdmin: CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME + "/" + CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN
          }
        )
    }
})
export default class SchedulePublic extends Vue {
    public readonly isAdmin!: boolean;

    @Prop({
        default: () => {
            return rs4Service.factoryR4sCompSchedule();
        }
    })
    public readonly r4sCompSchedule!: IR4sCompSchedule;

    public rs4Service: Rs4Service = rs4Service;
    public commonService: CommonService = new CommonService();
    public scoreboardData: ScoreboardData = new ScoreboardData();

    public sections = {
        SCHEDULE: "SCHEDULE",
        RANKING: "RANKING"
    };
    public showSection: string = this.sections.SCHEDULE;


    public scheduleDates: ISimpleDateModel[] = [];
    public scheduleDateDisplay: ISimpleDateModel = {
        iso: "",
        display: ""
    };
    // public scheduledEventsToDisplay: IR4sScheduledEvent[] = [];

    public scheduleTableRows: IScheduleTableRow[] = [];

    public eventGroupIdKeyEntries: Record<string, IR4sEntry[]> = {}
    public eventGroupIdKeyMeta: Record<string, Record<number, IPublicHeatGroupMeta>> = {}
    public showTheseEntries: IR4sEntry[] = [];

    public showThisMeta: Record<number, IPublicHeatGroupMeta> = this.rs4Service.factoryPublicEventGroupMeta();
    public showTheseResultRows: IResultRow[] = [];
    public showTheseResultRowsType: R4SEventType | "" = "";

    public trackPayloads: Record<string, IR4sPayloadTrack> = {};

    public e4SSocketProxy: E4SSocketProxy | null = null;

    public isLoadingLatestScore = false;

    public created() {
        this.e4SSocketProxy = getSocketInstance();
        this.e4SSocketProxy.e4sSocket.addEventListener("message", this.socketReceiveMessage);

        this.scheduleDates = this.getCompResultDates();
        this.scheduleDateDisplay = this.scheduleDates[0];

        this.createScheduleTableRows();
    }

    @Watch("r4sCompSchedule")
    public onR4sCompScheduleChanged() {
        this.createScheduleTableRows();
    }

    public socketReceiveMessage(message: IR4sSocketRootMessage) {
        console.log("ScoreboardOutputDisplay addEventListener Output got message....");

        //  @ts-ignore
        // const socketRootMessage: IR4sSocketRootMessage = JSON.parse((message.data)) as any as IR4sSocketRootMessage;
        // let messageData: IR4sSocketRootMessageData;
        //
        // if (socketRootMessage.data.action === "athlete-result") {
        //
        //     messageData = socketRootMessage.data as IR4sSocketMessageDataField;
        //     if (typeof messageData.payload === "string") {
        //         messageData.payload = JSON.parse(messageData.payload as unknown as string);
        //     }
        //     this.processMessage(messageData);
        //     return;
        // }
        // if (socketRootMessage.data.action === "photofinish") {
        //
        //     messageData = socketRootMessage.data as IR4sSocketMessageDataTrack;
        //     const payloadTrack = messageData.payload as IR4sPayloadTrack;
        //     payloadTrack.results = payloadTrack.results.map( (result) => {
        //         if (typeof result.options === "string") {
        //             result.options = JSON.parse((result.options as string).replace(/[\n\r]+/g, ''));
        //         }
        //         return result;
        //     })
        //     this.processMessage(messageData);
        //
        //     return;
        // }
    }

    public selectedScheduleByDate(startDate: ISimpleDateModel) {
        this.scheduleDateDisplay = startDate;
        this.showSection = this.sections.SCHEDULE;
        this.createScheduleTableRows();
    }

    public getCompResultDates(): ISimpleDateModel[] {

        return this.commonService
            .sortArray("iso", this.commonService
                .uniqueArrayById(
                    this.r4sCompSchedule.schedule.map( (schedule) => {
                        return {
                            iso: schedule.startDate.split("T")[0],
                            display: format(parse(schedule.startDate), "Do MMM")
                        }
                    }),
                    "iso"
                )
            );
    }

    public isDateSelected(simpleDateModel: ISimpleDateModel): boolean {
        return simpleDateModel.iso === this.scheduleDateDisplay.iso;
    }

    public sortEvents(r4sScheduledEvents: IR4sScheduledEvent[]): IR4sScheduledEvent[] {
        return this.commonService.sortArray("startDate", r4sScheduledEvents);
    }

    public createScheduleTableRows(): void {
        const scheduleRows: IScheduleTableRow[] = this.r4sCompSchedule.schedule.map( (scheduledEvent) => {
            return this.rs4Service.mapScheduleEventToScheduleTableRow(scheduledEvent);
        });

        const trackRows: IScheduleTableRow[] = Object.keys(this.trackPayloads).map( (key) => {
            return this.rs4Service.mapTrackToScheduleTableRow(this.trackPayloads[key]);
        })

        this.scheduleTableRows = this.commonService
            .sortArray("startTime",
                [...scheduleRows, ...trackRows]
                .filter( (scheduleTableRow) => {
                    return scheduleTableRow.startTime.split("T")[0] === this.scheduleDateDisplay.iso;
                })
            );
    }

    public get getLogoPath() {
        return CONFIG.E4S_HOST + this.r4sCompSchedule.logo;
    }

    public get getDate() {
        return format(parse(this.r4sCompSchedule.date), "Do MMM YYYY");
    }

    public getEventTime(r4sScheduledEvent: IR4sScheduledEvent) {
        return format(parse(r4sScheduledEvent.startDate), "HH:mm");
    }

    public getEventTimeForRow(scheduleTableRow: IScheduleTableRow) {
        return format(parse(scheduleTableRow.startTime), "HH:mm");
    }

    public getLatestScoreData(compId: number, eventGroupIds: number[]): Promise<boolean> {
        if (compId === 0 || eventGroupIds.length === 0) {
            return Promise.resolve(false);
        }
        this.isLoadingLatestScore = true;

        const prom = this.scoreboardData.getLatestScores(compId, eventGroupIds);
        handleResponseMessages(prom);
        return prom
            .then((resp) => {
                if (resp.errNo > 0) {
                    return false;
                }
                this.addEntriesByEventIds(resp.data);
                if (resp.meta) {
                    this.addMetaByEventIds(resp.meta);
                }
                return true;
            })
            .finally(() => {
                this.isLoadingLatestScore = false;
            })
    }

    public addEntriesByAthlete(entries: Record<number, IR4sEntry>): void {
        if (entries && Object.keys(entries).length > 0) {
            const entriesArray = this.commonService.convertObjectToArray(entries);
            if (entriesArray.length === 0 ) {
                return;
            }
            const eventGroupId: string = entriesArray[0].eventGroup.id.toString();
            this.eventGroupIdKeyEntries[eventGroupId] = entriesArray;
        }
    }

    public addEntriesByEventIds(entriesByEventIds: Record<number, Record<number, IR4sEntry>>) {
        Object.values(entriesByEventIds).forEach( (entriesByAthleteId) => {
            this.addEntriesByAthlete(entriesByAthleteId);
        });
    }

    public addMetaByEventIds(metasByEventKeyMap: Record<number, Record<number, IPublicHeatGroupMeta>>) {
        const keys = Object.keys(metasByEventKeyMap);
        //  @ts-ignore
        keys.forEach( (eventId: number) => {
            const key = eventId.toString();
            this.eventGroupIdKeyMeta[key] = metasByEventKeyMap[eventId];
        });
    }

    public displayRankings(eventGroupId: number) {
        this.showTheseEntries = [];
        this.showTheseResultRows = [];
        //  Get from the cache.  Any "new" scores will come up the socket and get loaded to cache.
        this.showSection = this.sections.RANKING;
        // if (this.eventGroupIdKeyEntries[eventGroupId.toString()]) {
        //     this.getFromCache(eventGroupId);
        //     return;
        // }
        this.getLatestScoreData(this.r4sCompSchedule.compId, [eventGroupId])
            .then(() => {
                this.getFromCache(eventGroupId);
            })
    }

    public getFromCache(eventGroupId: number) {

        const eventType: R4SEventType = this.r4sCompSchedule.schedule.reduce( (accum, schedule) => {
            if (schedule.id.toString() === eventGroupId.toString()){
                accum = schedule.type;
            }
            return accum;
        }, "T" as R4SEventType);

        // const includeZeroAndDnsDnf = true;
        // const r4sEntries = this.rs4Service.sortAndFilterEntries(
        //     R.clone(this.eventGroupIdKeyEntries[eventGroupId.toString()]),
        //     eventType,
        //     includeZeroAndDnsDnf
        // );
        const r4sEntries = this.rs4Service.sortAndFilterResultEntries(
            R.clone(this.eventGroupIdKeyEntries[eventGroupId.toString()]),
            eventType
        );
        this.showTheseEntries = r4sEntries;

        if (eventType === "T") {
            this.showTheseResultRows = this.rs4Service.mapR4sEntriesToResultRowTracks(r4sEntries);
            // this.showTheseResultRows = r4sEntries.map( (entry) => {
            //     return this.rs4Service.mapR4sEntryToResultRowTrack(entry);
            // });
        } else {
            this.showTheseResultRows = r4sEntries.map( (entry) => {
                return this.rs4Service.mapR4sEntryToIResultRow(entry);
            });
        }
        this.showThisMeta = this.rs4Service.factoryPublicEventGroupMeta();
        if (this.eventGroupIdKeyMeta[eventGroupId.toString()]) {
            this.showThisMeta = R.clone(this.eventGroupIdKeyMeta[eventGroupId]);
        }
        this.showTheseResultRowsType = eventType;
    }

    public addPayloadTrack(payloadTrack: IR4sPayloadTrack) {
        const key = this.rs4Service.getPayloadTrackKey(payloadTrack);
        const trackPayloads = R.clone(this.trackPayloads);
        trackPayloads[key] = payloadTrack;
        this.trackPayloads = trackPayloads;

        //  actually a hack as PhotoFinish are not coming up with schedule.
        this.createScheduleTableRows();
    }

    public processMessage(socketRootMessageData: IR4sSocketRootMessageData) {

        if (socketRootMessageData.action === "athlete-result") {
            const entries = (socketRootMessageData as IR4sSocketMessageDataField).payload.entries;
            this.addEntriesByAthlete(entries)

            //  If user currently looking at event being updated.
            if (this.showTheseEntries.length > 0) {

                const entriesArray = this.commonService.convertObjectToArray(entries);
                if (entriesArray.length === 0 ) {
                    return;
                }
                const eventGroupIdShowing = this.showTheseEntries[0].eventGroup.id.toString();
                const eventGroupIdReceived = entriesArray[0].eventGroup.id.toString();

                if (eventGroupIdShowing === eventGroupIdReceived) {
                    this.getFromCache(Number(eventGroupIdReceived));
                }
            }
        }

        if (socketRootMessageData.action === "photofinish") {
            const payloadTrack: IR4sPayloadTrack = (socketRootMessageData as IR4sSocketMessageDataTrack).payload;
            this.addPayloadTrack(payloadTrack);
        }

    }

}
</script>

<style scoped>

.r4s-schedule-public--header {
    border-bottom: 1px solid gray;
    padding: 0.5em 0;
}

.r4s-schedule-public--comp-name {
    font-size: 1.5em;
}

.r4s-schedule-public--comp-date-section {
    margin-top: 1em;
}

.r4s-schedule-public--comp-date-link {
    padding: 0 0.5em;
    border-right: 1px solid #c5c5c5;
}

.r4s-schedule-public--comp-date-link-selected {
    color: black;
}

.r4s-schedule-public--logo {
    height: 4em;
}

.r4s-schedule-public--schedule-row-head {
    margin: 0.5em 0;
}

.r4s-schedule-public--schedule-row-head-label {
    font-weight: 600;
}

.r4s-schedule-public--schedule-row {
    border-top: 1px solid lightgrey;
    border-bottom: 1px solid lightgrey;
    padding: 0.5em 0;
}

.r4s-schedule-public--schedule-row-odd {
    background-color: #f7f7f6;
}

.r4s-schedule-public--ranking-header {
    margin: 0.5em 0;
    font-size: 1.25em;
}

</style>
