<template>
    <div>
        <RankingsController
            :entries-by-event-ids="entriesByEventIds"
            :entries-by-athlete-id="entriesByAthleteId"
        ></RankingsController>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import {handleResponseMessages} from "../../../../../../common/handle-http-reponse"
import {ScoreboardData} from "../../../../scoreboard-data"
import {IR4sEntry} from "../../../rs4-scoreboard-models"
import RankingsController from "../../../rankings/rankings-controller.vue"


@Component({
    name: "rankings-public-route",
    components: {RankingsController}
})
export default class RankingsPublicRoute extends Vue {
    public compId: number = 0
    public eventGroupId: number = 0
    public isLoading: boolean = false;

    public scoreboardData: ScoreboardData = new ScoreboardData();

    public entriesByEventIds: Record<number, Record<number, IR4sEntry>> = {};
    public entriesByAthleteId: Record<number, IR4sEntry> = {};

    public created() {
        this.compId = isNaN(Number(this.$route.params.compId)) ? 0 : parseInt(this.$route.params.compId, 0);
        this.eventGroupId = isNaN(Number(this.$route.params.eventGroupId)) ? 0 : parseInt(this.$route.params.eventGroupId, 0);

        if (this.compId > 0 && this.eventGroupId > 0) {
            this.getLatestScoreData();
        }
    }

    public getLatestScoreData() {
        this.isLoading = true;

        const prom = this.scoreboardData.getLatestScores(this.compId, [this.eventGroupId]);
        handleResponseMessages(prom);
        return prom
            .then((resp) => {
                if (resp.errNo === 0) {
                    this.entriesByEventIds = resp.data;
                }
                return;
            })
            .finally(() => {
                this.isLoading = false;
            })

    }
}
</script>
