<template>
    <div>
        <table class="r4s-ranking--table">

            <tr class="r4s-ranking--table-header">
                <th class="r4s-ranking--table-pos">Pos</th>
                <th class="r4s-ranking--table-bib">Bib</th>
                <th class="r4s-ranking--table-th-ath">Athlete</th>
                <th class="r4s-ranking--table-th-club">Club</th>
                <th class="r4s-ranking--table-th-sco">Score</th>
            </tr>

            <tr
                v-for="(r4sEntry, index) in pageEntriesDisplay"
                :style="rowStyle(r4sEntry, index)"
                :key="r4sEntry.athlete.id"
            >
                <td class="r4s-ranking--table-pos"><span v-text="r4sEntry.results.currentPosition"></span></td>
                <td class="r4s-ranking--table-bib"><span v-text="r4sEntry.entry.bibNo"></span></td>
                <td class="r4s-ranking--table-th-ath"><span v-text="trimColumnText(r4sEntry.athlete.name)"></span></td>
                <td class="r4s-ranking--table-th-club"><span v-text="trimColumnText(r4sEntry.athlete.club)"></span></td>
                <td class="r4s-ranking--table-td-sco"><span v-text="getScore(r4sEntry)"></span></td>
            </tr>
        </table>
        <div class="r4s-ranking--page-count">
            Page: <span v-text="pageDisplaying"></span> / <span v-text="pagesTotal"></span>
        </div>
    </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import {Prop, Watch} from "vue-property-decorator"
import {IR4sEntry, IR4sSocketDataMessage, IRs4Score} from "./rs4-scoreboard-models"
import {Rs4Service} from "./rs4-service"
import {CommonService} from "../../../common/common-service"

@Component({
    name: "rs4-rankings"
})
export default class Rs4Rankings extends Vue {
    @Prop({required: true})
    public readonly r4sSocketDataMessage!: IR4sSocketDataMessage;

    @Prop({required: true})
    public readonly pageCycleTimeMs!: number;

    @Prop({default: 6})
    public readonly pageSize!: number;

    public pageDisplaying = 1;
    public pagesTotal = 1;
    public pageEntriesChunks: Array<IR4sEntry[]> = [];
    public pageEntriesDisplay: IR4sEntry[] = [];

    public rs4Service: Rs4Service = new Rs4Service();
    public commonService: CommonService = new CommonService();

    public timerPageCycle: number;

    public created() {
        this.init(this.r4sSocketDataMessage);
    }

    @Watch("r4sSocketDataMessage")
    public onR4sSocketDataMessageChanged(newValue: IR4sSocketDataMessage) {
        this.init(newValue);
    }

    public init(r4sSocketDataMessage: IR4sSocketDataMessage) {
        this.stopPageCycle();
        this.pageDisplaying = 1;
        this.pagesTotal = this.rs4Service.howManyEntriesPages(r4sSocketDataMessage, this.pageSize);
        let r4sEntries: IR4sEntry[] = this.rs4Service.getEntriesArray(r4sSocketDataMessage);
        r4sEntries = this.rs4Service.sortEntries(r4sEntries);
        this.pageEntriesChunks = this.commonService.chunkArray(r4sEntries, this.pageSize);
        if (r4sEntries.length > 0) {
            this.pageEntriesDisplay = this.pageEntriesChunks[0];
        }
        this.startPageCycle();
    }

    public rowStyle(r4sEntry: IR4sEntry, index: number) {
        const styles: Record<string, string> = {
            // lineHeight: "3vh"
        };

        if (this.doesAthleteMatchScore(r4sEntry)) {
            styles["backgroundColor"] = "#c5f1da";
        } else {
            if (index % 2 === 0) {
                styles["backgroundColor"] = "#fafafa";
            }
        }
        return styles;
    }

    public doesAthleteMatchScore(r4sEntry: IR4sEntry): boolean {
        return this.r4sSocketDataMessage.data.payload.score.athleteId === r4sEntry.athlete.id;
    }

    public getScore(r4sEntry: IR4sEntry) {
        // let score = "";
        // let scoreText = "";

        if (!this.doesAthleteMatchScore(r4sEntry)) {
            // score = this.commonService.roundNumberToDecimalPlaces(r4sEntry.results.highScore, 2, false).toString();
            // scoreText = r4sEntry.results.scoreText
            return this.rs4Service.getScoreFromEntry(r4sEntry);
        } else {
            const rs4Score: IRs4Score = this.r4sSocketDataMessage.data.payload.score;
            return this.rs4Service.getScoreFromScore(rs4Score);
        }
        // return score + " " + scoreText;
    }

    public startPageCycle() {
        this.timerPageCycle = window.setTimeout( () => {
            console.log("Rs4Rankings.startPageCycle...ms: " + this.pageCycleTimeMs);
            this.pageDisplaying++;
            if (this.pageDisplaying > this.pagesTotal) {
                this.pageDisplaying = 1;
                //  TODO clone could be expensive
                // this.$emit("gotToLastPage", R.clone(this.r4sSocketDataMessage));
                this.$emit("gotToLastPage", this.r4sSocketDataMessage);
            }
            this.pageEntriesDisplay = this.pageEntriesChunks[this.pageDisplaying - 1];
            console.log("Rs4Rankings.startPageCycle...pageDisplaying: " + this.pageDisplaying);
            this.startPageCycle();
        }, this.pageCycleTimeMs);
    }

    public stopPageCycle() {
        console.log("Rs4Rankings.stopPageCycle...");
        clearTimeout(this.timerPageCycle);
    }

    public beforeDestroy() {
        console.log("Rs4Rankings.beforeDestroy...");
        this.stopPageCycle();
    }

    public trimColumnText(content: string): string {
        if (content.length > 30) {
            return content.slice(0, 31) + "..."
        }
        return content;
    }

}
</script>

<style>

.r4s-ranking--table {
    font-size: 1.4em;
}

.r4s-ranking--table-pos {
    width: 10vw;
    text-align: center;
}

.r4s-ranking--table-bib {
    width: 10vw;
}

.r4s-ranking--table-th-ath {
    width: 35vw;
}

.r4s-ranking--table-th-club {
    width: 35vw;
}

.r4s-ranking--table-td-sco {
    width: 10vw;
}

.r4s-ranking--page-count {
    float: right;
    margin-right: 1em;
    font-size: 1.25em;
    font-weight: 500;
}

</style>
