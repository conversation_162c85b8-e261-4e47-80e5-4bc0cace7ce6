<template>
    <div class="scoreboard--score-wrapper">

        <div class="scoreboard--score-sep"></div>

        <div class="scoreboard--score-scoreboard-name scoreboard--border">
            <span v-text="getTitle"></span>
        </div>

        <div class="scoreboard--score-sep"></div>

        <div class="scoreboard--score-scoreboard-scores">
            <div class="scoreboard--score-scoreboard-score-wrapper scoreboard--border" :class="getPbClass">
                <span v-text="getScore"></span>
            </div>

            <div class="scoreboard--score-scoreboard-score-wrapper-right">
                <div class="scoreboard--score-scoreboard-bib-and-pb score-scoreboard--scoreboard-bib scoreboard--border">
                    POS: <span v-text="getPosition"></span>
                </div>

                <!--                <div class="scoreboard&#45;&#45;score-sep-pb"></div>-->

                <div class="scoreboard--score-scoreboard-bib-and-pb scoreboard--border">
                    PB: <span v-text="getPb"></span>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import {IR4sEntry, IR4sSocketDataMessage, IRs4Score} from "./rs4-scoreboard-models"
import {Rs4Service} from "./rs4-service"

@Component({
    name: "rs4-score"
})
export default class Rs4Score extends Vue {
    @Prop({required: true})
    public readonly r4sSocketDataMessage: IR4sSocketDataMessage;

    public rs4Service: Rs4Service = new Rs4Service();

    public get getTitle(): string {
        if (!this.doWeHaveData(this.r4sSocketDataMessage)) {
            return "";
        }
        const r4sEntry: IR4sEntry | null = this.rs4Service.getAthleteEntryFromScore(this.r4sSocketDataMessage);
        if (r4sEntry) {
            return r4sEntry.entry.bibNo + ": " + r4sEntry.athlete.name;
        }
        return "";
    }

    public get getScore(): string {
        if (!this.doWeHaveData(this.r4sSocketDataMessage)) {
            return "";
        }
        // const rs4Score: IRs4Score | null = this.r4sSocketDataMessage.data.payload.score;
        const rs4Score: IRs4Score | null = this.getScoreFromData(this.r4sSocketDataMessage);
        if (!rs4Score) {
            return "-";
        }
        // if (rs4Score.score === 0) {
        //     return "-";
        // }
        // return rs4Score.score.toString();
        return this.rs4Service.getScoreFromScore(rs4Score, false);
    }

    public get getPosition(): string {
        if (!this.doWeHaveData(this.r4sSocketDataMessage)) {
            return "";
        }
        const r4sEntry: IR4sEntry | null = this.rs4Service.getAthleteEntryFromScore(this.r4sSocketDataMessage);
        if (r4sEntry) {
            return r4sEntry.results.currentPosition.toString();
        }
        return "";
    }

    public get getPbClass(): string {
        if (!this.doWeHaveData(this.r4sSocketDataMessage)) {
            return "";
        }
        // const rs4Score: IRs4Score = this.r4sSocketDataMessage.data.payload.score;
        const rs4Score: IRs4Score | null = this.getScoreFromData(this.r4sSocketDataMessage);
        if (!rs4Score) {
            return "";
        }
        const r4sEntry: IR4sEntry | null = this.rs4Service.getAthleteEntryFromScore(this.r4sSocketDataMessage);
        if (r4sEntry && (rs4Score.score > r4sEntry.athlete.pb)) {
            return "scoreboard--scoreboard-score-above-pb";
        }
        return "";
    }


    public get getPb(): string {
        if (!this.doWeHaveData(this.r4sSocketDataMessage)) {
            return "";
        }
        const r4sEntry: IR4sEntry | null = this.rs4Service.getAthleteEntryFromScore(this.r4sSocketDataMessage);
        if (r4sEntry) {
            return r4sEntry.athlete.pb.toString();
        }
        return "";
    }

    public doWeHaveData(r4sSocketDataMessage: IR4sSocketDataMessage): boolean {
        return r4sSocketDataMessage && r4sSocketDataMessage.data && r4sSocketDataMessage.data.payload ? true : false;
    }

    public getScoreFromData(r4sSocketDataMessage: IR4sSocketDataMessage): IRs4Score | null {
        if (r4sSocketDataMessage && r4sSocketDataMessage.data && r4sSocketDataMessage.data.payload && r4sSocketDataMessage.data.payload.score){
            return r4sSocketDataMessage.data.payload.score;
        }
        return null;
    }

}

</script>

<style scoped>

.scoreboard--score-wrapper {
    width: 100%;
    /*border: solid 1px black;*/
    height: 85vh;
    padding: 1vw;
    background-color: black;
    color: white;
}

.scoreboard--score-scoreboard-bib {
    margin-bottom: 10vh;
}

.scoreboard--border {
    border: solid 0.5vh black;
}

.scoreboard--score-sep {
    height: 2vh;
}

.scoreboard--score-scoreboard-name {
    width:  100%;
    /*background-color: orange;*/
    height: 15vh;
    line-height: 15vh;
    font-size: 10vh;
    text-align: center;
    /*border: solid 2px black;*/
}

.scoreboard--score-scoreboard-scores {
    width:  100%;
    /*background-color: pink;*/
}

.scoreboard--score-scoreboard-score-wrapper {
    width:  46vw;
    height: 46vh;
    line-height: 45vh;
    /*background-color: yellow;*/
    display: inline-block;
    /*border: solid 2px black;*/
    vertical-align: top;
    text-align: center;
    vertical-align: middle;

    font-size: 20vh;
}

.scoreboard--score-scoreboard-score-wrapper-right {
    width:  46vw;
    height: 46vh;
    /*background-color: green;*/
    display: inline-block;
    /*border: solid 2px black;*/
    vertical-align: top;
    float: right;
}

.scoreboard--score-scoreboard-bib-and-pb {
    /*background-color: blue;*/
    height: 23vh;
    line-height: 23vh;
    text-align: center;
    vertical-align: middle;
    /*border: solid 2px black;*/
    font-size: 10vh;
}

.scoreboard--scoreboard-score-above-pb {
    /*background-color: #00C853;*/
}
</style>

