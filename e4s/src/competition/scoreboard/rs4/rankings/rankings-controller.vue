<template>
    <div>
        <a
            v-for="eventGroupId in eventIdOrderOfReceipt"
            :key="eventGroupId"
            v-on:click.prevent="showThis(eventGroupId)"
        >
            eventGroupId
        </a>

        <RankingsPublic :entries="showTheseEntries"></RankingsPublic>
    </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {IR4sEntry} from "../rs4-scoreboard-models"
import {Rs4Service} from "../rs4-service"
import {CommonService} from "../../../../common/common-service"
import RankingsPublic from "../output/public/rankings/rankings-public.vue"

@Component({
    name: "rankings-controller",
    components: {RankingsPublic}
})
export default class RankingsController extends Vue {
    @Prop({
        default: () => {
            return {}
        }
    })
    public readonly entriesByAthleteId!: Record<number, IR4sEntry>;                  //  Used for indiv "result"

    @Prop({
        default: () => {
            return {}
        }
    })
    public readonly entriesByEventIds!: Record<number, Record<number, IR4sEntry>>;    //  Used for preload

    public rs4Service: Rs4Service = new Rs4Service();
    public commonService: CommonService = new CommonService();

    public eventGroupIdKeyEntries: Record<string, IR4sEntry[]> = {}
    public eventIdOrderOfReceipt: string[] = [];
    public showTheseEntries: IR4sEntry[] = [];

    public created() {

    }

    @Watch("entriesByAthleteId")
    public onEntriesByAthleteChanged(newValue: Record<number, IR4sEntry>) {
        this.addEntriesByAthlete(newValue);
    }

    public addEntriesByAthlete(entries: Record<number, IR4sEntry>): void {
        if (entries && Object.keys(entries).length > 0) {
            const entriesArray = this.commonService.convertObjectToArray(entries);
            if (entriesArray.length === 0 ) {
                return;
            }
            const eventGroupId: string = entriesArray[0].eventGroup.id.toString();
            this.eventGroupIdKeyEntries[eventGroupId] = entriesArray;
            if (this.eventIdOrderOfReceipt.indexOf(eventGroupId) === -1) {
                this.eventIdOrderOfReceipt.push(eventGroupId);
            }
        }
    }

    @Watch("entriesByEventIds")
    public onEntriesByEventIdsChanged(newValue: Record<number, Record<number, IR4sEntry>>) {
        this.addEntriesByEventIds(newValue)
    }

    public addEntriesByEventIds(entriesByEventIds: Record<number, Record<number, IR4sEntry>>) {
        Object.values(entriesByEventIds).forEach( (entriesByAthleteId) => {
            this.addEntriesByAthlete(entriesByAthleteId);
        });
    }

    public showThis(eventGroupId: string) {
        if (this.eventGroupIdKeyEntries[eventGroupId.toString()]) {
            const r4sEntries = this.rs4Service.sortAndFilterEntries(R.clone(this.eventGroupIdKeyEntries[eventGroupId.toString()]));
            this.showTheseEntries = r4sEntries;
        }
    }

}
</script>
