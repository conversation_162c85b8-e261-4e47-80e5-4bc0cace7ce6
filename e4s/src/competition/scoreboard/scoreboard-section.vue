<template>
    <div class="scoreboard">
        <div class="no-print">
            <div class="scoreboard-nav" :class="'e4s-secondary-color-bg-' + configApp.theme">

                <a href="#/" class="scoreboard-logo-a">
                    <img class="scoreboard-logo" :src="configApp.logo"/>
                </a>

                <div class="scoreboard-title">
                    <div class="scoreboard-comp-event" v-text="scoreboardEventStoreState.eventGroup.name"></div>
                    <div class="scoreboard-comp-name" v-text="scoreboardEventStoreState.comp.name"></div>
                </div>
<!--                <span class="scoreboard-title" v-text="scoreboardEventStoreState.comp.name + ': ' + scoreboardEventStoreState.eventGroup.name"></span>-->

                <span class="scoreboard-date" v-text="getStartTime"></span>

            </div>
        </div>
        <main>
            <div class="scoreboard-body">
                <router-view :key="$route.fullPath"></router-view>
                <toaster></toaster>
            </div>
        </main>
        <div class="scoreboard-foot" :class="'e4s-secondary-color-bg-' + configApp.theme">
<!--            Powered by <span class="scoreboard-foot-url">uk.entry4sports.com</span>-->
            For full results  <span class="scoreboard-foot-url" v-text="getResultsUrl"></span>
        </div>
    </div>
</template>

<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import Toaster from "../../user-message/toaster.vue";
    import CommonHeader from "../../common/ui/menus/common-header.vue"
    import {mapState} from "vuex"
    import {CONFIG_STORE_CONST, IConfigStoreState} from "../../config/config-store"
    import {IConfigApp} from "../../config/config-app-models"
    import {IScoreboardStoreState, SCOREBOARD_STORE_CONST} from "./scorboard-store"
    import {IScoreboardEvent} from "./scoreboard-models"
    import { parse, format } from "date-fns";
    import {CONFIG} from "../../common/config";

    @Component({
        name: "scoreboard-section",
        components: {
            CommonHeader,
            Toaster
        },
        computed: {
            ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
                configApp: (state: any) => state.configApp
            }),
            ...mapState(SCOREBOARD_STORE_CONST.SCOREBOARD_CONST_MODULE_NAME, {
                scoreboardEventStoreState: (state: any) => state.scoreboardEvent
            })
        }
    })
    export default class ScoreboardSection extends Vue {
        public readonly configApp: IConfigApp;
        public scoreboardEventStoreState: IScoreboardEvent;

        // public get getFooterText() {
        //     return "Powered by Entry4Sports uk.entry4sports.com"; //  window.location.host;
        // }

        public mounted() {
            //  WP is setting page heights and stuff
            document.body.style.overflow = "hidden";
        }

        public get getStartTime() {
            const startDateIso = this.scoreboardEventStoreState && this.scoreboardEventStoreState.eventGroup ? this.scoreboardEventStoreState.eventGroup.startDateIso : "";

            return startDateIso.length > 0 ? format(parse(startDateIso), "Do MMM YYYY") :  "";
            //
            // if (this.scoreboardEventStoreState && this.scoreboardEventStoreState.eventGroup) {
            //     return format(parse(this.scoreboardEventStoreState.eventGroup.startDateIso), "Do MMM YYYY");
            // }
            // return "";
        }

        public get getResultsUrl() {
            return CONFIG.E4S_HOST + "/" + this.scoreboardEventStoreState.comp.id + "/results";
        }
    }

</script>

<style scoped>
    .scoreboard {
        height: 100vh;
        /*background-color: yellow;*/
    }

    .scoreboard-nav {
        height: 10vh;
        /*background-color: purple;*/
        line-height: 10vh;
        color: white;
        font-size: 3vh;
    }

    .scoreboard-logo-a {
        float: left;
    }

    .scoreboard-logo {
        height: 8vh;
    }

    .scoreboard-title {
        /*font-size: 3vh;*/
        /*text-align: center;*/
        /*align-content: center;*/
        display: inline-block;
        margin-left: 5vw;
        height: 10vh;
    }

    .scoreboard-comp-event {
        font-size: 5vh;
        height: 5vh;
        position: fixed;
        margin-top: -2vh;
    }

    .scoreboard-comp-name {
        height: 5vh;
        position: fixed;
        margin-top: 2vh;
        font-size: 2vh;
        color: #464242;
        font-weight: 500;
    }

    .scoreboard-date {
        float: right;
        margin-right: 2vw;
    }

    .scoreboard-body {
        height: 85vh;
    }

    .scoreboard-foot {
        height: 5vh;
        text-align: center;
        line-height: 5vh;
        font-weight: 500;
        font-size: 2vh;
        color: white;
    }
    .scoreboard-foot-url {
        color: #464242;
        padding-left: 1vw;
    }


</style>
