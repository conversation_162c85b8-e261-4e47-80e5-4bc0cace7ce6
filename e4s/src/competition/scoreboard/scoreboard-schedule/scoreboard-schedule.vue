<template>
  <div>
    <div class="row">
      <div class="input-field col s12 m6 l6">
        <label class="active"> Start </label>
<!--        <input-->
<!--          id="scoreboard-schedule&#45;&#45;startdt"-->
<!--          v-model="scoreboardScheduleInternal.startdt"-->
<!--        />-->
        <DateTimeEntry v-model="scoreboardScheduleInternal.startdt"/>
      </div>

      <div class="input-field col s12 m6 l6">
        <label class="active"> End </label>
<!--        <input-->
<!--          id="scoreboard-schedule&#45;&#45;enddt"-->
<!--          v-model="scoreboardScheduleInternal.enddt"-->
<!--        />-->
        <DateTimeEntry v-model="scoreboardScheduleInternal.enddt"/>
      </div>

      <div class="input-field col s12 m6 l6">
        <label class="active"> Design </label>
        <SimpleObjectDropDown
          v-if="rs4ScoreboardDesign.length > 0"
          :current-value="scoreboardScheduleInternal.design"
          prop-name="description"
          :simple-objects="rs4ScoreboardDesign"
          v-on:onSelected="scoreboardScheduleInternal.design = $event"
        ></SimpleObjectDropDown>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s12 m6 l6">
        <label class="active" for="scoreboard-schedule--filter">
          Filter (text)
        </label>
        <input id="scoreboard-schedule--filter" v-model="filterText" />
      </div>
      <div class="input-field col s12 m6 l6">
        <label class="active" for="scoreboard-schedule--filter">
          Filter Track/Field
        </label>
        <select v-model="filterType" class="browser-default">
          <option value="A">All</option>
          <option value="T">Track</option>
          <option value="F">Field</option>
        </select>
      </div>
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <div>
          <a href="#" v-on:click.prevent="setFilteredSelected(true)"
            >Set Selected</a
          >&nbsp;
          <span style="margin: 5px 0">|</span>
          <a href="#" v-on:click.prevent="setFilteredSelected(false)"
            >DeSelect All</a
          >
        </div>

<!--        scoreboardScheduleInternal.events-->
        <div
          v-for="rs4EventGroup in rs4EventGroups"
          :key="rs4EventGroup.id"
          class="col s12 m6 l4"
        >
          <label>
            <input
              class="e4s-checkbox"
              type="checkbox"
              :value="rs4EventGroup"
              v-model="eventsSelected"
            />
            <span
              v-text="getCheckBoxName(rs4EventGroup)"
              :class="getFilterClass(rs4EventGroup)"
            ></span>
          </label>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s12 m12 l12">
        <div class="right">
          <LoadingSpinner v-if="isLoading"></LoadingSpinner>
          <button
            :disabled="isLoading"
            class="e4s-button e4s-button--red e4s-button--10-wide"
            v-on:click="cancel"
          >
            Cancel
          </button>
          <button
            :disabled="isLoading"
            class="e4s-button e4s-button--green e4s-button--10-wide"
            v-on:click="submit"
          >
            Save
          </button>
        </div>
      </div>
    </div>

    <!--        <div class="row">-->
    <!--            <div class="input-field col s12 m12 l12">-->
    <!--                scoreboardScheduleInternal: {{scoreboardScheduleInternal}}-->
    <!--            </div>-->
    <!--        </div>-->
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { IScoreboardSchedule } from "./scoreboard-schedule-models";
import { ScoreboardScheduleService } from "./scoreboard-schedule-service";
import {
  IRs4EventGroup,
  IRs4ScoreboardDesign,
  IRs4ScoreboardOutput,
} from "../rs4/rs4-scoreboard-models";
import DateTimeEntry from "../../../common/ui/datetime/date-time-entry.vue";
import E4sModal from "../../../common/ui/e4s-modal.vue";
import SimpleObjectDropDown from "../../../common/ui/simple-object-drop-down.vue";
import CloseIcon from "../../../common/ui/close-icon.vue";
import { ValidationController } from "../../../validation/validation-controller";
import { format, parse } from "date-fns";
import {IBaseConcrete} from "../../../common/common-models";

const scoreboardScheduleService: ScoreboardScheduleService =
  new ScoreboardScheduleService();

@Component({
  name: "scoreboard-schedule",
  components: { CloseIcon, SimpleObjectDropDown, E4sModal, DateTimeEntry },
})
export default class ScoreboardSchedule extends Vue {
  @Prop({
    default: () => {
      return scoreboardScheduleService.factoryScoreboardSchedule();
    },
  })
  public readonly scoreboardSchedule!: IScoreboardSchedule;

  @Prop({ default: false })
  public readonly isLoading!: boolean;

  @Prop({
    required: true,
  })
  public readonly rs4ScoreboardOutput!: IRs4ScoreboardOutput;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly rs4EventGroups!: IRs4EventGroup[];

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly rs4ScoreboardDesign!: IRs4ScoreboardDesign[];

  public scoreboardScheduleInternal: IScoreboardSchedule =
    scoreboardScheduleService.factoryScoreboardSchedule();

  // public rs4EventGroupsForSelection: IBaseConcrete[] = [];
  public eventsSelected: IRs4EventGroup[] = [];


  public validationController: ValidationController =
    new ValidationController();

  public filterText = "";
  public filterType: "T" | "F" | "A" = "A";

  public created() {
    // this.init(this.scoreboardSchedule);
    // this.scoreboardScheduleInternal = R.clone(this.scoreboardSchedule)
    // this.scoreboardScheduleInternal.output = {
    //     id: this.rs4ScoreboardOutput.id,
    //     description: this.rs4ScoreboardOutput.description,
    //     outputNo: this.rs4ScoreboardOutput.output
    // }
    // this.scoreboardScheduleInternal.output = R.clone(this.rs4ScoreboardOutput);
  }

  @Watch("scoreboardSchedule", {immediate: true})
  public onScoreboardScheduleChanged(newValue: IScoreboardSchedule) {
    // this.scoreboardScheduleInternal = R.clone(newValue);
    this.init(newValue);
  }

  public init(scoreboardSchedule: IScoreboardSchedule) {
    this.scoreboardScheduleInternal = R.clone(scoreboardSchedule);
    this.scoreboardScheduleInternal.output = {
      id: this.rs4ScoreboardOutput.id,
      description: this.rs4ScoreboardOutput.description,
      outputNo: this.rs4ScoreboardOutput.output,
    };

    const rs4EventGroupsMap = this.rs4EventGroups.reduce<Record<string, IRs4EventGroup>>( (accum, rs4Group) => {
      accum[rs4Group.id.toString()] = rs4Group;
      return accum;
    }, {})
    this.scoreboardScheduleInternal.events.forEach( (baseConcrete: IBaseConcrete) => {
      if (rs4EventGroupsMap[baseConcrete.id.toString()]) {
        this.eventsSelected.push(rs4EventGroupsMap[baseConcrete.id.toString()]);
      }
    })

    // this.rs4EventGroupsForSelection = this.rs4EventGroups.map( (rs4EventGroup) => {
    //   return {
    //     id: rs4EventGroup.id,
    //     name: rs4EventGroup.name
    //   }
    // });

    // this.eventsSelected = this.scoreboardScheduleInternal.events;
  }

  public addEventGroup(eventGroup: IRs4EventGroup) {
    this.removeEventGroup(eventGroup.id);
    this.scoreboardScheduleInternal.events.push(eventGroup);
  }

  public removeEventGroup(id: number) {
    this.scoreboardScheduleInternal.events =
      this.scoreboardScheduleInternal.events.filter((eventGroup) => {
        return eventGroup.id !== id;
      });
  }

  public getCheckBoxName(eventGroup: IRs4EventGroup): string {
    const eventDate = format(parse(eventGroup.startdateiso), "DD MMM");
    return (
      eventDate +
      " [" +
      eventGroup.eventNo +
      "]: " +
      eventGroup.name +
      " (" +
      eventGroup.id +
      ")"
    );
  }

  public getFilterClass(
    eventGroup: IRs4EventGroup
  ): "scoreboard-schedule--match" | "scoreboard-schedule--not-match" | "" {
    // const isTypeFilter = this.filterType!=="A";
    // const isTextFilter = this.filterText.length > 0;
    // if (isTypeFilter || isTextFilter) {
    //     const filterText = this.filterText.toLowerCase();
    //     const filterType = this.filterType.toLowerCase();
    //     const isTypeFilterMatch = eventGroup.typeNo.substring(0,1).toLowerCase() === filterType;
    //     let isTextFilterMatch = false;
    //     if (isTextFilter) {
    //         isTextFilterMatch = eventGroup.name.substring(0, this.filterText.length).toLowerCase() === filterText;
    //     }
    //     return isTypeFilterMatch || isTextFilterMatch ? "scoreboard-schedule--match" : "scoreboard-schedule--not-match";
    // }
    // return "";

    return this.hasFilterMatch(eventGroup)
      ? "scoreboard-schedule--match"
      : "scoreboard-schedule--not-match";
  }

  public hasFilterMatch(eventGroup: IRs4EventGroup): boolean {
    const hasTypeFilter = this.filterType !== "A";
    const hasTextFilter = this.filterText.length > 0;


    // if (!hasTypeFilter) {
    //   return false;
    // }

    if (this.filterType === "A" && !hasTextFilter) {
      return true;
    }

    const filterType = this.filterType.toLowerCase();
    const filterText = this.filterText.toLowerCase();

    const typePred = () => {
      const isTypeFilterMatch =
        (eventGroup.typeNo &&
          eventGroup.typeNo.substring(0, 1).toLowerCase()) === filterType;
      return isTypeFilterMatch;
    }

    const textPred = () => {
      let isTextFilterMatch = false;
      isTextFilterMatch =
        eventGroup.name.substring(0, this.filterText.length).toLowerCase() ===
        filterText;
      return isTextFilterMatch;
    }

    if ( hasTextFilter && hasTypeFilter) {
      return typePred() && textPred();
    }

    if (hasTextFilter) {
      return textPred();
    }

    return typePred();

    // if (isTextFilter) {
    //   const filterText = this.filterText.toLowerCase();
    //
    //   let isTextFilterMatch = false;
    //   if (isTextFilter) {
    //     isTextFilterMatch =
    //       eventGroup.name.substring(0, this.filterText.length).toLowerCase() ===
    //       filterText;
    //   }
    //   return isTextFilterMatch;
    // }
    // if (isTypeFilter || isTextFilter) {
    //   const filterText = this.filterText.toLowerCase();
    //   const filterType = this.filterType.toLowerCase();
    //   const isTypeFilterMatch =
    //     (eventGroup.typeNo &&
    //       eventGroup.typeNo.substring(0, 1).toLowerCase()) === filterType;
    //   let isTextFilterMatch = false;
    //   if (isTextFilter) {
    //     isTextFilterMatch =
    //       eventGroup.name.substring(0, this.filterText.length).toLowerCase() ===
    //       filterText;
    //   }
    //   return isTypeFilterMatch || isTextFilterMatch;
    // }
    // return false;
  }

  public setFilteredSelected(setSelected: boolean) {

    //  Don't clear the selection, you might: filter for Discus, select all those, then
    //  filter for 100m, select all those...etc.
    // this.scoreboardScheduleInternal.events = [];
    // this.eventsSelected = [];

    if (!setSelected) {
      this.eventsSelected = [];
      return;
    }

    const intEvents = this.rs4EventGroups.filter(
      (eventGroup) => {
        const hasMatch = this.hasFilterMatch(
          eventGroup as unknown as IRs4EventGroup
        );
        return hasMatch;
      }
    );
    // this.scoreboardScheduleInternal.events = intEvents;
    this.eventsSelected = R.uniq([...this.eventsSelected, ...intEvents]);
  }

  public cancel() {
    this.$emit("cancel");
  }

  public submit() {

    this.scoreboardScheduleInternal.events = this.eventsSelected;

    this.$emit("submit", R.clone(this.scoreboardScheduleInternal));
  }
}
</script>

<style>
.scoreboard-schedule--not-match {
  color: grey !important;
}
.scoreboard-schedule--match {
  color: black !important;
  font-weight: bold !important;
}
</style>
