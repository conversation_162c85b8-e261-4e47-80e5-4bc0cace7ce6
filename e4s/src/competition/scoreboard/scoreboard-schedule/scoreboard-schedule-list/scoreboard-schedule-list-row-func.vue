<template functional>
  <div>
    <div class="row">
      <div class="col s6 m3 l3">
        <a
          href="#"
          v-on:click.prevent="listeners.editSchedule(props.scoreboardSchedule)"
          ><i class="material-icons bigger">edit</i></a
        >
        <a
          href="#"
          v-on:click.prevent="
            listeners.deleteSchedule(props.scoreboardSchedule)
          "
          ><i class="material-icons bigger red-text">delete</i></a
        >
        <a
          :href="
            $options.methods.getDisplayLink(
              props.scoreboardSchedule,
              props.compId
            )
          "
          ><i class="material-icons bigger">live_tv</i></a
        >
        <a
          :href="
            $options.methods.getDisplayLinkV2(
              props.scoreboardSchedule,
              props.compId
            )
          "
        ><i class="material-icons bigger">live_tv</i></a
        >
        <span
          v-text="
            $options.methods.getDateOutput(props.scoreboardSchedule, true)
          "
        ></span>
      </div>

      <div class="col s6 m3 l3">
        <span
          v-text="
            $options.methods.getDateOutput(props.scoreboardSchedule, false)
          "
        ></span>
      </div>

      <div class="col s6 m3 l3">
        <span v-text="props.scoreboardSchedule.design.description"></span>
      </div>

      <div class="input-field col s6 m3 l3">
        <span
          v-text="$options.methods.getEventsOutput(props.scoreboardSchedule)"
        ></span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import { IScoreboardSchedule } from "../scoreboard-schedule-models";
import { format, parse } from "date-fns";

export default {
  props: [
    "compId",
    "scoreboardSchedule",
    "deleteSchedule",
    "editSchedule",
    "displaySchedule",
  ],
  methods: {
    getDateOutput: (
      scoreboardSchedule: IScoreboardSchedule,
      isStartDate: boolean
    ) => {
      return format(
        parse(
          isStartDate ? scoreboardSchedule.startdt : scoreboardSchedule.enddt
        ),
        "YYYY-MM-DD hh:mm a"
      );
    },
    getEventsOutput: (scoreboardSchedule: IScoreboardSchedule) => {
      return scoreboardSchedule.events
        .map((eventGroup) => {
          return "(" + eventGroup.id + ") " + eventGroup.name;
        })
        .join(", ");
    },
    getDisplayLink: (
      scoreboardSchedule: IScoreboardSchedule,
      compId: number
    ) => {
      return "#/r4s/output/" + compId + "/" + scoreboardSchedule.output.id;
    },
    getDisplayLinkV2: (
      scoreboardSchedule: IScoreboardSchedule,
      compId: number
    ) => {
      return "#/r4s/output-v2/" + compId + "/" + scoreboardSchedule.output.id;
    },
  },
};
</script>
