<template>
  <div>
    <FormHeader :title="getFormTitle">
      <ScoreboardGoTo
        :comp-id="competitionSummaryPublic.compId"
        slot="e4s-form-header--right"
      ></ScoreboardGoTo>
    </FormHeader>

    <div v-if="showSection === sections.MAIN">
      <div class="row">
        <div class="right">
          <LoadingSpinner v-if="isLoading"></LoadingSpinner>
          <button
            :disabled="isLoading"
            class="e4s-button e4s-button--green e4s-button--10-wide"
            v-on:click="sendRefreshMessage"
          >
            <i class="material-icons bigger">flash_on</i> Refresh
          </button>
          <button
            :disabled="isLoading"
            class="e4s-button e4s-button--green e4s-button--10-wide"
            v-on:click="sendReloadMessage"
          >
            <i class="material-icons bigger">cached</i> Reload
          </button>
          <button
            :disabled="isLoading"
            class="e4s-button e4s-button--green e4s-button--10-wide"
            v-on:click="sendShowScheduleMessage"
          >
            <i class="material-icons bigger">access_time</i> Sched
          </button>
          <button
            :disabled="isLoading"
            class="e4s-button e4s-button--green e4s-button--10-wide"
            v-on:click="sendShowRanking"
          >
            <i class="material-icons bigger">grid_on</i> Rank
          </button>
          <button
            :disabled="isLoading"
            class="e4s-button e4s-button--green e4s-button--10-wide"
            v-on:click="showSection = sections.ADD"
          >
            Add
          </button>
        </div>
      </div>

      <!--            <div v-for="schedule in scoreboardSchedule">-->
      <!--                <ScoreboardScheduleListRowFunc-->
      <!--                    :scoreboard-schedule="schedule"-->
      <!--                    v-on:editSchedule="editSchedule"-->
      <!--                    v-on:deleteSchedule="confirmDelete"-->
      <!--                ></ScoreboardScheduleListRowFunc>-->
      <!--            </div>-->

      <table class="e4s-indicator--table">
        <template v-for="schedule in scoreboardSchedule">
          <tr class="e4s-indicator--row-green" :key="schedule.id">
            <td class="e4s-indicator--td e4s-indicator--green"></td>

            <td>
              <ScoreboardScheduleListRowFunc
                :comp-id="compId"
                :scoreboard-schedule="schedule"
                v-on:editSchedule="editSchedule"
                v-on:deleteSchedule="confirmDelete"
              ></ScoreboardScheduleListRowFunc>
            </td>

            <td class="e4s-indicator--td e4s-indicator--green"></td>
          </tr>
        </template>
      </table>
    </div>

    <div v-if="showSection === sections.ADD">
      <ScoreboardSchedule
        :scoreboard-schedule="scoreboardScheduleEdit"
        :is-loading="isLoading"
        :rs4-scoreboard-output="rs4ScoreboardOutput"
        :rs4-scoreboard-design="rs4ScoreboardDesign"
        :rs4-event-groups="rs4EventGroups"
        v-on:submit="submitSchedule"
        v-on:cancel="showSection = sections.MAIN"
      ></ScoreboardSchedule>
    </div>

    <E4sModal
      v-if="showDeleteConfirm"
      header-message="Confirm Delete"
      body-message="Delete schedule, continue?"
      :is-loading="isLoading"
      v-on:closeSecondary="cancelDelete"
      v-on:closePrimary="deleteSchedule"
    ></E4sModal>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import FormHeader from "../../../../common/ui/form/header/form-header.vue";
import { CompetitionService } from "../../../competiton-service";
import { ICompetitionSummaryPublic } from "../../../competition-models";
import { CompetitionData } from "../../../competition-data";
import { handleResponseMessages } from "../../../../common/handle-http-reponse";
import { ScoreboardData } from "../../scoreboard-data";
import {
  IRs4EventGroup,
  IRs4ScoreboardDesign,
  IRs4ScoreboardOutput,
  IR4SSocketMessageBase
} from "../../rs4/rs4-scoreboard-models";
import {IScoreboardSchedule, SocketAction} from "../scoreboard-schedule-models";
import { ScoreboardOutputData } from "../../scoreboard-output/scoreboard-output-data";
import ScoreboardGoTo from "../../scoreboard-go-to.vue";
import LoadingSpinnerRow from "../../../../common/ui/loading-spinner-row.vue";
import ScoreboardSchedule from "../scoreboard-schedule.vue";
import { ScoreboardScheduleData } from "../scoreboard-schedule-data";
import { ScoreboardScheduleService } from "../scoreboard-schedule-service";
import { CommonService } from "../../../../common/common-service";
import ScoreboardScheduleListRowFunc from "./scoreboard-schedule-list-row-func.vue";
import E4sModal from "../../../../common/ui/e4s-modal.vue";
import {
  E4SSocketProxy,
  getSocketInstance,
} from "../../../../socket/socket-controller";
import { ScoreboardOutputService } from "../../scoreboard-output/scoreboard-output-service";

@Component({
  name: "scoreboard-schedule-list",
  components: {
    E4sModal,
    ScoreboardScheduleListRowFunc,
    ScoreboardSchedule,
    LoadingSpinnerRow,
    ScoreboardGoTo,
    FormHeader,
  },
})
export default class ScoreboardScheduleList extends Vue {
  @Prop({
    default: 0,
  })
  public readonly compId: number;

  @Prop({
    default: 0,
  })
  public readonly outputId: number;

  public sections = {
    MAIN: "MAIN",
    ADD: "ADD",
  };
  public showSection: string = this.sections.MAIN;

  public isLoading: boolean = false;

  public scoreboardOutputService: ScoreboardOutputService =
    new ScoreboardOutputService();
  public scoreboardScheduleService: ScoreboardScheduleService =
    new ScoreboardScheduleService();
  public competitionService: CompetitionService = new CompetitionService();
  public competitionData: CompetitionData = new CompetitionData();
  public competitionSummaryPublic: ICompetitionSummaryPublic =
    this.competitionService.factorySummaryPublic();
  public scoreboardData: ScoreboardData = new ScoreboardData();
  public scoreboardScheduleData: ScoreboardScheduleData =
    new ScoreboardScheduleData();
  public commonService: CommonService = new CommonService();

  public scoreboardScheduleEdit: IScoreboardSchedule =
    this.scoreboardScheduleService.factoryScoreboardSchedule();

  public rs4ScoreboardOutput: IRs4ScoreboardOutput =
    this.scoreboardOutputService.factoryScoreboardOutput();
  public rs4EventGroups: IRs4EventGroup[] = [];
  public scoreboardSchedule: IScoreboardSchedule[] = [];
  public rs4ScoreboardDesign: IRs4ScoreboardDesign[] = [];

  public showDeleteConfirm = false;

  public e4SSocketProxy: E4SSocketProxy | null = null;

  public created() {
    if (this.compId > 0 && this.outputId > 0) {
      this.getData();
    }
    this.e4SSocketProxy = getSocketInstance();
    this.e4SSocketProxy.e4sSocket.addEventListener(
      "message",
      this.socketReceiveMessage
    );
  }

  public socketReceiveMessage(message: IR4SSocketMessageBase) {
    console.log(
      "ScoreboardScheduleList addEventListener Output got message...."
    );
  }

  public get getFormTitle() {
    return (
      "Scoreboard Schedule List for : " +
      this.competitionSummaryPublic.compName +
      " (" +
      this.competitionSummaryPublic.compId +
      ") " +
      this.rs4ScoreboardOutput.description +
      " (id:" +
      this.rs4ScoreboardOutput.id +
      " output: " +
      this.rs4ScoreboardOutput.output +
      ")"
    );
  }

  public getData() {
    this.isLoading = true;

    /**
     * Firing these all off in parallel we get a concurrent stream error from server.
     */
    const promScoreboardOutput = new ScoreboardOutputData().read(this.outputId);
    handleResponseMessages(promScoreboardOutput);
    promScoreboardOutput
      .then((respA) => {
        if (respA.errNo === 0) {
          this.rs4ScoreboardOutput = respA.data;
        }
        return;
      })
      .then(() => {
        const promComp = this.competitionData.getCompById(this.compId);
        handleResponseMessages(promComp);
        return promComp.then((respB) => {
          if (respB.errNo === 0) {
            this.competitionSummaryPublic = respB.data;
          }
          return;
        });
      })
      .then(() => {
        const promEventGroups = this.scoreboardData.getRs4EventGroups(
          this.compId
        );
        handleResponseMessages(promEventGroups);
        return promEventGroups.then((respC) => {
          if (respC.errNo === 0) {
            this.rs4EventGroups = respC.data;
          }
          return;
        });
      })
      .then(() => {
        return this.getSchedule();
      })
      .then(() => {
        const promRs4ScoreboardDesign = this.scoreboardData.getRs4DesignList();
        handleResponseMessages(promRs4ScoreboardDesign);
        return promRs4ScoreboardDesign.then((resp) => {
          if (resp.errNo === 0) {
            this.rs4ScoreboardDesign = resp.data;
          }
          return;
        });
      })
      .finally(() => {
        this.isLoading = false;
      });

    // Promise.all([
    //     promScoreboardOutput,
    //     promComp,
    //     promEventGroups,
    //     promRs4Schedule,
    //     promRs4ScoreboardDesign
    // ])
    // .finally( () => {
    //     this.isLoading = false;
    // })
  }

  public getSchedule() {
    const promRs4Schedule = this.scoreboardData.getRs4Schedule(
      this.compId,
      this.outputId
    );
    handleResponseMessages(promRs4Schedule);
    return promRs4Schedule.then((resp) => {
      if (resp.errNo === 0) {
        const scheds: IScoreboardSchedule[] = this.commonService
          .convertObjectToArray(resp.data)
          .map((sched) => {
            sched.startdt = sched.startdt.replace(" ", "T");
            sched.enddt = sched.enddt.replace(" ", "T");
            return sched;
          })
          .sort((a, b) => {
            if (a < b) {
              return -1;
            }
            if (a > b) {
              return 1;
            }
            return 0;
          });
        this.scoreboardSchedule = scheds;
      }
      return;
    });
  }

  public submitSchedule(scoreboardSchedule: IScoreboardSchedule) {
    this.isLoading = true;
    const scoreboardScheduleInbound =
      this.scoreboardScheduleService.convertScheduleToInbound(
        scoreboardSchedule
      );
    let prom;

    if (scoreboardScheduleInbound.id === 0) {
      prom = this.scoreboardScheduleData.create(scoreboardScheduleInbound);
    } else {
      prom = this.scoreboardScheduleData.update(scoreboardScheduleInbound);
    }
    handleResponseMessages(prom);
    prom
      .then((resp) => {
        if (resp.errNo === 0) {
          this.getSchedule();
          this.showSection = this.sections.MAIN;
        }
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public editSchedule(scoreboardSchedule: IScoreboardSchedule) {
    this.scoreboardScheduleEdit = R.clone(scoreboardSchedule);
    this.showSection = this.sections.ADD;
  }

  public cancelDelete() {
    this.scoreboardScheduleEdit =
      this.scoreboardScheduleService.factoryScoreboardSchedule();
    this.showDeleteConfirm = false;
  }

  public confirmDelete(scoreboardSchedule: IScoreboardSchedule) {
    this.scoreboardScheduleEdit = R.clone(scoreboardSchedule);
    this.showDeleteConfirm = true;
  }

  public deleteSchedule() {
    this.isLoading = true;
    const prom = this.scoreboardScheduleData.delete(
      this.scoreboardScheduleEdit.id
    );
    handleResponseMessages(prom);
    prom
      .then((resp) => {
        if (resp.errNo === 0) {
          this.getSchedule();
        }
      })
      .finally(() => {
        this.isLoading = false;
        this.cancelDelete();
      });
  }

  public sendRefreshMessage() {
    this.sendSocketMessage("output-schedule--refresh");
  }

  public sendReloadMessage() {
    this.sendSocketMessage("output-schedule--reload");
  }

  public sendShowScheduleMessage() {
    this.sendSocketMessage("output-schedule--show-schedule");
  }

  public sendShowRanking() {
    this.sendSocketMessage("output-schedule--show-ranking");
  }

  public sendSocketMessage(
    socketAction: SocketAction
  ) {
    if (this.e4SSocketProxy && this.e4SSocketProxy.e4sSocket) {
      // const socketMessage: IRs4SocketMessageOutput = {
      // const socketMessage: any = {
      //   action: "sendmessage",
      //   data: {
      //     compId: this.compId,
      //     outputId: this.rs4ScoreboardOutput.id,
      //     action: socketAction,
      //   },
      //   key: "",
      //   securityKey: "",
      //   deviceKey: "",
      //   domain: "dev.entry4sports.co.uk"
      // };

      // socketMessage.key = "";
      // socketMessage.securityKey = "";
      // socketMessage.deviceKey = "";
      // socketMessage.domain = "dev.entry4sports.co.uk";

      // this.e4SSocketProxy.e4sSocket.send(JSON.stringify(socketMessage));

      // const data: any = {
      //   key: "",
      //   comp: {
      //     id: 313,
      //   },
      //   action: "message",
      //   deviceKey: "",
      //   securityKey: "",
      //   payload: {
      //     title: "New Age Groups",
      //     message: "<br><br><br><br><br><br><br><br>",
      //   },
      //   domain: "dev.entry4sports.co.uk",
      // };
      // this.e4SSocketProxy.e4sSocket.send(JSON.stringify(data));

      new ScoreboardOutputData().sendSocketCommand(this.compId, this.rs4ScoreboardOutput.id, socketAction);

    }
  }

  public beforeDestroy() {
    console.log("ScoreboardOutputDisplay.beforeDestroy...");
    if (this.e4SSocketProxy && this.e4SSocketProxy.e4sSocket) {
      this.e4SSocketProxy.e4sSocket.removeEventListener(
        "message",
        this.socketReceiveMessage
      );
    }
  }
}
</script>
