<template>
    <div>
        <ScoreboardScheduleList :comp-id="compId" :output-id="outputId"></ScoreboardScheduleList>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import ScoreboardScheduleList from "./scoreboard-schedule-list.vue"

@Component({
    name: "scoreboard-schedule-list-route",
    components: {ScoreboardScheduleList}
})
export default class ScoreboardScheduleListRoute extends Vue {
    public compId: number = 0;
    public outputId: number = 0;
    public created() {
        this.compId = isNaN(Number(this.$route.params.compId)) ? 0 : parseInt(this.$route.params.compId, 0);
        this.outputId = isNaN(Number(this.$route.params.outputId)) ? 0 : parseInt(this.$route.params.outputId, 0);
    }
}
</script>
