import { IBaseConcrete, IBaseRaw } from "../../../common/common-models";
import { R4S_DESIGN_TYPE } from "../rs4/rs4-scoreboard-models";

export interface IRs4ScoreboardOutputWeird {
  id: number;
  description: string;
  outputNo: number;
}

/**
 *
 * It's similar, but not the same.
 * export interface IRs4ScoreboardDesign extends IBaseRaw {...
 */
export interface IScoreboardDesignSchedule extends IBaseRaw {
  description: string;
  designNo: R4S_DESIGN_TYPE; //    1 = Scoreboard, 2 = Ranking, 3 = Scoreboard + Ranking, 4 = Picture
}

export interface IScoreboardSchedule extends IBaseRaw {
  startdt: string; //    Iso Time
  enddt: string; //    Iso Time
  design: IScoreboardDesignSchedule;
  output: IRs4ScoreboardOutputWeird;
  events: IBaseConcrete[];
  // timings: {
  //     showScoreFor: number;
  // }
}

export interface IRs4EventGroupInbound {
  eventgroupid: number;
}

export interface IScoreboardScheduleInbound extends IBaseRaw {
  startdt: string; //    YYYY-MM-DD HH:mm:ss
  enddt: string; //    YYYY-MM-DD HH:mm:ss
  designid: number;
  outputid: number;
  events: IRs4EventGroupInbound[];
}

export type SocketAction =
  | "output-schedule--show-schedule"
  | "output-schedule--refresh"
  | "output-schedule--reload"
  | "output-schedule--show-ranking";
