<template>
  <div>
    <div class="e4s-section-padding-separator"></div>

    <div class="e4s-section-padding-separator"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        <table class="result-heat-read--table">
          <tr
            class="result-heat-read--header"
            :style="rs4ScoreboardOutput.options.ui.photoFinish.header"
          >
            <th class="result-heat-read--narrow">Pos#</th>
            <th class="result-heat-read--narrow">Bib</th>
            <th class="result-heat-read--athlete">Athlete</th>
            <th class="result-heat-read--club">Affiliation</th>
            <th class="result-heat-read--result">Result</th>
          </tr>

          <tr
            v-for="(resultAthlete, index) in pageCycling.state.objectsToDisplay"
            :key="resultAthlete.bibNo"
            :style="rs4ScoreboardOutput.options.ui.photoFinish.row"
          >
            <td class="result-heat-read--narrow">
              <span v-text="getPosition(resultAthlete)"></span>
            </td>
            <td class="result-heat-read--narrow">
              <span v-text="getBibNo(resultAthlete)"></span>
            </td>
            <td class="result-heat-read--athlete">
              <div class="result-heat-read--athlete-wrapper">
                <div v-text="trimColumnText(resultAthlete.athlete)"></div>
                <div
                  class="result-heat-read--athlete-subheader"
                  v-text="getAgeGroupDescription(resultAthlete)"
                ></div>
              </div>
            </td>
            <td class="result-heat-read--club">
              <span v-text="trimColumnText(resultAthlete.clubName)"></span>
            </td>
            <td class="result-heat-read--result">
              <EaAward
                :ea-level="resultAthlete.eaAward"
                class="e4s-force-inline-block"
                style="width: 35px"
              />
              <span v-text="getScore(resultAthlete)"></span>
              <!--              <span v-if="resultEvent.eventGroup.wind === 'A'">-->
              <!--                <i class="material-icons result-event-read&#45;&#45;wind-icon">flag</i>-->
              <!--                <span v-text="getWind(resultAthlete)"></span>-->
              <!--              </span>-->
              <!--              <PowerOfTenLink :urn="resultAthlete.urn"></PowerOfTenLink>-->
              <!--              <PowerOfTenLinkV2 :urn="resultAthlete.urn" />-->
            </td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onBeforeUnmount,
  PropType,
  SetupContext,
  watch,
} from "vue";
import { ScoreboardTrackResultV3 } from "./scoreboard-track-models-v3";
import * as ResultsServiceV2 from "../../../../../results/results-service-V2";
import { IResultAthlete } from "../../../../../results/results-models";
import EaAward from "../../../../../../common/ui/ea-award/EaAward.vue";
import {
  IR4sSocketAthleteFieldResults,
  IRs4ScoreboardOutput,
} from "../../../../rs4/rs4-scoreboard-models";
import {
  IUsePageCyclingConfig,
  usePageCycling,
} from "../../../../../../common/ui/page-cycling/usePageCycling";

export default defineComponent({
  name: "ScoreboardTrackHeatRead",
  components: { EaAward },
  props: {
    rs4ScoreboardOutput: {
      type: Object as PropType<IRs4ScoreboardOutput>,
      required: true,
    },
    socketDataMessage: {
      type: Object as PropType<IR4sSocketAthleteFieldResults>,
      required: true,
    },
  },
  setup(
    props: {
      rs4ScoreboardOutput: IRs4ScoreboardOutput;
      socketDataMessage: IR4sSocketAthleteFieldResults;
    },
    context: SetupContext
  ) {
    const pageCyclingConfig: IUsePageCyclingConfig<IResultAthlete> = {
      pageCycleMs:
        props.rs4ScoreboardOutput.options.timings.ranking.pageCycleMs,
      pageSize: props.rs4ScoreboardOutput.options.timings.ranking.pageSize,
      onFinishPageCycle: (pageNumber: number) => {
        context.emit("onFinishPageCycle", pageNumber);
      },
    };
    const pageCycling = usePageCycling<IResultAthlete>(pageCyclingConfig);

    watch(
      props.socketDataMessage,
      (newValue: IR4sSocketAthleteFieldResults) => {
        init(newValue);
      },
      { immediate: true }
    );

    function init(message: IR4sSocketAthleteFieldResults) {
      const resultAthletes = getResults(message);
      pageCycling.setData(resultAthletes, {
        pageCycleMs:
          props.rs4ScoreboardOutput.options.timings.ranking.pageCycleMs,
        pageSize: props.rs4ScoreboardOutput.options.timings.ranking.pageSize,
      });
    }

    function getResults(
      message: IR4sSocketAthleteFieldResults
    ): IResultAthlete[] {
      const scoreboardTrackResultV3: ScoreboardTrackResultV3 =
        message.payload.ranking;

      const heatResults = scoreboardTrackResultV3.heats[0].results;

      //  eventType can be "H" or "D" for track events, since always sort same
      //  way, just use "D" for both.
      return ResultsServiceV2.sortResults(heatResults, "D").filter(
        (resultAthlete: IResultAthlete) => {
          const isNullScore =
            resultAthlete.score === 0 ||
            resultAthlete.score.toString().length === 0;
          return !(resultAthlete.position === 0 && isNullScore);
        }
      );
    }

    function getPosition(result: IResultAthlete): string {
      return result.position > 0 ? result.position.toString() : "-";
    }

    function getBibNo(result: IResultAthlete): string {
      return ResultsServiceV2.getBibNo(result);
    }

    function getScore(result: IResultAthlete) {
      return (
        result.scoreValue.toString().replace("!#!", "") +
        (result.qualify.length > 0 ? " " + result.qualify : "") +
        (result.scoreText ? " " + result.scoreText : "")
      );
    }

    function trimColumnText(content: string): string {
      if (content.length > 30) {
        return content.slice(0, 31) + "...";
      }
      return content;
    }

    function getAgeGroupDescription(resultAthlete: IResultAthlete) {
      if (resultAthlete.ageGroup && resultAthlete.ageGroup.length > 0) {
        return "(" + resultAthlete.ageGroup + ")";
      }
      return "";
    }

    onBeforeUnmount(() => {
      pageCycling.stopPageCycle();
    });

    return {
      getPosition,
      getBibNo,
      getScore,
      trimColumnText,
      getAgeGroupDescription,
      pageCycling,
    };
  },
});
</script>
