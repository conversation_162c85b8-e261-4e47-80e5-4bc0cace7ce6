import { ScoreboardTrackResultV3 } from "../scoreboard-track-models-v3";
import { IR4sSocketAthleteFieldResults } from "../../../../../rs4/rs4-scoreboard-models";

export const scoreboardTrackDataMockHammer: ScoreboardTrackResultV3 = {
  comp: {
    id: 458,
    name: "Midland Masters T&F Championships 2023",
    logo: "/resources/midlandmasters.png",
  },
  eventGroup: {
    id: 9713,
    name: "F6 : Hammer M60-75",
    eventNo: 52,
    ageGroups: [
      {
        id: 20,
        name: "VET 60",
      },
      {
        id: 21,
        name: "VET 70",
      },
      {
        id: 22,
        name: "VET 65",
      },
      {
        id: 23,
        name: "VET 75",
      },
    ],
    wind: "",
    type: "D",
    seed: {
      firstLane: 1,
      laneCount: 0,
      gender: false,
      age: false,
      type: "O",
      waiting: false,
      qualifyToEg: {
        id: 0,
        compId: 0,
        name: "",
        eventNo: 0,
      },
      seeded: false,
    },
    scheduledTime: "2023-06-11T11:30:00+01:00",
  },
  heats: [
    {
      id: 9023,
      heatNo: 1,
      scheduledTime: "2023-06-11T11:30:00",
      actualTime: "2023-06-11T11:30:00",
      image: "",
      description: "",
      results: [
        {
          position: 1,
          laneNo: 0,
          ageGroup: "VET 60",
          bibNo: 443,
          image:
            "https://www.thepowerof10.info/athletes/profilepic.aspx?athleteid=0",
          athlete: "Ray BACON",
          gender: "M",
          athleteId: 185061,
          urn: 0,
          clubName: "Telford A.C.",
          scoreValue: "41.39",
          score: 41.39,
          scoreText: "",
          qualify: "",
          wind: "",
          id: 119256,
          eaAward: 0,
        },
        {
          position: 2,
          laneNo: 0,
          ageGroup: "VET 60",
          bibNo: 445,
          image:
            "https://www.thepowerof10.info/athletes/profilepic.aspx?athleteid=39940",
          athlete: "John MORELAND",
          gender: "M",
          athleteId: 131250,
          urn: 2738465,
          clubName: "Rugby and Northampton A.C.",
          scoreValue: "38.82",
          score: 38.82,
          scoreText: "",
          qualify: "",
          wind: "",
          id: 119257,
          eaAward: 0,
        },
        {
          position: 3,
          laneNo: 0,
          ageGroup: "VET 60",
          bibNo: 435,
          image:
            "https://www.thepowerof10.info/athletes/profilepic.aspx?athleteid=28728",
          athlete: "Kevin MURCH",
          gender: "M",
          athleteId: 101388,
          urn: 2738476,
          clubName: "Rugby and Northampton A.C.",
          scoreValue: "38.01",
          score: 38.01,
          scoreText: "",
          qualify: "",
          wind: "",
          id: 119258,
          eaAward: 0,
        },
        {
          position: 4,
          laneNo: 0,
          ageGroup: "VET 75",
          bibNo: 377,
          image:
            "https://www.thepowerof10.info/athletes/profilepic.aspx?athleteid=26480",
          athlete: "Barry HAWKSWORTH",
          gender: "M",
          athleteId: 106969,
          urn: 2661727,
          clubName: "Derby A.C.",
          scoreValue: "37.15",
          score: 37.15,
          scoreText: "",
          qualify: "",
          wind: "",
          id: 119259,
          eaAward: 0,
        },
        {
          position: 5,
          laneNo: 0,
          ageGroup: "VET 60",
          bibNo: 446,
          image:
            "https://www.thepowerof10.info/athletes/profilepic.aspx?athleteid=42436",
          athlete: "Dave STOKES",
          gender: "M",
          athleteId: 161782,
          urn: 7023681,
          clubName: "Deeside A.C.",
          scoreValue: "32.56",
          score: 32.56,
          scoreText: "",
          qualify: "",
          wind: "",
          id: 119260,
          eaAward: 0,
        },
        {
          position: 7,
          laneNo: 0,
          ageGroup: "VET 70",
          bibNo: 374,
          image:
            "https://www.thepowerof10.info/athletes/profilepic.aspx?athleteid=35134",
          athlete: "Bill RENSHAW",
          gender: "M",
          athleteId: 161470,
          urn: 2720830,
          clubName: "Rotherham Harriers A.C.",
          scoreValue: "28.19",
          score: 28.19,
          scoreText: "",
          qualify: "",
          wind: "",
          id: 119261,
          eaAward: 0,
        },
        {
          position: 8,
          laneNo: 0,
          ageGroup: "VET 70",
          bibNo: 449,
          image:
            "https://www.thepowerof10.info/athletes/profilepic.aspx?athleteid=782382",
          athlete: "Kevin BATES",
          gender: "M",
          athleteId: 153070,
          urn: 3631212,
          clubName: "Peterborough and Nene Valley A",
          scoreValue: "27.61",
          score: 27.61,
          scoreText: "",
          qualify: "",
          wind: "",
          id: 119262,
          eaAward: 0,
        },
        {
          position: 9,
          laneNo: 0,
          ageGroup: "VET 60",
          bibNo: 444,
          image:
            "https://www.thepowerof10.info/athletes/profilepic.aspx?athleteid=109451",
          athlete: "James GILLESPIE",
          gender: "M",
          athleteId: 100026,
          urn: 2667002,
          clubName: "Veterans A.C.",
          scoreValue: "27.51",
          score: 27.51,
          scoreText: "",
          qualify: "",
          wind: "",
          id: 119263,
          eaAward: 0,
        },
        {
          position: 10,
          laneNo: 0,
          ageGroup: "VET 65",
          bibNo: 448,
          image:
            "https://www.thepowerof10.info/athletes/profilepic.aspx?athleteid=87405",
          athlete: "Peter KNOTT",
          gender: "M",
          athleteId: 105422,
          urn: 2829262,
          clubName: "Corby A.C.",
          scoreValue: "26.36",
          score: 26.36,
          scoreText: "",
          qualify: "",
          wind: "",
          id: 119264,
          eaAward: 0,
        },
      ],
    },
  ],
} as any as ScoreboardTrackResultV3;

export const socketAthleteFieldResultsHammerMock: IR4sSocketAthleteFieldResults =
  {
    action: "field-results",
    comp: {
      id: 351,
    },
    deviceKey: "some-device-key",
    securityKey: "some-security-key",
    key: "F1",
    outputNo: 2,
    domain: "",
    payload: { ranking: scoreboardTrackDataMockHammer },
  };
