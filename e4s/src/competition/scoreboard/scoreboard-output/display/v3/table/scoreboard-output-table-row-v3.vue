<template>
  <div class="e4s-flex-row e4s-flex-start e4s-gap--standard">
    <!--    <div v-text="index"></div>-->

    <div v-text="eventName" style="width: 180px"></div>

    <div class="e4s-flex-column" style="width: 150px">
      <div v-text="message.action"></div>
      <div v-text="messageKey" class="e4s-subheader--general"></div>
    </div>

    <div class="e4s-flex-column" style="width: 120px">
      <div class="e4s-flex-row e4s-justify-flex-space-between">
        <div>Display:</div>
        <div v-text="displayTime"></div>
      </div>

      <div class="e4s-flex-row e4s-justify-flex-space-between">
        <div>Force:</div>
        <div v-text="message.meta.forceDisplay"></div>
      </div>

      <div class="e4s-flex-row e4s-justify-flex-space-between">
        <div>Next:</div>
        <div v-text="message.meta.showNext"></div>
      </div>
    </div>
    <div class="e4s-flex-column" style="width: 120px">
      <div class="e4s-flex-row e4s-justify-flex-space-between">
        <div>Updated:</div>
        <div v-text="message.meta.updatedCount"></div>
      </div>
      <div class="e4s-flex-row e4s-justify-flex-space-between">
        <div>Show:</div>
        <div v-text="message.meta.showNumberOfTimes"></div>
      </div>
      <div class="e4s-flex-row e4s-justify-flex-space-between">
        <div>Displayed:</div>
        <div v-text="message.meta.displayedCount"></div>
      </div>
    </div>
    <div class="e4s-flex-column">
      <a href="#" v-on:click.prevent="doAction('FORCE')">Force</a>
      <a href="#" v-on:click.prevent="doAction('NEXT')">Next</a>
      <a href="#" v-on:click.prevent="doAction('CANCEL')">Cancel</a>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "vue";
import {
  IR4sSocketDataMessageDatav3,
  SocketV3UserAction,
} from "../useScoreboardOutputDisplayV3";
import {
  getDisplayTime,
  getEventName,
  getSocketMessageKey,
} from "../scoreboard-output-display-service-v3";

export default defineComponent({
  name: "ScoreboardOutputTableRowV3",
  components: {},
  props: {
    message: {
      type: Object as PropType<IR4sSocketDataMessageDatav3>,
      required: true,
    },
    index: {
      type: Number,
      required: true,
    },
  },
  setup(
    props: { message: IR4sSocketDataMessageDatav3 },
    context: SetupContext
  ) {
    function doAction(action: SocketV3UserAction) {
      context.emit("action", { action, message: props.message });
    }

    const messageKey = computed(() => {
      return getSocketMessageKey(props.message);
    });

    const eventName = computed(() => {
      return getEventName(props.message);
    });

    const displayTime = computed(() => {
      return getDisplayTime(props.message);
    });

    return {
      doAction,
      messageKey,
      eventName,
      displayTime,
    };
  },
});
</script>
