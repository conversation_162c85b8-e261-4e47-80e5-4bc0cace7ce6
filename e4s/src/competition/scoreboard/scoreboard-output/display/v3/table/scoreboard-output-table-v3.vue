<template>
  <div class="e4s-flex-column">
    <ScoreboardOutputTableRowV3
      class="e4s-repeatable-grid--top"
      v-for="(message, index) in messages"
      :message="message"
      :index="index"
      v-on:action="doAction"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "vue";
import {
  IR4sSocketDataMessageDatav3,
  SocketV3UserActionPayload,
} from "../useScoreboardOutputDisplayV3";
import ScoreboardOutputTableRowV3 from "./scoreboard-output-table-row-v3.vue";

export default defineComponent({
  name: "ScoreboardOutputTableV3",
  components: { ScoreboardOutputTableRowV3 },
  props: {
    messages: {
      type: Array as PropType<IR4sSocketDataMessageDatav3[]>,
      required: true,
    },
  },
  setup(
    props: { messages: IR4sSocketDataMessageDatav3[] },
    context: SetupContext
  ) {
    // watch(
    //   () => props.athlete,
    //   (newValue: IR4sSocketDataMessageDatav3[]) => {
    //     athleteForm.init(newValue);
    //   },
    //   {
    //     immediate: true,
    //   }
    // );
    function doAction(payload: SocketV3UserActionPayload) {
      context.emit("action", payload);
    }
    return { doAction };
  },
});
</script>
