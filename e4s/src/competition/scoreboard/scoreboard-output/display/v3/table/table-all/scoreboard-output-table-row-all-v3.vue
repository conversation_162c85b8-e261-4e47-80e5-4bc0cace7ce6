<template>
  <div
    class="e4s-flex-row e4s-gap--standard"
    :class="
      scoreboardMessageReason.shouldMessageBeDisplayedResult.should
        ? 'scoreboard-output-table-row-all-v3--message-enabled'
        : 'scoreboard-output-table-row-all-v3--message-disabled'
    "
  >
    <div style="width: 60px" v-text="getTime"></div>
    <div
      style="width: 200px"
      v-text="scoreboardMessageReason.message.action"
    ></div>
    <div class="e4s-flex-column">
      <div
        v-text="
          scoreboardMessageReason.shouldMessageBeDisplayedResult.identifier
        "
      ></div>
      <div
        v-text="scoreboardMessageReason.shouldMessageBeDisplayedResult.reason"
      ></div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "vue";
import { IScoreboardMessageReason } from "../../useScoreboardOutputDisplayV3";
import { format, parse } from "date-fns";
import ScoreboardPhotoFinish from "../../../scoreboard-photo-finish/scoreboard-photo-finish.vue";

export default defineComponent({
  name: "ScoreboardOutputTableRowAllV3",
  components: { ScoreboardPhotoFinish },
  props: {
    scoreboardMessageReason: {
      type: Object as PropType<IScoreboardMessageReason>,
      required: true,
    },
  },
  setup(
    props: { scoreboardMessageReason: IScoreboardMessageReason },
    context: SetupContext
  ) {
    const messageKey = computed(() => {
      return (
        props.scoreboardMessageReason.message.key +
        props.scoreboardMessageReason.time
      );
    });

    const getTime = computed(() => {
      return format(parse(props.scoreboardMessageReason.time), "HH:mm:ss");
    });

    return {
      messageKey,
      getTime,
    };
  },
});
</script>

<style>
.scoreboard-output-table-row-all-v3--message-enabled {
  background-color: var(--green-50);
}
.scoreboard-output-table-row-all-v3--message-disabled {
  color: dimgrey;
}
</style>
