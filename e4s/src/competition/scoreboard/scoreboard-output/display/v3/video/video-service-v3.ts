import { IR4sSocketVideoPayload } from "../../../../rs4/rs4-scoreboard-models";
import { isValidHttpUrl } from "../../../../../../common/common-service-utils";
import { CONFIG } from "../../../../../../common/config";

export function getVideoLink(
  socketVideoMessage: IR4sSocketVideoPayload
): string {
  if (socketVideoMessage.video) {
    const isValid = isValidHttpUrl(socketVideoMessage.video);
    if (isValid) {
      return socketVideoMessage.video;
    }
    return CONFIG.E4S_HOST + socketVideoMessage.video;
  }
  return "#";
}
