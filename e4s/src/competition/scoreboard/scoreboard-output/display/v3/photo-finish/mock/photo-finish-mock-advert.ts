import { IR4sSocketPhotoFinishMessage } from "../../../../../rs4/rs4-scoreboard-models";

export const photoFinishMockAdvert: IR4sSocketPhotoFinishMessage = {
  key: "25-2",
  comp: {
    id: 351,
  },
  action: "photofinish",
  deviceKey: "",
  securityKey: "",
  domain: "dev.entry4sports.co.uk",
  payload: {
    picture: "https://entry4sports.co.uk/resources/adverts/esaa/e4s2.gif",
    comp: {
      id: 351,
    },
    eventNo: 25,
    heatNo: 2,
    typeNo: "T25",
    eventName: "400m SG",
    scheduleTime: "2023-06-30 14:25:00",
    eventTime: "2023-06-30 14:30:57",
    results: [
      {
        id: 47437,
        compid: 351,
        athleteid: 104753,
        eventno: 25,
        resultkey: "h2",
        resultvalue: "58.90",
        options: {
          scoreText: "",
          place: 9,
          bibNo: 73,
          lane: 9,
          athleteId: 104753,
          athlete: "Mia WAINWRIGHT",
          club: "Staffordshire",
          time: 58.9,
          ageGroup: "Inters",
          ws: "",
          eventTime: "14:30:57",
        },
        timeInSeconds: 58.9,
        eaAward: 0,
        ageGroup: "Inters",
      },
      {
        id: 47430,
        compid: 351,
        athleteid: 156686,
        eventno: 25,
        resultkey: "h2",
        resultvalue: "56.93",
        options: {
          scoreText: "",
          place: 2,
          bibNo: 53,
          lane: 4,
          athleteId: 156686,
          athlete: "Charlotte HARRIS",
          club: "Middlesex",
          time: 56.93,
          ageGroup: "Inters",
          ws: "",
          eventTime: "14:30:57",
        },
        timeInSeconds: 56.93,
        eaAward: 0,
        ageGroup: "Inters",
      },
      {
        id: 47432,
        compid: 351,
        athleteid: 156743,
        eventno: 25,
        resultkey: "h2",
        resultvalue: "57.10",
        options: {
          scoreText: "",
          place: 4,
          bibNo: 81,
          lane: 2,
          athleteId: 156743,
          athlete: "Monica DAVIDSON",
          club: "Warwickshire",
          time: 57.1,
          ageGroup: "Inters",
          ws: "",
          eventTime: "14:30:57",
        },
        timeInSeconds: 57.1,
        eaAward: 0,
        ageGroup: "Inters",
      },
      {
        id: 47433,
        compid: 351,
        athleteid: 157368,
        eventno: 25,
        resultkey: "h2",
        resultvalue: "57.13",
        options: {
          scoreText: "",
          place: 5,
          bibNo: 5,
          lane: 7,
          athleteId: 157368,
          athlete: "Anna BABIS",
          club: "Berkshire",
          time: 57.13,
          ageGroup: "Inters",
          ws: "",
          eventTime: "14:30:57",
        },
        timeInSeconds: 57.13,
        eaAward: 0,
        ageGroup: "Inters",
      },
      {
        id: 47431,
        compid: 351,
        athleteid: 158830,
        eventno: 25,
        resultkey: "h2",
        resultvalue: "56.96",
        options: {
          scoreText: "",
          place: 3,
          bibNo: 77,
          lane: 6,
          athleteId: 158830,
          athlete: "Caitlyn HARVEY",
          club: "Surrey",
          time: 56.96,
          ageGroup: "Inters",
          ws: "",
          eventTime: "14:30:57",
        },
        timeInSeconds: 56.96,
        eaAward: 0,
        ageGroup: "Inters",
      },
      {
        id: 47434,
        compid: 351,
        athleteid: 159639,
        eventno: 25,
        resultkey: "h2",
        resultvalue: "57.18",
        options: {
          scoreText: "",
          place: 6,
          bibNo: 29,
          lane: 8,
          athleteId: 159639,
          athlete: "Ruby BROOK",
          club: "Gloucestershire",
          time: 57.18,
          ageGroup: "Inters",
          ws: "",
          eventTime: "14:30:57",
        },
        timeInSeconds: 57.18,
        eaAward: 0,
        ageGroup: "Inters",
      },
      {
        id: 47429,
        compid: 351,
        athleteid: 166485,
        eventno: 25,
        resultkey: "h2",
        resultvalue: "54.60",
        options: {
          scoreText: "",
          place: 1,
          bibNo: 41,
          lane: 5,
          athleteId: 166485,
          athlete: "Charlotte HENRICH",
          club: "Kent",
          time: 54.6,
          ageGroup: "Inters",
          ws: "",
          eventTime: "14:30:57",
        },
        timeInSeconds: 54.6,
        eaAward: 0,
        ageGroup: "Inters",
      },
      {
        id: 47435,
        compid: 351,
        athleteid: 186579,
        eventno: 25,
        resultkey: "h2",
        resultvalue: "58.43",
        options: {
          scoreText: "",
          place: 7,
          bibNo: 38,
          lane: 3,
          athleteId: 186579,
          athlete: "Louise GREENFELL",
          club: "Hertfordshire",
          time: 58.43,
          ageGroup: "Inters",
          ws: "",
          eventTime: "14:30:57",
        },
        timeInSeconds: 58.43,
        eaAward: 0,
        ageGroup: "Inters",
      },
      {
        id: 47436,
        compid: 351,
        athleteid: 186873,
        eventno: 25,
        resultkey: "h2",
        resultvalue: "58.46",
        options: {
          scoreText: "",
          place: 8,
          bibNo: 63,
          lane: 1,
          athleteId: 186873,
          athlete: "Gaby SUTTON",
          club: "Nottinghamshire",
          time: 58.46,
          ageGroup: "Inters",
          ws: "",
          eventTime: "14:30:57",
        },
        timeInSeconds: 58.46,
        eaAward: 0,
        ageGroup: "Inters",
      },
    ],
  },
} as any as IR4sSocketPhotoFinishMessage;
