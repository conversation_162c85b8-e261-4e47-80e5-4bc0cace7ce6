<template>
  <div class="photo-finish---wrapper">
    <div class="photo-finish---header">
      <!--      <div-->
      <!--        v-text="'Wind Speed: ' + windSpeed"-->
      <!--        class="photo-finish-&#45;&#45;header-element"-->
      <!--      ></div>-->
      <!--      <div class="photo-finish-&#45;&#45;header-element">-->
      <!--        Time: <span v-text="raceTime"></span>-->
      <!--      </div>-->
    </div>

    <table cellpadding="0" v-if="!showImage">
      <tr>
        <td
          class="photo-finish---td-results"
          :style="rs4ScoreboardOutput.options.ui.photoFinish.athleteTableCell"
        >
          <table>
            <tr :style="rs4ScoreboardOutput.options.ui.photoFinish.header">
              <th class="photo-finish---td-centre photo-finish---pos">Pos</th>
              <th class="photo-finish---td-centre photo-finish---result">
                Time
              </th>
              <th class="photo-finish---td-centre photo-finish---bib">Bib</th>
              <th class="photo-finish---td-centre-x photo-finish---athlete">
                <span style="padding-left: 50px">Athlete</span>
              </th>
            </tr>

            <tr
              v-for="result in pageCycling.state.objectsToDisplay"
              :key="result.athleteid"
              class="photo-finish---row"
              :style="rs4ScoreboardOutput.options.ui.photoFinish.row"
            >
              <td
                v-text="getPlace(result)"
                class="
                  photo-finish---td photo-finish---td-centre photo-finish---pos
                "
              ></td>
              <td class="photo-finish---td photo-finish---result">
                <div
                  v-text="getResult(result)"
                  style="width: 60%; text-align: right"
                ></div>
                <!--TODO...move into component so only run through logic once.-->
                <div
                  v-if="getScoreText(result).length > 0"
                  v-text="getScoreText(result)"
                  style="text-align: center; color: yellow"
                ></div>
              </td>
              <td
                v-text="result.options.bibNo"
                class="
                  photo-finish---td photo-finish---td-centre photo-finish---bib
                "
              ></td>
              <td class="photo-finish---td photo-finish---athlete">
                <div class="photo-finish---athlete-wrapper">
                  <div class="e4s-flex-row e4s-gap--standard">
                    <EaAward :ea-level="result.eaAward" style="width: 35px" />

                    <div class="e4s-flex-column">
                      <span v-text="result.options.athlete"></span>
                      <span
                        v-text="result.options.club"
                        class="photo-finish---club"
                      ></span>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </table>

          <div
            class="scoreboard-out-display-v2----page-count"
            :style="rs4ScoreboardOutput.options.ui.photoFinish.paging"
          >
            Page: <span v-text="pageCycling.state.pageDisplaying"></span> /
            <span v-text="pageCycling.state.pagesTotal"></span>
          </div>
        </td>
      </tr>
    </table>

    <div class="photo-finish-v3--image" v-if="showImage">
      <img
        alt="Photo Finish Image"
        :src="photoFinishLink"
        class="photo-finish---image"
        style="width: 100%"
        :style="rs4ScoreboardOutput.options.ui.photoFinish.image"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  SetupContext,
  computed,
  PropType,
  watch,
  ref,
} from "vue";
import {
  IR4sPhotoFinishResult,
  IR4sSocketPhotoFinishMessage,
  IRs4ScoreboardOutput,
} from "../../../../rs4/rs4-scoreboard-models";
import { IScoreboardPhotoFinishState } from "../../scoreboard-photo-finish/scoreboard-photo-finish-models";
import { ScoreboardOutputService } from "../../../scoreboard-output-service";
import { Rs4Service } from "../../../../rs4/rs4-service";
import { usePageCycling } from "../../../../../../common/ui/page-cycling/usePageCycling";
import * as PhotoFinishService from "./photo-finish-service";
import EaAward from "../../../../../../common/ui/ea-award/EaAward.vue";
import { IR4sSocketDataMessageDatav3 } from "../useScoreboardOutputDisplayV3";

const scoreboardOutputService = new ScoreboardOutputService();
const rs4Service = new Rs4Service();

export default defineComponent({
  name: "PhotoFinishV3",
  components: { EaAward },
  props: {
    rs4ScoreboardOutput: {
      type: Object as PropType<IRs4ScoreboardOutput>,
      default: () => {
        return scoreboardOutputService.factoryScoreboardOutput();
      },
    },
    socketDataMessage: {
      type: Object as PropType<IR4sSocketPhotoFinishMessage>,
      required: true,
    },
  },
  setup(
    props: {
      rs4ScoreboardOutput: IRs4ScoreboardOutput;
      socketDataMessage: IR4sSocketPhotoFinishMessage;
    },
    context: SetupContext
  ) {
    let state: IScoreboardPhotoFinishState = reactive({
      socketPhotoFinishMessage: props.socketDataMessage,
      results: [],
      resultsToDisplay: [],
    });
    const showImage = ref(false);
    const photoFinishLink = ref("");

    const pageCycling = usePageCycling<IR4sPhotoFinishResult>({
      pageCycleMs:
        props.rs4ScoreboardOutput.options.timings.ranking.pageCycleMs,
      pageSize: props.rs4ScoreboardOutput.options.timings.ranking.pageSize,
      onFinishPageCycle: (pageNumber: number) => {
        //  TODO if the output says to show the image, then show it....but for now, just show it.
        photoFinishLink.value = PhotoFinishService.getPhotoFinishLink(
          props.socketDataMessage
        );
        showImage.value = true;

        console.log("photoFinishLink.value >>>>>: ", photoFinishLink.value);

        let pageCycleMs =
          props.rs4ScoreboardOutput.options.timings.ranking.pageCycleMs;
        const message =
          props.socketDataMessage as any as IR4sSocketDataMessageDatav3;
        if (message.meta) {
          pageCycleMs = message.meta.displayTimeOutMs;
        }

        window.setTimeout(() => {
          showImage.value = false;
          context.emit("onFinishPageCycle", pageNumber);
        }, pageCycleMs);
      },
    });

    init(props.socketDataMessage.payload.results);

    watch(
      () => props.socketDataMessage,
      (
        newValue: IR4sSocketPhotoFinishMessage,
        oldValue: IR4sSocketPhotoFinishMessage
      ) => {
        init(newValue.payload.results);
      }
    );

    function init(results: IR4sPhotoFinishResult[]) {
      state.results = rs4Service.sortPhotoFinishResults(results);

      let pageCycleMs =
        props.rs4ScoreboardOutput.options.timings.ranking.pageCycleMs;
      const message =
        props.socketDataMessage as any as IR4sSocketDataMessageDatav3;
      if (message.meta) {
        pageCycleMs = message.meta.displayTimeOutMs;
      }

      pageCycling.setData(state.results, {
        pageSize: props.rs4ScoreboardOutput.options.timings.ranking.pageSize,
        pageCycleMs: pageCycleMs,
      });
    }

    const windSpeed = computed(() => {
      if (state.results.length === 0) {
        return;
      }
      return state.results[0].options.ws;
    });

    const raceTime = computed(() => {
      if (state.results.length === 0) {
        return;
      }
      return state.results[0].options.eventTime;
    });

    function getResult(result: IR4sPhotoFinishResult): string {
      return PhotoFinishService.getResultForDisplay(result);
    }

    function getScoreText(result: IR4sPhotoFinishResult): string {
      if (result.options.scoreText) {
        return result.options.scoreText;
      }
      return "";
    }

    function getPlace(result: IR4sPhotoFinishResult): string {
      if (result.options.place === 0) {
        return "-";
      }
      return result.options.place.toString();
    }

    return {
      state,
      windSpeed,
      raceTime,
      pageCycling,
      photoFinishLink,
      getResult,
      getScoreText,
      getPlace,
      showImage,
    };
  },
});
</script>

<style>
.photo-finish---wrapper {
  height: 80vh;
}

.photo-finish---header {
  /*float: left;*/
  font-weight: 500;
  /*position: absolute;*/
  width: 100%;
  padding: 0 0 0 1em;
  background-color: black;
  color: white;
}

.photo-finish---header-element {
  margin-right: 5em;
  display: inline-block;
  /*width: 25%;*/
  font-size: 1.25em;
  font-weight: 500;
}

.photo-finish---row {
  border-bottom: 1px solid white;
}

.photo-finish---td {
  padding: 5px;
  vertical-align: top;
}

.photo-finish---td-results {
  width: 40%;
  vertical-align: top;
  font-weight: 500;
}

.photo-finish---td-club {
  /*font-weight: 400;*/
}

.photo-finish---pos {
  width: 10%;
}

.photo-finish---result {
  width: 20%;
}

.photo-finish---bib {
  width: 10%;
}

.photo-finish---athlete-wrapper {
  display: flex;
}

.photo-finish---athlete {
  width: auto;
}

.photo-finish---td-image {
  /*width: 75%;*/
  vertical-align: top;
}

.photo-finish---td-centre {
  text-align: center;
}

.photo-finish-v3--image {
  /*height: 75vh;*/
  width: 100vw;
  /*border: 5px solid #fab001;*/
}
</style>
