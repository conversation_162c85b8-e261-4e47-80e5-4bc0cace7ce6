import * as ScoreboardOutputDisplayServiceV3 from "./scoreboard-output-display-service-v3";
import { simpleClone } from "../../../../../common/common-service-utils";
import {
  IR4sSocketDataMessageDatav3,
  IScoreboardOutputDisplayStateV3,
} from "./useScoreboardOutputDisplayV3";
import { scoreboardMockState } from "./mock/scoreboard-mock-state";
import { socketAthleteFieldResultsHammerMock } from "./track/mock-data/scoreboard-track-data-mock-hammer-men";
import { confirmHeatMockData300m } from "./confirm-heat/confirm-heat-mock-data/confirm-heat-mock-data-300m";
import { scoreboardMockSchedule } from "./mock/scoreboard-mock-schedule";

describe("ScoreboardOutputDisplayServiceV3", () => {
  test("getMessagePositionInQueue", () => {
    const message1: IR4sSocketDataMessageDatav3 = {
      action: "photofinish",
      key: "T1",
      comp: {
        id: "1",
      },
    } as any as IR4sSocketDataMessageDatav3;

    const message2: IR4sSocketDataMessageDatav3 = {
      action: "photofinish",
      key: "T2",
      comp: {
        id: "1",
      },
    } as any as IR4sSocketDataMessageDatav3;

    const messagesQueue: IR4sSocketDataMessageDatav3[] = [];

    expect(
      ScoreboardOutputDisplayServiceV3.getMessagePositionInQueue(
        message1,
        messagesQueue
      )
    ).toBe(-1);

    messagesQueue.push(simpleClone(message1));
    messagesQueue.push(simpleClone(message2));

    expect(
      ScoreboardOutputDisplayServiceV3.getMessagePositionInQueue(
        message1,
        messagesQueue
      )
    ).toBe(0);

    expect(
      ScoreboardOutputDisplayServiceV3.getMessagePositionInQueue(
        message2,
        messagesQueue
      )
    ).toBe(1);

    expect(
      ScoreboardOutputDisplayServiceV3.isMessageAlreadyInQueue(
        message2,
        messagesQueue
      )
    ).toBe(true);
  });

  test("doesDomainMatch", () => {
    expect(
      ScoreboardOutputDisplayServiceV3.doesDomainMatch("a", ["a", "b"])
    ).toBe(true);

    expect(
      ScoreboardOutputDisplayServiceV3.doesDomainMatch(
        "dev.entry4sports.co.uk",
        ["https://dev.entry4sports.co.uk/#/", "b"]
      )
    ).toBe(true);
  });

  test("addMessageToQueue", () => {
    const messageDefault: IR4sSocketDataMessageDatav3 = {
      action: "photofinish",
      key: "T1",
      comp: {
        id: "1",
      },
      meta: {
        showNext: false,
        forceDisplay: false,
        updatedCount: 0,
      },
    } as any as IR4sSocketDataMessageDatav3;
    let queue: IR4sSocketDataMessageDatav3[] = [];

    const message1 = {
      ...simpleClone(messageDefault),
      key: "T1",
    } as any as IR4sSocketDataMessageDatav3;
    const message2 = {
      ...simpleClone(messageDefault),
      key: "T2",
    } as any as IR4sSocketDataMessageDatav3;
    const message3 = {
      ...simpleClone(messageDefault),
      key: "T3",
    } as any as IR4sSocketDataMessageDatav3;
    const message4 = {
      ...simpleClone(messageDefault),
      key: "T4",
    } as any as IR4sSocketDataMessageDatav3;

    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message1, queue);
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message2, queue);
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message3, queue);
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message4, queue);

    expect(queue.length).toBe(4);

    expect(queue[0].key).toBe("T1");
    expect(queue[1].key).toBe("T2");
    expect(queue[2].key).toBe("T3");
    expect(queue[3].key).toBe("T4");

    //  Adding T3 again but it's already in queue
    expect(queue[2].key).toBe("T3");
    expect(queue[2].meta.updatedCount).toBe(0);
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message3, queue);
    //  Queue stays same size
    expect(queue.length).toBe(4);
    //  and stays in same position
    expect(queue[2].key).toBe("T3");
    expect(queue[2].meta.updatedCount).toBe(1);

    //  Adding T3 again, it's already in queue but show it next...so move it.
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(
      {
        ...message3,
        meta: {
          showNext: true,
          forceDisplay: false,
        },
      } as any as IR4sSocketDataMessageDatav3,
      queue
    );
    expect(queue.length).toBe(4);

    expect(queue[0].key).toBe("T1");
    //  ...it's up next
    expect(queue[1].key).toBe("T3");
    expect(queue[2].key).toBe("T2");
    expect(queue[3].key).toBe("T4");

    const message5 = {
      ...simpleClone(messageDefault),
      key: "T5",
      comp: {
        id: "1",
      },
      meta: {
        showNext: true,
        forceDisplay: false,
      },
    } as any as IR4sSocketDataMessageDatav3;

    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message5, queue);
    expect(queue.length).toBe(5);
    expect(queue[0].key).toBe("T1");
    expect(queue[1].key).toBe("T5");
    expect(queue[2].key).toBe("T3");

    const message6 = {
      ...simpleClone(messageDefault),
      key: "T6",
      comp: {
        id: "1",
      },
      meta: {
        showNext: false,
        forceDisplay: true,
      },
    } as any as IR4sSocketDataMessageDatav3;
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message6, queue);
    expect(queue.length).toBe(6);
    expect(queue[0].key).toBe("T6");
    expect(queue[1].key).toBe("T1");
    expect(queue[2].key).toBe("T5");
    expect(queue[3].key).toBe("T3");

    // Nothing in queue
    const messageForce = {
      ...simpleClone(messageDefault),
      key: "T6",
      comp: {
        id: "1",
      },
      meta: {
        showNext: false,
        forceDisplay: true,
      },
    } as any as IR4sSocketDataMessageDatav3;
    queue = [];
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(
      messageForce,
      queue
    );
    expect(queue.length).toBe(1);

    // Next in queue
    const messageNext = {
      ...simpleClone(messageDefault),
      key: "T6",
      meta: {
        showNext: true,
        forceDisplay: false,
      },
    } as any as IR4sSocketDataMessageDatav3;
    queue = [];
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(
      messageNext,
      queue
    );
    expect(queue.length).toBe(1);

    queue = [];
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message1, queue);
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(
      messageNext,
      queue
    );
    expect(queue.length).toBe(2);
    expect(queue[0].key).toBe("T1");
    expect(queue[1].key).toBe("T6");

    queue = [];
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message1, queue);
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message2, queue);
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(
      messageNext,
      queue
    );
    expect(queue.length).toBe(3);
    expect(queue[0].key).toBe("T1");
    expect(queue[1].key).toBe("T6");
    expect(queue[2].key).toBe("T2");

    queue = [];
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(
      messageNext,
      queue
    );
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(
      messageNext,
      queue
    );
    expect(queue.length).toBe(1);
    expect(queue[0].key).toBe("T6");
  });

  test("canMessageBeDisplayedAgain", () => {
    const messageDefault: IR4sSocketDataMessageDatav3 = {
      action: "photofinish",
      key: "T1",
      comp: {
        id: "1",
      },
      meta: {
        showNext: false,
        forceDisplay: false,
        showNumberOfTimes: -1,
        displayedCount: 0,
      },
    } as any as IR4sSocketDataMessageDatav3;
    let message;

    message = simpleClone(messageDefault);
    message.meta.showNumberOfTimes = -1;
    message.meta.displayedCount = 0;
    expect(
      ScoreboardOutputDisplayServiceV3.canMessageBeDisplayedAgain(message)
    ).toBe(true);

    message = simpleClone(messageDefault);
    message.meta.showNumberOfTimes = -1;
    message.meta.displayedCount = 33;
    expect(
      ScoreboardOutputDisplayServiceV3.canMessageBeDisplayedAgain(message)
    ).toBe(true);

    message = simpleClone(messageDefault);
    message.meta.showNumberOfTimes = 1;
    message.meta.displayedCount = 0;
    expect(
      ScoreboardOutputDisplayServiceV3.canMessageBeDisplayedAgain(message)
    ).toBe(true);

    message = simpleClone(messageDefault);
    message.meta.showNumberOfTimes = 1;
    message.meta.displayedCount = 1;
    expect(
      ScoreboardOutputDisplayServiceV3.canMessageBeDisplayedAgain(message)
    ).toBe(false);

    message = simpleClone(messageDefault);
    message.meta.showNumberOfTimes = 1;
    message.meta.displayedCount = 2;
    expect(
      ScoreboardOutputDisplayServiceV3.canMessageBeDisplayedAgain(message)
    ).toBe(false);
  });

  test("getNextQueueState", () => {
    const messageDefault: IR4sSocketDataMessageDatav3 = {
      action: "photofinish",
      key: "T1",
      comp: {
        id: "1",
      },
      meta: {
        showNext: false,
        forceDisplay: false,
        showNumberOfTimes: -1,
      },
    } as any as IR4sSocketDataMessageDatav3;
    let queue: IR4sSocketDataMessageDatav3[] = [];

    const message1 = {
      ...simpleClone(messageDefault),
      key: "T1",
    } as any as IR4sSocketDataMessageDatav3;
    const message2 = {
      ...simpleClone(messageDefault),
      key: "T2",
    } as any as IR4sSocketDataMessageDatav3;
    const message3 = {
      ...simpleClone(messageDefault),
      key: "T3",
    } as any as IR4sSocketDataMessageDatav3;
    const message4 = {
      ...simpleClone(messageDefault),
      key: "T4",
    } as any as IR4sSocketDataMessageDatav3;

    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message1, queue);
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message2, queue);
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message3, queue);
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message4, queue);
    expect(queue.length).toBe(4);

    expect(queue[0].key).toBe("T1");
    queue = ScoreboardOutputDisplayServiceV3.getNextQueueState(queue, 5);
    expect(queue.length).toBe(4);
    expect(queue[0].key).toBe("T2");
    queue = ScoreboardOutputDisplayServiceV3.getNextQueueState(queue, 5);
    expect(queue[0].key).toBe("T3");
    queue = ScoreboardOutputDisplayServiceV3.getNextQueueState(queue, 5);
    expect(queue[0].key).toBe("T4");
    queue = ScoreboardOutputDisplayServiceV3.getNextQueueState(queue, 5);
    expect(queue[0].key).toBe("T1");

    //
    const mess1 = simpleClone(message1);
    mess1.meta.showNumberOfTimes = -1;
    mess1.meta.displayedCount = 0;

    const mess2 = simpleClone(message2);
    mess2.meta.showNumberOfTimes = 1;
    mess2.meta.displayedCount = 0;

    const mess3 = simpleClone(message3);
    mess3.meta.showNumberOfTimes = -1;
    mess3.meta.displayedCount = 1;
    expect(
      ScoreboardOutputDisplayServiceV3.canMessageBeDisplayedAgain(mess3)
    ).toBe(true);

    const mess4 = simpleClone(message4);
    mess4.meta.showNumberOfTimes = -1;
    mess4.meta.displayedCount = 1;

    queue = [];
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(mess1, queue);
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(mess2, queue);
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(mess3, queue);
    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(mess4, queue);

    queue = ScoreboardOutputDisplayServiceV3.getNextQueueState(queue, 5);
    expect(queue.length).toBe(4);
  });

  test("getNextQueueState over queue size", () => {
    let queue: IR4sSocketDataMessageDatav3[] = [];

    const message1 = {
      key: "T1",
      comp: {
        id: "1",
      },
      meta: {
        showNext: false,
        forceDisplay: false,
        showNumberOfTimes: 2,
        displayedCount: 0,
      },
    } as any as IR4sSocketDataMessageDatav3;

    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message1, queue);
    expect(queue.length).toBe(1);
    expect(
      ScoreboardOutputDisplayServiceV3.canMessageBeDisplayedAgain(message1)
    ).toBe(true);

    const message2 = {
      key: "T2",
      comp: {
        id: "1",
      },
      meta: {
        showNext: false,
        forceDisplay: false,
        showNumberOfTimes: 2,
        displayedCount: 0,
      },
    } as any as IR4sSocketDataMessageDatav3;

    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message2, queue);
    expect(queue.length).toBe(2);
    expect(
      ScoreboardOutputDisplayServiceV3.canMessageBeDisplayedAgain(message2)
    ).toBe(true);

    const message3 = {
      key: "T3",
      comp: {
        id: "1",
      },
      meta: {
        showNext: false,
        forceDisplay: false,
        showNumberOfTimes: 2,
        displayedCount: 0,
      },
    } as any as IR4sSocketDataMessageDatav3;

    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message3, queue);
    expect(queue.length).toBe(3);
    expect(
      ScoreboardOutputDisplayServiceV3.canMessageBeDisplayedAgain(message3)
    ).toBe(true);

    //  First loop T1 at top
    expect(queue[0].key).toBe("T1");
    queue =
      ScoreboardOutputDisplayServiceV3.messageInQueueFinishedDisplaying(queue);
    expect(queue[0].meta.displayedCount).toBe(1);
    queue = ScoreboardOutputDisplayServiceV3.getNextQueueState(queue, 3);
    expect(queue.length).toBe(3);
    expect(queue[0].key).toBe("T2");
    expect(queue[0].meta.displayedCount).toBe(0);
    expect(queue[1].key).toBe("T3");
    expect(queue[1].meta.displayedCount).toBe(0);
    expect(queue[2].key).toBe("T1");
    expect(queue[2].meta.displayedCount).toBe(1);

    //  Second loop T2 at top
    expect(queue[0].key).toBe("T2");
    queue =
      ScoreboardOutputDisplayServiceV3.messageInQueueFinishedDisplaying(queue);
    expect(queue[0].meta.displayedCount).toBe(1);
    queue = ScoreboardOutputDisplayServiceV3.getNextQueueState(queue, 3);
    expect(queue.length).toBe(3);
    expect(queue[0].key).toBe("T3");
    expect(queue[0].meta.displayedCount).toBe(0);
    expect(queue[1].key).toBe("T1");
    expect(queue[1].meta.displayedCount).toBe(1);
    expect(queue[2].key).toBe("T2");
    expect(queue[2].meta.displayedCount).toBe(1);

    //  third loop T3 at top
    expect(queue[0].key).toBe("T3");
    queue =
      ScoreboardOutputDisplayServiceV3.messageInQueueFinishedDisplaying(queue);
    expect(queue[0].meta.displayedCount).toBe(1);
    queue = ScoreboardOutputDisplayServiceV3.getNextQueueState(queue, 3);
    expect(queue.length).toBe(3);
    expect(queue[0].key).toBe("T1");
    expect(queue[0].meta.displayedCount).toBe(1);
    expect(queue[1].key).toBe("T2");
    expect(queue[1].meta.displayedCount).toBe(1);
    expect(queue[2].key).toBe("T3");
    expect(queue[2].meta.displayedCount).toBe(1);

    //  forth loop T1 back at top
    expect(queue[0].key).toBe("T1");
    expect(queue[0].meta.displayedCount).toBe(1);
    queue =
      ScoreboardOutputDisplayServiceV3.messageInQueueFinishedDisplaying(queue);
    expect(queue[0].meta.displayedCount).toBe(2);
    queue = ScoreboardOutputDisplayServiceV3.getNextQueueState(queue, 3);
    expect(queue.length).toBe(2);
    expect(queue[0].key).toBe("T2");
    expect(queue[0].meta.displayedCount).toBe(1);
    expect(queue[1].key).toBe("T3");
    expect(queue[1].meta.displayedCount).toBe(1);
  });

  test("cancelMessage", () => {
    let queue: IR4sSocketDataMessageDatav3[] = [];

    const message1 = {
      key: "T1",
      comp: {
        id: "1",
      },
      meta: {
        showNext: false,
        forceDisplay: false,
        showNumberOfTimes: 2,
        displayedCount: 0,
      },
    } as any as IR4sSocketDataMessageDatav3;

    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message1, queue);
    expect(queue.length).toBe(1);
    expect(
      ScoreboardOutputDisplayServiceV3.canMessageBeDisplayedAgain(message1)
    ).toBe(true);

    const message2 = {
      key: "T2",
      comp: {
        id: "1",
      },
      meta: {
        showNext: false,
        forceDisplay: false,
        showNumberOfTimes: 2,
        displayedCount: 0,
      },
    } as any as IR4sSocketDataMessageDatav3;

    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message2, queue);
    expect(queue.length).toBe(2);
    expect(
      ScoreboardOutputDisplayServiceV3.canMessageBeDisplayedAgain(message2)
    ).toBe(true);

    const message3 = {
      key: "T3",
      comp: {
        id: "1",
      },
      meta: {
        showNext: false,
        forceDisplay: false,
        showNumberOfTimes: 2,
        displayedCount: 0,
      },
    } as any as IR4sSocketDataMessageDatav3;

    queue = ScoreboardOutputDisplayServiceV3.addMessageToQueue(message3, queue);
    expect(queue.length).toBe(3);
    expect(
      ScoreboardOutputDisplayServiceV3.canMessageBeDisplayedAgain(message3)
    ).toBe(true);

    expect(queue.length).toBe(3);
    expect(queue[2].key).toBe("T3");

    queue = ScoreboardOutputDisplayServiceV3.cancelMessage(
      simpleClone(message2),
      queue
    );
    expect(queue.length).toBe(2);
    expect(queue[0].key).toBe("T1");
    expect(queue[1].key).toBe("T3");
  });

  test("shouldMessageBeDisplayed", () => {
    let res;
    const mockState: IScoreboardOutputDisplayStateV3 =
      simpleClone(scoreboardMockState);

    res = ScoreboardOutputDisplayServiceV3.shouldMessageBeDisplayed(
      socketAthleteFieldResultsHammerMock as any as IR4sSocketDataMessageDatav3,
      mockState
    );
    expect(res.should).toBe(false);
    expect(res.reason).toBe(
      "field-results: is not in the list of message types to listen for: photofinish, confirmHeat, message, output-schedule--reload, output-schedule--message-relay"
    );

    mockState.ui.listenForMessageTypes.push("field-results");
    res = ScoreboardOutputDisplayServiceV3.shouldMessageBeDisplayed(
      socketAthleteFieldResultsHammerMock as any as IR4sSocketDataMessageDatav3,
      mockState
    );
    expect(res.should).toBe(true);
  });

  test("getSocketMessageKey", () => {
    let res;

    res = ScoreboardOutputDisplayServiceV3.getSocketMessageKey(
      simpleClone(
        socketAthleteFieldResultsHammerMock
      ) as any as IR4sSocketDataMessageDatav3
    );
    expect(res).toBe("351-field-results-9713");

    res = ScoreboardOutputDisplayServiceV3.getSocketMessageKey(
      simpleClone(confirmHeatMockData300m) as any as IR4sSocketDataMessageDatav3
    );
    expect(res).toBe("417-confirmHeat-7927-2");
  });

  test("getEventName", () => {
    let res;

    res = ScoreboardOutputDisplayServiceV3.getEventName(
      simpleClone(
        socketAthleteFieldResultsHammerMock
      ) as any as IR4sSocketDataMessageDatav3
    );
    expect(res).toBe("F6 : Hammer M60-75");

    res = ScoreboardOutputDisplayServiceV3.getEventName(
      simpleClone(confirmHeatMockData300m) as any as IR4sSocketDataMessageDatav3
    );
    expect(res).toBe("T23: 300m U15B, Race: 2");
  });

  test("getEventsFromSchedules", () => {
    let res;

    res = ScoreboardOutputDisplayServiceV3.getEventsFromSchedules(
      simpleClone(scoreboardMockSchedule)
    );
    expect(res["5379"].id).toBe(5379);
    expect(res["5290"].id).toBe(5290);
    expect(res["9999"].id).toBe(9999);
  });
});
