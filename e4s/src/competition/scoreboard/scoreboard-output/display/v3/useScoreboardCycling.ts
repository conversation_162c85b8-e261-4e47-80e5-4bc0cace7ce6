import {
  IR4sSocketDataMessageDatav3,
  ISocketAthletesMetaV3,
  ISocketMetaV3,
} from "./useScoreboardOutputDisplayV3";
import {
  IUsePageCyclingConfig,
  usePageCycling,
} from "../../../../../common/ui/page-cycling/usePageCycling";

export interface IScoreboardCyclingInput {
  socketDataMessageDatav3: IR4sSocketDataMessageDatav3;
  getPagingData: (socketDataMessageDatav3: IR4sSocketDataMessageDatav3) => unknown[];
  onFinish: (socketDataMessageDatav3: IR4sSocketDataMessageDatav3) => void;
}

export function factoryScoreboardCycling(
  scoreboardCyclingInput: IScoreboardCyclingInput
) {
  const meta: ISocketMetaV3 | ISocketAthletesMetaV3 =
    scoreboardCyclingInput.socketDataMessageDatav3.meta;

  const usePageCyclingConfig: IUsePageCyclingConfig<ISocketMetaV3> = {
    pageSize: 1,
    pageCycleMs: meta.displayTimeOutMs,
  };

  if ("pageSize" in meta) {
    usePageCyclingConfig.pageSize = (meta as ISocketAthletesMetaV3).pageSize;
    usePageCyclingConfig.pageCycleMs = (
      meta as ISocketAthletesMetaV3
    ).pageAthleteCycleMs;
  }

  const pageCycling =
    usePageCycling<IR4sSocketDataMessageDatav3>(usePageCyclingConfig);


  return {
    pageCycling,
  };
}
