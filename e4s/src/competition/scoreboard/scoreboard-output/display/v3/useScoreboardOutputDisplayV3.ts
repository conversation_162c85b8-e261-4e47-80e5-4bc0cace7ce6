import { computed, reactive, SetupContext } from "vue";
import { useSimpleQueue } from "../../../../../common/collections/simplequeue/useSimpleQueue";
import {
  IR4sSocketDataMessageData,
  R4S_SOCKET_ACTIONS,
  SocketAction,
  SocketActionData,
  SocketActionMetaConfig,
} from "../../../rs4/rs4-scoreboard-models";

import {
  convertDateToIsoWithOffset,
  simpleClone,
} from "../../../../../common/common-service-utils";
import {
  ISocketControllerInput,
  useSocketController,
} from "../../../../../socket/useSocketController";
import * as ScoreboardOutputDisplayServiceV3 from "./scoreboard-output-display-service-v3";
import {
  IScoreboardSetUpState,
  useScoreboardSetUp,
} from "./useScoreboardSetUp";
import * as ScoreboardOutputDisplayData from "./scoreboard-output-display-data";
import { handleResponseMessages } from "../../../../../common/handle-http-reponse";
import { IShouldMessageBeDisplayedResult } from "./scoreboard-output-display-service-v3";
import {
  EventGroupIdString,
  IBaseConcrete,
  IsoTime,
} from "../../../../../common/common-models";

export type SocketV3UserAction = "FORCE" | "CANCEL" | "NEXT";
export type SocketV3UserActionPayload = {
  action: SocketV3UserAction;
  message: IR4sSocketDataMessageDatav3;
};

export type SectionType = "CLOCK" | "SCHEDULE" | "SCORES";
export type SectionAdminType =
  | "MESSAGES"
  | "MESSAGES_ALL"
  | "CONFIG"
  | "TESTING";

export interface ISocketMetaV3 {
  displayTimeOutMs: number; // how long to display message for
  minTimeToDisplayMs: number; // min time to display message for
  showNumberOfTimes: number | -1; // -1 = show forever
  displayedCount: number; // how many times has this message been displayed
  updatedCount: number; // how many times has this message been updated
  showNext: boolean; // show next message in queue
  forceDisplay: boolean; // force display of message
}

export type SocketMetaMapV3 = Record<SocketActionMetaConfig, ISocketMetaV3>;

export interface ISocketAthletesMetaV3 extends ISocketMetaV3 {
  // cycling through athletes, if no athletes, then set athletes to 1 and use displayTimeOutMs...?
  pageSize: number | 1; // how many athletes to display per page
  pageAthleteCycleMs: number; // how long to display each page of athletes for, e.g. 1500m might have 15 athletes.
}

export interface ISocketScoreboardConfigV3 {
  displayTime: number; // how long to display message for in seconds
  force: "false" | "true"; // "force" display of message or "next".
}

export interface IR4sSocketDataMessageDatav3 extends IR4sSocketDataMessageData {
  action: SocketActionData;
  meta: ISocketMetaV3 | ISocketAthletesMetaV3;
}

export interface IScoreboardMessageReason {
  time: IsoTime;
  shouldMessageBeDisplayedResult: IShouldMessageBeDisplayedResult;
  message: IR4sSocketDataMessageData;
}

export interface IScoreboardOutputDisplayStateV3 {
  scoreboardSetUpState: IScoreboardSetUpState;
  isLoading: boolean;
  messagesAll: IScoreboardMessageReason[];
  ignoreActionsNotWatching: boolean; //  If action like "e4s-init" is not being watched, ignore it.
  messagesLoop: IR4sSocketDataMessageDatav3[];
  queue: {
    maxQueueSize: number;
  };
  ui: {
    showSection: SectionType;
    showAdminSection: SectionAdminType;
    headerText: string;
    currentSocketDataMessageData: IR4sSocketDataMessageDatav3 | null;
    listenForMessageTypes: R4S_SOCKET_ACTIONS[];
    listenForMessageDataTypes: SocketActionData[];
    listenForMessageDomains: string[];
    listenForCompIds: number[];
    listenForEventGroupIds: number[];
    listenForEventGroupIdsMap: Record<EventGroupIdString, IBaseConcrete>;
  };
  loop: {
    position: number;
    messageTypesAllowed: SocketActionData[];
  };
  storage: {
    useLocalStorage: boolean;
  };
}

export function factoryScoreboardOutputDisplayControllerV3(
  scoreboardSetUpState: IScoreboardSetUpState
) {
  const defaultTitle =
    scoreboardSetUpState.scoreboardOutput.options.ui.header.defaultTitle;

  const state: IScoreboardOutputDisplayStateV3 = {
    scoreboardSetUpState,
    isLoading: false,
    messagesAll: [],
    ignoreActionsNotWatching: true,
    messagesLoop: [],
    queue: {
      maxQueueSize:
        scoreboardSetUpState.scoreboardOutput.options.timings.photoFinish
          .maxCount, //  TODO bad use of property.
    },
    storage: {
      useLocalStorage: true,
    },
    ui: {
      showSection: "CLOCK",
      showAdminSection: "MESSAGES",
      headerText:
        defaultTitle && defaultTitle.length > 0 ? defaultTitle : "Score Board",
      currentSocketDataMessageData: null,
      listenForMessageTypes: [
        "photofinish",
        "video",
        "confirmHeat",
        "message",
        "output-schedule--reload",
        "output-schedule--refresh",
        "output-schedule--reset",
        "output-schedule--message-relay",
        "field-results",
      ],
      listenForMessageDataTypes: [
        "photofinish",
        "video",
        "confirmHeat",
        "message",
        "field-results",
      ],
      listenForMessageDomains: [scoreboardSetUpState.inputConfig.domain],
      listenForCompIds: [scoreboardSetUpState.inputConfig.compId], //  TODO remove 417, 407
      listenForEventGroupIds: [],
      listenForEventGroupIdsMap: {},
    },
    loop: {
      position: 0,
      messageTypesAllowed: [
        "photofinish",
        "video",
        "confirmHeat",
        "field-results",
      ],
    },
  };

  const queuePersistent = window.localStorage;

  const simpleQueueAll = useSimpleQueue<IScoreboardMessageReason>(false, false);
  simpleQueueAll.setMaxSize(100);

  const simpleQueueLoop = useSimpleQueue<unknown>(false, false);
  simpleQueueLoop.setMaxSize(
    scoreboardSetUpState.scoreboardOutput.options.timings.photoFinish.maxCount
  );

  const socketControllerInput: ISocketControllerInput = {
    startImmediately: true,
    onMessage: socketReceiveMessage,
  };
  const socketController = useSocketController(socketControllerInput);

  init();

  function init() {
    if (state.storage.useLocalStorage) {
      loadFromLocalStorage();
    }

    //  This next block is stupid...mapping data from one place to another but
    //  it's the same!?!?!?

    const listenForOtherComps: string =
      typeof (scoreboardSetUpState.scoreboardOutput.options
        .listenForOtherComps as any as number) === "number"
        ? scoreboardSetUpState.scoreboardOutput.options.listenForOtherComps.toString()
        : scoreboardSetUpState.scoreboardOutput.options.listenForOtherComps;

    // if (
    //   state.scoreboardSetUpState.scoreboardOutput.options.listenForOtherComps
    //     .length > 0
    // ) {

    if (listenForOtherComps.length > 0) {
      state.ui.listenForCompIds = state.ui.listenForCompIds.concat(
        listenForOtherComps.split(",").map((id) => parseInt(id, 10))
      );
    }

    if (
      state.scoreboardSetUpState.scoreboardOutput.options.listenForOtherDomains
        .length > 0
    ) {
      state.ui.listenForMessageDomains =
        state.ui.listenForMessageDomains.concat(
          state.scoreboardSetUpState.scoreboardOutput.options.listenForOtherDomains.split(
            ","
          )
        );
    }

    if (
      state.scoreboardSetUpState.scoreboardOutput.options.listenForEventGroupIds
        .length > 0
    ) {
      state.ui.listenForEventGroupIds = state.ui.listenForEventGroupIds.concat(
        state.scoreboardSetUpState.scoreboardOutput.options.listenForEventGroupIds
          .split(",")
          .map((id) => Number(id))
      );
    }

    state.ui.listenForEventGroupIdsMap =
      ScoreboardOutputDisplayServiceV3.getEventsFromSchedules(
        state.scoreboardSetUpState.validScoreboardSchedules
      );
  }

  function setScoreboardSetUpState(setUpState: IScoreboardSetUpState) {
    state.scoreboardSetUpState = simpleClone(setUpState);
  }

  function addAllMessage(
    socketDataMessageData: IR4sSocketDataMessageData,
    shouldMessageBeDisplayedResult: IShouldMessageBeDisplayedResult
  ) {
    const socketDataMessageDataAction: R4S_SOCKET_ACTIONS =
      socketDataMessageData.action;
    if (
      state.ui.listenForMessageTypes.indexOf(socketDataMessageDataAction) === -1
    ) {
      return;
    }

    const scoreboardMessageReason: IScoreboardMessageReason = {
      time: convertDateToIsoWithOffset(new Date()),
      shouldMessageBeDisplayedResult,
      message: socketDataMessageData,
    };

    simpleQueueAll.addMessage(scoreboardMessageReason);
    state.messagesAll = simpleQueueAll.state.events;
  }

  function addMessage(socketDataMessageData: IR4sSocketDataMessageDatav3) {
    const action = socketDataMessageData.action;

    if (state.loop.messageTypesAllowed.indexOf(action) > -1) {
      const defaultSocketMeta: ISocketMetaV3 = {
        displayTimeOutMs: 5000, // how long to display message for
        minTimeToDisplayMs: 3000, // min time to display message for
        showNumberOfTimes: 1, // -1 = show forever
        displayedCount: 0, // how many times has this message been displayed
        updatedCount: 0, // how many times has this message been updated
        showNext: false, // show next message in queue
        forceDisplay: false, // force display of message
      };

      let socketMeta: ISocketMetaV3 | undefined =
        state.scoreboardSetUpState.scoreboardOutput.options.timings
          .socketMetaMap[action];

      if (!socketMeta) {
        socketMeta = defaultSocketMeta;
      }

      socketDataMessageData =
        ScoreboardOutputDisplayServiceV3.mergeInMessageMetaIfMissing(
          socketDataMessageData,
          socketMeta
        );

      //  TODO verify message has a key.

      state.messagesLoop = ScoreboardOutputDisplayServiceV3.addMessageToQueue(
        socketDataMessageData,
        state.messagesLoop
      );
    }
  }

  function goToNext() {
    const currentQueue =
      ScoreboardOutputDisplayServiceV3.messageInQueueFinishedDisplaying(
        state.messagesLoop
      );

    const nextQueueState = ScoreboardOutputDisplayServiceV3.getNextQueueState(
      currentQueue,
      state.queue.maxQueueSize
    );
    state.messagesLoop = nextQueueState;

    if (state.storage.useLocalStorage) {
      loadToLocalStorage();
    }

    if (state.messagesLoop.length === 0) {
      setCurrentMessage(null);
    } else {
      setCurrentMessage(state.messagesLoop[0]);
    }
  }

  function setCurrentMessage(message: IR4sSocketDataMessageDatav3 | null) {
    let defaultTitle =
      scoreboardSetUpState.scoreboardOutput.options.ui.header.defaultTitle;
    defaultTitle =
      defaultTitle && defaultTitle.length > 0 ? defaultTitle : "Score Board";
    if (!message) {
      state.ui.showSection = "CLOCK";
      state.ui.headerText = defaultTitle;
      return;
    }
    state.ui.showSection = "SCORES";
    state.ui.currentSocketDataMessageData = message;
    state.ui.headerText = message
      ? ScoreboardOutputDisplayServiceV3.getHeaderText(message)
      : defaultTitle;
  }

  function socketReceiveMessage(messageEvent: MessageEvent) {
    console.log(
      "factoryScoreboardOutputDisplayControllerV3.socketReceiveMessage A >>>>>>>>>>>>>>>" +
        messageEvent,
      simpleClone(messageEvent)
    );
    let data: IR4sSocketDataMessageData = JSON.parse(messageEvent.data);

    if (data.action === "output-schedule--message-relay") {
      //  Sent message from "Admin" screen.
      //  @ts-ignore
      data = data.payload.data;
    }

    if (data.action === "output-schedule--message-cancel") {
      //  Sent message from "Admin" screen.
      //  @ts-ignore
      data = data.payload.data;
      // data.action = "output-schedule--message-cancel";
      const queue = ScoreboardOutputDisplayServiceV3.cancelMessage(
        data as IR4sSocketDataMessageDatav3,
        state.messagesLoop
      );
      state.messagesLoop = simpleClone(queue);
      if (state.messagesLoop.length === 0) {
        setCurrentMessage(null);
      }
      return;
    }

    processMessage(data);
  }

  function processMessage(message: IR4sSocketDataMessageData) {
    const action = message.action;

    const shouldMessageBeDisplayedResult: IShouldMessageBeDisplayedResult =
      ScoreboardOutputDisplayServiceV3.shouldMessageBeDisplayed(
        message as IR4sSocketDataMessageDatav3,
        state
      );

    addAllMessage(message, shouldMessageBeDisplayedResult);

    //  don't even have a domain or compId match
    if (shouldMessageBeDisplayedResult.level < 4) {
      return;
    }

    if (action === "output-schedule--refresh") {
      getSchedule();
      return;
    }

    if (action === "output-schedule--reload") {
      window.location.reload();
      return;
    }

    if (action === "output-schedule--reset") {
      reset();
      return;
    }

    if (shouldMessageBeDisplayedResult.should) {
      addMessage(message as IR4sSocketDataMessageDatav3);

      //  First message to arrive.
      if (state.messagesLoop.length === 1) {
        setCurrentMessage(state.messagesLoop[0]);
      }

      return;
    }

    console.log(
      "factoryScoreboardOutputDisplayControllerV3.processMessage Z >>>>>>>>>>>>>>> action: " +
        action +
        " not handled"
    );
  }

  /**
   * We need some security around this.
   * @param socketDataMessageData
   * @param socketAction
   */
  function sendSocketMessage(
    socketAction: SocketAction,
    socketDataMessageData?: IR4sSocketDataMessageData
  ) {
    const prom = ScoreboardOutputDisplayData.sendSocketCommand(
      state.scoreboardSetUpState.competitionSummaryPublic.compId,
      state.scoreboardSetUpState.scoreboardOutput.id,
      socketAction,
      socketDataMessageData
    );
    handleResponseMessages(prom);
  }

  function getSchedule(): Promise<void> {
    return useScoreboardSetUp()
      .getSchedulesAllAndValid(
        state.scoreboardSetUpState.inputConfig.compId,
        state.scoreboardSetUpState.inputConfig.outputId
      )
      .then((resp) => {
        state.scoreboardSetUpState.scoreboardSchedules = resp.all;
        state.scoreboardSetUpState.validScoreboardSchedules = resp.valid;
      });
  }

  function doAction(payload: SocketV3UserActionPayload) {
    const payloadMessage = simpleClone(payload.message);
    if (payload.action === "FORCE") {
      payloadMessage.meta.forceDisplay = true;
    }

    if (payload.action === "NEXT") {
      payloadMessage.meta.showNext = true;
      payloadMessage.meta.forceDisplay = false;
    }

    if (payload.action === "FORCE" || payload.action === "NEXT") {
      sendSocketMessage(
        "output-schedule--message-relay" as SocketAction,
        payloadMessage
      );
      return;
    }

    if (payload.action === "CANCEL") {
      sendSocketMessage(
        "output-schedule--message-cancel" as SocketAction,
        payloadMessage
      );
    }
  }

  function destroy() {
    socketController.destroy();
  }

  function setAdminSection(section: SectionAdminType) {
    state.ui.showAdminSection = section;
  }

  function loadToLocalStorage() {
    queuePersistent.setItem("message-loop", JSON.stringify(state.messagesLoop));
  }

  function loadFromLocalStorage() {
    const messageLoop = queuePersistent.getItem("message-loop");
    if (messageLoop) {
      const messageLoopData = JSON.parse(messageLoop);
      state.messagesLoop = messageLoopData;
      if (messageLoopData.length > 0) {
        setCurrentMessage(messageLoopData[0]);
      }
    }
  }

  function reset() {
    state.ui.showSection = "CLOCK";
    setCurrentMessage(null);
    state.messagesLoop = [];
    queuePersistent.setItem("message-loop", JSON.stringify([]));
  }

  return {
    state,
    setScoreboardSetUpState,
    processMessage,
    socketController,
    destroy,
    scoreboardSetUpState,
    goToNext,
    doAction,
    sendSocketMessage,
    setAdminSection,
    loadFromLocalStorage,
    reset,
  };
}

export function useScoreboardOutputDisplay(
  scoreboardSetUpState: IScoreboardSetUpState,
  context: SetupContext
) {
  const controller =
    factoryScoreboardOutputDisplayControllerV3(scoreboardSetUpState);
  const state = reactive(controller.state);

  const getCurrentMessageAction = computed(() => {
    return state.ui.currentSocketDataMessageData
      ? state.ui.currentSocketDataMessageData.action
      : "";
  });

  const listeningToComps = computed(() => {
    return state.ui.listenForCompIds.join(", ");
  });
  const listeningToDomains = computed(() => {
    return state.ui.listenForMessageDomains.join(", ");
  });

  const listenToScheuledEvents = computed(() => {
    return Object.values(state.ui.listenForEventGroupIdsMap)
      .map((scheduledEvent) => {
        return scheduledEvent.id + ": " + scheduledEvent.name;
      })
      .join(", ");
  });

  return {
    state,
    controller,
    getCurrentMessageAction,
    listeningToComps,
    listeningToDomains,
    listenToScheuledEvents,
  };
}
