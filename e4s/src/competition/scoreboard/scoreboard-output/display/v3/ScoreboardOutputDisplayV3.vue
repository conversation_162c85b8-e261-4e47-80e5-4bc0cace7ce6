<template>
  <div
    class="scoreboard-out-display-v3"
    :style="
      scoreboardSetUpState.scoreboardOutput.options.ui.body.main +
      scoreboardSetUpState.scoreboardOutput.options.ui.body.all
    "
  >
    <!--    <div v-if="scoreboardOutputDisplay.state.ui.showSection !== 'SCORE'">-->
    <R4sRouteHeader
      :event-name="scoreboardOutputDisplay.state.ui.headerText"
      comp-name=""
      :rs4-scoreboard-output="
        scoreboardOutputDisplay.state.scoreboardSetUpState.scoreboardOutput
      "
    />

    <div
      class="r4s-body-v3"
      :class="
        scoreboardSetUpState.inputConfig.showControls
          ? 'r4s-body-v3--show-controls'
          : ''
      "
      :style="scoreboardSetUpState.scoreboardOutput.options.ui.body.all"
    >
      <div class="scoreboard-out-display-v3--header-sep"></div>
      <div class="scoreboard-out-display-v3--vert-spacer"></div>

      <div
        v-if="scoreboardOutputDisplay.state.ui.showSection === 'CLOCK'"
        :style="
          scoreboardOutputDisplay.state.scoreboardSetUpState.scoreboardOutput
            .options.ui.displayClock.body
        "
      >
        <DisplayClock
          :style="
            scoreboardOutputDisplay.state.scoreboardSetUpState.scoreboardOutput
              .options.ui.displayClock.time
          "
        />
      </div>

      <ScoreboardOutputDisplaySched
        v-if="scoreboardOutputDisplay.state.ui.showSection === 'SCHEDULE'"
        :scoreboard-schedules="
          scoreboardOutputDisplay.state.scoreboardSetUpState
            .validScoreboardSchedules
        "
      />

      <div v-if="scoreboardOutputDisplay.state.ui.showSection === 'SCORES'">
        <PhotoFinishV3
          v-if="
            scoreboardOutputDisplay.getCurrentMessageAction.value ===
            'photofinish'
          "
          :rs4-scoreboard-output="
            scoreboardOutputDisplay.state.scoreboardSetUpState.scoreboardOutput
          "
          :socket-data-message="
            scoreboardOutputDisplay.state.ui.currentSocketDataMessageData
          "
          v-on:onFinishPageCycle="scoreboardOutputDisplay.controller.goToNext()"
        />

        <ConfirmHeatWrapper
          v-if="
            scoreboardOutputDisplay.getCurrentMessageAction.value ===
            'confirmHeat'
          "
          :rs4-scoreboard-output="
            scoreboardOutputDisplay.state.scoreboardSetUpState.scoreboardOutput
          "
          :socket-data-message="
            scoreboardOutputDisplay.state.ui.currentSocketDataMessageData
          "
          v-on:onFinishPageCycle="scoreboardOutputDisplay.controller.goToNext()"
        />

        <ScoreboardTrackHeatRead
          v-if="
            scoreboardOutputDisplay.getCurrentMessageAction.value ===
            'field-results'
          "
          :rs4-scoreboard-output="
            scoreboardOutputDisplay.state.scoreboardSetUpState.scoreboardOutput
          "
          :socket-data-message="
            scoreboardOutputDisplay.state.ui.currentSocketDataMessageData
          "
          v-on:onFinishPageCycle="scoreboardOutputDisplay.controller.goToNext()"
        />

        <VideoV3
          v-if="
            scoreboardOutputDisplay.getCurrentMessageAction.value === 'video'
          "
          :rs4-scoreboard-output="
            scoreboardOutputDisplay.state.scoreboardSetUpState.scoreboardOutput
          "
          :socket-data-message="
            scoreboardOutputDisplay.state.ui.currentSocketDataMessageData
          "
          v-on:onFinishPageCycle="scoreboardOutputDisplay.controller.goToNext()"
        />
      </div>
    </div>

    <div
      v-if="scoreboardSetUpState.inputConfig.showControls"
      class="r4s-body-v3--admin-section e4s-flex-column e4s-gap--standard"
      style="padding: var(--e4s-gap--standard)"
    >
      <SectionLinksWrapper class="e4s-flex-column">
        <SectionLinkSimple
          style="min-width: 120px"
          link-id="MESSAGES"
          :is-active="
            scoreboardOutputDisplay.state.ui.showAdminSection === 'MESSAGES'
          "
          v-on:selected="
            scoreboardOutputDisplay.controller.setAdminSection('MESSAGES')
          "
        >
          <span slot="default" class="e4s-header--300">Messages</span>
        </SectionLinkSimple>

        <SectionLinkSimple
          style="min-width: 120px"
          link-id="ALL_MESSAGES"
          :is-active="
            scoreboardOutputDisplay.state.ui.showAdminSection === 'MESSAGES_ALL'
          "
          v-on:selected="
            scoreboardOutputDisplay.controller.setAdminSection('MESSAGES_ALL')
          "
        >
          <span slot="default" class="e4s-header--300">All Messages</span>
        </SectionLinkSimple>

        <SectionLinkSimple
          style="min-width: 120px"
          link-id="CONFIG"
          :is-active="
            scoreboardOutputDisplay.state.ui.showAdminSection === 'CONFIG'
          "
          v-on:selected="
            scoreboardOutputDisplay.controller.setAdminSection('CONFIG')
          "
        >
          <span slot="default" class="e4s-header--300">Config</span>
        </SectionLinkSimple>

        <SectionLinkSimple
          v-if="configController.isAdmin.value"
          style="min-width: 120px"
          link-id="TESTING"
          :is-active="
            scoreboardOutputDisplay.state.ui.showAdminSection === 'TESTING'
          "
          v-on:selected="
            scoreboardOutputDisplay.controller.setAdminSection('TESTING')
          "
        >
          <span slot="default" class="e4s-header--300">Testing</span>
        </SectionLinkSimple>
      </SectionLinksWrapper>

      <div
        class="e4s-flex-column e4s-gap--standard"
        v-if="scoreboardOutputDisplay.state.ui.showAdminSection === 'MESSAGES'"
      >
        <div class="e4s-flex-row e4s-gap--standard">
          Use Local Storage:
          <FieldCheckboxV2
            v-model="scoreboardOutputDisplay.state.storage.useLocalStorage"
          />
        </div>

        <!--        <div class="e4s-flex-row e4s-gap&#45;&#45;standard">-->
        <!--          <div class="e4s-flex-row e4s-gap&#45;&#45;standard">-->
        <!--            Debug Mode (accepts all events):-->
        <!--            <span-->
        <!--              v-model="-->
        <!--                scoreboardSetUpState.scoreboardOutput.options.inDebugMode-->
        <!--              "-->
        <!--            ></span>-->
        <!--          </div>-->
        <!--          <div class="e4s-flex-row e4s-gap&#45;&#45;standard">-->
        <!--            Comp Ids:-->
        <!--            <span>{{ scoreboardOutputDisplay.listeningToComps.value }}</span>-->
        <!--          </div>-->
        <!--          <div class="e4s-flex-row e4s-gap&#45;&#45;standard">-->
        <!--            Domains:-->
        <!--            <span>{{ scoreboardOutputDisplay.listeningToDomains.value }}</span>-->
        <!--          </div>-->
        <!--        </div>-->

        <ScoreboardOutputTableV3
          :messages="scoreboardOutputDisplay.state.messagesLoop"
          v-on:action="scoreboardOutputDisplay.controller.doAction"
        />
      </div>

      <ScoreboardOutputTableAllV3
        v-if="
          scoreboardOutputDisplay.state.ui.showAdminSection === 'MESSAGES_ALL'
        "
        :scoreboard-message-reasons="scoreboardOutputDisplay.state.messagesAll"
      />

      <div
        class="e4s-flex-column e4s-gap--standard"
        v-if="scoreboardOutputDisplay.state.ui.showAdminSection === 'CONFIG'"
      >
        <div class="e4s-flex-row e4s-gap--standard">
          Debug Mode (accepts all events):
          <span
            v-model="scoreboardSetUpState.scoreboardOutput.options.inDebugMode"
          ></span>
        </div>
        <div class="e4s-flex-row e4s-gap--standard">
          Comp Ids:
          <span>{{ scoreboardOutputDisplay.listeningToComps.value }}</span>
        </div>
        <div class="e4s-flex-row e4s-gap--standard">
          Domains:
          <span>{{ scoreboardOutputDisplay.listeningToDomains.value }}</span>
        </div>

        <div class="e4s-flex-row e4s-gap--standard">
          Events:
          <span>{{
            scoreboardOutputDisplay.listenToScheuledEvents.value
          }}</span>
        </div>

        <div class="e4s-flex-row e4s-gap--standard">
          <div
            class="e4s-flex-row e4s-justify-flex-space-between e4s-full-width"
          >
            <div class="e4s-flex-column e4s-gap--standard">
              <div class="e4s-flex-row e4s-gap--standard e4s-flex-center">
                <ButtonGenericV2
                  text="Refresh"
                  v-on:click="
                    scoreboardOutputDisplay.controller.sendSocketMessage(
                      'output-schedule--refresh'
                    )
                  "
                />

                <p>
                  Refreshes the config for all scoreboards listening to this
                  competition. E.g. you have changed what a scoreboard is
                  listening to or the CSS has been changed.
                </p>
              </div>

              <div class="e4s-flex-row e4s-gap--standard e4s-flex-center">
                <ButtonGenericV2
                  text="Reset"
                  v-on:click="
                    scoreboardOutputDisplay.controller.sendSocketMessage(
                      'output-schedule--reset'
                    )
                  "
                />

                <p>
                  Edge cases can cause the scoreboards local data cache to get
                  out of sync. This will reset the cache.
                </p>
              </div>

              <div class="e4s-flex-row e4s-gap--standard e4s-flex-center">
                <ButtonGenericV2
                  text="Reload"
                  v-on:click="
                    scoreboardOutputDisplay.controller.sendSocketMessage(
                      'output-schedule--reload'
                    )
                  "
                />

                <p>
                  Reloads all scoreboards listening to this competition. Most
                  likely debugging or released a new code stream.
                </p>
              </div>
            </div>

            <ScoreboardGoTo
              :comp-id="
                scoreboardOutputDisplay.state.scoreboardSetUpState
                  .competitionSummaryPublic.compId
              "
            />
          </div>
        </div>
      </div>

      <div
        v-if="scoreboardOutputDisplay.state.ui.showAdminSection === 'TESTING'"
      >
        <div class="e4s-flex-column">
          <div>
            Force:
            <FieldCheckboxV2 v-model="socketAthletesMetaV3.forceDisplay" /> Show
            Next: <FieldCheckboxV2 v-model="socketAthletesMetaV3.showNext" />

            Displayed Count:
            <FieldNumberV2 v-model="socketAthletesMetaV3.displayedCount" /> Show
            #:
            <FieldNumberV2 v-model="socketAthletesMetaV3.showNumberOfTimes" />
            Updated Count:
            <FieldNumberV2 v-model="socketAthletesMetaV3.updatedCount" />
            DisplayTimeOutMs:
            <FieldNumberV2 v-model="socketAthletesMetaV3.displayTimeOutMs" />
            minTimeToDisplayMs:
            <FieldNumberV2 v-model="socketAthletesMetaV3.minTimeToDisplayMs" />
            <ButtonGenericV2 text="Test" v-on:click="testPush" />

            <ButtonGenericV2
              text="Advert"
              v-on:click="testLoadPhototFinishWithAdvert"
            />

            <ButtonGenericV2
              text="Lynx"
              v-on:click="testLoadPhototFinishWithLynx"
            />
          </div>
        </div>

        <div class="e4s-flex-row e4s-gap--standard">
          <div>
            loop:
            <span
              v-text="scoreboardOutputDisplay.state.messagesLoop.length"
            ></span>
          </div>
          <ButtonGenericV2 text="confirm 1" v-on:click="testConfirm1" />
          <ButtonGenericV2
            text="confirm random"
            v-on:click="testConfirmRandom"
          />
          <ButtonGenericV2 text="confirm force" v-on:click="testConfirmForce" />
          <ButtonGenericV2
            text="High"
            button-type="secondary"
            v-on:click="testField('HJ')"
          />
          <ButtonGenericV2
            text="Hammer"
            button-type="secondary"
            v-on:click="testField('Hammer')"
          />
          <ButtonGenericV2
            text="Shot"
            button-type="secondary"
            v-on:click="testField('Shot')"
          />
          <ButtonGenericV2 text="confirm next" v-on:click="testConfirmNext" />
          <ButtonGenericV2
            text="NEXT"
            v-on:click="scoreboardOutputDisplay.controller.goToNext()"
          />
        </div>
      </div>
    </div>

    <R4sFooterV3
      :comp-id="
        scoreboardOutputDisplay.state.scoreboardSetUpState
          .competitionSummaryPublic.compId
      "
      :current-socket-state="
        scoreboardOutputDisplay.controller.socketController.socketConfig
          .currentSocketState
      "
      :rs4-scoreboard-output="
        scoreboardOutputDisplay.state.scoreboardSetUpState.scoreboardOutput
      "
    />
    <!--    <R4sFooter-->
    <!--      :comp-id="-->
    <!--        scoreboardOutputDisplay.state.scoreboardSetUpState-->
    <!--          .competitionSummaryPublic.compId-->
    <!--      "-->
    <!--      :current-socket-state="-->
    <!--        scoreboardOutputDisplay.controller.socketController.socketConfig-->
    <!--          .currentSocketState-->
    <!--      "-->
    <!--      :output-name="-->
    <!--        scoreboardOutputDisplay.state.scoreboardSetUpState.scoreboardOutput-->
    <!--          .description-->
    <!--      "-->
    <!--    />-->

    <LoadingSpinnerV2 v-if="scoreboardOutputDisplay.state.isLoading" />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "vue";
import {
  ISocketAthletesMetaV3,
  useScoreboardOutputDisplay,
} from "./useScoreboardOutputDisplayV3";
import R4sRouteHeader from "../../../rs4/r4s-route-header.vue";
import LoadingSpinnerV2 from "../../../../../common/ui/loading-spinner-v2.vue";
import ScoreboardOutputDisplaySched from "../scoreboard-output-display-sched.vue";
import ButtonGenericV2 from "../../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { confirmHeatSocketDataMessageDataMock1 } from "./scoreboard-output-display-mocks-v3";
import { IScoreboardSetUpState } from "./useScoreboardSetUp";
import ScoreboardOutputTableV3 from "./table/scoreboard-output-table-v3.vue";
import { simpleClone } from "../../../../../common/common-service-utils";

import * as ScoreboardOutputDisplayServiceV3 from "./scoreboard-output-display-service-v3";
import FormGenericInputTextV2 from "../../../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import FieldNumberV2 from "../../../../../common/ui/layoutV2/fields/field-number-v2.vue";
import FieldCheckboxV2 from "../../../../../common/ui/layoutV2/fields/field-checkbox-v2.vue";
import ScoreboardGoTo from "../../../scoreboard-go-to.vue";
import PhotoFinishV3 from "./photo-finish/PhotoFinishV3.vue";
import ConfirmHeat from "../v2/confirm-heat/confirm-heat.vue";
import ConfirmHeatWrapper from "./confirm-heat/ConfirmHeatWrapper.vue";
import ScoreboardTrackHeatRead from "./track/scoreboard-track-heat-read.vue";
import {
  IR4sSocketAthleteFieldResults,
  IR4sSocketDataMessageData,
} from "../../../rs4/rs4-scoreboard-models";
import { socketAthleteFieldResultsHighJumpMock } from "./track/mock-data/scoreboard-track-data-mock-highjump";
import { socketAthleteFieldResultsHammerMock } from "./track/mock-data/scoreboard-track-data-mock-hammer-men";
import { socketAthleteFieldResultsShotPutWomenMock } from "./track/mock-data/scoreboard-track-data-mock-shotput-women";
import ScoreboardOutputTableAllV3 from "./table/table-all/scoreboard-output-table-all-v3.vue";
import SectionLinksWrapper from "../../../../../common/ui/layoutV2/tabs/section-links-wrapper.vue";
import SectionLinkSimple from "../../../../../common/ui/layoutV2/tabs/section-link-simple.vue";
import R4sFooterV3 from "./r4s-footer-v3.vue";
import { photoFinishMockAdvert } from "./photo-finish/mock/photo-finish-mock-advert";
import { photoFinishMockLynx } from "./photo-finish/mock/photo-finish-mock-lynx";
import VideoV3 from "./video/VideoV3.vue";
import { useConfigController } from "../../../../../config/useConfigStore";
import DisplayClock from "./DisplayClock.vue";

export default defineComponent({
  name: "ScoreboardOutputDisplayV3",
  components: {
    DisplayClock,
    VideoV3,
    R4sFooterV3,
    SectionLinkSimple,
    SectionLinksWrapper,
    ScoreboardOutputTableAllV3,
    ScoreboardTrackHeatRead,
    ConfirmHeatWrapper,
    ConfirmHeat,
    PhotoFinishV3,
    ScoreboardGoTo,
    FieldCheckboxV2,
    FieldNumberV2,
    FormGenericInputTextV2,
    ScoreboardOutputTableV3,
    ButtonGenericV2,
    ScoreboardOutputDisplaySched,
    LoadingSpinnerV2,
    R4sRouteHeader,
  },
  props: {
    scoreboardSetUpState: {
      type: Object as PropType<IScoreboardSetUpState>,
    },
  },
  setup(props: any, context: SetupContext) {
    const configController = useConfigController();

    const scoreboardOutputDisplay = useScoreboardOutputDisplay(
      props.scoreboardSetUpState,
      context
    );

    const socketAthletesMetaV3 = ref(
      ScoreboardOutputDisplayServiceV3.factorySocketAthletesMetaV3()
    );

    watch(
      () => props.scoreboardSetUpState,
      (newValue: IScoreboardSetUpState) => {
        scoreboardOutputDisplay.controller.setScoreboardSetUpState(newValue);
      },
      {
        immediate: true,
      }
    );

    function testConfirm1() {
      scoreboardOutputDisplay.controller.processMessage(
        simpleClone(confirmHeatSocketDataMessageDataMock1)
      );
    }

    function testConfirmRandom() {
      const message = simpleClone(confirmHeatSocketDataMessageDataMock1);
      // Set key to 6 random characters
      message.key = Math.random().toString(36).substring(2, 8);
      scoreboardOutputDisplay.controller.processMessage(message);
    }

    function testConfirmForce() {
      const message = simpleClone(confirmHeatSocketDataMessageDataMock1);
      const meta: ISocketAthletesMetaV3 = {
        displayTimeOutMs: 5000,
        minTimeToDisplayMs: 2000,
        showNumberOfTimes: 1,
        displayedCount: 0,
        updatedCount: 0,
        showNext: false,
        forceDisplay: true,
        pageSize: 4,
        pageAthleteCycleMs: 1000,
      };

      // Set key to 6 random characters
      message.key = Math.random().toString(36).substring(2, 8) + "-force";
      message.meta = meta;
      scoreboardOutputDisplay.controller.processMessage(message);
    }

    function testConfirmNext() {
      const message = simpleClone(confirmHeatSocketDataMessageDataMock1);
      const meta: ISocketAthletesMetaV3 = {
        displayTimeOutMs: 5000,
        minTimeToDisplayMs: 2000,
        showNumberOfTimes: 1,
        displayedCount: 0,
        updatedCount: 0,
        showNext: true,
        forceDisplay: false,
        pageSize: 4,
        pageAthleteCycleMs: 1000,
      };

      // Set key to 6 random characters
      message.key = Math.random().toString(36).substring(2, 8) + "-next";
      message.meta = meta;
      scoreboardOutputDisplay.controller.processMessage(message);
    }

    function testPush() {
      const message = simpleClone(confirmHeatSocketDataMessageDataMock1);
      message.key = Math.random().toString(36).substring(2, 8) + "-next";
      message.meta = simpleClone(socketAthletesMetaV3.value);
      scoreboardOutputDisplay.controller.processMessage(message);
    }

    function testLoadPhototFinishWithAdvert() {
      const message = simpleClone(photoFinishMockAdvert);
      scoreboardOutputDisplay.controller.processMessage(
        message as any as IR4sSocketDataMessageData
      );
    }

    function testLoadPhototFinishWithLynx() {
      const message = simpleClone(photoFinishMockLynx);
      scoreboardOutputDisplay.controller.processMessage(
        message as any as IR4sSocketDataMessageData
      );
    }

    function testField(fieldType: "HJ" | "Hammer" | "Shot") {
      let socketAthleteFieldResults: IR4sSocketAthleteFieldResults;
      if (fieldType === "HJ") {
        socketAthleteFieldResults = socketAthleteFieldResultsHighJumpMock;
      }

      if (fieldType === "Hammer") {
        socketAthleteFieldResults = socketAthleteFieldResultsHammerMock;
      }

      if (fieldType === "Shot") {
        socketAthleteFieldResults = socketAthleteFieldResultsShotPutWomenMock;
      }

      scoreboardOutputDisplay.controller.sendSocketMessage(
        "output-schedule--message-relay",
        socketAthleteFieldResults! as unknown as IR4sSocketDataMessageData
      );
    }

    return {
      scoreboardOutputDisplay,
      configController,
      testConfirm1,
      testConfirmRandom,
      testConfirmForce,
      testConfirmNext,
      socketAthletesMetaV3,
      testPush,
      testField,
      testLoadPhototFinishWithAdvert,
      testLoadPhototFinishWithLynx,
    };
  },
});
</script>

<style>
.r4s-body-v3 {
  height: 85vh;
  background-color: black;
  color: white;
}

.r4s-body-v3--show-controls {
  height: 30vh;
  background-color: black;
  color: white;
  overflow-y: auto;
}

.r4s-body-v3--admin-section {
  height: 55vh;
  background-color: white;
  color: black;
  overflow-y: auto;
}

.scoreboard-out-display-v3--header-sep {
  height: 0.5rem;
  width: 100%;
  background-color: white;
}
.scoreboard-out-display-v3--vert-spacer {
  height: 1rem;
  /*background-color: black;*/
}
</style>
