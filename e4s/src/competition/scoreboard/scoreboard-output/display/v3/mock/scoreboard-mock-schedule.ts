import { IScoreboardSchedule } from "../../../../scoreboard-schedule/scoreboard-schedule-models";

export const scoreboardMockSchedule: IScoreboardSchedule[] = [
  {
    id: 73,
    startdt: "2023-06-01T00:00:00",
    enddt: "2023-11-30T00:00:00",
    design: { id: 2, designNo: 2, description: "Ranking" },
    output: { id: 77, description: "New2", outputNo: 2 },
    events: [
      { id: 5284, name: "100m Finals" },
      { id: 5285, name: "100m Inters Girls" },
      { id: 5286, name: "100m Inters Men" },
      { id: 5287, name: "100m Juniors Girls" },
      { id: 5288, name: "100m Juniors Boys" },
      { id: 5289, name: "100m Seniors Women" },
      { id: 5290, name: "100m Seniors Men" },
      { id: 5293, name: "Team Event" },
      { id: 5294, name: "100m Relay" },
      { id: 5295, name: "Mixed Relay" },
      { id: 5379, name: "200m  Inters" },
      { id: 5473, name: "Long Jump" },
      { id: 5474, name: "High Jump" },
      { id: 5475, name: "Pole Vault" },
    ],
  },
  {
    id: 74,
    startdt: "",
    enddt: "",
    design: { id: 2, designNo: 2, description: "Ranking" },
    output: { id: 77, description: "New2", outputNo: 2 },
    events: [
      { id: 5290, name: "100m Seniors Men" },
      { id: 9999, name: "Some Event" },
    ],
  },
];
