import { IScoreboardOutputDisplayStateV3 } from "../useScoreboardOutputDisplayV3";
import { ICompetitionSummaryPublic } from "../../../../../competition-models";

export const scoreboardMockState: IScoreboardOutputDisplayStateV3 = {
  scoreboardSetUpState: {
    isLoading: false,
    loadingMessage: "Loading latest score data...",
    isReady: true,
    inputConfig: {
      compId: 351,
      outputId: 77,
      domain: "https://dev.entry4sports.co.uk",
      showControls: true,
    },
    competitionSummaryPublic: {
      ID: 351,
      active: true,
      IndoorOutdoor: "",
      link: "",
      yearFactor: 0,
      areaid: 0,
      options: {
        isClubComp: true,
        sourceId: 0,
        anonymize: true,
        standards: [1, 2],
        allowExpiredRegistration: true,
        useTeamBibs: false,
        bacs: { enabled: false, msg: "" },
        scoreboard: { image: "/results/blank.png" },
        seqEventNo: false,
        disabled: false,
        stripeMandatory: false,
        resultsAvailable: false,
        homeInfo: "",
        shortCode: "esaa2023",
        priority: { required: false, code: "", dateTime: "", message: "" },
        bibNos: 1,
        bibSort1: "clubname",
        bibSort2: "surname",
        bibSort3: "dob",
        heatOrder: "s",
        stadium: "Manchester",
        adjustEventNo: 0,
        athleteType: "A",
        tickets: { enabled: false },
        pf: { pfTargetDirectory: "", type: "T" },
        contact: { email: "", id: 121, tel: "", userName: " ", visible: false },
        cardInfo: { enabled: true, availableFrom: "", callRoom: false },
        subscription: {
          enabled: false,
          timeCloses: "",
          organiserMessage: "",
          e4sMessage: "",
          refunded: "2023-04-12T21:17:36+01:00",
          process: true,
          processRefundTime: "",
        },
        card: {
          header: { field: "Comp Default", track: "track default" },
          footer: { field: "", track: "" },
        },
        checkIn: {
          enabled: false,
          checkInDateTimeOpens: "",
          defaultFrom: 180,
          defaultTo: 60,
          qrCode: true,
          text: "Welcome to the Check-in service for our competition. To help us in the registration process, we would appreciate if you could let us know which events you are taking part in today. This will help reduce the number of athletes and the time you spend at the registration area picking up your bib number.\r\nWe have provided a number of methods to do this, please choose one of the methods below.",
          terms: "",
          useTerms: false,
          seedOnEntries: false,
        },
        school: false,
        orgFreeEntry: false,
        autoPayFreeEntry: true,
        cheques: { allow: false, ends: "" },
        allowAdd: { unregistered: true, registered: true },
        timetable: "provisional",
        helpText: { schedule: "", teams: "", cart: "some text for shop" },
        showTeamAthletes: true,
        singleAge: false,
        showAthleteAgeInEntries: false,
        report: {
          summary: true,
          athletes: true,
          ttathletes: true,
          ttentries: true,
          individual_entries: true,
          teams: true,
          subscriptions: true,
          orders: true,
          events: true,
        },
        athleteSecurity: { areas: [], clubs: [], onlyClubsUpTo: "" },
        ui: {
          enterButtonText: "Enter",
          entryDefaultPanel: "SHOP_ONLY",
          ticketComp: 0,
          ticketCompButtonText: "Tickets",
          sectionsToHide: {
            ATHLETES: false,
            TEAMS: false,
            SCHEDULE: false,
            SHOP: true,
          },
        },
        athleteQrData: false,
        nonGuests: [],
        disabledReason: "",
        paymentCode: "",
        laneCount: 8,
        compLimits: { athletes: 0, entries: 0, teams: 0 },
        stopReport: false,
        cancelEvent: {
          hrsBeforeClose: 48,
          refund: { allow: false, type: "E4S_FEES" },
          credit: { allow: false },
        },
        autoEntries: {
          selectedTargetComp: { id: 0, name: "", timeTronicsUrl: "" },
          targetable: { allowedSources: [], enabled: false },
        },
        level: "",
        pfTargetDirectory: "",
        dates: ["2023-05-02", "2023-07-09", "2023-07-21"],
        clubComp: false,
        footer: { field: "Comp footer Default", track: "track footer default" },
        categoryId: 3855,
      },
      teamid: 0,
      lastentrymod: "2023-06-18 21:43:43",
      information: "",
      r4s: 0,
      waitingrefunded: 1,
      areaname: "All",
      today: "2023-06-21",
      systemtime: "2023-06-21 05:22:11",
      compDate: "2023-07-21",
      daysToComp: 100,
      daysToClose: -20,
      location: {
        id: 97,
        name: "Manchester",
        address1: "",
        address2: "",
        town: "",
        postcode: "",
        county: "",
        map: "",
        directions: "",
        website: "",
      },
      loccontact: "",
      logo: "/wp-json/view/137816/1685705733",
      ctcid: null,
      maxathletes: null,
      maxteams: null,
      maxmale: null,
      maxfemale: null,
      maxagegroup: null,
      uniqueevents: null,
      singleagegroup: null,
      ctc: {
        ctcid: null,
        maxathletes: null,
        maxteams: null,
        maxmale: null,
        maxfemale: null,
        maxagegroup: null,
        uniqueevents: null,
        singleagegroup: null,
      },
      compOrgId: 104,
      compName: "ESAA Track and Field Championships 2023",
      entityid: null,
      opendate: "2023-01-05T06:00:00+00:00",
      closedate: "2023-06-01T23:59:00+01:00",
      club: "English Schools Athletic Association",
      dates: ["2023-05-02", "2023-07-09", "2023-07-21"],
      saleenddate: null,
      entries: {
        eventCount: 41,
        teamEventCount: 8,
        indivEventCount: 33,
        indiv: 26,
        waitingCount: 0,
        uniqueIndivAthletes: 26,
        team: 4,
        uniqueTeamAthletes: 2,
        teamAthletes: 2,
        athletes: 27,
      },
      access: "",
      permissions: {
        adminMenu: true,
        builder: true,
        check: true,
        report: true,
      },
      reportId: "",
      reportAccess: true,
      status: {
        id: 0,
        compid: 351,
        description: "No Status",
        status: "NO_STATUS",
        invoicelink: "",
        reference: "",
        notes: "",
        value: 0,
        code: 9010,
        wfid: 0,
        previd: 0,
        prevdescription: "",
        nextdescription: "",
        nextid: 0,
      },
      organisers: [
        {
          permId: 152,
          orgId: 104,
          compId: 0,
          user: {
            id: 18243,
            userEmail: "<EMAIL>",
            displayName: "Andy Hulse",
          },
          role: { id: 6, name: "admin" },
        },
        {
          permId: 153,
          orgId: 104,
          compId: 0,
          user: {
            id: 26337,
            userEmail: "<EMAIL>",
            displayName: "esaaticket",
          },
          role: { id: 6, name: "admin" },
        },
        {
          permId: 155,
          orgId: 104,
          compId: 0,
          user: {
            id: 26429,
            userEmail: "<EMAIL>",
            displayName: "ESAA 2021",
          },
          role: { id: 6, name: "admin" },
        },
      ],
      compId: 351,
      e4sNotes: "",
      newsFlash: "",
      termsConditions: "",
      emailText: "",
      compRules: [
        {
          id: 209,
          agid: 0,
          options: {
            maxCompEvents: 1,
            maxCompField: 0,
            maxCompTrack: 0,
            maxExcludedEvents: 0,
            displayMaxDayFields: false,
          },
        },
      ],
      eventTypes: { indivEvents: true, teamEvents: true, tickets: false },
      clubCompInfo: {
        configData: {
          clubCompId: 32,
          clubId: 10600,
          clubName: "Avon",
          categoryId: 1,
          categoryName: "ESAA A",
          maxEntries: 70,
          maxRelays: 2,
          bibNos: [1, 2, "2a"],
          maxPerEvent: 3,
        },
        clubs: [
          {
            clubCompId: 32,
            clubId: 10600,
            clubName: "Avon",
            categoryId: 1,
            categoryName: "ESAA A",
            maxEntries: 70,
            maxRelays: 2,
            bibNos: [1, 2, "2a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 7,
            clubId: 10618,
            clubName: "Bedfordshire and Luton",
            categoryId: 2,
            categoryName: "ESAA B",
            maxEntries: 40,
            maxRelays: 4,
            bibNos: [11, 12, "12a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 8,
            clubId: 10604,
            clubName: "Berkshire",
            categoryId: 4,
            categoryName: "ESAA D",
            maxEntries: 24,
            maxRelays: 3,
            bibNos: [13, 14, "14a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 10,
            clubId: 10612,
            clubName: "Buckinghamshire",
            categoryId: 2,
            categoryName: "ESAA B",
            maxEntries: 40,
            maxRelays: 4,
            bibNos: [15, 16, "16a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 11,
            clubId: 10609,
            clubName: "Cambridgeshire",
            categoryId: 2,
            categoryName: "ESAA B",
            maxEntries: 40,
            maxRelays: 4,
            bibNos: [17, 18, "18a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 12,
            clubId: 10628,
            clubName: "Channel Islands",
            categoryId: 3,
            categoryName: "ESAA C",
            maxEntries: 32,
            maxRelays: 4,
            bibNos: [19, 20, "20a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 13,
            clubId: 10619,
            clubName: "Cheshire",
            categoryId: 3,
            categoryName: "ESAA C",
            maxEntries: 32,
            maxRelays: 4,
            bibNos: [21, 22, "22a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 14,
            clubId: 10626,
            clubName: "Cleveland",
            categoryId: 1,
            categoryName: "ESAA A",
            maxEntries: 70,
            maxRelays: 2,
            bibNos: [23, 24, "24a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 15,
            clubId: 10614,
            clubName: "Cornwall",
            categoryId: 3,
            categoryName: "ESAA C",
            maxEntries: 32,
            maxRelays: 4,
            bibNos: [25, 26, "26a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 17,
            clubId: 10616,
            clubName: "Cumbria",
            categoryId: 1,
            categoryName: "ESAA A",
            maxEntries: 70,
            maxRelays: 2,
            bibNos: [27, 28, "28a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 18,
            clubId: 10625,
            clubName: "Derbyshire",
            categoryId: 2,
            categoryName: "ESAA B",
            maxEntries: 40,
            maxRelays: 4,
            bibNos: [29, 30, "30a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 3,
            clubId: 10594,
            clubName: "Devon",
            categoryId: 3,
            categoryName: "ESAA C",
            maxEntries: 32,
            maxRelays: 4,
            bibNos: [3, 4, "4a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 19,
            clubId: 10615,
            clubName: "Dorset",
            categoryId: 3,
            categoryName: "ESAA C",
            maxEntries: 32,
            maxRelays: 4,
            bibNos: [31, 32, "32a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 20,
            clubId: 10591,
            clubName: "Durham",
            categoryId: 1,
            categoryName: "ESAA A",
            maxEntries: 70,
            maxRelays: 2,
            bibNos: [33, 34, "34a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 21,
            clubId: 10583,
            clubName: "Essex",
            categoryId: 4,
            categoryName: "ESAA D",
            maxEntries: 24,
            maxRelays: 3,
            bibNos: [35, 36, "36a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 22,
            clubId: 10605,
            clubName: "Gloucestershire",
            categoryId: 3,
            categoryName: "ESAA C",
            maxEntries: 32,
            maxRelays: 4,
            bibNos: [37, 38, "38a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 23,
            clubId: 10608,
            clubName: "Greater Manchester",
            categoryId: 4,
            categoryName: "ESAA D",
            maxEntries: 24,
            maxRelays: 3,
            bibNos: [39, 40, "40a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 24,
            clubId: 10607,
            clubName: "Hampshire and Vectis",
            categoryId: 2,
            categoryName: "ESAA B",
            maxEntries: 40,
            maxRelays: 4,
            bibNos: [41, 42, "42a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 25,
            clubId: 10588,
            clubName: "Hereford and Worcestershire",
            categoryId: 4,
            categoryName: "ESAA D",
            maxEntries: 24,
            maxRelays: 3,
            bibNos: [43, 44, "44a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 26,
            clubId: 10587,
            clubName: "Hertfordshire",
            categoryId: 3,
            categoryName: "ESAA C",
            maxEntries: 32,
            maxRelays: 4,
            bibNos: [45, 46, "46a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 27,
            clubId: 10606,
            clubName: "Humberside",
            categoryId: 2,
            categoryName: "ESAA B",
            maxEntries: 40,
            maxRelays: 4,
            bibNos: [47, 48, "48a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 28,
            clubId: 10624,
            clubName: "Isle of Man",
            categoryId: 3,
            categoryName: "ESAA C",
            maxEntries: 32,
            maxRelays: 4,
            bibNos: [49, 50, "50a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 4,
            clubId: 10589,
            clubName: "Kent",
            categoryId: 1,
            categoryName: "ESAA A",
            maxEntries: 70,
            maxRelays: 2,
            bibNos: [5, 6, "6a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 29,
            clubId: 10601,
            clubName: "Lancashire",
            categoryId: 1,
            categoryName: "ESAA A",
            maxEntries: 70,
            maxRelays: 2,
            bibNos: [51, 52, "52a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 30,
            clubId: 10585,
            clubName: "Leicestershire and Rutland",
            categoryId: 1,
            categoryName: "ESAA A",
            maxEntries: 70,
            maxRelays: 2,
            bibNos: [53, 54, "54a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 31,
            clubId: 10592,
            clubName: "Lincolnshire",
            categoryId: 3,
            categoryName: "ESAA C",
            maxEntries: 32,
            maxRelays: 4,
            bibNos: [55, 56, "56a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 46,
            clubId: 10595,
            clubName: "London",
            categoryId: 2,
            categoryName: "ESAA B",
            maxEntries: 40,
            maxRelays: 4,
            bibNos: [89, 90, "90a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 1,
            clubId: 10610,
            clubName: "Merseyside",
            categoryId: 2,
            categoryName: "ESAA B",
            maxEntries: 40,
            maxRelays: 4,
            bibNos: [59, 60, "60a", "60b"],
            maxPerEvent: 4,
          },
          {
            clubCompId: 2,
            clubId: 10596,
            clubName: "Middlesex",
            categoryId: 3,
            categoryName: "ESAA C",
            maxEntries: 32,
            maxRelays: 4,
            bibNos: [61, 62, "62a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 16,
            clubId: 10623,
            clubName: "Norfolk",
            categoryId: 1,
            categoryName: "ESAA A",
            maxEntries: 70,
            maxRelays: 2,
            bibNos: [63, 64, "64a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 34,
            clubId: 10611,
            clubName: "North Yorkshire",
            categoryId: 2,
            categoryName: "ESAA B",
            maxEntries: 40,
            maxRelays: 4,
            bibNos: [65, 66, "66a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 35,
            clubId: 10590,
            clubName: "Northamptonshire",
            categoryId: 4,
            categoryName: "ESAA D",
            maxEntries: 24,
            maxRelays: 3,
            bibNos: [67, 68, "68a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 36,
            clubId: 10599,
            clubName: "Northumberland",
            categoryId: 4,
            categoryName: "ESAA D",
            maxEntries: 24,
            maxRelays: 3,
            bibNos: [69, 70, "70a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 5,
            clubId: 10603,
            clubName: "Nottinghamshire",
            categoryId: 2,
            categoryName: "ESAA B",
            maxEntries: 40,
            maxRelays: 4,
            bibNos: [7, 8, "8a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 38,
            clubId: 10617,
            clubName: "Oxfordshire",
            categoryId: 1,
            categoryName: "ESAA A",
            maxEntries: 70,
            maxRelays: 2,
            bibNos: [73, 74, "74a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 39,
            clubId: 10602,
            clubName: "Shropshire",
            categoryId: 1,
            categoryName: "ESAA A",
            maxEntries: 70,
            maxRelays: 2,
            bibNos: [75, 76, "76a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 40,
            clubId: 10613,
            clubName: "Somerset",
            categoryId: 4,
            categoryName: "ESAA D",
            maxEntries: 24,
            maxRelays: 3,
            bibNos: [77, 78, "78a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 41,
            clubId: 10597,
            clubName: "South Yorkshire",
            categoryId: 3,
            categoryName: "ESAA C",
            maxEntries: 32,
            maxRelays: 4,
            bibNos: [79, 80, "80a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 42,
            clubId: 10620,
            clubName: "Staffordshire",
            categoryId: 4,
            categoryName: "ESAA D",
            maxEntries: 24,
            maxRelays: 3,
            bibNos: [81, 82, "82a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 43,
            clubId: 10627,
            clubName: "Suffolk",
            categoryId: 2,
            categoryName: "ESAA B",
            maxEntries: 40,
            maxRelays: 4,
            bibNos: [83, 84, "84a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 44,
            clubId: 10586,
            clubName: "Surrey",
            categoryId: 2,
            categoryName: "ESAA B",
            maxEntries: 40,
            maxRelays: 4,
            bibNos: [85, 86, "86a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 45,
            clubId: 10593,
            clubName: "Sussex",
            categoryId: 2,
            categoryName: "ESAA B",
            maxEntries: 40,
            maxRelays: 4,
            bibNos: [87, 88, "88a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 33,
            clubId: 10598,
            clubName: "Warwickshire",
            categoryId: 3,
            categoryName: "ESAA C",
            maxEntries: 32,
            maxRelays: 4,
            bibNos: [57, 58, "58a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 6,
            clubId: 10584,
            clubName: "West Midlands",
            categoryId: 3,
            categoryName: "ESAA C",
            maxEntries: 32,
            maxRelays: 4,
            bibNos: [9, 10, "10a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 47,
            clubId: 10621,
            clubName: "West Yorkshire",
            categoryId: 2,
            categoryName: "ESAA B",
            maxEntries: 40,
            maxRelays: 4,
            bibNos: [91, 92, "92a"],
            maxPerEvent: 3,
          },
          {
            clubCompId: 37,
            clubId: 10622,
            clubName: "Wiltshire",
            categoryId: 4,
            categoryName: "ESAA D",
            maxEntries: 24,
            maxRelays: 3,
            bibNos: [71, 72, "72a"],
            maxPerEvent: 3,
          },
        ],
        entryData: {
          entries: [],
          entryCnt: 0,
          teams: {
            "5293": {
              athletes: [],
              teams: ["Avon 4 x 100 Female Inters A"],
              eventGroup: "Team Event",
              eventAgeGroup: "Inters",
              egCount: 1,
            },
          },
          teamCnt: 2,
        },
      },
    } as any as ICompetitionSummaryPublic,
    scoreboardOutput: {
      id: 77,
      compid: 351,
      output: 2,
      description: "New2",
      options: {
        requestScreen: false,
        timings: {
          ranking: { pageSize: 10, pageCycleMs: 4000 },
          scoreboard: { displayForMs: 5000 },
          photoFinish: { cycle: true, displayForMs: 6000, maxCount: 5 },
          socketMetaMap: {
            photofinish: {
              displayTimeOutMs: 5000,
              minTimeToDisplayMs: 5000,
              showNumberOfTimes: -1,
              showNext: false,
              displayedCount: 0,
              updatedCount: 0,
              forceDisplay: false,
            },
            confirmHeat: {
              displayTimeOutMs: 15000,
              minTimeToDisplayMs: 10000,
              showNumberOfTimes: 1,
              showNext: true,
              displayedCount: 0,
              updatedCount: 0,
              forceDisplay: false,
            },
            "field-results": {
              displayTimeOutMs: 6000,
              minTimeToDisplayMs: 3000,
              showNumberOfTimes: 1,
              showNext: false,
              displayedCount: 0,
              updatedCount: 0,
              forceDisplay: false,
            },
          },
        },
        ui: {
          body: {
            main: "",
            all: "",
          },
          header: { body: "", defaultTitle: "", title: "", rightContent: "" },
          displayClock: { body: "", time: "" },
          confirmHeat: { header: "", row: "", paging: "" },
          photoFinish: {
            header: "",
            row: "",
            paging: "",
            athleteTableCell: "",
            image: "",
          },
          message: { body: "" },
        },
        listenForOtherComps: "",
        listenForOtherDomains: "",
        listenForEventGroupIds: "",
        inDebugMode: false,
      },
    },
    scoreboardSchedules: [
      {
        id: 73,
        startdt: "2023-06-01T00:00:00",
        enddt: "2023-11-30T00:00:00",
        design: { id: 2, designNo: 2, description: "Ranking" },
        output: { id: 77, description: "New2", outputNo: 2 },
        events: [
          { id: 5284, name: "100m Finals" },
          { id: 5285, name: "100m Inters Girls" },
          { id: 5286, name: "100m Inters Men" },
          { id: 5287, name: "100m Juniors Girls" },
          { id: 5288, name: "100m Juniors Boys" },
          { id: 5289, name: "100m Seniors Women" },
          { id: 5290, name: "100m Seniors Men" },
          { id: 5293, name: "Team Event" },
          { id: 5294, name: "100m Relay" },
          { id: 5295, name: "Mixed Relay" },
          { id: 5379, name: "200m  Inters" },
          { id: 5473, name: "Long Jump" },
          { id: 5474, name: "High Jump" },
          { id: 5475, name: "Pole Vault" },
        ],
      },
    ],
    validScoreboardSchedules: [
      {
        id: 73,
        startdt: "2023-06-01T00:00:00",
        enddt: "2023-11-30T00:00:00",
        design: { id: 2, designNo: 2, description: "Ranking" },
        output: { id: 77, description: "New2", outputNo: 2 },
        events: [
          { id: 5284, name: "100m Finals" },
          { id: 5285, name: "100m Inters Girls" },
          { id: 5286, name: "100m Inters Men" },
          { id: 5287, name: "100m Juniors Girls" },
          { id: 5288, name: "100m Juniors Boys" },
          { id: 5289, name: "100m Seniors Women" },
          { id: 5290, name: "100m Seniors Men" },
          { id: 5293, name: "Team Event" },
          { id: 5294, name: "100m Relay" },
          { id: 5295, name: "Mixed Relay" },
          { id: 5379, name: "200m  Inters" },
          { id: 5473, name: "Long Jump" },
          { id: 5474, name: "High Jump" },
          { id: 5475, name: "Pole Vault" },
        ],
      },
    ],
    entriesByEventIds: {},
  },
  isLoading: false,
  messagesAll: [],
  ignoreActionsNotWatching: true,
  messagesLoop: [],
  queue: { maxQueueSize: 10 },
  storage: {
    useLocalStorage: true,
  },
  ui: {
    showSection: "SCHEDULE",
    showAdminSection: "MESSAGES",
    headerText: "Score Board",
    currentSocketDataMessageData: null,
    listenForMessageTypes: [
      "photofinish",
      "confirmHeat",
      "message",
      "output-schedule--reload",
      "output-schedule--message-relay",
    ],
    listenForMessageDataTypes: [
      "photofinish",
      "confirmHeat",
      "message",
      "field-results",
    ],
    listenForMessageDomains: ["https://dev.entry4sports.co.uk"],
    listenForCompIds: [351],
    listenForEventGroupIds: [],
    listenForEventGroupIdsMap: {},
  },
  loop: {
    position: 0,
    messageTypesAllowed: ["photofinish", "confirmHeat", "field-results"],
  },
};
