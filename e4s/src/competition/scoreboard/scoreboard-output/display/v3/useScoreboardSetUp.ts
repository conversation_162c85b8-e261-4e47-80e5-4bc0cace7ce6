import { handleResponseMessages } from "../../../../../common/handle-http-reponse";
import { ScoreboardOutputData } from "../../scoreboard-output-data";
import { IScoreboardSchedule } from "../../../scoreboard-schedule/scoreboard-schedule-models";
import {
  convertObjectToArray,
  sortArray,
} from "../../../../../common/common-service-utils";
import { CompetitionData } from "../../../../competition-data";
import { ICompetitionSummaryPublic } from "../../../../competition-models";
import { CompetitionService } from "../../../../competiton-service";
import { ScoreboardOutputService } from "../../scoreboard-output-service";
import { IRs4ScoreboardOutput } from "../../../rs4/rs4-scoreboard-models";
import { ScoreboardData } from "../../../scoreboard-data";
import { Rs4Service } from "../../../rs4/rs4-service";

const competitionData: CompetitionData = new CompetitionData();
const competitionService: CompetitionService = new CompetitionService();
const scoreboardOutputService = new ScoreboardOutputService();
const scoreboardData: ScoreboardData = new ScoreboardData();
const rs4Service: Rs4Service = new Rs4Service();

export interface IScoreboardSetUpStateInputConfigV3 {
  compId: number;
  outputId: number;
  domain: string;
  showControls: boolean;
}

export interface IScoreboardSetUpState {
  isLoading: boolean;
  loadingMessage: string;
  isReady: boolean;
  inputConfig: IScoreboardSetUpStateInputConfigV3;
  competitionSummaryPublic: ICompetitionSummaryPublic;
  scoreboardOutput: IRs4ScoreboardOutput;
  scoreboardSchedules: IScoreboardSchedule[];
  validScoreboardSchedules: IScoreboardSchedule[];
  entriesByEventIds: Record<string, unknown>;
}

export function factoryScoreboardSetUpState(): IScoreboardSetUpState {
  return {
    isLoading: false,
    loadingMessage: "",
    isReady: false,
    inputConfig: {
      compId: 0,
      outputId: 0,
      domain: "",
      showControls: false,
    },
    competitionSummaryPublic: competitionService.factorySummaryPublic(),
    scoreboardOutput: scoreboardOutputService.factoryScoreboardOutput(),
    scoreboardSchedules: [],
    validScoreboardSchedules: [],
    entriesByEventIds: {},
  };
}

export function useScoreboardSetUp(
  config?: IScoreboardSetUpStateInputConfigV3
) {
  const state: IScoreboardSetUpState = factoryScoreboardSetUpState();

  function setConfig(cfig: IScoreboardSetUpStateInputConfigV3) {
    state.inputConfig = cfig;
  }

  function getData(): Promise<void> {
    state.isLoading = true;

    state.loadingMessage = "Loading scoreboard setup data...";
    return getScoreboardOutputData()
      .then(() => {
        const promComp = competitionData.getCompById(state.inputConfig.compId);
        handleResponseMessages(promComp);
        return promComp.then((respB) => {
          if (respB.errNo === 0) {
            state.competitionSummaryPublic = respB.data;
          }
          return;
        });
      })
      .then(() => {
        state.loadingMessage = "Loading schedule data...";

        return getSchedule(
          state.inputConfig.compId,
          state.inputConfig.outputId
        ).then((scoreboardSchedules) => {
          state.loadingMessage = "Loading latest score data...";
          state.scoreboardSchedules = scoreboardSchedules;
          state.validScoreboardSchedules =
            rs4Service.anyEventsInScheduleTimeRange(scoreboardSchedules);
          return getLatestScoreData();
        });
      })
      .finally(() => {
        state.isLoading = false;
        state.isReady = true;
      });
  }

  function getScoreboardOutputData(): Promise<void> {
    const promScoreboardOutput = new ScoreboardOutputData().read(
      state.inputConfig.outputId
    );
    handleResponseMessages(promScoreboardOutput);
    return promScoreboardOutput.then((resp) => {
      if (resp.errNo === 0) {
        const rs4ScoreboardOutput = resp.data;

        rs4ScoreboardOutput.options =
          scoreboardOutputService.verifyOptions(rs4ScoreboardOutput);

        state.scoreboardOutput = rs4ScoreboardOutput;
      }
      return;
    });
  }

  function getSchedule(
    compId: number,
    outputId: number
  ): Promise<IScoreboardSchedule[]> {
    const promRs4Schedule = scoreboardData.getRs4Schedule(compId, outputId);
    handleResponseMessages(promRs4Schedule);
    return promRs4Schedule.then((resp) => {
      if (resp.errNo === 0) {
        let scheds: IScoreboardSchedule[] = convertObjectToArray(resp.data).map(
          (sched) => {
            sched.startdt = sched.startdt.replace(" ", "T");
            sched.enddt = sched.enddt.replace(" ", "T");
            return sched;
          }
        );
        scheds = sortArray("startdt", scheds);
        return scheds;
      }
      return [];
    });
  }

  function getSchedulesAllAndValid(
    compId: number,
    outputId: number
  ): Promise<{ all: IScoreboardSchedule[]; valid: IScoreboardSchedule[] }> {
    return getSchedule(compId, outputId).then((schedules) => {
      return {
        all: schedules,
        valid: rs4Service.anyEventsInScheduleTimeRange(schedules),
      };
    });
  }

  function getLatestScoreData(): Promise<void> {
    if (state.validScoreboardSchedules.length === 0) {
      return Promise.resolve();
    }
    const eventGroupIds: number[] = state.validScoreboardSchedules.reduce(
      (accum, schedule) => {
        schedule.events.forEach((evt) => {
          accum.push(evt.id);
        });
        return accum;
      },
      [] as number[]
    );

    const prom = scoreboardData.getLatestScores(
      state.inputConfig.compId,
      eventGroupIds
    );
    handleResponseMessages(prom);
    return prom.then((resp) => {
      if (resp.errNo === 0) {
        state.entriesByEventIds = resp.data;
      }
      return;
    });
  }

  return {
    state,
    setConfig,
    getData,
    getSchedulesAllAndValid,
  };
}
