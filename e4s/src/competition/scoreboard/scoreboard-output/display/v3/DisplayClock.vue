<template>
  <div v-text="state.timeToDisplay"></div>
</template>

<script lang="ts">
import {
  defineComponent,
  onBeforeUnmount,
  reactive,
  SetupContext,
} from "vue";

interface IDisplayClockState {
  timeDate: Date;
  timeToDisplay: string;
}

export default defineComponent({
  name: "DisplayClock",
  components: {},
  props: {},
  setup(props: any, context: SetupContext) {
    const state = reactive<IDisplayClockState>({
      timeDate: new Date(),
      timeToDisplay: "",
    });

    let pointerTimeout: number = 0;

    updateDisplay();

    function updateDisplay() {
      console.log("updateDisplay()");
      pointerTimeout = window.setTimeout(function () {
        state.timeDate = new Date();
        state.timeToDisplay = state.timeDate.toLocaleTimeString();
        console.log("updateDisplay() timeToDisplay: " + state.timeToDisplay);
        updateDisplay();
      }, 1000);
    }

    // stop the interval when the component is destroyed
    onBeforeUnmount(() => {
      console.log("destroyed");
      // clear setTimeout
      clearTimeout(pointerTimeout);
    });

    return { state };
  },
});
</script>
