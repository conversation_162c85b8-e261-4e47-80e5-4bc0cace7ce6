<template>
  <div>
    <ScoreboardOutputDisplayV3
      v-if="scoreboardSetUp.state.isReady"
      :scoreboardSetUpState="scoreboardSetUp.state"
    />
    <LoadingSpinnerV2
      v-if="scoreboardSetUp.state.isLoading"
      :loading-message="scoreboardSetUp.state.loadingMessage"
    />
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import ScoreboardOutputDisplayV3 from "./ScoreboardOutputDisplayV3.vue";
import { CONFIG } from "../../../../../common/config";
import {
  IScoreboardSetUpStateInputConfigV3,
  useScoreboardSetUp,
} from "./useScoreboardSetUp";
import LoadingSpinnerV2 from "../../../../../common/ui/loading-spinner-v2.vue";

@Component({
  name: "ScoreboardOutputDisplayRouteV3",
  components: { LoadingSpinnerV2, ScoreboardOutputDisplayV3 },
})
export default class ScoreboardOutputDisplayRouteV3 extends Vue {
  public scoreboardOutputDisplayControllerInputConfigV3: IScoreboardSetUpStateInputConfigV3 =
    {
      compId: 0,
      outputId: 0,
      domain: CONFIG.E4S_HOST,
      showControls: false,
    };

  public scoreboardSetUp = useScoreboardSetUp();

  public created() {
    this.scoreboardOutputDisplayControllerInputConfigV3.compId = isNaN(
      Number(this.$route.params.compId)
    )
      ? 0
      : parseInt(this.$route.params.compId, 0);

    this.scoreboardOutputDisplayControllerInputConfigV3.outputId = isNaN(
      Number(this.$route.params.outputId)
    )
      ? 0
      : parseInt(this.$route.params.outputId, 0);

    this.scoreboardOutputDisplayControllerInputConfigV3.showControls = isNaN(
      Number(this.$route.params.showControls)
    )
      ? false
      : parseInt(this.$route.params.showControls, 0) === 1;

    this.scoreboardSetUp.setConfig(
      this.scoreboardOutputDisplayControllerInputConfigV3
    );
    this.scoreboardSetUp.getData();
  }
}
</script>
