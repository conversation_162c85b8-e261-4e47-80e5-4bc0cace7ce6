<template>
    <div class="rs4-event-displaying--wrapper">
        <span class="rs4-event-displaying--up-next">Up Next:</span>
        <span
            v-for="eventDisplaying in eventsDisplaying"
            :key="eventDisplaying.eventGroupId"
            v-text="eventDisplaying.eventGroupName"
            class="rs4-event-displaying--event-group"
            :class="eventDisplaying.isActive ? 'rs4-event-displaying--event-group-match' : ''"
        ></span>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {IR4sSocketDataMessage} from "../../rs4/rs4-scoreboard-models"
import {CommonService} from "../../../../common/common-service"
import * as R from "ramda"

const commonService: CommonService = new CommonService();

@Component({
    name: "event-displaying"
})
export default class EventDisplaying extends Vue {
    @Prop({
        default: () => {
            return [];
        }
    })
    public readonly socketDataMessages!: IR4sSocketDataMessage[];

    @Prop({
        default: () => {
            return null;
        }
    })
    public readonly currentSocketDataMessageRanking!: IR4sSocketDataMessage | null;

    public eventsDisplaying: { eventGroupId: number, eventGroupName: string, isActive: boolean}[] = [];

    public created() {

    }

    @Watch("currentSocketDataMessageRanking")
    public onCurrentSocketDataMessageRankingChanged(newValue: IR4sSocketDataMessage | null) {
        if (newValue) {
            this.reCalcDisplay();
        }
    }

    @Watch("socketDataMessages")
    public onSocketDataMessagesChanged(newValue: IR4sSocketDataMessage[]) {
        if (newValue) {
            this.reCalcDisplay();
        }
    }

    public reCalcDisplay() {
        let eventsDisplaying: { eventGroupId: number, eventGroupName: string, isActive: boolean}[] = this.socketDataMessages.map( (socketDataMessage) => {
            return {
                eventGroupId: socketDataMessage.data.payload.score.eventGroup.id,
                eventGroupName: socketDataMessage.data.payload.score.eventGroup.groupName,
                isActive: this.isCurrentlyDisplayingClass(socketDataMessage)
            }
        });

        // const eventsDisplaying = this.socketDataMessages.map( (socketDataMessage) => {
        //     return {
        //         eventGroupId: socketDataMessage.data.payload.score.eventGroup.id,
        //         eventGroupName: socketDataMessage.data.payload.score.eventGroup.groupName,
        //         isActive: false
        //     }
        // });

        const pos = R.findIndex( (eventDisplaying: { eventGroupId: number, eventGroupName: string, isActive: boolean}) => {
            return eventDisplaying.isActive;
        })(eventsDisplaying);

        eventsDisplaying = commonService.startArrayFromPosition(eventsDisplaying, pos).slice(0, 5);

        this.eventsDisplaying = eventsDisplaying;
    }

    public isCurrentlyDisplayingClass(socketDataMessage: IR4sSocketDataMessage): boolean {

        if (this.currentSocketDataMessageRanking && this.currentSocketDataMessageRanking.data) {
            return this.currentSocketDataMessageRanking.data.payload.score.eventGroup.id === socketDataMessage.data.payload.score.eventGroup.id;
        }
        return false
    }
}
</script>

<style scoped>

.rs4-event-displaying--wrapper {
    background-color: black;
    color: white;
    width: 100%;
}

.rs4-event-displaying--up-next {
    margin: 0 1em;
    font-size: 1.25em;
    font-weight: 500;
}

.rs4-event-displaying--event-group {
    margin: 0 1em;
    font-size: 1.25em;
    font-weight: 500;
}

.rs4-event-displaying--event-group-match {
    color: red;
}
</style>
