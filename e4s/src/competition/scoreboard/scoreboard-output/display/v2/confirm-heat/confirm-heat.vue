<template>
  <div>
    <table class="r4s-ranking--table">
      <tr
        class="r4s-ranking--table-header confirm-heat--table-header"
        :style="rs4ScoreboardOutput.options.ui.confirmHeat.header"
      >
        <th class="r4s-ranking--table-pos">Lane</th>
        <th class="r4s-ranking--table-bib">Bib</th>
        <th class="r4s-ranking--table-th-ath">Athlete</th>
        <th class="r4s-ranking--table-th-club">Affiliation</th>
      </tr>

      <tr
        v-for="(confirmHeatParticipant, index) in confirmHeatController
          .pageCycling.state.objectsToDisplay"
        :key="confirmHeatParticipant.athlete.id"
        class="confirm-heat--table-row"
        :style="rs4ScoreboardOutput.options.ui.confirmHeat.row"
      >
        <td class="r4s-ranking--table-th r4s-ranking--table-pos">
          <span v-text="confirmHeatParticipant.laneNo"></span>
        </td>
        <td class="r4s-ranking--table-th r4s-ranking--table-bib">
          <span v-text="confirmHeatParticipant.athlete.bibno"></span>
        </td>
        <td class="r4s-ranking--table-th r4s-ranking--table-th-ath">
          <span v-text="trimNameText(confirmHeatParticipant)"></span>
        </td>
        <td class="r4s-ranking--table-th r4s-ranking--table-th-club">
          <span v-text="trimClubText(confirmHeatParticipant)"></span>
        </td>
      </tr>
    </table>
    <div
      class="scoreboard-out-display-v2----page-count"
      :style="rs4ScoreboardOutput.options.ui.confirmHeat.paging"
    >
      Page:
      <span
        v-text="confirmHeatController.pageCycling.state.pageDisplaying"
      ></span>
      /
      <span v-text="confirmHeatController.pageCycling.state.pagesTotal"></span>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  SetupContext,
  watch,
} from "vue";
import { IConfirmHeat, IConfirmHeatParticipant } from "./confirm-heat-models";
import { useConfirmHeat } from "./useConfirmHeat";
import * as ConfirmHeatService from "./confirm-heat-service";
import { IRs4ScoreboardOutput } from "../../../../rs4/rs4-scoreboard-models";
import { ScoreboardOutputService } from "../../../scoreboard-output-service";

const scoreboardOutputService = new ScoreboardOutputService();

export default defineComponent({
  name: "confirm-heat",
  components: {},
  props: {
    rs4ScoreboardOutput: {
      type: Object as PropType<IRs4ScoreboardOutput>,
      default: () => {
        return scoreboardOutputService.factoryScoreboardOutput();
      },
    },
    confirmHeat: {
      type: Object as PropType<IConfirmHeat>,
      default: () => {
        return ConfirmHeatService.factoryConfirmHeat();
      },
    },
  },
  setup(props: { confirmHeat: IConfirmHeat }, context: SetupContext) {
    //  @See useConfirmHeat for context.emit("onFinishPageCycle", pageNumber)
    const confirmHeatController = useConfirmHeat(context);
    confirmHeatController.setConfirmState(props.confirmHeat);

    watch(
      () => props.confirmHeat,
      (newValue: IConfirmHeat, oldValue: IConfirmHeat) => {
        confirmHeatController.setConfirmState(newValue);
      }
    );

    function rowStyle(index: number) {
      const styles: Record<string, string> = {
        // lineHeight: "3vh"
      };

      if (index % 2 === 0) {
        styles["backgroundColor"] = "#fafafa";
      }
      return styles;
    }

    function trimNameText(
      confirmHeatParticipant: IConfirmHeatParticipant
    ): string {
      const nameToTrim =
        confirmHeatParticipant.athlete.firstname +
        " " +
        confirmHeatParticipant.athlete.surname;
      return ConfirmHeatService.trimColumnText(nameToTrim);
    }

    function trimClubText(
      confirmHeatParticipant: IConfirmHeatParticipant
    ): string {
      return ConfirmHeatService.trimColumnText(
        confirmHeatParticipant.athlete.clubname
      );
    }

    return {
      confirmHeatController,
      rowStyle,
      trimNameText,
      trimClubText,
    };
  },
});
</script>

<style>
.confirm-heat--table-header {
  border-bottom: 2px solid white;
}
.confirm-heat--table-row {
  border-bottom: 1px solid white;
}

.r4s-ranking--table-th {
  padding: 3px !important;
}
</style>
