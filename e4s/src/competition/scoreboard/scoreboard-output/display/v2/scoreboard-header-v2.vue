<template>
  <div class="e4s-flex-row results-header">
    <div class="e4s-flex-row">
      <img src="/content/e4s_logo.png" alt="" />
    </div>
    <div class="e4s-flex-row results-header-info-wrapper">
      <div class="e4s-flex-row results-header-info-container">
        <h3 v-text="eventName"></h3>
        <h3 v-if="heatNo.length > 0" v-text="'Heat No: ' + heatNo"></h3>
      </div>
      <div class="e4s-flex-row results-header-info-container">
        <h3 v-if="windSpeed.length > 0">Wind Speed: <span>N/A m/s</span></h3>
<!--        <h3>00/00/0000 - 00:00</h3>-->
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext, PropType } from "vue";
import { IRs4ScoreboardOutput } from "../../../rs4/rs4-scoreboard-models";
import { ScoreboardOutputService } from "../../scoreboard-output-service";

const scoreboardOutputService = new ScoreboardOutputService();

export default defineComponent({
  name: "scoreboard-header-v2",
  components: {},
  props: {
    rs4ScoreboardOutput: {
      type: Object as PropType<IRs4ScoreboardOutput>,
      default: () => {
        return scoreboardOutputService.factoryScoreboardOutput();
      },
    },
    eventName: {
      type: String,
      default: () => {
        return "";
      },
    },
    heatNo: {
      type: String,
      default: () => {
        return "";
      },
    },
    windSpeed: {
      type: String,
      default: () => {
        return "";
      },
    },
  },
  setup(
    props: { rs4ScoreboardOutput: IRs4ScoreboardOutput; eventName: string },
    context: SetupContext
  ) {
    return {};
  },
});
</script>

<style>
.results-header {
  flex-wrap: wrap;
  align-items: center;
  column-gap: 2vw;
  width: 100%;
  border-bottom: 0.2vh solid rgba(255, 255, 255, 0.3);
}

.results-header img {
  position: relative;
  top: -1vh;
  width: 10vw;
  min-width: 150px;
  object-fit: scale-down;
}

.results-header-info-wrapper {
  flex-grow: 1;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: baseline;
  column-gap: 3vw;
  padding-bottom: 1vh;
  color: white;
}

.results-header-info-container {
  flex-wrap: wrap;
  column-gap: 3vw;
}

.results-header-info-container:nth-child(1) {
  font-size: 3vh;
}

.results-header-info-container:nth-child(2) {
  font-size: 2vh;
}

.results-header-info-container h3 {
  font-family: "Play", sans-serif;
}
</style>
