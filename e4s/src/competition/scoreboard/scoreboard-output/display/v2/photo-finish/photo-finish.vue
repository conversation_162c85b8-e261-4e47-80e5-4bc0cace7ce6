<template>
  <div class="photo-finish---wrapper">
    <div class="photo-finish---header">
      <div
        v-text="'Wind Speed: ' + windSpeed"
        class="photo-finish---header-element"
      ></div>
      <div class="photo-finish---header-element">
        Time: <span v-text="raceTime"></span>
      </div>
    </div>

    <table cellpadding="0">
      <tr>
        <td
          class="photo-finish---td-results"
          :style="rs4ScoreboardOutput.options.ui.photoFinish.athleteTableCell"
        >
          <table>
            <tr :style="rs4ScoreboardOutput.options.ui.photoFinish.header">
              <th class="photo-finish---td-centre photo-finish---pos">Pos</th>
              <th class="photo-finish---td-centre photo-finish---result">
                Time
              </th>
              <th class="photo-finish---td-centre photo-finish---bib">Bib</th>
              <th class="photo-finish---td-centre photo-finish---athlete">
                Athlete
              </th>
            </tr>

            <tr
              v-for="result in pageCycling.state.objectsToDisplay"
              :key="result.athleteid"
              class="photo-finish---row"
              :style="rs4ScoreboardOutput.options.ui.photoFinish.row"
            >
              <td
                v-text="getPlace(result)"
                class="
                  photo-finish---td photo-finish---td-centre photo-finish---pos
                "
              ></td>
              <td class="photo-finish---td photo-finish---result">
                <div
                  v-text="getResult(result)"
                  style="width: 60%; text-align: right"
                ></div>
                <!--TODO...move into component so only run through logic once.-->
                <div
                  v-if="getScoreText(result).length > 0"
                  v-text="getScoreText(result)"
                  style="text-align: center; color: yellow"
                ></div>
              </td>
              <td
                v-text="result.options.bibNo"
                class="
                  photo-finish---td photo-finish---td-centre photo-finish---bib
                "
              ></td>
              <td class="photo-finish---td photo-finish---athlete">
                <div class="photo-finish---athlete-wrapper">
                  <div class="e4s-flex-row e4s-gap--standard">
                    <EaAward :ea-level="result.eaAward" style="width: 35px" />

                    <div class="e4s-flex-column">
                      <span v-text="result.options.athlete"></span>
                      <span
                        v-text="result.options.club"
                        class="photo-finish---club"
                      ></span>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </table>

          <div
            class="scoreboard-out-display-v2----page-count"
            :style="rs4ScoreboardOutput.options.ui.photoFinish.paging"
          >
            Page: <span v-text="pageCycling.state.pageDisplaying"></span> /
            <span v-text="pageCycling.state.pagesTotal"></span>
          </div>
        </td>

        <td class="photo-finish---td-image">
          <img
            alt="Photo Finish Image"
            :src="getPhotoFinishLink"
            class="photo-finish---image"
            :style="rs4ScoreboardOutput.options.ui.photoFinish.image"
          />
        </td>
      </tr>
    </table>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  SetupContext,
  computed,
  PropType,
  watch,
} from "vue";
import {
  IR4sPhotoFinishResult,
  IR4sSocketPhotoFinishMessage,
  IRs4ScoreboardOutput,
} from "../../../../rs4/rs4-scoreboard-models";
import { IScoreboardPhotoFinishState } from "../../scoreboard-photo-finish/scoreboard-photo-finish-models";
import { ScoreboardOutputService } from "../../../scoreboard-output-service";
import { Rs4Service } from "../../../../rs4/rs4-service";
import { usePageCycling } from "../../../../../../common/ui/page-cycling/usePageCycling";
// import * as CommonServiceUtils from "../../../../../../common/common-service-utils";

import * as PhotoFinishService from "./phot-finish-service";
import EaAward from "../../../../../../common/ui/ea-award/EaAward.vue";
import {
  isValidHttpUrl,
  simpleClone,
} from "../../../../../../common/common-service-utils";
import { CONFIG } from "../../../../../../common/config";

const scoreboardOutputService = new ScoreboardOutputService();
const rs4Service = new Rs4Service();

export default defineComponent({
  name: "photo-finish",
  components: { EaAward },
  props: {
    rs4ScoreboardOutput: {
      type: Object as PropType<IRs4ScoreboardOutput>,
      default: () => {
        return scoreboardOutputService.factoryScoreboardOutput();
      },
    },
    socketDataMessage: {
      type: Object as PropType<IR4sSocketPhotoFinishMessage>,
      required: true,
    },
  },
  setup(
    props: {
      rs4ScoreboardOutput: IRs4ScoreboardOutput;
      socketDataMessage: IR4sSocketPhotoFinishMessage;
    },
    context: SetupContext
  ) {
    let state: IScoreboardPhotoFinishState = reactive({
      socketPhotoFinishMessage: props.socketDataMessage,
      results: [],
      resultsToDisplay: [],
    });

    const pageCycling = usePageCycling<IR4sPhotoFinishResult>({
      pageCycleMs:
        props.rs4ScoreboardOutput.options.timings.ranking.pageCycleMs,
      pageSize: props.rs4ScoreboardOutput.options.timings.ranking.pageSize,
    });

    init(props.socketDataMessage);

    watch(
      () => props.socketDataMessage,
      (
        newValue: IR4sSocketPhotoFinishMessage,
        oldValue: IR4sSocketPhotoFinishMessage
      ) => {
        init(newValue);
      }
    );

    function init(socketDataMessage: IR4sSocketPhotoFinishMessage) {
      state.socketPhotoFinishMessage = simpleClone(socketDataMessage);
      state.results = rs4Service.sortPhotoFinishResults(
        socketDataMessage.payload.results
      );
      pageCycling.setData(state.results, {
        pageSize: props.rs4ScoreboardOutput.options.timings.ranking.pageSize,
        pageCycleMs:
          props.rs4ScoreboardOutput.options.timings.ranking.pageCycleMs,
      });
    }

    const windSpeed = computed(() => {
      if (state.results.length === 0) {
        return;
      }
      return state.results[0].options.ws;
    });

    const raceTime = computed(() => {
      if (state.results.length === 0) {
        return;
      }
      return state.results[0].options.eventTime;
    });

    const getPhotoFinishLink = computed(() => {
      const isValid = isValidHttpUrl(
        state.socketPhotoFinishMessage.payload.picture
      );
      if (isValid) {
        return state.socketPhotoFinishMessage.payload.picture;
      }
      return CONFIG.E4S_HOST + state.socketPhotoFinishMessage.payload.picture;

      if (state.socketPhotoFinishMessage.payload.picture) {
        // return (
        //   "https://" +
        //   state.socketPhotoFinishMessage.payload.domain +
        //   state.socketPhotoFinishMessage.payload.picture
        // );
        return state.socketPhotoFinishMessage.payload.picture;
      }
    });

    function getResult(result: IR4sPhotoFinishResult): string {
      return PhotoFinishService.getResultForDisplay(result);
      /*
      const scoreText =
        result.options.scoreText && result.options.scoreText.length > 0
          ? result.options.scoreText
          : "";
      */
      /*
      if (!CommonServiceUtils.isNumeric(result.resultvalue.toString())) {
        //  All we can do is return value.
        return result.resultvalue.toString();
      }
      */

      /*
      const result2dp: string = CommonServiceUtils.roundNumberToDecimalPlaces(
        result.timeInSeconds,
        2,
        false
      ).toString();

      const resultForDisplay = PhotoFinishService.getResultForDisplay(result.timeInSeconds.toString());

      return Number(result2dp) === 0 ? scoreText : resultForDisplay;
      */
    }

    function getScoreText(result: IR4sPhotoFinishResult): string {
      if (result.options.scoreText) {
        return result.options.scoreText;
      }
      return "";
    }

    function getPlace(result: IR4sPhotoFinishResult): string {
      if (result.options.place === 0) {
        return "-";
      }
      return result.options.place.toString();
    }

    return {
      state,
      windSpeed,
      raceTime,
      pageCycling,
      getPhotoFinishLink,
      getResult,
      getScoreText,
      getPlace,
    };
  },
});
</script>

<style>
.photo-finish---wrapper {
  height: 80vh;
}

.photo-finish---header {
  /*float: left;*/
  font-weight: 500;
  /*position: absolute;*/
  width: 100%;
  padding: 0 0 0 1em;
  background-color: black;
  color: white;
}

.photo-finish---header-element {
  margin-right: 5em;
  display: inline-block;
  /*width: 25%;*/
  font-size: 1.25em;
  font-weight: 500;
}

.photo-finish---row {
  border-bottom: 1px solid white;
}

.photo-finish---td {
  padding: 5px;
  vertical-align: top;
}

.photo-finish---td-results {
  width: 40%;
  vertical-align: top;
  font-weight: 500;
}

.photo-finish---td-club {
  /*font-weight: 400;*/
}

.photo-finish---pos {
  width: 10%;
}

.photo-finish---result {
  width: 20%;
}

.photo-finish---bib {
  width: 10%;
}

.photo-finish---athlete-wrapper {
  display: flex;
}

.photo-finish---athlete {
  width: auto;
}

.photo-finish---td-image {
  /*width: 75%;*/
  vertical-align: top;
}

.photo-finish---td-centre {
  text-align: center;
}

.photo-finish---image {
  height: 75vh;
  width: 100%;
  /*border: 5px solid #fab001;*/
}
</style>
