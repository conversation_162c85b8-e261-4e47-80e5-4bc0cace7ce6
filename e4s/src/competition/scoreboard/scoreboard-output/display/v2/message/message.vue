<template>
  <div
    v-html="scoreboardMessage.message"
    style="padding: 1rem;font-size: 3rem;"
    :style="rs4ScoreboardOutput.options.ui.message.body"
  ></div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "vue";
import {IRs4ScoreboardOutput, IScoreboardMessage} from "../../../../rs4/rs4-scoreboard-models";
import {ScoreboardOutputService} from "../../../scoreboard-output-service";

const scoreboardOutputService = new ScoreboardOutputService();

export default defineComponent({
  name: "scoreboard-message",
  components: {},
  props: {
    rs4ScoreboardOutput: {
      type: Object as PropType<IRs4ScoreboardOutput>,
      default: () => {
        return scoreboardOutputService.factoryScoreboardOutput();
      },
    },
    scoreboardMessage: {
      type: Object as PropType<IScoreboardMessage>,
      default: () => {
        return {
          title: "",
          message: ""
        } as IScoreboardMessage;
      },
    },
  },
});
</script>

