import {IR4sPhotoFinishResult, IR4sSocketPhotoFinishMessage} from "../../../rs4/rs4-scoreboard-models";

export function cleanServerPayloadResults(results: IR4sPhotoFinishResult[]): IR4sPhotoFinishResult[] {
  return results.map((result) => {
    if (typeof result.options === "string") {
      result.options = JSON.parse(
        (result.options as string).replace(/[\n\r]+/g, "")
      );
    }
    return result;
  });
}

export function getHeaderText(socketPhotoFinishMessage: IR4sSocketPhotoFinishMessage): string {
  const payload = socketPhotoFinishMessage.payload;
  return  payload.typeNo + ": " + payload.eventName + ", heat: " + payload.heatNo;
}
