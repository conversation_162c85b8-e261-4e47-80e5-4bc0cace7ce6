<template>
  <div>
    <div v-if="showSection === sections.SCORE">
      <Rs4ScoreV2
        v-if="isCurrentScoreDataAvailable"
        :r4s-socket-data-message="currentSocketDataMessageScore"
      />
      <!--          <Rs4Score-->
      <!--            v-if="isCurrentScoreDataAvailable"-->
      <!--            :r4s-socket-data-message="currentSocketDataMessageScore"-->
      <!--          ></Rs4Score>-->
    </div>

    <!--      v-if="showSection !== sections.SCORE"-->
    <div v-if="showSection !== sections.SCORE">
      <R4sRouteHeader
        comp-name=""
        :event-name="headerText"
      >
        <div slot="r4s-right-content">
          <span v-text="getOutputName"></span>
<!--          (<span v-text="currentSocketState"></span>)-->
<!--          <ScoreboardGoTo-->
<!--            :comp-id="competitionSummaryPublic.compId"-->
<!--          ></ScoreboardGoTo>-->
        </div>
      </R4sRouteHeader>

      <div class="r4s-body">
        <div v-if="showSection === sections.SCHEDULE">
          <div class="row">
            <div class="right">
              <LoadingSpinner v-if="isLoading"></LoadingSpinner>
            </div>
          </div>
          <ScoreboardOutputDisplaySched
            :scoreboard-schedules="scoreboardSchedules"
          ></ScoreboardOutputDisplaySched>
        </div>

        <!--            <div v-if="showSection === sections.SCORE">-->
        <!--              <Rs4Score-->
        <!--                v-if="isCurrentScoreDataAvailable"-->
        <!--                :r4s-socket-data-message="currentSocketDataMessageScore"-->
        <!--              ></Rs4Score>-->
        <!--            </div>-->

        <div v-show="showSection === sections.RANKINGS">
          <EventDisplaying
            style="position: absolute"
            :current-socket-data-message-ranking="
              currentSocketDataMessageRanking
            "
            :socket-data-messages="currentSocketDataMessages"
          ></EventDisplaying>
          <!--                <RankingsController-->
          <!--                    :entries-by-event-ids="entriesByEventIds"-->
          <!--                    :entries-by-athlete-id="entriesByAthleteId"-->
          <!--                ></RankingsController>-->
          <Rs4Rankings
            v-if="isCurrentDataAvailable"
            :r4s-socket-data-message="currentSocketDataMessageRanking"
            :page-size="rs4ScoreboardOutput.options.timings.ranking.pageSize"
            :page-cycle-time-ms="
              rs4ScoreboardOutput.options.timings.ranking.pageCycleMs
            "
            v-on:gotToLastPage="displayNextEventRanking"
          ></Rs4Rankings>
        </div>

        <div v-if="showSection === sections.PHOTO_FINISH">
<!--          <ScoreboardPhotoFinish-->
<!--            :socket-data-message="currentSocketDataMessagePhotoFinish"-->
<!--            :scoreboard-schedules="scoreboardSchedules"-->
<!--          ></ScoreboardPhotoFinish>-->
          <PhotoFinish
            :rs4-scoreboard-output="rs4ScoreboardOutput"
            :socket-data-message="currentSocketDataMessagePhotoFinish.data"
          />
        </div>

        <div v-if="showSection === sections.NO_SCORES">
          No scores available.
        </div>
      </div>

      <R4sFooter
        :comp-id="compId"
        :current-socket-state="currentSocketState"
      ></R4sFooter>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import {
  IR4sSocketDataMessage,
  IR4sSocketPayload,
  IRs4ScoreboardOutput,
  IR4SSocketMessageBase,
  IRs4SocketMessageOutput,
  IR4sSocketPhotoFinishMessage,
  R4S_DESIGN_TYPE,
  IR4sEntry,
} from "../../rs4/rs4-scoreboard-models";
import { CompetitionService } from "../../../competiton-service";
import { CommonService } from "../../../../common/common-service";
import LoadingSpinnerRow from "../../../../common/ui/loading-spinner-row.vue";
import { CompetitionData } from "../../../competition-data";
import { ScoreboardOutputData } from "../scoreboard-output-data";
import FormHeader from "../../../../common/ui/form/header/form-header.vue";
import { ScoreboardData } from "../../scoreboard-data";
import { IScoreboardSchedule } from "../../scoreboard-schedule/scoreboard-schedule-models";
import { ICompetitionSummaryPublic } from "../../../competition-models";
import { handleResponseMessages } from "../../../../common/handle-http-reponse";
import ScoreboardGoTo from "../../scoreboard-go-to.vue";
import {
  E4SSocketProxy,
  getSocketInstance,
} from "../../../../socket/socket-controller";
import { Rs4Service } from "../../rs4/rs4-service";
import Rs4Rankings from "../../rs4/rs4-rankings.vue";
import Rs4Score from "../../rs4/rs4-score.vue";
import { format } from "date-fns";
import R4sRouteHeader from "../../rs4/r4s-route-header.vue";
import R4sFooter from "../../rs4/r4s-footer.vue";
import ScoreboardOutputDisplaySched from "./scoreboard-output-display-sched.vue";
import { ScoreboardOutputService } from "../scoreboard-output-service";
import EventDisplaying from "./event-displaying.vue";
import ScoreboardPhotoFinish from "./scoreboard-photo-finish/scoreboard-photo-finish.vue";
import RankingsController from "../../rs4/rankings/rankings-controller.vue";
import Rs4ScoreV2 from "../../rs4/rs4-score-v2.vue";
// import { mockScoreboardDataV1 } from "./scoreboard-mock";
import * as ScoreboardPhotoFinishService from "./scoreboard-photo-finish/scoreboard-photo-finish-service";
import PhotoFinish from "./v2/photo-finish/photo-finish.vue";

@Component({
  name: "scoreboard-output-display",
  components: {
    PhotoFinish,
    Rs4ScoreV2,
    RankingsController,
    ScoreboardPhotoFinish,
    EventDisplaying,
    ScoreboardOutputDisplaySched,
    R4sFooter,
    R4sRouteHeader,
    Rs4Score,
    Rs4Rankings,
    LoadingSpinnerRow,
    ScoreboardGoTo,
    FormHeader,
  },
})
export default class ScoreboardOutputDisplay extends Vue {
  @Prop({
    default: 0,
  })
  public readonly compId: number;

  @Prop({
    default: 0,
  })
  public readonly outputId: number;

  public sections = {
    NO_SCORES: "NO_SCORES",
    SCORE: "SCORE",
    RANKINGS: "RANKINGS",
    SCHEDULE: "SCHEDULE",
    PHOTO_FINISH: "PHOTO_FINISH",
  };
  public showSection: string = this.sections.NO_SCORES;

  public isLoading: boolean = false;

  public scoreboardOutputService: ScoreboardOutputService =
    new ScoreboardOutputService();
  public competitionService: CompetitionService = new CompetitionService();
  public competitionData: CompetitionData = new CompetitionData();
  public competitionSummaryPublic: ICompetitionSummaryPublic =
    this.competitionService.factorySummaryPublic();
  public scoreboardData: ScoreboardData = new ScoreboardData();
  public commonService: CommonService = new CommonService();
  public rs4Service: Rs4Service = new Rs4Service();

  public rs4ScoreboardOutput: IRs4ScoreboardOutput =
    this.scoreboardOutputService.factoryScoreboardOutput();

  // public rs4ScoreboardOutput: IRs4ScoreboardOutput = mockScoreboardData;

  public scoreboardSchedules: IScoreboardSchedule[] = [];

  public e4SSocketProxy: E4SSocketProxy | null = null;
  public SOCKET_ALIVE_POLL_EVERY_MS = 5000;
  public socketAlivePollingTimer: any;
  public isTryingReconnect = false;

  public currentSocketDataMessages: IR4sSocketDataMessage[] = [];
  public currentSocketDataMessageRanking: IR4sSocketDataMessage | null = null;
  public currentSocketDataMessageScore: IR4sSocketDataMessage | null = null;

  public entriesByEventIds: Record<number, Record<number, IR4sEntry>> = {};
  public entriesByAthleteId: Record<number, IR4sEntry> = {};

  public currentSocketDataMessagePhotoFinish: IR4sSocketDataMessage | null =
    null;
  public socketDataMessagePhotoFinishesCounter = 0;
  public socketDataMessagePhotoFinishes: IR4sSocketDataMessage[] = [];
  public currentSocketState = "";

  public timerShowScore: number;
  public timerEventSwitch: number;
  public timerShowPhotoFinish: number;

  public showEventCounter: number = 0;

  public headerText = "Score Board";

  public created() {
    if (this.compId > 0 && this.outputId > 0) {
      this.getData();

      this.e4SSocketProxy = getSocketInstance();
      this.e4SSocketProxy.e4sSocket.addEventListener(
        "message",
        this.socketReceiveMessage
      );

      this.checkSocketConnected();

      // window.setTimeout(() => {
      //   this.processMessage({
      //     action: "sendmessage",
      //     data: mockScoreboardDataV1,
      //   } as any as IR4sSocketDataMessage);
      // }, 5000);
    }
  }

  public get getOutputName() {
    return (
      this.rs4ScoreboardOutput.output +
      ": " +
      this.rs4ScoreboardOutput.description
    );
  }

  public socketReceiveMessage(message: IR4sSocketDataMessage) {
    console.log(
      "ScoreboardOutputDisplay addEventListener Output got message...."
    );

    //  @ts-ignore  TODO
    message = {
      action: "sendmessage",
      data:
        typeof message.data === "string"
          ? JSON.parse(message.data)
          : message.data,
    };

    //  @ts-ignore  TODO
    // const socketDataMessage: IR4sSocketDataMessage = JSON.parse(message.data) as any as IR4sSocketDataMessage;
    //  @ts-ignore  TODO
    if (typeof message.data.payload === "string") {
      message.data.payload = JSON.parse(
        message.data.payload as unknown as string
      ) as IR4sSocketPayload;
    }
    this.processMessage(message);
  }

  public get getFormTitle() {
    return (
      "Scoreboard Schedule List for : " +
      this.competitionSummaryPublic.compName +
      " (" +
      this.competitionSummaryPublic.compId +
      ") " +
      this.rs4ScoreboardOutput.description +
      " (id:" +
      this.rs4ScoreboardOutput.id +
      " output: " +
      this.rs4ScoreboardOutput.output +
      ")"
    );
  }

  public get isCurrentDataAvailable(): boolean {
    return this.currentSocketDataMessageRanking &&
      this.currentSocketDataMessageRanking.data &&
      this.currentSocketDataMessageRanking.data.payload
      ? true
      : false;
  }

  public get isCurrentScoreDataAvailable(): boolean {
    return this.currentSocketDataMessageScore &&
      this.currentSocketDataMessageScore.data &&
      this.currentSocketDataMessageScore.data.payload
      ? true
      : false;
  }

  public getData() {
    this.isLoading = true;

    /**
     * Firing these all off in parallel we get a concurrent stream error from server.
     */
    const promScoreboardOutput = new ScoreboardOutputData().read(this.outputId);
    handleResponseMessages(promScoreboardOutput);
    promScoreboardOutput
      .then((resp) => {
        if (resp.errNo === 0) {
          const rs4ScoreboardOutput = resp.data;

          if (!rs4ScoreboardOutput.options) {
            rs4ScoreboardOutput.options =
              this.scoreboardOutputService.factoryScoreboardOutputOptions();
          }
          if (
            rs4ScoreboardOutput.options &&
            typeof rs4ScoreboardOutput.options === "string"
          ) {
            rs4ScoreboardOutput.options = JSON.parse(
              rs4ScoreboardOutput.options
            );
          }

          this.rs4ScoreboardOutput = rs4ScoreboardOutput;
        }
        return;
      })
      .then(() => {
        const promComp = this.competitionData.getCompById(this.compId);
        handleResponseMessages(promComp);
        return promComp.then((respB) => {
          if (respB.errNo === 0) {
            this.competitionSummaryPublic = respB.data;
          }
          return;
        });
      })
      .then(() => {
        return this.getSchedule().then(() => {
          this.getLatestScoreData();
        });
      })
      .finally(() => {
        this.isLoading = false;
        this.processTimers();
        this.handleOutputLoaded();
      });

    // Promise.all([
    //     promScoreboardOutput,
    //     promComp,
    //     promEventGroups,
    //     promRs4Schedule,
    //     promRs4ScoreboardDesign
    // ])
    // .finally( () => {
    //     this.isLoading = false;
    // })
  }

  public handleOutputLoaded() {
    this.showSection = this.sections.SCHEDULE;
  }

  public processTimers(eventCycleRateMs: number = 5000) {
    const dateDebug = format(new Date(), "HH:mm:ss");
    console.log(
      dateDebug + " processTimers...eventCycleRateMs: " + eventCycleRateMs
    );
    this.timerEventSwitch = window.setTimeout(() => {
      let eventCycleMs = eventCycleRateMs;
      // if (this.currentSocketDataMessages.length === 0) {
      //     this.showSection = this.sections.NO_SCORES;
      // } else {
      //
      //     if (this.rs4ScoreboardOutput.description === "Ranking") {
      //         if (this.showSection = this.sections.SCORE) {
      //             /// currently showing a score...leave it
      //         } else {
      //             this.showSection = this.sections.RANKINGS;
      //         }
      //     }
      //
      //     console.log(dateDebug + " processTimers...(current) showEventCounter: " + this.showEventCounter);
      //     // let counter = this.showEventCounter;
      //     this.showEventCounter++;
      //     console.log(dateDebug + " processTimers...(add) showEventCounter: " + this.showEventCounter);
      //     if (this.showEventCounter > (this.currentSocketDataMessages.length - 1)) {
      //         console.log(dateDebug + " processTimers...currentSocketDataMessages.length: " +
      //             this.currentSocketDataMessages.length + " reset to ZERO");
      //         this.showEventCounter = 0;
      //     } else {
      //         // this.showEventCounter++;
      //     }
      //     const currentSocketDataMessage = R.clone(this.currentSocketDataMessages[this.showEventCounter]);
      //     const pageNumbers = this.rs4Service.howManyEntriesPages(currentSocketDataMessage, this.PAGE_SIZE);
      //     console.log(dateDebug + " processTimers...pageNumbers: " + pageNumbers);
      //     eventCycleMs = pageNumbers * this.PAGE_REFRESH_RATE;
      //     console.log(dateDebug + " processTimers...eventCycleMs: " + eventCycleMs);
      //     this.currentSocketDataMessageRanking = currentSocketDataMessage;
      // }

      const validSchedules = this.rs4Service.anyEventsInScheduleTimeRange(
        this.scoreboardSchedules
      );
      console.log(
        dateDebug +
          " processTimers...(current) validSchedules: " +
          validSchedules.length
      );
      if (validSchedules.length === 0) {
        console.log(
          dateDebug +
            " processTimers...(current) validSchedules: " +
            validSchedules.length +
            " switching to ranking"
        );
        this.showSection = this.sections.SCHEDULE;
      }

      //  TODO strip out rankings with incorrect eventId in that does not match schedule

      this.processTimers(eventCycleMs);
    }, eventCycleRateMs);
  }

  public displayNextEventRanking(r4sSocketDataMessage: IR4sSocketDataMessage) {
    const dateDebug = format(new Date(), "HH:mm:ss");
    console.log(
      dateDebug +
        " ScoreboardOutputDisplay.displayNextEventRanking...(current) showEventCounter: " +
        this.showEventCounter
    );
    // let counter = this.showEventCounter;
    this.showEventCounter++;
    console.log(
      dateDebug +
        " ScoreboardOutputDisplay.displayNextEventRanking...(add) showEventCounter: " +
        this.showEventCounter
    );
    if (this.showEventCounter > this.currentSocketDataMessages.length - 1) {
      console.log(
        dateDebug +
          " ScoreboardOutputDisplay.displayNextEventRanking...currentSocketDataMessages.length: " +
          this.currentSocketDataMessages.length +
          " reset to ZERO"
      );
      this.showEventCounter = 0;
    } else {
      // this.showEventCounter++;
    }
    const currentSocketDataMessage = R.clone(
      this.currentSocketDataMessages[this.showEventCounter]
    );
    console.log(
      dateDebug +
        " ScoreboardOutputDisplay.displayNextEventRanking...!!!!! A: " +
        currentSocketDataMessage.data.payload.score.eventGroup.id
    );
    // const pageNumbers = this.rs4Service.howManyEntriesPages(currentSocketDataMessage, this.PAGE_SIZE);
    // console.log(dateDebug + " ScoreboardOutputDisplay.displayNextEventRanking...pageNumbers: " + pageNumbers);
    // eventCycleMs = pageNumbers * this.PAGE_REFRESH_RATE;
    // console.log(dateDebug + " ScoreboardOutputDisplay.displayNextEventRanking...eventCycleMs: " + eventCycleMs);
    this.currentSocketDataMessageRanking = currentSocketDataMessage;
  }

  public getSchedule() {
    const promRs4Schedule = this.scoreboardData.getRs4Schedule(
      this.compId,
      this.outputId
    );
    handleResponseMessages(promRs4Schedule);
    return promRs4Schedule.then((resp) => {
      if (resp.errNo === 0) {
        let scheds: IScoreboardSchedule[] = this.commonService
          .convertObjectToArray(resp.data)
          .map((sched) => {
            sched.startdt = sched.startdt.replace(" ", "T");
            sched.enddt = sched.enddt.replace(" ", "T");
            return sched;
          });
        scheds = this.commonService.sortArray("startdt", scheds);
        this.scoreboardSchedules = scheds;
      }
      return;
    });
  }

  public getLatestScoreData() {
    const validSchedules = this.rs4Service.anyEventsInScheduleTimeRange(
      this.scoreboardSchedules
    );
    if (validSchedules.length === 0) {
      return;
    }
    const eventGroupIds: number[] = validSchedules.reduce((accum, schedule) => {
      schedule.events.forEach((evt) => {
        accum.push(evt.id);
      });
      return accum;
    }, [] as number[]);

    const prom = this.scoreboardData.getLatestScores(
      this.compId,
      eventGroupIds
    );
    handleResponseMessages(prom);
    return prom.then((resp) => {
      if (resp.errNo === 0) {
        this.entriesByEventIds = resp.data;
      }
      return;
    });
  }

  public addMessageToQueue(r4sSocketDataMessage: IR4sSocketDataMessage) {
    const eventGroupIdToAdd =
      this.rs4Service.getEventGroupIdFromMessage(r4sSocketDataMessage);
    const messages: IR4sSocketDataMessage[] =
      this.currentSocketDataMessages.filter(
        (message: IR4sSocketDataMessage) => {
          const eventGroupId =
            this.rs4Service.getEventGroupIdFromMessage(message);
          return eventGroupId !== eventGroupIdToAdd;
        }
      );
    messages.push(r4sSocketDataMessage);
    this.currentSocketDataMessages = messages;
    this.entriesByAthleteId = R.clone(
      r4sSocketDataMessage.data.payload.entries
    );
  }

  public processMessage(socketMessageBase: IR4SSocketMessageBase) {
    console.log("processMessage", socketMessageBase);


    // if (this.showSection === this.sections.SCORE) {
    //   if (this.currentSocketDataMessageScore) {
    //     return this.rs4Service.getEventName(this.currentSocketDataMessageScore);
    //   }
    // }

    if (socketMessageBase.data.action === "photofinish") {
      this.processResult(socketMessageBase as IR4sSocketDataMessage);
      return;
    }

    if (
      socketMessageBase.data.action === "athlete-result" ||
      socketMessageBase.data.action === "height-result"
    ) {
      this.processResult(socketMessageBase as IR4sSocketDataMessage);
    }

    if (socketMessageBase.data.action === "output-schedule--reload") {
      window.location.reload();
      return;
    }

    if (socketMessageBase.data.action === "output-schedule--refresh") {
      const sockConfig = socketMessageBase as IRs4SocketMessageOutput;
      if (
        sockConfig.data.compId === this.competitionSummaryPublic.compId &&
        sockConfig.data.outputId === this.rs4ScoreboardOutput.id
      ) {
        this.getSchedule();
      }
      return;
    }

    if (socketMessageBase.data.action === "output-schedule--show-schedule") {
      this.showSection = this.sections.SCHEDULE;
      return;
    }

    if (socketMessageBase.data.action === "output-schedule--show-ranking") {
      this.showSection = this.sections.RANKINGS;
      return;
    }
  }

  public processResult(r4sSocketDataMessage: IR4sSocketDataMessage) {
    if (
      r4sSocketDataMessage.data.comp.id !== this.competitionSummaryPublic.compId
    ) {
      console.log(
        "processMessage received comp id: " +
          r4sSocketDataMessage.data.comp.id +
          ", output: " +
          this.competitionSummaryPublic.compId +
          " does NOT match...ignore message."
      );
      return;
    }

    const hasOutputNumber =
      r4sSocketDataMessage.data.outputNo &&
      r4sSocketDataMessage.data.outputNo > 0;

    if (hasOutputNumber) {
      if (!this.rs4ScoreboardOutput.options.requestScreen) {
        console.log(
          "processMessage received forOutputNo: " +
            r4sSocketDataMessage.data.outputNo +
            ", this output is Not a REQUEST screen: " +
            this.rs4ScoreboardOutput.options.requestScreen
        );
        return;
      } else {
        if (
          r4sSocketDataMessage.data.outputNo !== this.rs4ScoreboardOutput.output
        ) {
          console.log(
            "processMessage received forOutputNo: " +
              r4sSocketDataMessage.data.outputNo +
              ", this output Number: " +
              this.rs4ScoreboardOutput.output +
              " does NOT match...ignore message."
          );
          return;
        }
      }
    } else {
      if (this.rs4ScoreboardOutput.options.requestScreen) {
        console.log(
          "processMessage this screen is request screen: " +
            this.rs4ScoreboardOutput.options.requestScreen +
            " with no output number, so do not display"
        );
        return;
      }
    }

    let res;
    let canShowPictures = false;
    if (this.scoreboardSchedules.length === 1) {
      canShowPictures = this.scoreboardSchedules[0].design.designNo === 4;
    }

    if (canShowPictures) {
      if (r4sSocketDataMessage.data.action === "photofinish") {
        res = this.rs4Service.shouldDisplayPhotoFinish(
          r4sSocketDataMessage,
          this.scoreboardSchedules
        );
        if (!res.result) {
          return;
        }
        this.showPhotoFinish(r4sSocketDataMessage);
        return;
      }
    }

    clearTimeout(this.timerShowPhotoFinish);

    res = this.rs4Service.shouldDisplayScore(
      r4sSocketDataMessage,
      this.scoreboardSchedules
    );
    console.log("processMessage res,", res);
    if (!res.result) {
      return;
    }
    this.currentSocketDataMessageScore = R.clone(r4sSocketDataMessage);

    // if (this.showSection === this.sections.SCORE) {
    //   if (this.currentSocketDataMessageScore) {
    //     return this.rs4Service.getEventName(this.currentSocketDataMessageScore);
    //   }
    // }

    //  TODO should we show rankings...Schedule.  If more than 1 active schedule...show as Rankings.
    //  to initialise at least 1 ranking to show.
    if (!this.currentSocketDataMessageRanking) {
      this.currentSocketDataMessageRanking = R.clone(r4sSocketDataMessage);
    }
    this.addMessageToQueue(r4sSocketDataMessage);

    //  TODO should we show score...Schedule.  If more than 1 active schedule...show as Rankings.
    //   show Score.
    // const eventGroupId =  this.rs4Service.getEventGroupIdFromMessage(r4sSocketDataMessage);
    // const validSchedules = this.rs4Service.getValidSchedules(eventGroupId, this.scoreboardSchedules);

    //  1 = Scoreboard, 2 = Ranking, 3 = Scoreboard + Ranking, 4 = Picture
    //  We must have at least 1 else we would not have got past shouldDisplayScore()
    //  If we have more than 1 match...which do we use?!?!, so just use first one.
    // const designType = this.rs4Service.getDesignTypeForSchedule(validSchedules[0])
    const designType = this.getCurrentDesignType(r4sSocketDataMessage);
    if (designType === 2 || designType === 3) {
      this.showSection = this.sections.RANKINGS;
    }

    if (designType === 1 || designType === 3) {
      this.showScore(r4sSocketDataMessage);
    }
  }

  public showScore(r4sSocketDataMessage: IR4sSocketDataMessage) {
    this.showSection = this.sections.SCORE;

    if (this.currentSocketDataMessageScore) {
      this.headerText = this.rs4Service.getEventName(r4sSocketDataMessage);
    }

    this.timerShowScore = window.setTimeout(() => {
      //  1 = Scoreboard, 2 = Ranking, 3 = Scoreboard + Ranking, 4 = Picture
      const designType = this.getCurrentDesignType(r4sSocketDataMessage);
      if (designType === 2 || designType === 3) {
        this.showSection = this.sections.RANKINGS;
      }
    }, this.rs4ScoreboardOutput.options.timings.scoreboard.displayForMs);
  }

  public showPhotoFinish(
    r4sSocketDataMessage: IR4sSocketDataMessage,
    resetCounter: boolean = true
  ) {
    //  New message, start looping again
    if (resetCounter) {
      this.socketDataMessagePhotoFinishesCounter = 0;
    }

    const socketDataMessagePhotoFinish = R.clone(r4sSocketDataMessage);

    //  In case not looping
    this.headerText = ScoreboardPhotoFinishService.getHeaderText(
      r4sSocketDataMessage.data as any as IR4sSocketPhotoFinishMessage
    );

    console.log(new Date() + " showPhotoFinish ()========================= a");
    const socketPhotoFinishMessage: IR4sSocketPhotoFinishMessage =
      socketDataMessagePhotoFinish.data as unknown as IR4sSocketPhotoFinishMessage;
    socketPhotoFinishMessage.payload.results =
      socketPhotoFinishMessage.payload.results.map((result) => {
        if (typeof result.options === "string") {
          result.options = JSON.parse(
            (result.options as string).replace(/[\n\r]+/g, "")
          );
        }
        return result;
      });

    this.currentSocketDataMessagePhotoFinish = socketDataMessagePhotoFinish;

    //  If not already in list.
    const MAX_PHOTOS_TO_CYCLE =
      this.rs4ScoreboardOutput.options.timings.photoFinish &&
      this.rs4ScoreboardOutput.options.timings.photoFinish.maxCount
        ? this.rs4ScoreboardOutput.options.timings.photoFinish.maxCount
        : 5;

    let socketDataMessagePhotoFinishes: IR4sSocketDataMessage[] =
      this.socketDataMessagePhotoFinishes;
    socketDataMessagePhotoFinishes.unshift(
      R.clone(socketDataMessagePhotoFinish)
    );
    if (socketDataMessagePhotoFinishes.length > MAX_PHOTOS_TO_CYCLE) {
      socketDataMessagePhotoFinishes = socketDataMessagePhotoFinishes.slice(
        0,
        MAX_PHOTOS_TO_CYCLE
      );
    }
    this.socketDataMessagePhotoFinishes = socketDataMessagePhotoFinishes;

    this.showSection = this.sections.PHOTO_FINISH;

    if (
      this.rs4ScoreboardOutput.options.timings.photoFinish &&
      this.rs4ScoreboardOutput.options.timings.photoFinish.cycle
    ) {
      clearTimeout(this.timerShowPhotoFinish);
      this.startPhotoFinishLoop();
    }
  }

  public startPhotoFinishLoop() {
    const displayForMs =
      this.rs4ScoreboardOutput.options.timings.photoFinish &&
      this.rs4ScoreboardOutput.options.timings.photoFinish.displayForMs
        ? this.rs4ScoreboardOutput.options.timings.photoFinish.displayForMs
        : this.rs4ScoreboardOutput.options.timings.scoreboard.displayForMs;

    this.timerShowPhotoFinish = window.setTimeout(() => {
      console.log(
        new Date() +
          " showPhotoFinish ()========================= setTimeout() start..."
      );

      if (
        this.socketDataMessagePhotoFinishesCounter >=
        this.socketDataMessagePhotoFinishes.length
      ) {
        this.socketDataMessagePhotoFinishesCounter = 0;
      }

      // this.currentSocketDataMessagePhotoFinish = this.socketDataMessagePhotoFinishes[this.socketDataMessagePhotoFinishesCounter];
      console.log(
        new Date() +
          " showPhotoFinish ()========================= call showPhotoFinish() again"
      );

      this.currentSocketDataMessagePhotoFinish =
        this.socketDataMessagePhotoFinishes[
          this.socketDataMessagePhotoFinishesCounter
        ];

      //  This deals with changing if looping diff events.
      this.headerText = ScoreboardPhotoFinishService.getHeaderText(
        this.currentSocketDataMessagePhotoFinish.data as any as IR4sSocketPhotoFinishMessage
      );

      this.socketDataMessagePhotoFinishesCounter++;
      this.startPhotoFinishLoop();
    }, displayForMs);
  }

  public getCurrentDesignType(r4sSocketDataMessage: IR4sSocketDataMessage) {
    const eventGroupId =
      this.rs4Service.getEventGroupIdFromMessage(r4sSocketDataMessage);
    const validSchedules = this.rs4Service.getValidSchedules(
      eventGroupId,
      this.scoreboardSchedules
    );

    return this.rs4Service.getDesignTypeForSchedule(validSchedules[0]);
  }

  public getCurrentDesignTypeForEventNo(
    eventGroupId: number
  ): R4S_DESIGN_TYPE | -1 {
    const validSchedules = this.rs4Service.getValidSchedules(
      eventGroupId,
      this.scoreboardSchedules
    );
    if (validSchedules.length === 0) {
      return -1;
    }
    return this.rs4Service.getDesignTypeForSchedule(validSchedules[0]);
  }

  public get getEventName() {
    //  No messages yet
    // if (this.showSection === this.sections.SCHEDULE) {
    //     return "Schedule for " + format(new Date(), "ddd Do MMM");
    // }

    //  qwerty
    if (this.showSection === this.sections.SCORE) {
      if (this.currentSocketDataMessageScore) {
        return this.rs4Service.getEventName(this.currentSocketDataMessageScore);
      }
    }

    if (this.showSection === this.sections.PHOTO_FINISH) {
      if (this.currentSocketDataMessageScore) {
        return ScoreboardPhotoFinishService.getHeaderText(this.currentSocketDataMessageScore as any as IR4sSocketPhotoFinishMessage);
      }
    }

    // if (this.showSection === this.sections.RANKINGS) {
    //     if (this.currentSocketDataMessages.length > 0 && this.currentSocketDataMessageRanking) {
    //         return this.rs4Service.getEventName(this.currentSocketDataMessageRanking);
    //     }
    // }

    return "Entry4Sports Results";
  }

  public getSocketState(
    readyState: number
  ): "CONNECTING" | "OPEN" | "CLOSING" | "CLOSED" | "NA" {
    if (readyState === 0) {
      return "CONNECTING";
    }
    if (readyState === 1) {
      return "OPEN";
    }
    if (readyState === 2) {
      return "CLOSING";
    }
    if (readyState === 3) {
      return "CLOSED";
    }

    return "NA";
  }

  public checkSocketConnected(): void {
    this.socketAlivePollingTimer = window.setTimeout(() => {
      // const currentStatus = this.status;
      if (this.e4SSocketProxy && this.e4SSocketProxy.e4sSocket) {
        const currentSocketState = this.e4SSocketProxy.e4sSocket.readyState;
        this.currentSocketState = this.getSocketState(currentSocketState);

        console.log(
          "socketAlivePollingTimer: " +
            this.socketAlivePollingTimer +
            " " +
            new Date() +
            " CleoSocketWrapperController.checkSocketConnected()...currentStatus: " +
            currentSocketState +
            ", Init at: " +
            format(this.e4SSocketProxy.startedTime, "HH:mm:ss") +
            ", isTryingReconnect: " +
            this.isTryingReconnect
        );

        if (currentSocketState === WebSocket.CLOSED) {
          if (!this.isTryingReconnect) {
            this.isTryingReconnect = true;

            this.e4SSocketProxy = getSocketInstance();
            this.e4SSocketProxy.e4sSocket.addEventListener(
              "message",
              this.socketReceiveMessage
            );
            this.isTryingReconnect = false;
            //  Do not get out of this "if" else will trigger another checkSocketConnected()
            // this.checkSocketConnected();
            // return;
          }
        }
      }

      this.checkSocketConnected();
    }, this.SOCKET_ALIVE_POLL_EVERY_MS);
  }

  public stopEventSwitching() {
    console.log("ScoreboardOutputDisplay.stopEventSwitching...");
    clearTimeout(this.timerEventSwitch);
  }

  public stopShowScore() {
    console.log("ScoreboardOutputDisplay.stopShowScore...");
    clearTimeout(this.timerShowScore);
  }

  public stopShowPhotoFinish() {
    console.log("ScoreboardOutputDisplay.stopShowPhotoFinish...");
    clearTimeout(this.timerShowPhotoFinish);
  }

  public stopSocket() {
    console.log("ScoreboardOutputDisplay.stopSocket...");
    clearTimeout(this.socketAlivePollingTimer);
  }

  public beforeDestroy() {
    console.log("ScoreboardOutputDisplay.beforeDestroy...");
    this.stopEventSwitching();
    this.stopShowScore();
    this.stopShowPhotoFinish();
    this.stopSocket();
    if (this.e4SSocketProxy && this.e4SSocketProxy.e4sSocket) {
      this.e4SSocketProxy.e4sSocket.removeEventListener(
        "message",
        this.socketReceiveMessage
      );
    }
  }
}
</script>

<style>
.r4s-body {
  height: 85vh;
}
</style>
