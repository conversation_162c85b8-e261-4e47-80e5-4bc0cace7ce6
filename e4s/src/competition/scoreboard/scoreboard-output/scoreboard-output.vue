<template>
    <div>
<!--        <FormHeader-->
<!--            title="Scoreboard Output"-->
<!--        >-->
<!--        </FormHeader>-->

        <div class="row">

            <div class="input-field col s12 m6 l6">
                <label class="active"
                       for="scoreboard-output--output">
                    Output Number (comp/ID: <span v-text="scoreboardOutputInternal.id + '/' + scoreboardOutputInternal.compid"></span>)
                </label>
                <input
                    id="scoreboard-output--output"
                    name="scoreboard-output--output"
                    v-model="scoreboardOutputInternal.output">
            </div>

            <div class="input-field col s12 m6 l6">
                <label class="active"
                       for="scoreboard-output--desc">
                    Description
                </label>
                <input
                    id="scoreboard-output--desc"
                    name="scoreboard-output--output"
                    v-model="scoreboardOutputInternal.description">
            </div>

        </div>

        <div class="row">
            <div class="input-field col s12 m12 l12">

                <div class="right">
                    <LoadingSpinner v-if="isLoading"></LoadingSpinner>
                    <button
                        v-if="showCancel"
                        :disabled="isLoading"
                        class="e4s-button e4s-button--red e4s-button--10-wide"
                        v-on:click="cancel"
                    >
                        Cancel
                    </button>
                    <button
                        :disabled="isLoading"
                        class="e4s-button e4s-button--green e4s-button--10-wide"
                        v-on:click="submit"
                    >
                        Save
                    </button>
                </div>

            </div>
        </div>

    </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {ScoreboardOutputService} from "./scoreboard-output-service"
import FormHeader from "../../../common/ui/form/header/form-header.vue"
import {IRs4ScoreboardOutput} from "../rs4/rs4-scoreboard-models"

const scoreboardOutputService: ScoreboardOutputService = new ScoreboardOutputService();

@Component({
    name: "scoreboard-output",
    components: {FormHeader}
})
export default class ScoreboardOutput extends Vue {
    @Prop({
        default: () => {
            return scoreboardOutputService.factoryScoreboardOutput();
        }
    })
    public readonly scoreboardOutput: IRs4ScoreboardOutput;

    @Prop({
        default: true
    })
    public readonly showCancel!: boolean;

    public isLoading: boolean = false;
    public scoreboardOutputInternal: IRs4ScoreboardOutput = scoreboardOutputService.factoryScoreboardOutput();

    public created() {
        this.scoreboardOutputInternal = R.clone(this.scoreboardOutput);
    }

    @Watch("scoreboardOutput")
    public onScoreboardOutputChanged(newValue: IRs4ScoreboardOutput, oldValue: IRs4ScoreboardOutput) {
        this.scoreboardOutputInternal = R.clone(newValue);
    }


    public cancel() {
        this.$emit("cancel");
    }
    public submit() {
        this.$emit("submit", this.scoreboardOutputInternal);
    }
}
</script>
