import { ResourceData } from "../../../common/resource/resource-service";
import https from "../../../common/https";
import { IServerResponse } from "../../../common/common-models";
import {
  IRs4ScoreboardOutput,
  IRs4ScoreboardOutputList,
} from "../rs4/rs4-scoreboard-models";
import { SocketAction } from "../scoreboard-schedule/scoreboard-schedule-models";

export class ScoreboardOutputData extends ResourceData<IRs4ScoreboardOutput> {
  constructor() {
    super("/v5/r4s/output");
  }

  public getList(
    compId: number
  ): Promise<IServerResponse<IRs4ScoreboardOutputList>> {
    return https.get("/v5/r4s/output/list/" + compId) as any as Promise<
      IServerResponse<IRs4ScoreboardOutputList>
    >;
  }

  public sendSocketCommand(
    compId: number,
    outputId: number,
    socketAction: SocketAction,
    payload?: any
  ): Promise<void> {
    return https.post(
      "/v5/scoreboard/command/" + compId + "/" + outputId + "/" + socketAction
    ) as any as Promise<void>;
  }
}
