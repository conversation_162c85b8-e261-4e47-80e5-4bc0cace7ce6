<template>
  <div>
    <div class="row">
      <div class="input-field col s4 m4 l4">
        <label class="active" :for="PREFIX + 'scoreboard-output--output'">
          Output Number/Description (<span
            v-text="scoreboardOutputInternal.id"
          ></span
          >)
          <a v-if="scoreboardOutputInternal.id > 0" :href="getScheduleLink"
            >Sched</a
          >
          <span style="margin: 5px 0; color: grey">|</span>
          <a v-if="scoreboardOutputInternal.id > 0" :href="getDisplayLink"
            >Display</a
          >
          <span style="margin: 5px 0; color: grey">|</span>
          <a v-if="scoreboardOutputInternal.id > 0" :href="getDisplayLinkV2"
            >V2</a
          >
          <span style="margin: 5px 0; color: grey">|</span>
          <a v-if="scoreboardOutputInternal.id > 0" :href="getDisplayLinkV3d"
            >V3 d</a
          >
          <span style="margin: 5px 0; color: grey">|</span>
          <a v-if="scoreboardOutputInternal.id > 0" :href="getDisplayLinkV3a"
            >V3 a</a
          >
        </label>
        <div class="e4s-force-inline-block">
          <input
            :id="PREFIX + 'scoreboard-output--output'"
            :name="PREFIX + 'scoreboard-output--output'"
            class="e4s-input e4s-input-small-number"
            v-model="scoreboardOutputInternal.output"
          />
        </div>
        <div class="e4s-force-inline-block">
          <input
            :id="PREFIX + 'scoreboard-output--desc'"
            :name="PREFIX + 'scoreboard-output--desc'"
            class="e4s-input"
            v-model="scoreboardOutputInternal.description"
          />
        </div>
      </div>

      <!--      <div class="input-field col s2 m2 l2">-->
      <!--        <label class="active" :for="PREFIX + 'scoreboard-output&#45;&#45;desc'">-->
      <!--          Description-->
      <!--        </label>-->
      <!--        <input-->
      <!--          :id="PREFIX + 'scoreboard-output&#45;&#45;desc'"-->
      <!--          :name="PREFIX + 'scoreboard-output&#45;&#45;desc'"-->
      <!--          class="e4s-input"-->
      <!--          v-model="scoreboardOutputInternal.description"-->
      <!--        />-->
      <!--      </div>-->

      <div class="input-field col s1 m1 l1">
        <label class="active" :for="PREFIX + 'scoreboard-output--rank-ms'">
          Rank (Ms)
        </label>
        <input
          :id="PREFIX + 'scoreboard-output--rank-ms'"
          :name="PREFIX + 'scoreboard-output--rank-ms'"
          class="e4s-input"
          v-model="scoreboardOutputInternal.options.timings.ranking.pageCycleMs"
        />
      </div>
      <div class="input-field col s1 m1 l1">
        <label
          class="active"
          :for="PREFIX + 'scoreboard-output--rank-page-size'"
        >
          Page Size
        </label>
        <input
          :id="PREFIX + 'scoreboard-output--rank-page-size'"
          :name="PREFIX + 'scoreboard-output--rank-page-size'"
          class="e4s-input"
          v-model="scoreboardOutputInternal.options.timings.ranking.pageSize"
        />
      </div>

      <div class="input-field col s1 m1 l1">
        <label class="active" :for="PREFIX + 'scoreboard-output--score-ms'">
          Score (Ms)
        </label>
        <input
          :id="PREFIX + 'scoreboard-output--score-ms'"
          :name="PREFIX + 'scoreboard-output--score-ms'"
          class="e4s-input"
          v-model="
            scoreboardOutputInternal.options.timings.scoreboard.displayForMs
          "
        />
      </div>

      <div class="input-field col s1 m1 l1">
        <label class="active" :for="PREFIX + 'scoreboard-output--photo-ms'">
          Photo (Ms)
        </label>
        <input
          :id="PREFIX + 'scoreboard-output--photo-ms'"
          :name="PREFIX + 'scoreboard-output--photo-ms'"
          class="e4s-input"
          v-model="
            scoreboardOutputInternal.options.timings.photoFinish.displayForMs
          "
        />
      </div>

      <div class="input-field col s1 m1 l1">
        <label
          class="active"
          :for="PREFIX + 'scoreboard-output--photo--cycle-ms'"
        >
          <a
            href="#"
            v-on:click.prevent="
              scoreboardOutputInternal.options.timings.photoFinish.cycle =
                !scoreboardOutputInternal.options.timings.photoFinish.cycle
            "
            >Loop</a
          >
        </label>
        <span
          v-text="scoreboardOutputInternal.options.timings.photoFinish.cycle"
        ></span>
      </div>

      <div class="input-field col s1 m1 l1">
        <label
          class="active"
          :for="PREFIX + 'scoreboard-output--photo--cycle-ms'"
        >
          <a
            href="#"
            v-on:click.prevent="
              scoreboardOutputInternal.options.requestScreen =
                !scoreboardOutputInternal.options.requestScreen
            "
            >Req</a
          >
        </label>
        <span v-text="scoreboardOutputInternal.options.requestScreen"></span>
      </div>

      <div class="input-field col s1 m1 l1">
        <label
          class="active"
          :for="PREFIX + 'scoreboard-output--photo-maxcount-ms'"
        >
          Max
        </label>
        <input
          :id="PREFIX + 'scoreboard-output--photo-maxcount-ms'"
          :name="PREFIX + 'scoreboard-output--photo-maxcount-ms'"
          class="e4s-input"
          v-model="
            scoreboardOutputInternal.options.timings.photoFinish.maxCount
          "
        />
      </div>

      <div class="input-field col s1 m1 l1">
        <div class="right">
          <LoadingSpinner v-if="isLoading"></LoadingSpinner>
          <button
            :disabled="isLoading"
            class="e4s-button e4s-button--green e4s-button--medium right"
            v-on:click="submit"
          >
            Save
          </button>
        </div>
      </div>

      <div class="row">
        <div class="col s12 m12 l12">
          <a href="#" v-on:click.prevent="showStyles = !showStyles"
            ><span v-text="showStyles ? 'Hide' : 'Show'"></span> styles</a
          >

          <a href="#" v-on:click.prevent="showMeta = !showMeta"
            ><span v-text="showMeta ? 'Hide' : 'Show'"></span> Meta</a
          >
        </div>
      </div>

      <div v-if="showStyles">
        <!--body-->
        <div class="row">
          <div class="col s12 m12 l12">
            <h5>Body</h5>
          </div>
        </div>

        <div class="row">
          <div class="input-field col s12 m12 l12">
            <label class="active"> Main </label>
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.body.main"
            />
          </div>
        </div>

        <div class="row">
          <div class="input-field col s12 m12 l12">
            <label class="active"> All </label>
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.body.all"
            />
          </div>
        </div>
        <!--/body-->

        <!--Header-->
        <div class="row">
          <div class="col s12 m12 l12">
            <h5>Header</h5>
          </div>
        </div>

        <div class="row">
          <div class="input-field col s12 m12 l12">
            <label class="active"> Body </label>
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.header.body"
            />
          </div>

          <div class="input-field col s12 m12 l12">
            <label class="active">
              Default Title (Not CSS, this is probably comp name)</label
            >
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.header.defaultTitle"
            />
          </div>

          <div class="input-field col s12 m12 l12">
            <label class="active"> Title </label>
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.header.title"
            />
          </div>

          <div class="input-field col s12 m12 l12">
            <label class="active"> Right Content </label>
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.header.rightContent"
            />
          </div>
        </div>
        <!--/Header-->

        <!--Display clock-->
        <div class="row">
          <div class="col s12 m12 l12">
            <h5>Display Clock</h5>
          </div>
        </div>

        <div class="row">
          <div class="input-field col s12 m12 l12">
            <label class="active"> Body </label>
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.displayClock.body"
            />
          </div>
        </div>
        <div class="row">
          <div class="input-field col s12 m12 l12">
            <label class="active"> Time </label>
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.displayClock.time"
            />
          </div>
        </div>
        <!--/Display clock-->

        <!--ConfirmHeat-->
        <div class="row">
          <div class="col s12 m12 l12">
            <h5>Confirm Heat</h5>
          </div>
        </div>

        <div class="row">
          <div class="input-field col s12 m12 l12">
            <label class="active"> Header </label>
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.confirmHeat.header"
            />
          </div>
        </div>

        <div class="row">
          <div class="input-field col s12 m12 l12">
            <label class="active"> Row </label>
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.confirmHeat.row"
            />
          </div>
        </div>

        <div class="row">
          <div class="input-field col s12 m12 l12">
            <label class="active"> Paging </label>
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.confirmHeat.paging"
            />
          </div>
        </div>
        <!--/ConfirmHeat-->

        <!--PhotoFinish-->
        <div class="row">
          <div class="col s12 m12 l12">
            <h5>Photo Finish</h5>
          </div>
        </div>

        <div class="row">
          <div class="input-field col s12 m12 l12">
            <label class="active"> Table Header </label>
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.photoFinish.header"
            />
          </div>
        </div>

        <div class="row">
          <div class="input-field col s12 m12 l12">
            <label class="active"> Row </label>
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.photoFinish.row"
            />
          </div>
        </div>

        <div class="row">
          <div class="input-field col s12 m12 l12">
            <label class="active"> Paging </label>
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.photoFinish.paging"
            />
          </div>
        </div>

        <div class="row">
          <div class="input-field col s12 m12 l12">
            <label class="active"> Athlete table cell </label>
            <input
              class="e4s-input"
              v-model="
                scoreboardOutputInternal.options.ui.photoFinish.athleteTableCell
              "
            />
          </div>
        </div>

        <div class="row">
          <div class="input-field col s12 m12 l12">
            <label class="active"> Image </label>
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.photoFinish.image"
            />
          </div>
        </div>

        <!--/PhotoFinish-->

        <!--Message-->
        <div class="row">
          <div class="col s12 m12 l12">
            <h5>Message</h5>
          </div>
        </div>

        <div class="row">
          <div class="input-field col s12 m12 l12">
            <label class="active"> Row </label>
            <input
              class="e4s-input"
              v-model="scoreboardOutputInternal.options.ui.message.body"
            />
          </div>
        </div>
        <!--/Message-->
      </div>

      <div class="e4s-flex-row e4s-gap--standard">
        <div class="e4s-flex-row e4s-gap--standard">
          Debug Mode:
          <FieldCheckboxV2
            v-model="scoreboardOutputInternal.options.inDebugMode"
          />
        </div>
        <div class="e4s-flex-row e4s-gap--standard">
          <span>Listen for other comps</span>
          <FieldTextV2
            v-model="scoreboardOutputInternal.options.listenForOtherComps"
          />
        </div>
      </div>
      <div class="e4s-flex-row e4s-gap--standard">
        <span>Listen for other domains</span>
        <FieldTextV2
          v-model="scoreboardOutputInternal.options.listenForOtherDomains"
        />
      </div>
      <div class="e4s-flex-row e4s-gap--standard">
        <span>Listen for Events</span>
        <FieldTextV2
          v-model="scoreboardOutputInternal.options.listenForEventGroupIds"
        />
      </div>

      <div v-if="showMeta" style="padding: var(--e4s-gap--standard)">
        <ScoreboardOutputMeta
          v-model="scoreboardOutputInternal.options.timings.socketMetaMap"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { ScoreboardOutputService } from "./scoreboard-output-service";
import FormHeader from "../../../common/ui/form/header/form-header.vue";
import { IRs4ScoreboardOutput } from "../rs4/rs4-scoreboard-models";
import { LAUNCH_ROUTES_PATHS } from "../../../launch/launch-routes";
import { R4S_ROUTES } from "../rs4/r4s-routes";
import ScoreboardOutputMeta from "./ScoreboardOutputMeta.vue";
import FieldTextV2 from "../../../common/ui/layoutV2/fields/field-text-v2.vue";
import FieldCheckboxV2 from "../../../common/ui/layoutV2/fields/field-checkbox-v2.vue";

const scoreboardOutputService: ScoreboardOutputService =
  new ScoreboardOutputService();

@Component({
  name: "scoreboard-output-row",
  components: {
    FieldCheckboxV2,
    FieldTextV2,
    ScoreboardOutputMeta,
    FormHeader,
  },
})
export default class ScoreboardOutputRow extends Vue {
  @Prop({
    default: () => {
      return scoreboardOutputService.factoryScoreboardOutput();
    },
  })
  public readonly scoreboardOutput!: IRs4ScoreboardOutput;

  @Prop({
    default: false,
  })
  public readonly isLoading!: boolean;

  public PREFIX = Math.random().toString(36).substring(2);
  public scoreboardOutputInternal: IRs4ScoreboardOutput =
    scoreboardOutputService.factoryScoreboardOutput();

  public showStyles = false;
  public showMeta = false;

  // public created() {
  //   this.scoreboardOutputInternal = R.clone(this.scoreboardOutput);
  // }

  @Watch("scoreboardOutput", { immediate: true })
  public onScoreboardOutputChanged(
    newValue: IRs4ScoreboardOutput,
    oldValue: IRs4ScoreboardOutput
  ) {
    const scoreboardOutputInternal = R.clone(newValue);

    scoreboardOutputInternal.options = scoreboardOutputService.verifyOptions(
      scoreboardOutputInternal
    );
    this.scoreboardOutputInternal = scoreboardOutputInternal;
  }

  public submit() {
    this.$emit("submit", this.scoreboardOutputInternal);
  }

  public get getScheduleLink() {
    return (
      "#/" +
      LAUNCH_ROUTES_PATHS.SCOREBOARD_SCHEDULE_LIST +
      "/" +
      this.scoreboardOutputInternal.compid +
      "/" +
      this.scoreboardOutputInternal.id
    );
  }

  public get getDisplayLink() {
    return (
      "#/r4s/" +
      R4S_ROUTES.R4S_OUTPUT_DISPLAY +
      "/" +
      this.scoreboardOutputInternal.compid +
      "/" +
      this.scoreboardOutputInternal.id
    );
  }

  public get getDisplayLinkV2() {
    return (
      "#/r4s/" +
      R4S_ROUTES.R4S_OUTPUT_DISPLAY_V2 +
      "/" +
      this.scoreboardOutputInternal.compid +
      "/" +
      this.scoreboardOutputInternal.id
    );
  }

  public get getDisplayLinkV3d() {
    return (
      "#/r4s/" +
      R4S_ROUTES.R4S_OUTPUT_DISPLAY_V3 +
      "/" +
      this.scoreboardOutputInternal.compid +
      "/" +
      this.scoreboardOutputInternal.id +
      "/0"
    );
  }

  public get getDisplayLinkV3a() {
    return (
      "#/r4s/" +
      R4S_ROUTES.R4S_OUTPUT_DISPLAY_V3 +
      "/" +
      this.scoreboardOutputInternal.compid +
      "/" +
      this.scoreboardOutputInternal.id +
      "/1"
    );
  }
}
</script>
