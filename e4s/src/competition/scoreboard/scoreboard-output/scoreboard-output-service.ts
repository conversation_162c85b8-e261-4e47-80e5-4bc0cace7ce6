import {
  IRs4ScoreboardOutput,
  IRs4ScoreboardOutputOptions,
  IRs4ScoreboardOutputOptionsUi,
  SocketActionData,
} from "../rs4/rs4-scoreboard-models";
import * as CommonServiceUtils from "../../../common/common-service-utils";
import { ISocketMetaV3 } from "./display/v3/useScoreboardOutputDisplayV3";
import { mergeObjects } from "../../../common/common-service-utils";

export class ScoreboardOutputService {
  public factoryScoreboardOutput(): IRs4ScoreboardOutput {
    return {
      id: 0,
      compid: 0,
      output: 0,
      description: "Ranking",
      options: this.factoryScoreboardOutputOptions(),
    };
  }

  public factoryScoreboardOutputOptions(): IRs4ScoreboardOutputOptions {
    return {
      requestScreen: false,
      timings: {
        ranking: {
          pageSize: 10,
          pageCycleMs: 4000,
        },
        scoreboard: {
          displayForMs: 5000,
        },
        photoFinish: {
          cycle: true,
          displayForMs: 6000,
          maxCount: 5,
        },
        socketMetaMap: this.factorySocketMetaMap(),
      },
      ui: this.factoryScoreboardOutputOptionsUi(),
      listenForOtherComps: "",
      listenForOtherDomains: "",
      listenForEventGroupIds: "",
      inDebugMode: false,
    };
  }

  public factorySocketMetaMap(): Partial<
    Record<SocketActionData, ISocketMetaV3>
  > {
    return {
      photofinish: {
        displayTimeOutMs: 5000,
        minTimeToDisplayMs: 5000,
        showNumberOfTimes: -1,
        showNext: false,
        displayedCount: 0,
        updatedCount: 0,
        forceDisplay: false,
      },
      confirmHeat: {
        displayTimeOutMs: 15000,
        minTimeToDisplayMs: 10000,
        showNumberOfTimes: 1,
        showNext: true,
        displayedCount: 0,
        updatedCount: 0,
        forceDisplay: false,
      },
      "field-results": {
        displayTimeOutMs: 6000,
        minTimeToDisplayMs: 3000,
        showNumberOfTimes: 1,
        showNext: false,
        displayedCount: 0,
        updatedCount: 0,
        forceDisplay: false,
      },
      video: {
        displayTimeOutMs: 6000,
        minTimeToDisplayMs: 6000,
        showNumberOfTimes: 1,
        showNext: true,
        displayedCount: 0,
        updatedCount: 0,
        forceDisplay: false,
      },
    };
  }

  public factoryScoreboardOutputOptionsUi(): IRs4ScoreboardOutputOptionsUi {
    return {
      body: {
        main: "",
        all: "",
      },
      header: {
        body: "",
        defaultTitle: "",
        title: "",
        rightContent: "",
      },
      displayClock: {
        body: "",
        time: "",
      },
      confirmHeat: {
        header: "",
        row: "",
        paging: "",
      },
      photoFinish: {
        header: "",
        row: "",
        paging: "",
        athleteTableCell: "",
        image: "",
      },
      message: {
        body: "",
      },
    };
  }

  public verifyOptions(
    scoreboardOutput: IRs4ScoreboardOutput
  ): IRs4ScoreboardOutputOptions {
    const scoreboardOutputInternal =
      CommonServiceUtils.simpleClone(scoreboardOutput);
    if (!scoreboardOutputInternal.options) {
      scoreboardOutputInternal.options = this.factoryScoreboardOutputOptions();
      return scoreboardOutputInternal.options;
    }
    if (
      scoreboardOutputInternal.options &&
      typeof scoreboardOutputInternal.options === "string"
    ) {
      scoreboardOutputInternal.options = JSON.parse(
        scoreboardOutputInternal.options
      );
    }

    // Dynamically add missing options that may have been added in newer versions, but config is old.
    scoreboardOutputInternal.options = mergeObjects(
      this.factoryScoreboardOutputOptions(),
      scoreboardOutputInternal.options
    );

    /*
    if (!scoreboardOutputInternal.options.timings.photoFinish) {
      scoreboardOutputInternal.options.timings.photoFinish = {
        cycle: true,
        displayForMs: 6000,
        maxCount: 5,
      };
    }

    if (!scoreboardOutputInternal.options.ui) {
      scoreboardOutputInternal.options.ui =
        this.factoryScoreboardOutputOptionsUi();
    } else {
      if (!scoreboardOutputInternal.options.ui.body) {
        scoreboardOutputInternal.options.ui.body = {
          main: "",
          all: "",
        };
      }
      if (!scoreboardOutputInternal.options.ui.header) {
        scoreboardOutputInternal.options.ui.header = {
          body: "",
          defaultTitle: "",
          title: "",
          rightContent: "",
        };
      }
      if (!scoreboardOutputInternal.options.ui.header.defaultTitle) {
        scoreboardOutputInternal.options.ui.header.defaultTitle = "";
      }

      if (!scoreboardOutputInternal.options.ui.confirmHeat) {
        scoreboardOutputInternal.options.ui.confirmHeat = {
          header: "",
          row: "",
          paging: "",
        };
      }
      if (!scoreboardOutputInternal.options.ui.photoFinish) {
        scoreboardOutputInternal.options.ui.photoFinish = {
          header: "",
          row: "",
          paging: "",
          athleteTableCell: "",
          image: "",
        };
      }
      if (!scoreboardOutputInternal.options.ui.message) {
        scoreboardOutputInternal.options.ui.message = {
          body: "",
        };
      }

      const defaultClockBody =
        "display: flex;flex-direction: column;align-items: center;";
      const defaultClockTime = "font-size: xxx-large;color: white;";

      if (!scoreboardOutputInternal.options.ui.displayClock) {
        scoreboardOutputInternal.options.ui.displayClock = {
          body: defaultClockBody,
          time: defaultClockTime,
        };
      }
      if (!scoreboardOutputInternal.options.ui.displayClock.body) {
        scoreboardOutputInternal.options.ui.displayClock.body =
          defaultClockBody;
      }
      if (!scoreboardOutputInternal.options.ui.displayClock.time) {
        scoreboardOutputInternal.options.ui.displayClock.time =
          defaultClockTime;
      }
    }

    if (!scoreboardOutputInternal.options.timings.socketMetaMap) {
      scoreboardOutputInternal.options.timings.socketMetaMap = {
        ...this.factorySocketMetaMap(),
      };
    }

    if (!scoreboardOutputInternal.options.timings.socketMetaMap) {
      scoreboardOutputInternal.options.timings.socketMetaMap =
        this.factorySocketMetaMap();
    } else {
      //  Older versions of the scoreboard may have partial socketMetaMap.
      scoreboardOutputInternal.options.timings.socketMetaMap = Object.assign(
        this.factorySocketMetaMap(),
        scoreboardOutputInternal.options.timings.socketMetaMap
      );
    }
    if (!scoreboardOutputInternal.options.listenForOtherComps) {
      scoreboardOutputInternal.options.listenForOtherComps = "";
    }
    if (!scoreboardOutputInternal.options.listenForOtherDomains) {
      scoreboardOutputInternal.options.listenForOtherDomains = "";
    }
    if (!scoreboardOutputInternal.options.listenForEventGroupIds) {
      scoreboardOutputInternal.options.listenForEventGroupIds = "";
    }
    */

    if (typeof scoreboardOutputInternal.options.inDebugMode === "undefined") {
      scoreboardOutputInternal.options.inDebugMode = false;
    }

    return scoreboardOutputInternal.options;
  }
}
