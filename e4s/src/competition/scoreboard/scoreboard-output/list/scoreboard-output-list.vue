<template>
  <div>
    <FormHeader
      :title="
        'Scoreboard Output List for : ' +
        scoreboardOutputList.comp.Name +
        ' (' +
        scoreboardOutputList.comp.ID +
        ')'
      "
    >
      <CompetitionGoTo
        :comp-id="scoreboardOutputList.comp.ID"
        slot="e4s-form-header--right"
      ></CompetitionGoTo>
    </FormHeader>

    <div v-if="showSection === sections.MAIN">
      <div
        v-for="scoreboard in scoreboardOutputList.outputs"
        :key="scoreboard.id"
      >
        <ScoreboardOutputRow
          :scoreboard-output="scoreboard"
          :is-loading="isLoading"
          v-on:submit="submit"
          v-on:editSchedule="editSchedule"
        ></ScoreboardOutputRow>

        <div class="e4s-horz-line-sep"></div>

      </div>

      <div class="col s12 m12 l12">
        <div class="right">
          <LoadingSpinner v-if="isLoading"></LoadingSpinner>
          <button
            :disabled="isLoading"
            class="e4s-button e4s-button--green e4s-button--medium right"
            v-on:click="add"
          >
            Add
          </button>
        </div>
      </div>
    </div>

    <div v-if="showSection === sections.ADD">
      <ScoreboardOutput
        v-on:submit="submit"
        v-on:cancel="addCancel"
      ></ScoreboardOutput>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { ScoreboardOutputData } from "../scoreboard-output-data";
import { handleResponseMessages } from "../../../../common/handle-http-reponse";
import ScoreboardOutputRow from "../scoreboard-output-row.vue";
import FormHeader from "../../../../common/ui/form/header/form-header.vue";
import ScoreboardOutput from "../scoreboard-output.vue";
import {
  IRs4ScoreboardOutput,
  IRs4ScoreboardOutputList,
} from "../../rs4/rs4-scoreboard-models";
import CompetitionGoTo from "../../../ui/competition-go-to.vue";
import { ScoreboardOutputService } from "../scoreboard-output-service";

// const scoreboardOutputService = new ScoreboardOutputService();

@Component({
  name: "scoreboard-output-list",
  components: {
    CompetitionGoTo,
    ScoreboardOutput,
    ScoreboardOutputRow,
    FormHeader,
  },
})
export default class ScoreboardOutputList extends Vue {
  @Prop({
    default: 0,
  })
  public readonly compId: number;

  public sections = {
    MAIN: "MAIN",
    ADD: "ADD",
  };
  public showSection: string = this.sections.MAIN;

  public isLoading: boolean = false;

  public scoreboardOutputList: IRs4ScoreboardOutputList = {
    comp: {
      ID: 0,
      Name: "",
      Date: "",
    },
    outputs: [],
  };

  public scoreboardOutputData: ScoreboardOutputData =
    new ScoreboardOutputData();
  // public rs4Service: Rs4Service = new Rs4Service();
  public scoreboardOutputService: ScoreboardOutputService =
    new ScoreboardOutputService();

  public created() {
    // this.scoreboardOutputsInternal = R.clone(this.scoreboardOutputs);
    this.getData();
  }

  @Watch("compId")
  public onCompIdChanged(newValue: number) {
    if (newValue > 0) {
      this.getData();
    }
  }

  public add() {
    this.showSection = this.sections.ADD;
  }

  public addCancel() {
    this.showSection = this.sections.MAIN;
  }

  public submit(scoreboardOutput: IRs4ScoreboardOutput) {
    this.isLoading = true;
    let prom;

    const scoreboardOutputSubmit = R.clone(scoreboardOutput);

    //  TODO server wants this.
    //  @ts-ignore
    scoreboardOutputSubmit.options = JSON.stringify(scoreboardOutput.options);

    if (scoreboardOutput.id === 0) {
      scoreboardOutputSubmit.compid = this.scoreboardOutputList.comp.ID;
      prom = this.scoreboardOutputData.create(scoreboardOutputSubmit);
    } else {
      prom = this.scoreboardOutputData.update(scoreboardOutputSubmit);
    }
    handleResponseMessages(prom);
    prom
      .then((resp) => {
        if (resp.errNo === 0) {
          this.showSection = this.sections.MAIN;
          new ScoreboardOutputData().sendSocketCommand(this.compId, scoreboardOutput.id , "output-schedule--reload");
          this.getData();
        }
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public getData() {
    this.isLoading = true;
    const prom = this.scoreboardOutputData.getList(this.compId);
    handleResponseMessages(prom);
    prom
      .then((resp) => {
        if (resp.errNo === 0) {
          const scoreboardOutputList = resp.data;

          scoreboardOutputList.outputs = scoreboardOutputList.outputs.map(
            (outPut) => {
              outPut.options = this.scoreboardOutputService.verifyOptions(outPut);
              // if (!outPut.options) {
              //   outPut.options =
              //     this.scoreboardOutputService.factoryScoreboardOutputOptions();
              // }
              // if (outPut.options && typeof outPut.options === "string") {
              //   outPut.options = JSON.parse(outPut.options);
              // }
              //
              // if (!outPut.options.timings.photoFinish) {
              //   outPut.options.timings.photoFinish = {
              //     cycle: true,
              //     displayForMs: 6000,
              //     maxCount: 5,
              //   };
              // }
              //
              // if (!outPut.options.ui) {
              //   outPut.options.ui =
              //     scoreboardOutputService.factoryScoreboardOutputOptionsUi();
              // }

              return outPut;
            }
          );
          this.scoreboardOutputList = scoreboardOutputList;
        }
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public editSchedule(rs4ScoreboardOutput: IRs4ScoreboardOutput) {
    // this.$router.push({
    //     path: "/" + LAUNCH_ROUTES_PATHS.SCOREBOARD_SCHEDULE_LIST + "/" + rs4ScoreboardOutput.compid + "/" + rs4ScoreboardOutput.output
    // });
  }
}
</script>
