<template>
    <div>
        <ScoreboardOutput :scoreboard-output="scoreboardOutput"></ScoreboardOutput>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import ScoreboardOutput from "./scoreboard-output.vue"
import {ScoreboardOutputData} from "./scoreboard-output-data"
import {handleResponseMessages} from "../../../common/handle-http-reponse"
import {ScoreboardOutputService} from "./scoreboard-output-service"
import {IRs4ScoreboardOutput} from "../rs4/rs4-scoreboard-models"

const scoreboardOutputService: ScoreboardOutputService = new ScoreboardOutputService();

@Component({
    name: "scoreboard-output-route",
    components: {ScoreboardOutput}
})
export default class ScoreboardOutputRoute extends Vue {
    public id: number = 0
    public isLoading: boolean = false;

    public scoreboardOutput: IRs4ScoreboardOutput = scoreboardOutputService.factoryScoreboardOutput();

    public created() {
        const id: number = isNaN(Number(this.$route.params.id)) ? 0 : parseInt(this.$route.params.id, 0);
        const compId: number = isNaN(Number(this.$route.params.compId)) ? 0 : parseInt(this.$route.params.compId, 0);
        this.id = id;
        // this.scoreboardOutput.compid = compId;

        if (this.id > 0) {
            this.getData();
        } else {
            const scoreboardOutput = scoreboardOutputService.factoryScoreboardOutput();
            scoreboardOutput.compid = compId;
            this.scoreboardOutput = scoreboardOutput;
        }
    }

    public getData() {
        this.isLoading = true;
        const prom = new ScoreboardOutputData().read(this.id);
        handleResponseMessages(prom);
        prom.then((resp) => {
            this.scoreboardOutput = resp.data;
        })
            .finally(() => {
                this.isLoading = false;
            })
    }
}
</script>
