<template>
  <div class="e4s-flex-row e4s-gap--standard e4s-full-width">
    <div class="e4s-flex-column e4s-gap--standard">
      <span>Confirm Heat</span>
      <ScoreboardMeta
        :value="socketMetaMapInternal.confirmHeat"
        v-on:input="onScoreBoardMetaChanged('confirmHeat', $event)"
      />
    </div>

    <div class="e4s-flex-column e4s-gap--standard">
      <span>Photo Finish</span>
      <ScoreboardMeta
        :value="socketMetaMapInternal.photofinish"
        v-on:input="onScoreBoardMetaChanged('photofinish', $event)"
      />
    </div>

    <div class="e4s-flex-column e4s-gap--standard">
      <span>Field Results</span>
      <ScoreboardMeta
        :value="socketMetaMapInternal['field-results']"
        v-on:input="onScoreBoardMetaChanged('field-results', $event)"
      />
    </div>

    <div class="e4s-flex-column e4s-gap--standard">
      <span>Video</span>
      <ScoreboardMeta
        :value="socketMetaMapInternal.video"
        v-on:input="onScoreBoardMetaChanged('video', $event)"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "vue";
import {
  ISocketMetaV3,
  SocketMetaMapV3,
} from "./display/v3/useScoreboardOutputDisplayV3";
import FormGenericInputNumberV2 from "../../../common/ui/layoutV2/form/form-generic--input-number-v2.vue";
import { simpleClone } from "../../../common/common-service-utils";
import FormGenericInputTemplateV2 from "../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import FieldCheckboxV2 from "../../../common/ui/layoutV2/fields/field-checkbox-v2.vue";
import ScoreboardMeta from "./ScoreboardMeta.vue";

export default defineComponent({
  name: "ScoreboardOutputMeta",
  components: {
    ScoreboardMeta,
    FieldCheckboxV2,
    FormGenericInputTemplateV2,
    FormGenericInputNumberV2,
  },
  props: {
    value: {
      type: Object as PropType<SocketMetaMapV3>,
      required: true,
    },
  },
  setup(props: { value: SocketMetaMapV3 }, context: SetupContext) {
    const socketMetaMapInternal = ref<SocketMetaMapV3>(props.value);

    watch(
      () => props.value,
      (newValue: SocketMetaMapV3) => {
        socketMetaMapInternal.value = simpleClone(newValue);
      },
      {
        immediate: true,
      }
    );

    function onScoreBoardMetaChanged(
      prop: keyof SocketMetaMapV3,
      value: ISocketMetaV3
    ) {
      socketMetaMapInternal.value[prop] = value;
      onChange();
    }

    function onChange() {
      context.emit("input", simpleClone(socketMetaMapInternal.value));
    }

    return { socketMetaMapInternal, onScoreBoardMetaChanged };
  },
});
</script>
