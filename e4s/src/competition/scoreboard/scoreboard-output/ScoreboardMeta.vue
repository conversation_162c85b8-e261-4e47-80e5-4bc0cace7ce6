<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <FormGenericInputNumberV2
      form-label="Total Display Time (ms)"
      :value="socketMetaV3Internal.displayTimeOutMs"
      v-on:input="onInputChanged('displayTimeOutMs', $event)"
    />
    <!--    <FormGenericInputNumberV2-->
    <!--      form-label="Min Display Time"-->
    <!--      :value="socketMetaV3Internal.minTimeToDisplayMs"-->
    <!--      v-on:input="onInputChanged('minTimeToDisplayMs', $event)"-->
    <!--    />-->
    <FormGenericInputNumberV2
      form-label="Show Number of Times"
      :value="socketMetaV3Internal.showNumberOfTimes"
      v-on:input="onInputChanged('showNumberOfTimes', $event)"
    />

    <FormGenericInputTemplateV2 form-label="Show Next">
      <FieldCheckboxV2
        slot="field"
        :value="socketMetaV3Internal.showNext"
        v-on:input="onInputChanged('showNext', $event)"
      />
    </FormGenericInputTemplateV2>

    <FormGenericInputTemplateV2 form-label="Force">
      <FieldCheckboxV2
        slot="field"
        :value="socketMetaV3Internal.forceDisplay"
        v-on:input="onInputChanged('forceDisplay', $event)"
      />
    </FormGenericInputTemplateV2>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "vue";
import { ISocketMetaV3 } from "./display/v3/useScoreboardOutputDisplayV3";
import FormGenericInputNumberV2 from "../../../common/ui/layoutV2/form/form-generic--input-number-v2.vue";
import { simpleClone } from "../../../common/common-service-utils";
import FormGenericInputTemplateV2 from "../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import FieldCheckboxV2 from "../../../common/ui/layoutV2/fields/field-checkbox-v2.vue";

export default defineComponent({
  name: "ScoreboardMeta",
  components: {
    FieldCheckboxV2,
    FormGenericInputTemplateV2,
    FormGenericInputNumberV2,
  },
  props: {
    value: {
      type: Object as PropType<ISocketMetaV3>,
      required: true,
    },
  },
  setup(props: { value: ISocketMetaV3 }, context: SetupContext) {
    const socketMetaV3Internal = ref<ISocketMetaV3>(simpleClone(props.value));

    watch(
      () => props.value,
      (newValue: ISocketMetaV3, oldValue: ISocketMetaV3) => {
        if (JSON.stringify(newValue) === JSON.stringify(oldValue)) {
          return;
        }
        socketMetaV3Internal.value = simpleClone(newValue);
      }
    );

    // watch(
    //   () => socketMetaV3.value,
    //   (newValue: ISocketMetaV3, oldValue: ISocketMetaV3) => {
    //     context.emit("input", simpleClone(socketMetaV3.value));
    //   },
    //   {
    //     deep: true,
    //   }
    // );

    function onInputChanged2(value: number) {
      socketMetaV3Internal.value.displayTimeOutMs = value;
      onChange();
    }

    function onInputChanged(
      propName: keyof ISocketMetaV3,
      value: string | number
    ) {
      //  @ts-ignore
      socketMetaV3Internal.value[propName]! = value;
      onChange();
    }

    function onChange() {
      context.emit("input", simpleClone(socketMetaV3Internal.value));
    }

    return { socketMetaV3Internal, onChange, onInputChanged, onInputChanged2 };
  },
});
</script>
