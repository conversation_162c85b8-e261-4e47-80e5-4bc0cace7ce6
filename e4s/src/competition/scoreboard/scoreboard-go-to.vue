<template>
    <a :href="getPath">
<!--        Scoreboard Page-->
        <i class="material-icons bigger">important_devices</i>
    </a>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { LAUNCH_ROUTES_PATHS } from "../../launch/launch-routes";

@Component({
    name: "scoreboard-go-to"
})
export default class ScoreboardGoTo extends Vue {
    @Prop({
        default: 0
    })
    public readonly compId!: number;

    public get getPath() {
        return "#/" + LAUNCH_ROUTES_PATHS.SCOREBOARD_OUTPUT_LIST + "/" +this.compId
    }
}
</script>
