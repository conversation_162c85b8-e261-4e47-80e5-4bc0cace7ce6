<template>
    <div style="overflow: auto;height: 80vh;">
        <div class="row">
            <div class="col s12 m12 l12">
                Score Board
            </div>
        </div>

        <div class="row" v-if="isLoading">
            <div class="col s12 m12 l12">
                Loading...<LoadingSpinner></LoadingSpinner>
            </div>
        </div>

        <div v-if="!isLoading">
            <div v-for="(scoreboardEventGroup, index) in scoreboardEventGroups"
                 :key="scoreboardEventGroup.id">

                <div class="row" :style="rowStyle(scoreboardEventGroup, index)">

                    <div class="col s12 m6 l6">
                        <span v-text="scoreboardEventGroup.name + '(' + scoreboardEventGroup.eventNo + ')'"></span>
                    </div>

                    <div class="col s4 m2 l2">
                        <a :href="getEventLink(scoreboardEventGroup, 'all')">
                            <span>All</span>
                        </a>
                    </div>

                    <div class="col s4 m2 l2">
                        <a :href="getEventLink(scoreboardEventGroup, 'rank')">
                            <span>Rank</span>
                        </a>
                    </div>

                    <div class="col s4 m2 l2">
                        <a :href="getEventLink(scoreboardEventGroup, 'score')">
                            <span>Score</span>
                        </a>
                    </div>

                </div>

            </div>

            <div class="e4s-section-padding-separator"></div>
            <div class="e4s-section-padding-separator"></div>

            <div v-if="!isLoading">
                <div>

                    <div class="row">
                        <div class="col s12 m12 l12">
                            All Events
                        </div>
                    </div>

                    <div class="row">

                        <div class="col s12 m4 l3" v-for="scoreboardEventGroup in scoreboardEventGroups"
                             :key="scoreboardEventGroup.id">

                            <p>
                                <label>
                                    <input type="checkbox"
                                           v-model="scoreboardEventGroupsSelected[scoreboardEventGroup.eventNo]" />
                                    <span>
                                        <span v-text="scoreboardEventGroup.name + '(' + scoreboardEventGroup.eventNo + ')'"></span>
                                    </span>
                                </label>
                            </p>
                        </div>
                    </div>

                </div>
            </div>

            <div class="row">
                <div class="col s12 m12 l12">
<!--                    <a :href="'#/scoreboard/' + compId + '/all'">-->
<!--                        <span>All Events</span>-->
<!--                    </a>-->
                    <button class="btn waves-effect waves green"
                            v-on:click.stop="openAllEvents">
                        <span>Go</span>
                    </button>
                </div>

<!--                <div class="col s12 m6 l6">-->
<!--                    <span>All Events</span>-->
<!--                </div>-->

<!--                <div class="col s4 m2 l2">-->
<!--                    <a :href="getEventLink(scoreboardEventGroup, 'all')">-->
<!--                        <span>All</span>-->
<!--                    </a>-->
<!--                </div>-->

<!--                <div class="col s4 m2 l2">-->
<!--                    <a :href="getEventLink(scoreboardEventGroup, 'rank')">-->
<!--                        <span>Rank</span>-->
<!--                    </a>-->
<!--                </div>-->

<!--                <div class="col s4 m2 l2">-->
<!--                    <a :href="getEventLink(scoreboardEventGroup, 'score')">-->
<!--                        <span>Score</span>-->
<!--                    </a>-->
<!--                </div>-->

            </div>


        </div>



    </div>
</template>

<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";
    import {messageDispatchHelper} from "../../user-message/user-message-store";
    import {USER_MESSAGE_LEVEL} from "../../user-message/user-message-models";
    import {IScoreboardCompEventGroup} from "./scoreboard-models"
    import {IScoreboardsEventQuery, ScoreboardData} from "./scoreboard-data"
    import ScoreboardEvent from "./scoreboard-event/scoreboard-event.vue";
    import { SCOREBOARD_ROUTES } from "./scoreboard-routes";
    import {SCOREBOARD_STORE_CONST} from "./scorboard-store"
    import {ScoreboardService} from "./scoreboard-service"
    import {RawLocation} from "vue-router"

    @Component({
        name: "scoreboard-route",
        components: {ScoreboardEvent}
    })
    export default class ScoreboardRoute extends Vue {

        public compId: number = 0;
        scoreboardEventGroups: IScoreboardCompEventGroup[] = [];
        public scoreboardEventGroupsSelected: Record<string, boolean> = {};
        public readonly scoreboardRoutes = SCOREBOARD_ROUTES;
        public scoreboardService: ScoreboardService = new ScoreboardService();

        public isLoading: boolean = false;


        public created() {

            this.$store.commit(SCOREBOARD_STORE_CONST.SCOREBOARD_CONST_MODULE_NAME + "/" +
                SCOREBOARD_STORE_CONST.SCOREBOARD_MUTATIONS_SET_SCOREBOARD, this.scoreboardService.factoryScoreboardEvent());

            this.compId = isNaN(Number(this.$route.params.id)) ? 0 : parseInt(this.$route.params.id, 0);
            this.getEventsForCompetition(this.compId);
        }

        public getEventsForCompetition(compId: number) {
            this.isLoading = true;
            new ScoreboardData().getCompetitionEventGroups(compId)
                .then( (response) => {
                    if (response.errNo > 0) {
                        messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
                        return;
                    }

                    this.scoreboardEventGroupsSelected = response.data.reduce( (accum, sb) => {
                        //  @ts-ignore
                        accum[sb.eventNo.toString()] = false;
                        return accum;
                    }, {});

                    this.scoreboardEventGroups = response.data;
                    return;
                })
                .catch((error) => {
                    messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
                    return;
                })
            .finally(() => {
                this.isLoading = false;
            })
        }

        public rowStyle(scoreboardCompEventGroup: IScoreboardCompEventGroup, index: number) {
            const styles: Record<string, string> = {
                // lineHeight: "3vh"
            };

            if (index % 2 === 0) {
                styles["backgroundColor"] = "#fafafa";
            }
            return styles;
        }

        public getEventLink(scoreboardCompEventGroup: IScoreboardCompEventGroup, showElement: "all" | "rank" | "score") {
            return "#/scoreboard/" + scoreboardCompEventGroup.compId + "/" + scoreboardCompEventGroup.eventNo + "?show=" + showElement;
        }

        public openAllEvents() {

            const eventNos: IScoreboardsEventQuery[] = Object.keys(this.scoreboardEventGroupsSelected).reduce( (accum, key) => {
                const evtNumber = Number(key);
                if (this.scoreboardEventGroupsSelected[key] === true) {
                    accum.push({
                        //  @ts-ignore
                        eventNo: evtNumber
                    });
                }
                return accum;
            }, []);

            this.$store.commit(SCOREBOARD_STORE_CONST.SCOREBOARD_CONST_MODULE_NAME + "/" +
                SCOREBOARD_STORE_CONST.SCOREBOARD_MUTATIONS_SET_SCOREBOARD_EVT_GRP_SEL, eventNos);

            const location: RawLocation = {
                path: this.compId + "/all"
            } as RawLocation;
            this.$router.push(location);

        }

    }


</script>
