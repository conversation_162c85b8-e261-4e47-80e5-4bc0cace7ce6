import https from "../../common/https";
import {
  IScoreboardEvent,
  IScoreboardCompEventGroup,
} from "./scoreboard-models";
import {
  AthleteIdString,
  AthleteUrnNumber,
  EntryIdNumber,
  EventGroupIdNumber,
  EventGroupIdString,
  GenderType,
  IServerResponse,
  IServerResponseList,
} from "../../common/common-models";
import {
  IPublicHeatGroupMeta,
  IR4sAthleteEntry,
  IR4sCompSchedule,
  IR4sEntry,
  IRs4EventGroup,
  IRs4ScoreboardDesign,
} from "./rs4/rs4-scoreboard-models";
import { IScoreboardSchedule } from "./scoreboard-schedule/scoreboard-schedule-models";
import { ENTITY_LEVEL_NUMBER } from "../../config/config-app-models";
import { AO_CODE } from "../../common/ui/athletic-org/athletic-org-models";
import { EventType } from "../../athleteCompSched/athletecompsched-models";

export interface IAllScoreboards {
  scoreboards: IScoreboardEvent[];
  config: {
    ranksPageTime: number;
    ranksPerPage: number;
  };
}

export interface IScoreboardsEventQuery {
  eventNo: number;
}

export interface ILatestScoresQueryAgeGroup {
  ageGroup: string;
  ageGroupID: number;
  eventAgeGroup?: string;
}

export interface ILatestScoresQueryAthlete {
  id: number;
  name: string;
  aocode: AO_CODE;
  club: string;
  gender: GenderType;
  pb: number;
  sb: number;
  urn: AthleteUrnNumber | null;
}

export interface ILatestScoresQueryEntry {
  id: number;
  bibNo: number;
  checkedIn: boolean;
  collected: boolean;
  pb: number;
  present: boolean;
  teambibno: string;
}

export interface ILatestScoresQueryEventGroup {
  id: number;
  groupName: string;
  eventNo: number;
  type: EventType;
}

export interface ILatestScoresQueryHeatInfo {
  heatNo: number;
  heatNoCheckedIn: number;
  laneNo: number;
  laneNoCheckedIn: number;
  position: number;
}

export interface ILatestScoresQueryResults {
  currentPosition: number;
  heatPosition: number;
  highScore: number;
  isPb: boolean;
  isSb: boolean;
  scoreText: string;
}

export interface ILatestScoresQuery {
  ageGroup: ILatestScoresQueryAgeGroup;
  athlete: ILatestScoresQueryAthlete;
  entry: ILatestScoresQueryEntry;
  eventGroup: ILatestScoresQueryEventGroup;
  heatInfo: ILatestScoresQueryHeatInfo;
  results: ILatestScoresQueryResults;
}

export type ILatestScoresQueryResponseType = Record<
  EventGroupIdString,
  Record<AthleteIdString, ILatestScoresQuery>
>;

export type IFeederAllEntriesByEntryId = Record<
  EntryIdNumber,
  IR4sAthleteEntry
>;

export type IFeederAllEntriesForCompByEventGroupIdByEntryId = Record<
  EventGroupIdNumber,
  IFeederAllEntriesByEntryId
>;

export class ScoreboardData {
  public getCompetitionEventGroups(
    id: number
  ): Promise<IServerResponseList<IScoreboardCompEventGroup>> {
    return https.get("/v5/scoreboard/" + id) as any as Promise<
      IServerResponseList<IScoreboardCompEventGroup>
    >;
  }

  public getEventLatestScore(
    compId: number,
    eventNo: number
  ): Promise<IServerResponse<IScoreboardEvent>> {
    return https.get(
      "/v5/scoreboard/" + compId + "/" + eventNo
    ) as any as Promise<IServerResponse<IScoreboardEvent>>;
  }

  public getCompetitionEventGroupsAll(
    id: number
  ): Promise<IServerResponse<IAllScoreboards>> {
    return https.post("/v5/scoreboard/all/" + id) as any as Promise<
      IServerResponse<IAllScoreboards>
    >;
  }

  public getScoreboards(
    compId: number,
    eventNumbers: IScoreboardsEventQuery[]
  ): Promise<IServerResponse<IScoreboardEvent>> {
    const payload = {
      eventNos: eventNumbers,
    };
    return https.post(
      "/v5/scoreboard/events/" + compId,
      payload
    ) as any as Promise<IServerResponse<IScoreboardEvent>>;
  }

  public getRs4EventGroups(
    compId: number
  ): Promise<IServerResponse<IRs4EventGroup[]>> {
    return https.get("/v5/eventgroups?compId=" + compId) as any as Promise<
      IServerResponse<IRs4EventGroup[]>
    >;
  }

  /**
   * E.g.https://result4sports.co.uk/wp-json/e4s/v5/r4s/schedule/list/351?full=true&outputid=17
   * @param compId
   * @param outputId
   * @param requestFullObject
   */
  public getRs4Schedule(
    compId: number,
    outputId: number,
    requestFullObject: boolean = true
  ): Promise<IServerResponse<Record<number, IScoreboardSchedule>>> {
    return https.get(
      "/v5/r4s/schedule/list/" +
        compId +
        "?full=" +
        (requestFullObject ? "true" : "false") +
        "&outputid=" +
        outputId
    ) as any as Promise<IServerResponse<Record<number, IScoreboardSchedule>>>;
  }

  /**
   * E.g.https://result4sports.co.uk/wp-json/e4s/v5/r4s/design/list
   * @param compId
   * @param outputId
   * @param requestFullObject
   */
  public getRs4DesignList(): Promise<IServerResponse<IRs4ScoreboardDesign[]>> {
    return https.get("/v5/r4s/design/list") as any as Promise<
      IServerResponse<IRs4ScoreboardDesign[]>
    >;
  }

  public getLatestScoresNew(
    compId: number,
    eventGroupIds: number[]
  ): Promise<
    IServerResponse<
      ILatestScoresQueryResponseType,
      Record<number, Record<number, IPublicHeatGroupMeta>>
    >
  > {
    const payload = eventGroupIds.map((eventGroupId) => {
      return {
        id: eventGroupId,
      };
    });
    return https.post(
      "/v5/uicard/entries/" + compId,
      payload
    ) as any as Promise<
      IServerResponse<
        ILatestScoresQueryResponseType,
        Record<number, Record<number, IPublicHeatGroupMeta>>
      >
    >;
  }

  public getLatestScores(
    compId: number,
    eventGroupIds: number[]
  ): Promise<
    IServerResponse<
      Record<number, Record<number, IR4sEntry>>,
      Record<number, Record<number, IPublicHeatGroupMeta>>
    >
  > {
    const payload = eventGroupIds.map((eventGroupId) => {
      return {
        id: eventGroupId,
      };
    });
    return https.post(
      "/v5/uicard/entries/" + compId,
      payload
    ) as any as Promise<
      IServerResponse<
        Record<number, Record<number, IR4sEntry>>,
        Record<number, Record<number, IPublicHeatGroupMeta>>
      >
    >;
  }

  public getCompSchedule(
    compId: number,
    getEntries: boolean = false
  ): Promise<IServerResponse<IR4sCompSchedule>> {
    return https.get(
      "/v5/competition/schedule/" + compId + (getEntries ? "?entries=true" : "")
    ) as any as Promise<IServerResponse<IR4sCompSchedule>>;
  }

  public getEntriesForEventGroup(
    compId: number,
    eventGroupId: number,
    entityId: number = 0,
    entityLevel: ENTITY_LEVEL_NUMBER = 0
  ): Promise<IServerResponse<IR4sAthleteEntry[]>> {
    return https.get("/v5/event/entries/" + compId + "/" + eventGroupId, {
      params: {
        entityId,
        entityLevel,
      },
    }) as any as Promise<IServerResponse<IR4sAthleteEntry[]>>;
  }

  public getAllEntriesForComp(
    compId: number,
    entityId: number = 0,
    entityLevel: ENTITY_LEVEL_NUMBER = 0
  ): Promise<
    IServerResponse<
      Record<EventGroupIdNumber, Record<EventGroupIdString, IR4sAthleteEntry>>
    >
  > {
    return https.get("/v5/event/entries/" + compId, {
      params: {
        entityId,
        entityLevel,
      },
    });
  }

  public getEntriesForEventGroupIncludingUnpaid(
    compId: number,
    eventGroupId: number,
    entityId: number = 0,
    entityLevel: ENTITY_LEVEL_NUMBER = 0
  ): Promise<IServerResponse<IR4sAthleteEntry[]>> {
    return https.get("/v5/event/allentries/" + compId + "/" + eventGroupId, {
      params: {
        entityId,
        entityLevel,
      },
    });
  }

  public getAllEntriesForCompIncludingUnpaid(
    compId: number,
    entityId: number = 0,
    entityLevel: ENTITY_LEVEL_NUMBER = 0
  ): Promise<IServerResponse<IFeederAllEntriesForCompByEventGroupIdByEntryId>> {
    return https.get("/v5/event/allentries/" + compId, {
      params: {
        entityId,
        entityLevel,
      },
    });
  }
}

export interface IAthleteEntryFeederBase {
  paid: boolean;
  egId: EventGroupIdNumber;
  entryId: EntryIdNumber;
}

export interface IAthleteEntryFeeder extends IAthleteEntryFeederBase {
  athleteId: number;
  urn: AthleteUrnNumber;
}

export interface ITeamEntryFeederAthlete extends IAthleteEntryFeeder {
  id: number;
  urn: AthleteUrnNumber;
}

export interface ITeamEntryFeeder extends IAthleteEntryFeederBase {
  teamName: string;
  athletes: ITeamEntryFeederAthlete[];
}
