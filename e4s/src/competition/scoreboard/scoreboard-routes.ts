
export const SCOREBOARD_ROUTES = {
    SCOREBOARD_COMP_ROUTE: "comp",
    SCOREBOARD_COMP_EVENT_ROUTE: "event",
    SCOREBOARD_COMP_EVENT_ALL_ROUTE: "all",
    SCOREBOARD_OUTPUT_DISPLAY: "output"
};


export default [
    {
        name: "",
        path: ":id",
        component: () => {
            return import("./scoreboard-route.vue");
        }
    },
    {
        name: SCOREBOARD_ROUTES.SCOREBOARD_COMP_EVENT_ALL_ROUTE,
        path: ":compid/all",
        component: () => {
            return import("./scoreboard-event-all/scoreboard-event-all.vue");
        }
    },
    {
        name: SCOREBOARD_ROUTES.SCOREBOARD_COMP_EVENT_ROUTE,
        path: ":compid/:eventno",
        component: () => {
            return import("./scoreboard-event/scoreboard-event-route.vue");
        }
    },
    {
        name: SCOREBOARD_ROUTES.SCOREBOARD_OUTPUT_DISPLAY,
        path: SCOREBOARD_ROUTES.SCOREBOARD_OUTPUT_DISPLAY + "/:compId/:outputId",
        component: () => {
            return import("./scoreboard-output/display/scoreboard-output-display-route.vue");
        }
    }
    // {
    //     name: SCOREBOARD_ROUTES.SCOREBOARD_OUTPUT_DISPLAY_V2,
    //     path: SCOREBOARD_ROUTES.SCOREBOARD_OUTPUT_DISPLAY_V2 + "/:compId/:outputId",
    //     component: () => {
    //         return import("./scoreboard-output/display/v2/scoreboard-output-display-v2-route.vue");
    //     }
    // }
];
