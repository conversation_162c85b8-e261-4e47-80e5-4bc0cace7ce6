<template>
    <div>
        <span v-text="message"></span>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import {ICompEventGroupAthletes} from "../../athlete/athlete-models";
import {AthleteService} from "../../athlete/athlete-service";

const athleteService: AthleteService = new AthleteService();

@Component({
    name: "event-group-athlete-grid",
    components: {
    }
})
export default class EventGroupAthleteGrid extends Vue {
    @Prop({
        default: () => {
            return athleteService.factoryCompEventGroupAthletes();
        }
    })
    public readonly compEventGroupAthletes: ICompEventGroupAthletes;
}
</script>
