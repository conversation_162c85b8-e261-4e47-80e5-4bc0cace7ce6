<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <div v-if="athleteSecurity.areas.length > 0">
      This competition is currently restricted to athletes from the following
      area<span v-text="athleteSecurity.areas.length > 1 ? 's' : ''"></span>:

      <li
        v-for="club in restrictedAreas"
        :key="club.id"
        v-text="club.name"
      ></li>
    </div>

    <div v-if="athleteSecurity.clubs.length > 0">
      This competition is currently restricted to athletes from the following
      club<span v-text="athleteSecurity.clubs.length > 1 ? 's' : ''"></span>:

      <li
        v-for="club in restrictedClubs"
        :key="club.id"
        v-text="club.name"
      ></li>
    </div>

    <div v-text="cutOffDateTime"></div>
    Athlete's club or second claim club must be one of the above, please ensure
    athlete profiles are updated accordingly.

    <div v-if="athlete.id > 0">
      <a :href="getAthleteProfileLink"
        >Edit <span v-text="athlete.firstName"></span>'s profile</a
      >.
    </div>

    <div v-if="showAthleteProfilesLink">
      All of your athlete profiles can be found under
      <a :href="getMyAccountAthletes">My Account\ Athletes</a>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "vue";
import { IAthleteSecurity } from "../../builder/builder-models";
import { IBase } from "../../common/common-models";
import { format, parse } from "date-fns";
import { IAthlete } from "../../athlete/athlete-models";
import { LAUNCH_ROUTES_PATHS } from "../../launch/launch-routes";
import { simpleClone } from "../../common/common-service-utils";

export default defineComponent({
  name: "CompRestrictedV2",
  components: {},
  props: {
    athleteSecurity: {
      type: Object as PropType<IAthleteSecurity>,
      default: () => {
        return {
          areas: [],
          clubs: [],
          onlyClubsUpTo: "",
        };
      },
    },
    athlete: {
      type: Object as PropType<IAthlete>,
      default: () => {
        return {
          id: 0,
          firstName: "",
        };
      },
    },
    showAthleteProfilesLink: {
      type: Boolean,
      default: true,
    },
  },
  setup(
    props: {
      athleteSecurity: IAthleteSecurity;
      athlete: IAthlete;
      showAthleteProfilesLink: boolean;
    },
    context: SetupContext
  ) {
    const restrictedAreas = computed<IBase[]>(() => {
      const areas = simpleClone(props.athleteSecurity.areas);
      return areas.sort((a: IBase, b: IBase) => {
        if (a.name && b.name) {
          return a.name.localeCompare(b.name);
        }
        return 1;
      });
    });

    const restrictedClubs = computed<IBase[]>(() => {
      const clubs = simpleClone(props.athleteSecurity.clubs);
      return clubs.sort((a: IBase, b: IBase) => {
        if (a.name && b.name) {
          return a.name.localeCompare(b.name);
        }
        return 1;
      });
    });

    const cutOffDateTime = computed(() => {
      if (
        props.athleteSecurity.onlyClubsUpTo &&
        props.athleteSecurity.onlyClubsUpTo.length > 0
      ) {
        const onlyClubsUpTo = format(
          parse(props.athleteSecurity.onlyClubsUpTo),
          "Do MMM HH:mm"
        );
        return " Restriction Ends: " + onlyClubsUpTo;
      }
      //  No date set, so comp completely locked down to these clubs.
      return "";
    });

    const getAthleteProfileLink = computed(() => {
      return "#/" + LAUNCH_ROUTES_PATHS.ATHLETE + "/" + props.athlete.id;
    });

    const getMyAccountAthletes = computed(() => {
      return "#/" + LAUNCH_ROUTES_PATHS.ATHLETES;
    });

    return {
      restrictedAreas,
      restrictedClubs,
      cutOffDateTime,
      getAthleteProfileLink,
      getMyAccountAthletes,
    };
  },
});
</script>
