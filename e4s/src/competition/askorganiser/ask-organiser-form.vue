<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <div class="e4s-flex-column e4s-gap--standard">
          <span>
            Your request will sent to the organiser of this competition.
          </span>
          <span
            v-text="getOrganiserDetails.userName"
            v-if="getOrganiserDetails.userName.length > 0"
          ></span>
          <span
            v-text="getOrganiserDetails.email"
            v-if="getOrganiserDetails.email.length > 0"
          ></span>
        </div>
      </div>
    </div>

    <div style="padding: 1rem 0 0 0"></div>

    <!--    <div class="row">-->
    <!--      <div class="col s12 m12 l12">-->
    <!--        Select category and enter your query below.-->
    <!--      </div>-->

    <!--      <div style="padding: 3rem 0 0 0"></div>-->
    <!--    </div>-->

    <div class="row">
      <!--      <div class="col s12 m12 l12">-->
      <!--        <label class="active" for="selected-category">-->
      <!--          Category-->
      <!--          <ValidationFieldLable-->
      <!--            :validation-controller="validationController"-->
      <!--            prop-path="selectedCategory"-->
      <!--          ></ValidationFieldLable>-->
      <!--        </label>-->

      <!--        <select-->
      <!--          class="browser-default"-->
      <!--          id="selected-category"-->
      <!--          v-model="selectedCategory"-->
      <!--        >-->
      <!--          <option value="">Please Choose</option>-->
      <!--          <option-->
      <!--            v-for="simpleValue in categories"-->
      <!--            :value="simpleValue"-->
      <!--            v-text="simpleValue"-->
      <!--          ></option>-->
      <!--        </select>-->
      <!--      </div>-->

      <div class="col s12 m12 l12">
        <label class="active" for="message-body">
          Message
          <span :style="isMaxLength ? 'color: red' : ''"
            >(max <span v-text="maxBodyLength"></span> characters)</span
          >
          <ValidationFieldLable
            :validation-controller="validationController"
            prop-path="body"
          ></ValidationFieldLable>
        </label>
        <InputRestrictLength
          id="message-body"
          :use-text-area="true"
          :max-length="maxBodyLength"
          text-area-class="ask-organiser--text-area"
          v-on:isMaxLength="isMaxLength = $event"
          v-on:onChanged="body = $event"
        >
        </InputRestrictLength>
      </div>

      <div class="row" v-if="!isLoggedIn">
        <div class="input-field col s12 m12 l12">
          <label class="active" for="contact-email">
            Email
            <ValidationFieldLable
              :validation-controller="validationController"
              prop-path="email"
            ></ValidationFieldLable>
          </label>
          <input
            id="contact-email"
            name="contact-email"
            class="e4s-input"
            v-model="email"
            placeholder=""
          />
        </div>
      </div>

      <div class="col s12 m12 l12">
        <div class="right">
          <LoadingSpinner v-if="isLoading"></LoadingSpinner>
          <div class="e4s-flex-row">
            <div class="e4s-flex-row--end e4s-gap--standard">
              <ButtonGenericV2
                text="Cancel"
                @click="onClose"
                :disabled="isLoading"
                button-type="secondary"
              />
              <ButtonGenericV2
                text="Send"
                :disabled="isLoading"
                @click="onSubmit"
              />
            </div>
          </div>

          <!--          <button-->
          <!--            class="btn waves-effect waves red"-->
          <!--            :disabled="isLoading"-->
          <!--            v-on:click="onClose"-->
          <!--          >-->
          <!--            Cancel-->
          <!--          </button>-->
          <!--          <button-->
          <!--            class="btn waves-effect waves green"-->
          <!--            :disabled="isLoading"-->
          <!--            v-on:click="onSubmit"-->
          <!--          >-->
          <!--            Send-->
          <!--          </button>-->
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <span v-text="validationMessage"></span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import InputRestrictLength from "../../common/ui/field/input-restrict-length/input-restrict-length.vue";
import { AskOrganiserData } from "./ask-organiser-data";
import { Prop, Watch } from "vue-property-decorator";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
import { ValidationController } from "../../validation/validation-controller";
import ValidationFieldLable from "../../validation/validation-field-lable.vue";
import { mapState } from "vuex";
import { AUTH_STORE_CONST, IAuthStoreState } from "../../auth/auth-store";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import { IConfigApp, IUserApplication } from "../../config/config-app-models";
import {
  ICompetitionInfo,
  ICompetitionSummaryPublic,
} from "../competition-models";
import { IBuilderOptions, IContact } from "../../builder/builder-models";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";

@Component({
  name: "ask-organiser-form",
  components: { ButtonGenericV2, ValidationFieldLable, InputRestrictLength },
  computed: {
    ...mapState(AUTH_STORE_CONST.AUTH_CONST_MODULE_NAME, [
      'isLoggedIn'
    ]),
    ...mapState({
      userApplication: (state: any) => state[CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME].configApp.userInfo.user,
      configApp: (state: any) => state[CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME].configApp,
    }),
  },
})
export default class AskOrganiserForm extends Vue {
  public readonly isLoggedIn!: boolean;
  public userApplication!: IUserApplication;
  public configApp: IConfigApp;

  @Prop({ default: 0 })
  public readonly compId: number;

  @Prop({
    default: () => {
      return { id: 0 } as ICompetitionSummaryPublic;
    },
  })
  public readonly competition: ICompetitionSummaryPublic | ICompetitionInfo;

  public categories: string[] = [
    "Competition query",
    "Technical or Payment Query",
  ];

  public selectedCategory: string = "Competition query";
  public body: string = "";
  public email = "";

  public isLoading = false;
  public validationMessage = "";
  public isMaxLength: boolean = false;
  public maxBodyLength: number = 600;

  public validationController: ValidationController =
    new ValidationController();

  public created() {
    this.email = this.userApplication.user_email;
    if (this.configApp.theme.toUpperCase() === "IRL") {
      this.categories.push("Registration Query");
    }
  }

  public get getOrganiserDetails(): Pick<IContact, "userName" | "email"> {
    const defaultContact: Pick<IContact, "userName" | "email"> = {
      userName: "",
      email: "",
    };
    if ((this.competition as ICompetitionInfo).id === 0) {
      return defaultContact;
    }

    if (this.competition.compId && this.competition.compId === 0) {
      return defaultContact;
    }

    const options: IBuilderOptions = this.competition.options;
    if (options) {
      defaultContact.userName = options.contact.userName || "";
      defaultContact.email = options.contact.email || "";
    }
    return defaultContact;
  }

  @Watch("userApplication")
  public onUserApplicationChanged(newValue: IUserApplication) {
    this.email = newValue.user_email;
  }

  public reset() {
    this.selectedCategory = "";
    this.body = "";
  }

  public validate() {
    this.validationController.reset();
    if (this.selectedCategory.length === 0) {
      this.validationController.addError("selectedCategory", [
        "Please select.",
      ]);
    }
    const body = this.body.replace(/\s/g, "");
    if (body.length === 0) {
      this.validationController.addError("body", ["Please enter a message."]);
    }
    if (!this.isLoggedIn && this.email.length === 0) {
      this.validationController.addError("email", [
        "Please enter a contact email address.",
      ]);
    }
  }

  public onClose() {
    this.$emit("onClose");
  }

  public onSubmit() {
    this.validate();

    if (!this.validationController.isValid) {
      return;
    }

    this.isLoading = true;

    new AskOrganiserData()
      .submitAskOrganiser(
        this.compId,
        this.selectedCategory,
        this.body,
        this.email
      )
      .then((response) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }

        messageDispatchHelper(
          "Message sent.",
          USER_MESSAGE_LEVEL.INFO.toString()
        );
        this.onClose();
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
      })
      .finally(() => {
        this.isLoading = false;
      });
  }
}
</script>

<style>
.ask-organiser--text-area {
  height: 10rem !important;
}
</style>
