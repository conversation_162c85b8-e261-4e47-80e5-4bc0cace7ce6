<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <div class="e4s-flex-row">
      <FormGenericSectionTitleV2
        title-size="400"
        :section-title="formTitle"
        :section-overview="getSectionOverview"
      />

      <div class="e4s-flex-row--end">
        <slot name="top-right-back-button"></slot>
      </div>
    </div>

    <div class="e4s-vertical-spacer--standard"></div>

    <!--    <FormGenericInputTemplateV2 form-label="Category">-->
    <!--      <template slot="field">-->
    <!--        <select-->
    <!--          class="browser-default e4s-input-field e4s-input-field&#45;&#45;primary"-->
    <!--          v-model="selectedCategory"-->
    <!--        >-->
    <!--          <option value="">Please Choose</option>-->
    <!--          <option-->
    <!--            v-for="simpleValue in categories"-->
    <!--            :value="simpleValue"-->
    <!--            v-text="simpleValue"-->
    <!--          ></option>-->
    <!--        </select>-->
    <!--        <FormGenericInputErrorMessageV2-->
    <!--          :error-message="errorMessages.selectedCategory"-->
    <!--        />-->
    <!--      </template>-->
    <!--    </FormGenericInputTemplateV2>-->

    <FormGenericInputTemplateV2
      :form-label="
        'Message (max 600 characters, current: ' + currentCharLength + ')'
      "
    >
      <template slot="field">
        <InputRestrictLength
          slot="content"
          :use-text-area="true"
          :max-length="600"
          text-area-class="browser-default e4s-input-field e4s-input-field--primary ask-organiser--text-area"
          @isMaxLength="isMaxLength = $event"
          @onChanged="body = $event"
          @charLength="currentCharLength = $event"
        >
        </InputRestrictLength>
        <FormGenericInputErrorMessageV2 :error-message="errorMessages.body" />
      </template>
    </FormGenericInputTemplateV2>

    <FormGenericInputTextV2
      v-if="!authStore.isLoggedIn"
      form-label="Your Email"
      :value="email"
      :error-message="errorMessages.email"
    />

    <div class="e4s-flex-row e4s-justify-flex-space-between">
      <ButtonGenericV2
        text="Cancel"
        button-type="tertiary"
        v-on:click="close"
      />
      <ButtonGenericV2 text="Send" v-on:click="submit" />
    </div>

    <LoadingSpinnerV2 v-if="isLoading" />
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  ref,
  SetupContext,
} from "vue";
import { ICompetitionSummaryPublic } from "../competition-models";
import { useCompPermissions } from "../../config/useCompPermissions";
import { AskOrganiserData } from "./ask-organiser-data";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
import CompContentSectionCellV2 from "../../public/entry-public/public-list/v2/comp-content-section-cell-v2.vue";
import FormGenericSectionTitleV2 from "../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import InputRestrictLength from "../../common/ui/field/input-restrict-length/input-restrict-length.vue";
import { ValidationController } from "../../validation/validation-controller";
import { useAuthStore } from "../../auth/useAuthStore";
import ValidationFieldLable from "../../validation/validation-field-lable.vue";
import { useValidationControllerV2 } from "../../validation/useValidationControllerV2";
import FormGenericInputErrorMessageV2 from "../../common/ui/layoutV2/form/form-generic-input-error-message-v2.vue";
import FormGenericInputTextV2 from "../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import CardGenericV2 from "../../common/ui/layoutV2/card-generic-v2.vue";
import FormGenericButtonBar from "../../common/ui/layoutV2/form/form-generic-button-bar.vue";
import { useConfigStore } from "../../config/useConfigStore";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import LoadingSpinnerV2 from "../../common/ui/loading-spinner-v2.vue";
import { getOrganiserDetailsToDisplay } from "./ask-organiser-service";
import FormGenericInputTemplateV2 from "../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";

export default defineComponent({
  name: "ask-organiser-form-v2",
  components: {
    FormGenericInputTemplateV2,
    LoadingSpinnerV2,
    ButtonGenericV2,
    FormGenericButtonBar,
    CardGenericV2,
    FormGenericInputTextV2,
    FormGenericInputErrorMessageV2,
    ValidationFieldLable,
    InputRestrictLength,
    FormGenericSectionTitleV2,
    CompContentSectionCellV2,
  },
  props: {
    competitionSummaryPublic: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      default: () => {
        return [];
      },
    },
    formTitle: {
      type: String,
      default: "Ask Organiser",
    },
    sectionOverview: {
      type: String,
      default: "",
    },
    foaE4s: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
  },
  setup(
    props: {
      competitionSummaryPublic: ICompetitionSummaryPublic;
      formTitle: string;
      sectionOverview: string;
      foaE4s: boolean;
    },
    context: SetupContext
  ) {
    const compPermissions = useCompPermissions(props.competitionSummaryPublic);
    const authStore = useAuthStore();
    const configStore = useConfigStore();

    const selectedCategory = ref("Competition query");
    const email = ref(
      authStore.isLoggedIn ? configStore.configApp.userInfo.user.user_email : ""
    );
    const body = ref("");
    const isLoading = ref(false);
    const isMaxLength = ref(false);
    const maxBodyLength = 600;
    const currentCharLength = ref(600);

    const errorMessages = reactive({
      selectedCategory: "",
      body: "",
      email: "",
    });

    const validationController: ValidationController =
      new ValidationController();

    const validationControllerV2 =
      useValidationControllerV2(validationController);

    const categories: string[] = [
      "Competition query",
      "Technical or Payment query",
    ];

    const organiserDetailsToDisplay = computed(() => {
      return getOrganiserDetailsToDisplay(props.competitionSummaryPublic);
    });

    const getSectionOverview = computed(() => {
      if (props.sectionOverview.length > 0) {
        return props.sectionOverview;
      }
      return (
        "Your request will sent to the organiser of this competition:" +
        organiserDetailsToDisplay.value
      );
    });

    function close() {
      context.emit("cancel");
    }

    function validate() {
      validationController.reset();
      errorMessages.body = "";
      errorMessages.email = "";
      errorMessages.selectedCategory = "";
      if (selectedCategory.value.length === 0) {
        validationController.addError("selectedCategory", [
          "Please select a category.",
        ]);
        errorMessages.selectedCategory = "Please select a category.";
      }
      const bodyInt = body.value.replace(/\s/g, "");
      if (bodyInt.length === 0) {
        validationController.addError("body", ["Please enter a message."]);
        errorMessages.body = "Please enter a message.";
      }
      if (bodyInt.length > maxBodyLength) {
        validationController.addError("body", [
          "Please only enter " + maxBodyLength + " characters.",
        ]);
        errorMessages.body =
          "Please only enter " + maxBodyLength + " characters.";
      }
      if (!authStore.isLoggedIn && email.value.length === 0) {
        validationController.addError("email", [
          "Please enter a contact email address.",
        ]);
        errorMessages.email = "Please enter a contact email address.";
      }
    }

    function submit() {
      validate();
      if (!validationController.isValid) {
        return;
      }

      isLoading.value = true;
      new AskOrganiserData()
        .submitAskOrganiser(
          props.competitionSummaryPublic.compId
            ? props.competitionSummaryPublic.compId
            : props.competitionSummaryPublic.id,
          selectedCategory.value,
          body.value,
          email.value,
          props.foaE4s
        )
        .then((response) => {
          if (response.errNo > 0) {
            messageDispatchHelper(
              response.error,
              USER_MESSAGE_LEVEL.ERROR.toString()
            );
            return;
          }

          messageDispatchHelper(
            "Message sent.",
            USER_MESSAGE_LEVEL.INFO.toString()
          );
          close();
        })
        .catch((error) => {
          messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        })
        .finally(() => {
          isLoading.value = false;
        });
    }

    return {
      maxBodyLength,
      organiserDetailsToDisplay,
      categories,
      selectedCategory,
      body,
      email,
      isLoading,
      isMaxLength,
      close,
      compPermissions,
      submit,
      validationController,
      validationControllerV2,
      errorMessages,
      authStore,
      currentCharLength,
      getSectionOverview,
    };
  },
});
</script>

<style>
.ask-organiser--text-area {
  height: 10rem !important;
}
</style>
