import { useHttpResponseControllerReactive } from "../common/useHttpController";
import * as CompetitionDataV2 from "./v2/competition-data-v2";
import { ICompFilterParams } from "./v2/competition-data-v2";
import { reactive } from "vue";
import {
  IBaseConcrete,
  IServerResponseListMeta,
} from "../common/common-models";
import { ICompetitionSummaryPublicV2 } from "./competition-models";

export interface ICompetitionDataControllerResult extends IBaseConcrete {
  dates: string[];
}

export function useCompetitionDataController() {
  const httpController =
    useHttpResponseControllerReactive<
      IServerResponseListMeta<ICompetitionDataControllerResult[], unknown>
    >();

  const searchParams = reactive<ICompFilterParams>({
    fromDate: "",
    toDate: "",
    freeTextSearch: "", // club, location, organiser,comp name
    organiser: {
      id: 0,
      name: "",
    },
    location: {
      id: 0,
      name: "",
    },
    type: "ALL",
    compOrg: {
      id: 0,
      name: "",
    }, // the user has builder perms. "My comps"
    event: [],
    pagenumber: 1,
  });

  function getMyComps() {
    function getData(): Promise<
      IServerResponseListMeta<IBaseConcrete, unknown>
    > {
      return new Promise((resolve, reject) => {
        CompetitionDataV2.getFilteredComps(searchParams).then((response) => {
          if (response?.errNo === 0) {
            const data: ICompetitionSummaryPublicV2[] =
              response.data as ICompetitionSummaryPublicV2[];
            const compsConcrete: ICompetitionDataControllerResult[] = data.map(
              (comp) => {
                return {
                  id: comp.competition.id,
                  name: comp.competition.name,
                  dates: [comp.competition.date],
                };
              }
            );
            const responseMapped: IServerResponseListMeta<
              ICompetitionDataControllerResult,
              unknown
            > = response as any as IServerResponseListMeta<
              ICompetitionDataControllerResult,
              unknown
            >;
            const defaultComp: ICompetitionDataControllerResult = {
              id: 0,
              name: "Please Select",
              dates: [],
            };
            responseMapped.data = [defaultComp].concat(compsConcrete);
            resolve(responseMapped);
          }

          //  @ts-ignore
          resolve(response);
        });
      });
    }

    return new Promise<IBaseConcrete[]>((resolve, reject) => {
      httpController.getData(getData()).finally(() => {
        resolve([] as IBaseConcrete[]);
      });
    });
  }

  function setSearchParams(params: Partial<ICompFilterParams>) {
    //  Loop across all the properties and set them
    const keys: (keyof ICompFilterParams)[] = Object.keys(
      params
    ) as (keyof ICompFilterParams)[];

    keys.forEach((key: keyof ICompFilterParams) => {
      const element: unknown = params[key];
      if (element) {
        // @ts-ignore
        searchParams[key] = element;
      }
    });
  }

  return {
    searchParams,
    httpController,
    getMyComps,
    setSearchParams,
  };
}
