{
    "include": ["./src"],
    "exclude": ["**/node_modules", "**/.*/"],
    "compilerOptions": {
        "strict": true,
        "module": "esnext",
        "target": "es5",
        "sourceMap": true,
        "esModuleInterop": true,
        "moduleResolution": "node",
        "allowSyntheticDefaultImports": true,
        "experimentalDecorators": true,
        "noUnusedLocals": true,
        "noImplicitReturns": true,
        "emitDecoratorMetadata": true,
        "strictPropertyInitialization": false,
        //  "noImplicitAny": true,
        "lib": ["dom", "es2015"],
        "skipLibCheck": true
    },   //,
    "plugins": [
        {
            "name": "typescript-tslint-plugin"
        }
    ]
    // "exclude": [
    //     "node_modules"
    // ],
}


//"allowJs": true,
//"checkJs": true,