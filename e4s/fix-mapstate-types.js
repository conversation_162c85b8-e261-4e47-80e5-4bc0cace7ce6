#!/usr/bin/env node

/**
 * Comprehensive script to fix all mapState TypeScript errors
 * Replaces specific store state types with 'any' to match Vuex expectations
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Store state interface patterns to replace
const STORE_STATE_PATTERNS = [
  'IUserMessageStoreState',
  'ITeamStoreState', 
  'IConfigStoreState',
  'ICheckInStoreState',
  'IAthCompSchedStoreState',
  'IEntryStoreState',
  'ISecondaryStoreState',
  'IAuthStoreState',
  'IPublicCompsStoreState',
  'IBuilderStoreState',
  'IFieldHelpStoreState',
  'ICompEventTeamsStoreState',
  'IPermissionStoreState',
  'IClubStoreState',
  'ICartStoreState',
  'ISecondaryCustStoreState',
  'IScoreboardStoreState',
  'IResultsStoreState',
  'IScheduleCompPublicStoreState'
];

/**
 * Process a single Vue file to fix mapState type issues
 */
function processFile(filePath) {
  console.log(`Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  let changeCount = 0;

  // Fix each store state interface pattern
  STORE_STATE_PATTERNS.forEach(storeStateType => {
    const pattern = new RegExp(`\\(state:\\s*${storeStateType}\\)`, 'g');
    const matches = content.match(pattern);
    
    if (matches) {
      content = content.replace(pattern, '(state: any)');
      hasChanges = true;
      changeCount += matches.length;
      console.log(`  ✓ Fixed ${matches.length} instances of ${storeStateType}`);
    }
  });

  // Also fix any remaining specific interface patterns we might have missed
  const genericPattern = /\(state:\s*I\w+StoreState\)/g;
  const genericMatches = content.match(genericPattern);
  if (genericMatches) {
    content = content.replace(genericPattern, '(state: any)');
    hasChanges = true;
    changeCount += genericMatches.length;
    console.log(`  ✓ Fixed ${genericMatches.length} additional store state type references`);
  }

  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  ✅ File updated with ${changeCount} changes: ${filePath}`);
    return changeCount;
  }

  return 0;
}

/**
 * Main execution
 */
function main() {
  console.log('🔧 Starting comprehensive mapState type fixes...\n');
  
  // Find all Vue files
  const vueFiles = glob.sync('src/**/*.vue', { cwd: __dirname });
  
  let totalFiles = 0;
  let modifiedFiles = 0;
  let totalChanges = 0;
  
  vueFiles.forEach(file => {
    totalFiles++;
    const fullPath = path.join(__dirname, file);
    
    const changes = processFile(fullPath);
    if (changes > 0) {
      modifiedFiles++;
      totalChanges += changes;
    }
  });
  
  console.log(`\n📊 Summary:`);
  console.log(`   Total files processed: ${totalFiles}`);
  console.log(`   Files modified: ${modifiedFiles}`);
  console.log(`   Files unchanged: ${totalFiles - modifiedFiles}`);
  console.log(`   Total type fixes applied: ${totalChanges}`);
  
  if (modifiedFiles > 0) {
    console.log(`\n✅ mapState type fixes completed!`);
    console.log(`\n🔍 Next steps:`);
    console.log(`   1. Run: npx tsc --noEmit`);
    console.log(`   2. Run: npm run build`);
    console.log(`   3. Test the application`);
    console.log(`   4. Commit changes if everything works`);
  } else {
    console.log(`\n✅ No mapState type issues found - all files are already correct!`);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { processFile, STORE_STATE_PATTERNS };